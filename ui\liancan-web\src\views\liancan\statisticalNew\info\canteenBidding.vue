<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
<!--      <template slot="menu" slot-scope="scope">-->
<!--        <el-button type="text" size="small" icon="el-icon-view" @click="opendialog(scope.row)">查看-->
<!--        </el-button>-->
<!--      </template>-->
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList} from "@/api/backendBidding/bidding";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        indexLabel:"序号",
        menu: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        align: "center",
        column: [
          {
            label: "所属食堂",
            prop: "biddingUnit",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: true,
            search: false,
          },
          {
            label: "招标类型",
            prop: "purchaseType",
            type: "select",
            addDisplay: false,
            search: true,
            dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict",
            props: {
              label: "name",
              value: "id"
            },
            rules: [{
              required: true,
              message: "请选择招标类型",
              trigger: "blur"
            }],
            cascaderItem: ['commodityType'],
            cascaderChange: true,
            width: 80,
          },
          {
            label: "招标大类",
            prop: "commodityType",
            type: "select",
            addDisplay: false,
            search: true,
            dicFlag: false,
            dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            rules: [{
              required: true,
              message: "请选择招标目录类型",
              trigger: "blur"
            }],
          },
          {
            label: "项目名称",
            prop: "entryName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
          },
          {
            label: "招标单位",
            prop: "unitName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "招标预算",
            prop: "money",
            type: "number",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "项目编号",
            prop: "number",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "已报名单位数量",
            prop: "quantity",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            width: 110,
          },
          {
            label: "报名开始时间",
            prop: "startTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "报名截止时间",
            prop: "endTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            width: 150,
          },
          {
            label: "联系人",
            prop: "name",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "联系电话",
            prop: "phone",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "到货时间要求",
            prop: "arrivalTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "签约时间要求",
            prop: "signingTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "付款方式",
            prop: "payType",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "收货地址",
            prop: "shoppingAddress",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "开标地址",
            prop: "bidOpeningAddress",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "开标时间",
            prop: "bidOpeningTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "商品参数及质量要求",
            prop: "qualityRequirements",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            overHidden: true,
          },
          {
            label: "招标理由",
            prop: "reason",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "审批人",
            prop: "approvedBy",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "进度",
            prop: "rateOfProgress",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=rate_of_progress",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            width: 70,
          },
          {
            label: "审批",
            prop: "approvalStatus",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=approval_status",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            width: 70,
          },
          {
            label: "入围名单",
            prop: "shortlist",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ]
      },
      data: [],
    }
  },
  created(){
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
  },
  methods: {
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {"biddingUnit": this.schoolId, "isStatistical": 1}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
  }
}
</script>

<style scoped>

</style>
