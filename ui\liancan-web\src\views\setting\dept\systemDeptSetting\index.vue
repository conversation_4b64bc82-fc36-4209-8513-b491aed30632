<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               ref="crud"
               v-model="form"
               :permission="permissionList"
               :before-open="beforeOpen"
               :before-close="beforeClose"
               @row-del="rowDel"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @refresh-change="refreshChange"
               @on-load="onLoad"
               @tree-load="treeLoad">
<!--      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   v-if="permission.dept_delete"
                   plain
                   @click="handleDelete">删 除
        </el-button>
      </template>-->
      <template slot-scope="{row}"
                slot="deptCategory">
        <el-tag>{{row.deptCategoryName}}</el-tag>
      </template>
      <template slot="menuLeft">
        <el-button v-if="permission.system_dept_setting_synchron_dept && this.modeType == '2'" class="filter-item" size="small" type="warning" icon="iconfont iconicon_cloud_history" @click="synchronSchoolDept">同步云校部门</el-button>
<!--        <el-button v-if="permission.system_dept_setting_synchron_class && this.modeType == '2' && this.dept.canteenType == '1'" class="filter-item" size="small" type="warning" icon="iconfont iconicon_cloud_history" @click="synchronSchoolClass">同步云校班级</el-button> -->
        <el-button v-if="permission.system_dept_setting_synchron_class && this.modeType == '2'" class="filter-item" size="small" type="warning" icon="iconfont iconicon_cloud_history" @click="synchronSchoolClass">同步云校班级</el-button>
        <el-button v-if="this.modeType2 == '1' && this.modeType3 != '3'" class="filter-item" size="small" type="warning" icon="el-icon-download" @click="synchronCard">同步一卡通部门数据</el-button>
      </template>
    </avue-crud>
    <el-dialog title="同步" :visible.sync="batchEditVisible" :append-to-body="true" @close="batchEditClose" width="60%">
      <avue-form ref="editForm" :option="editOption" v-model="editForm" @submit="batchUpdatePersonnel">

      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {
    getLazyList,
    remove,
    update,
    add,
    getDept,
    getDeptTree,
    synchronSchoolDept,
    synchronSchoolClass,
    synchronCard
  } from "@/api/setting/dept/systemDeptSetting";
  import {
    getDeptAgencyType,
  } from "@/api/system/dept";
  import {mapGetters} from "vuex";
  import website from '@/config/website';

  export default {
    data() {
      return {
        form: {},
        selectionList: [],
        query: {},
        loading: true,
        parentId: 0,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0,
        },
        dept: {},
        modeType:undefined,
        modeType2:undefined,
        modeType3:undefined,
        option: {
          lazy: true,
          tip: false,
          searchShow: true,
          searchMenuSpan: 6,
          tree: true,
          border: true,
          index: true,
          selection: true,
          viewBtn: true,
          menuWidth: 300,
          addBtn:true,
          dialogClickModal: false,
          column: [
            {
              label: "部门名称",
              prop: "deptName",
              rules: [{
                required: true,
                message: "请输入部门名称",
                trigger: "blur"
              }]
            },
            {
              label: "部门负责人",
              prop: "teacherId",
              type: "select",
              dicUrl: "/api/service/rabbit-personnel/systemPersonnel/teacher/dict",
              props: {
                label: "userName",
                value:"id"
              },
              hide: false,
 /*             rules: [{
                required: true,
                message: "请选择班主任",
                trigger: "click"
              }]*/
            },
     /*       {
              label: "班主任",
              prop: "teacherId",
              rules: [{
                required: true,
                message: "请输入部门名称",
                trigger: "blur"
              }]
            },*/
            {
              label: "上级部门",
              prop: "parentId",
              dicData: [],
              type: "tree",
              hide: true,
              props: {
                label: "title"
              },
              rules: [{
                required: false,
                message: "请选择上级部门",
                trigger: "click"
              }]
            },
            {
              label: "排序",
              prop: "sort",
              type: "number",
              align: "right",
              width: 80,
              rules: [{
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }]
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.system_dept_setting_add, false),
          viewBtn: this.vaildData(this.permission.system_dept_setting_view, false),
          delBtn: this.vaildData(this.permission.system_dept_setting_delete, false),
          editBtn: this.vaildData(this.permission.system_dept_setting_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      getDeptAgencyType().then(res =>{
        if (res.data.data.applicationMode === '1'){
          this.modeType = '1';
          this.modeType2 = '2';
          this.modeType3 = '1';
        }else if (res.data.data.applicationMode === '2'){
          this.modeType = '2';
          this.modeType2 = '2';
          this.modeType3 = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }else if (res.data.data.applicationMode === '3'){
          this.modeType = '1';
          this.modeType2 = '1';
          this.modeType3 = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }else if (res.data.data.applicationMode === '0'){
          this.modeType = '1';
          this.modeType2 = '1';
          this.modeType3 = '3';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }
        this.dept = res.data.data;
      })
      this.initData();
    },
    methods: {
      initData() {
        getDeptTree().then(res => {
          const data = res.data.data;
          const index = this.$refs.crud.findColumnIndex("parentId");
          this.option.column[index].dicData = data;
        });
      },
      handleAdd(row) {
        this.$refs.crud.value.parentId = row.id;
        this.$refs.crud.option.column.filter(item => {
          if (item.prop === "parentId") {
            item.value = row.id;
            item.addDisabled = true;
          }
        });
        this.$refs.crud.rowAdd();
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      searchReset() {
        this.query = {};
        this.parentId = 0;
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.parentId = '';
        this.page.currentPage = 1;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDept(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      beforeClose(done) {
        this.$refs.crud.value.parentId = "";
        this.$refs.crud.value.addDisabled = false;
        this.$refs.crud.option.column.filter(item => {
          if (item.prop === "parentId") {
            item.value = "";
            item.addDisabled = false;
          }
        });
        done();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
      },
      refreshChange() {
        this.onLoad(this.page, this.query);
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getLazyList(this.parentId, Object.assign(params, this.query)).then(res => {
          this.data = res.data.data;
          this.loading = false;
          this.selectionClear();
        });
      },
      treeLoad(tree, treeNode, resolve) {
        const parentId = tree.id;
        getLazyList(parentId).then(res => {
          resolve(res.data.data);
        });
      },
      synchronSchoolDept(){
        const loading = this.$loading({
          lock: true,
          text: '正在同步云校部门数据......',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        synchronSchoolDept().then(() => {
          loading.close()
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "同步云校部门数据成功!"
          });
        }, error => {
          loading.close()
          console.log(error);
        });
      },
      synchronSchoolClass(){
        const loading = this.$loading({
          lock: true,
          text: '正在同步云校班级数据......',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        synchronSchoolClass().then(() => {
          loading.close()
          this.page.currentPage = 1;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "同步云校班级数据成功!"
          });
        }, error => {
          loading.close()
          console.log(error);
        });
      },
      synchronCard(){
        const loading = this.$loading({
          lock: true,
          text: '正在同步一卡通数据......',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        synchronCard().then(() => {
          loading.close()
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "触发同步一卡通部门操作，请稍后刷新查询数据"
          });
        }, error => {
          loading.close()
          console.log(error);
        });
      }
    }
  };
</script>

<style>
</style>
