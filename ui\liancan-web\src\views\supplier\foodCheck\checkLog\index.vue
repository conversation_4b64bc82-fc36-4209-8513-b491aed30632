<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page="page" :permission="permissionList" :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate"
      @row-save="rowSave" @row-del="handleDelete" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
      @size-change="sizeChange" @on-load="onLoad">
      <template slot="menu" slot-scope="{row}">
        <el-button type="text" size="small" icon="el-icon-view" @click="viewDetail(row)">查看</el-button>
      </template>
      <template slot="spImg" slot-scope="scope">
        <img v-for="e in scope.row.spImg.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickPreview(e)" style="margin-right:10px;cursor: pointer;">
      </template>
      <template slot="rcImg" slot-scope="scope">
        <img v-for="e in scope.row.rcImg.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickPreview(e)" style="margin-right:10px;cursor: pointer;">
      </template>
    </avue-crud>
    <el-dialog :visible.sync="viewImgDialogVisible" @close="viewImgDialogVisible=false" width="30%" height="30%" :modal-append-to-body="false">
      <img width="100%" :src="viewImgDialogImageUrl" alt="">
    </el-dialog>
    <el-dialog :visible.sync="videoDialogVisible" @close="videoDialogVisible=false" width="30%" height="30%" :modal-append-to-body="false" class="videoplay">
      <video :src="videoUrl" controls="controls" autoplay ondragstart="return false" style="transform: scale(1) rotate(0deg); max-width: 100%; max-height: 100%;cursor: pointer;"></video>
    </el-dialog>
    <el-dialog :visible.sync="viewDetailVisible" title="检测明细" @close="viewDetailVisible=false" width="50%" height="30%" :modal-append-to-body="false">
      <div>
        <el-card class="box-card">
          <div slot="header" class="clearfix" style="padding: 10px;font-weight: bold;"><span>基础信息</span></div>
            <table border="0" class="table1">
              <tr>
                <td class="col1 colTitle">样本编号：</td><td class="col2 colContent"><el-input :value="detailObj.simpleCode" readonly/></td>
                <td class="col3 colTitle">样本名称：</td><td class="col4 colContent"><el-input :value="detailObj.goodsName" readonly/></td>
              </tr>
              <tr>
                <td class="col1 colTitle">样本分类：</td><td class="col2 colContent"><el-input :value="detailObj.goodsTypeName" readonly/></td>
                <td class="col3 colTitle">样本总量：</td><td class="col4 colContent"><el-input :value="detailObj.sampleWeight" readonly><template slot="append">克</template></el-input></td>
              </tr>
              <tr>
                <td class="col1 colTitle">被检单位：</td><td class="col2 colContent"><el-input :value="detailObj.deptIdName" readonly/></td>
                <td class="col3 colTitle">采样地址：</td><td class="col4 colContent"><el-input :value="detailObj.spAddress" readonly/></td>
              </tr>
              <tr>
                <td class="col1 colTitle">采样员：</td><td class="col2 colContent"><el-input :value="detailObj.spUserName" readonly/></td>
                <td class="col3 colTitle">采样时间：</td><td class="col4 colContent"><el-input :value="detailObj.spTime" readonly/></td>
              </tr>
              <tr>
                <td class="col1 colTitle">收样员：</td><td class="col2 colContent"><el-input :value="detailObj.rcUserName" readonly/></td>
                <td class="col3 colTitle">收样时间：</td><td class="col4 colContent"><el-input :value="detailObj.rcTime" readonly/></td>
              </tr>
              <tr>
                <td class="col1 colTitle">检验类型：</td><td class="col2 colContent"><el-input :value="detailObj.checkTypeName" readonly/></td>
              </tr>
              <tr>
                <td class="col1 colTitle">样本照片：</td><td class="col2 colContent" colspan="3">
                  <div v-if="detailObj.spImg"><img v-for="e in detailObj.spImg.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickPreview(e)" style="margin-right:10px;cursor: pointer;"></div>
                </td>
              </tr>
            </table>
        </el-card>

        <el-card class="box-card" style="margin-top: 30px;">

            <div slot="header" class="clearfix" style="padding: 10px;font-weight: bold;"><span>检测信息</span></div>
            <div v-for="(it,i) in detailCheckItemList" >
              <table border="0" class="table1 checkItemTable">
                <tr>
                  <td class="col1 colTitle">检测项目：</td><td class="col2 colContent"><el-input :value="it.checkItem" readonly/></td>
                  <td class="col3 colTitle">检验结果：</td><td class="col4 colContent"><el-input :value="it.checkResultName" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">初验结果：</td><td class="col2 colContent"><el-input :value="it.checkResultName" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">复检结果：</td><td class="col2 colContent"><el-input :value="it.recheckResultName" readonly/></td>
                  <td class="col3 colTitle">复检理由：</td><td class="col4 colContent"><el-input :value="it.recheckReason" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">测量值：</td><td class="col2 colContent"><el-input :value="it.checkValue" readonly/></td>
                  <td class="col3 colTitle">单位：</td><td class="col4 colContent"><el-input :value="it.unit" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">抑制率：</td><td class="col2 colContent"><el-input :value="it.inhibitionRate" readonly/></td>
                  <td class="col3 colTitle">吸光度：</td><td class="col4 colContent"><el-input :value="it.absorbance" readonly/></td>
                </tr>

                <tr>
                  <td class="col1 colTitle">定性结果：</td><td class="col2 colContent"><el-input :value="it.checkQualitativeResult" readonly/></td>
                  <td class="col3 colTitle">定量结果：</td><td class="col4 colContent"><el-input :value="it.checkQuantitativeResult" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">检测机构：</td><td class="col2 colContent"><el-input :value="it.checkOrgan" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">检测员：</td><td class="col2 colContent"><el-input :value="it.checkUserName" readonly/></td>
                  <td class="col3 colTitle">检测时间：</td><td class="col4 colContent"><el-input :value="it.checkTime" readonly/></td>
                </tr>
                <tr>
                  <td class="col1 colTitle">检测方式：</td><td class="col2 colContent"><el-input :value="it.checkWayName" readonly/></td>
                  <td class="col3 colTitle">设备标号：</td><td class="col4 colContent"><el-input :value="it.deviceNum" readonly/></td>
                </tr>
              </table>
              <div v-if="i!==checkItemInfo.length-1" style="border-top: 2px solid #ddd;margin:10px;"></div>
            </div>
        </el-card>

        <el-card class="box-card"  style="margin-top: 30px;">
          <div slot="header" class="clearfix" style="padding: 10px;font-weight: bold;"><span>销毁信息</span></div>
            <table border="0" class="table1 destroyTable">
              <tr>
                <td class="col1 colTitle">销毁员：</td><td class="col2 colContent"><el-input :value="detailObj.destroyUserName" readonly/></td>
              </tr>
              <tr>
                <td class="col3 colTitle">销毁时间：</td><td class="col4 colContent"><el-input :value="detailObj.destroyTime" readonly/></td>
              </tr>
              <tr>
                <td class="col3 colTitle">销毁地址：</td><td class="col4 colContent"><el-input :value="detailObj.destroyAddress" readonly/></td>
              </tr>
              <tr>
                <td class="col3 colTitle">销毁图片：</td><td class="col4 colContent">
                  <div v-if="detailObj.destroyImg"><img v-for="e in detailObj.destroyImg.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickPreview(e)" style="margin-right:10px;cursor: pointer;"></div>
                </td>
              </tr>
              <tr>
                <td class="col3 colTitle">销毁视频：</td><td class="col4 colContent">
                  <div v-if="detailObj.destroyVideo"><video v-for="e in detailObj.destroyVideo.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickVideo(e)" style="cursor: pointer;"/></div>
                </td>
              </tr>
            </table>
        </el-card>
      </div>
    </el-dialog>
  </basic-container>
</template>
<script>
import mixinViewModule from '@/mixins/view-module'
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date"; //dateFormat(new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),"yyyy-MM-dd")    dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss")
export default {
  mixins: [mixinViewModule],
  data() {
    return {
      mixinViewModuleOptions: {
        listUrl: '/api/service/rabbit-supplier/foodcheck/list',
        pageUrl: '/api/service/rabbit-supplier/foodcheck/page',
        detailUrl: '/api/service/rabbit-supplier/foodcheck/detail',
        removeUrl: '/api/service/rabbit-supplier/foodcheck/remove',
        saveUrl: '/api/service/rabbit-supplier/foodcheck/save',
        updateUrl: '/api/service/rabbit-supplier/foodcheck/update',
      },
      viewImgDialogVisible: false,
      viewDetailVisible:false,
      videoDialogVisible:false,
      videoUrl:'',
      detailObj:{},
      detailCheckItemList:[],
      viewImgDialogImageUrl: '',
      checkItemInfo:[{itemName:'重金属'},{itemName:'吊白块'},{itemName:'亚硝酸盐'}],
      option: {
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn: true,
        searchFilterable: true,
        menuWidth: 120,
        column: [
          { label: '被检单位', prop: 'deptId',span:12, search: true, type: 'select', dicUrl: '/api/service/rabbit-system/dept/getDeptAndSubDept?deptCategory=4', props: { label: 'deptName', value: 'id' }, rules: [{ required: true, message: '请选择食堂', trigger: 'blur' }] }
          , { label: '样本编码', prop: 'simpleCode',addDisplay:true,editDisplay:false,viewDisplay:true,rules: [{ required: true, message: '请输入样本编码', trigger: 'blur' }]}
          , { label: '样本分类',prop: "goodsType",type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=goods_category",props: {label: "dictValue",value: "dictKey"},addDisplay:false,editDisplay:false,viewDisplay:true,hide:true}
          , { label: '样本总量', prop: 'sampleWeight',hide:true,addDisplay:false,editDisplay:false,viewDisplay:true}
          , { label: '样本名称', prop: 'goodsIds',span:12, search: false,hide:true, type: 'select',multiple:true, dicUrl: '/api/service/rabbit-basic/basicFood/select-all', props: { label: 'name', value: 'id' }, rules: [{ required: true, message: '请输入检测商品名称', trigger: 'blur' }] }
          , { label: '样本名称', prop: 'goodsName',search:true,addDisplay:false,editDisplay:false,viewDisplay:false,searchWidth:100}
          , { label: '采样员', prop: 'spUserId', type: 'select', dicUrl: '/api/service/rabbit-food/user/listUser', props: { label: 'realName', value: 'id' }, hide:true, addDisplay:true, editDisplay:false, viewDisplay:true, rules: [{ required: true, message: '请选择采样员', trigger: 'blur' }]}
          , { label: '采样员', prop: 'spUserName',hide:false,addDisplay:false,editDisplay:false,viewDisplay:true}
          , { label: '检验机构', prop: 'checkOrgan',hide:false,addDisplay:true,editDisplay:false,viewDisplay:true,rules: [{ required: true, message: '请输入检验机构', trigger: 'blur' }]}
          , { label: '检测方式',prop: "checkWay",search:true,type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_check_way",props: {label: "dictValue",value: "dictKey"},addDisplay:true,editDisplay:false,viewDisplay:true,hide:false,rules: [{ required: true, message: '请选择检测方式', trigger: 'blur' }]}
          , { label: '检验结果',search:true,prop: "checkResult",type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_check_result",props: {label: "dictValue",value: "dictKey"},addDisplay:true,editDisplay:false,viewDisplay:true,hide:false,rules: [{ required: true, message: '请选择检验结果', trigger: 'blur' }]}
          , { label: '检验员', prop: 'checkUserId', type: 'select', dicUrl: '/api/service/rabbit-food/user/listUser', props: { label: 'realName', value: 'id' }, hide:true,addDisplay:true,editDisplay:false,viewDisplay:true,rules: [{ required: true, message: '请选择检验员', trigger: 'blur' }]}
          , { label: '检验员', prop: 'checkUser',hide:false,addDisplay:false,editDisplay:false,viewDisplay:true}
          , { label: '检验时间', prop: 'checkTime', type: "datetime", format: 'yyyy-MM-dd HH:mm:ss', valueFormat: 'yyyy-MM-dd HH:mm:ss', hide:false, addDisplay:true, editDisplay:false, viewDisplay:true,rules: [{ required: true, message: '请选择检验时间', trigger: 'blur' }]}
          , { label: '采样时间期限',hide:true, prop: 'spTimeLimit',span:12, type: 'datetime', value: dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss"), disabled: false, format: 'yyyy-MM-dd', valueFormat: 'yyyy-MM-dd', rules: [{ required: true, message: '请输入采样时间', trigger: 'blur' }], addDisplay:false, viewDisplay:false}
          , { label: '采样时间',prop: 'spTime',type:'datetime',format: 'yyyy-MM-dd HH:mm:ss',valueFormat: 'yyyy-MM-dd HH:mm:ss',searchSpan:4,rules: [{required: false,message: '请输入采样时间',trigger: 'blur'}],addDisplay:false,editDisplay:false,viewDisplay:false,hide:true}
          , { label: '采样地址', prop: 'spAddress',hide:true,addDisplay:false,editDisplay:false,viewDisplay:true}
          , { label: '检测类型',prop: "checkType",type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_check_type",props: {label: "dictValue",value: "dictKey"},addDisplay:false,editDisplay:false,viewDisplay:true,hide:true}
          , { label: '收样员',prop: 'reUserName',searchSpan:4,rules: [{required: false,message: '请输入收样员',trigger: 'blur'}],addDisplay:false,editDisplay:false,viewDisplay:false,hide:true}
          , { label: '收样时间',prop: 'rcTime',type:'datetime',format: 'yyyy-MM-dd HH:mm:ss',valueFormat: 'yyyy-MM-dd HH:mm:ss',searchSpan:4,rules: [{required: false,message: '请输入收样时间',trigger: 'blur'}],addDisplay:false,editDisplay:false,viewDisplay:false,hide:true}
          , { label: '检单状态',prop: "checkStatus",search:false,type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_check_status",props: {label: "dictValue",value: "dictKey"},addDisplay:false,editDisplay:false,viewDisplay:true,hide:true}
          , { label: '备注', prop: 'taskRemark',hide:true, span:12, rules: [{ required: false, message: '请输入采样备注', trigger: 'blur' }], addDisplay:false }
          , { label: '采样图片',prop: 'spImg',listType: 'picture-img',type:'upload',searchSpan:4,rules: [{required: false,message: '请输入采样图片(多个,号分隔)',trigger: 'blur'},{ min: 0, max: 1000, message: '长度不能超过1000个字符', trigger: 'blur' }],addDisplay:false,editDisplay:false,viewDisplay:true,hide:true}
          , { label: '检测时间起',prop: 'checkTimeStart',searchLabelWidth:100,search:true,type:'datetime',hide:true,format: 'yyyy-MM-dd',valueFormat: 'yyyy-MM-dd',searchSpan:4, addDisplay:false}
          , { label: '检测时间止',prop: 'checkTimeEnd',searchLabelWidth:100,search:true,type:'datetime',hide:true,format: 'yyyy-MM-dd',valueFormat: 'yyyy-MM-dd',searchSpan:4, addDisplay:false}

        ]
      }
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.foodcanteengoodschecktask_add, false),
        viewBtn: this.vaildData(this.permission.foodcanteengoodschecktask_view, false),
        delBtn: this.vaildData(this.permission.foodcanteengoodschecktask_remove, false),
        editBtn: this.vaildData(this.permission.foodcanteengoodschecktask_edit, false)
      };
    }
  },
  created() {
  },
  mounted(){
    console.log('dic====',this.$refs.crud.DIC)
  },
  methods: {
    getDictName(row,prop,value){
      try{
        let dicArr=this.$refs.crud.DIC[prop];
        for(let i=0;i<dicArr.length;i++){
          let e=dicArr[i]
          let v1=''+e.dictKey;
          let v2=''+value;
          if(v1===v2){
            return e.dictValue;
          }
        }
      }catch(e){}
      return '';
    },
    getDictNameById(row,prop,value,retField){
      try{
        let dicArr=this.$refs.crud.DIC[prop];
        for(let i=0;i<dicArr.length;i++){
          let e=dicArr[i]
          let v1=''+e.id;
          let v2=''+value;
          //console.log(prop+"  "+v1+"="+v2)
          if(v1===v2){
            return e[retField];
          }
        }
      }catch(e){}
      return '';
    },
    viewDetail(row){
      //加载检测项目
      this.getData('/api/foodcheck/listCheckItem',{id:row.id},(res)=>{
        console.log('res=====33===',res)
        let tmpData=res.data.data;
        if(tmpData&&tmpData.length>0){
          tmpData.forEach(row2=>{
            row2["checkResultName"]=this.getDictName(row2,'checkResult',row2['checkResult']);
            row2["recheckResultName"]=this.getDictName(row2,'checkResult',row2['recheckResult']);
            row2["checkWayName"]=this.getDictName(row2,'checkWay',row2['checkWay']);
            row2["deviceNum"]=row.deviceNum;
          })
        }
        this.detailCheckItemList=tmpData;
      });
      //加载检测项目END

      let arr=['checkResult','checkType','checkWay','goodsType'];
      arr.forEach(e=>{
        row[e+"Name"]=this.getDictName(row,e,row[e]);
      })
      row["deptIdName"]=this.getDictNameById(row,'deptId',row['deptId'],'deptName');
      row["spUserName"]=this.getDictNameById(row,'spUserId',row['spUserId'],'realName');
      row["rcUserName"]=this.getDictNameById(row,'spUserId',row['rcUserId'],'realName');
      row["destroyUserName"]=this.getDictNameById(row,'',row['destroyUserId'],'realName');
      row["destroyUserName"]=this.getDictNameById(row,'spUserId',row['destroyUserId'],'realName');

      console.log('row....==',row)
      this.detailObj=row;
      this.viewDetailVisible=true;
    },
    beforeRowSave(row) {
      row['goodNames']=row.$goodsIds.split(' | ');

      if (row.spImg instanceof Array) { row.spImg = '' }
      //row.attachments = row.attachments.join(",");
    }, beforeRowUpdate(row) {
      if (row.spImg instanceof Array) { row.spImg = '' }
      //row.attachments = row.attachments.join(",");
    },
    handleClickPreview: function (url) {
      this.viewImgDialogImageUrl = url;
      this.viewImgDialogVisible = true;
    },
    handleClickVideo: function (url) {
        this.videoUrl = url;
        this.videoDialogVisible = true;
    },
  }
};
</script>
<style>

.table1{width:100%}
.table1 td{padding: 5px 0px;}
.table1 .colTitle{width:120px;min-width: 120px;text-align: right;}
.table1 .colContent{width:50%;text-align: right;text-align: left;}
.destroyTable .colTitle{width:120px;min-width: 120px;text-align: right;}
.destroyTable .colContent{width:100%;text-align: right;text-align: left;}
.videoplay .el-dialog__body{text-align: center;}
</style>
