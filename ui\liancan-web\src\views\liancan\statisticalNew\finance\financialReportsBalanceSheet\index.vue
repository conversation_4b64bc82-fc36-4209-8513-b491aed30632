<template>
  <basic-container>
    <div class="table-form">
      <div style="margin-left: 15px;">
        <el-form ref="form" :model="form" label-width="80px" >
          <el-form-item label="当前帐套:">
            <div style="display: flex;flex-direction: row;">
              <div>
                <el-select v-model="accountSetsValue" placeholder="请选择帐套" style="width:400px;">
                  <el-option
                    v-for="item in accountSetsList"
                    :key="item.id"
                    :label="item.accountName"
                    :value="item.id">
                  </el-option>
                </el-select>
              </div>
              <div style="margin-left: 100px;">
                <el-input v-model="this.peroidStr" placeholder="请选择日期" readonly="readonly" style="width: 250px;"></el-input>
                <el-button type="primary" icon="el-icon-caret-bottom" @click="selectPeroidHandle">选择</el-button>
                <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <avue-crud :option="option"
                :table-loading="loading"
                :data="data"
                :page="page"
                :permission="permissionList"
                :before-open="beforeOpen"
                v-model="form"
                ref="crud"
                @search-change="searchChange"
                @search-reset="searchReset"
                @selection-change="selectionChange"
                @current-change="currentChange"
                @size-change="sizeChange"
                @on-load="onLoad">
        <template slot="menuLeft">
          <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportTable">打印</el-button>
          <el-button type="success" size="small" @click="handleExport">导出</el-button>
        </template>
        <template slot="header">
          <div>
            <h1 style="text-align:center;">资产负债表</h1>
          </div>
          <div style="display:flex;justify-content:space-between;">
            <div><span>食堂名称：</span>{{this.deptName}}</div>
            <div><span>日期：</span>{{this.tableDate}}</div>
            <div><span>单元：元</span></div>
            <div><span>食堂负责人：</span>{{this.createUserName}}</div>
            <div><span>制表人:</span>{{this.userName}}</div>
          </div>
        </template>
      </avue-crud>
    </div>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog title="选择会计期间"
               append-to-body
               :visible.sync="selectPeroidShow"
               width="600px">
      <el-form ref="form" label-width="80px">
        <el-form-item label="查询月份">
          <el-col :span="11">
            <el-select v-model="endPeroid" ref="endPeroidName" placeholder="请选择月份">
              <el-option
                v-for="item in peroidList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="selectPeroidShow = false">取 消</el-button>
        <el-button type="primary"
                   @click="afterSelectPeroidHandle">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getForSubjectCountByMonth, processExportForSubjectCountByMonth} from "@/api/finance/subject";
import {mapGetters} from "vuex";
import {getAccountSetsList,getPeroidList} from "@/api/finance/voucher";

export default {
  data() {
    return {
      deptName: '',//食堂名称
      tableDate: '',//日期
      userName: '',//制表人
      createUserName: '',//食堂负责人
      selectVoucherPeroid: '',
      peroidStr: '',
      endPeroid: '',
      peroidList: [],
      selectPeroidShow: false,
      accountSetsValue: '',
      accountSetsList:[],//当前食堂所有的帐套
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 100000,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      option: {
        menu: false,
        // height:'auto',
        // calcHeight: 30,
        // searchShow: false,
        // searchMenuSpan: 6,
        // tip: false,
        border: true,
        // index: false,
        // viewBtn: false,
        // editBtn: false,
        addBtn:false,
        // refreshBtn: false,
        // searchBtn: false,
        // selection: false,
        // columnBtn: false,
        // addRowBtn: false,
        // printBtn:true,
        // excelBtn:true,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            // labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            // labelWidth: 150,
          },
          {
            label: "资产",
            prop: "column1",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // maxlength:30,
            // labelWidth: 150,
          },
          {
            label: "期末余额",
            prop: "column2",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // maxlength:30,
            // labelWidth: 150,
          },
          {
            label: "年初余额",
            prop: "column3",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // maxlength:30,
            // labelWidth: 150,
          },
          {
            label: "负债和所有者权益",
            prop: "column4",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // maxlength:30,
            // labelWidth: 150,
          },
          {
            label: "期末余额",
            prop: "column5",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // maxlength:30,
            // labelWidth: 150,
          },
          {
            label: "年初余额",
            prop: "column6",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // maxlength:30,
            // labelWidth: 150,
          },
        ]
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    console.log("aaaaaa")
    console.log(this.userInfo)
    if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
    }
  },
  methods: {
    refreshData() {
      // this.page.currentPage = 1
      // this.onLoad(this.page, this.query);
      if(this.peroidStr === '' || this.endPeroid === ''){
        this.$message.warning('请先选择月份');
        this.loading = false;
        return;
      }
      this.getLedgerList()
    },
    selectPeroidHandle() {
      this.selectPeroidShow = true;
    },
    afterSelectPeroidHandle() {
      this.peroidStr = "";
      const e = this.$refs.endPeroidName.selectedLabel;

      if(e != '') {
        this.peroidStr = e;
        this.selectPeroidShow = false;
      } else {
        if(e == '') {
          this.$message.warning("请选择月份");
        }
      }
    },
    beforeOpen(done, type) {
      // if (["edit", "view"].includes(type)) {
      //   getDetail(this.form.id).then(res => {
      //     this.form = res.data.data;
      //   });
      // }
      done();
    },
    searchReset() {
      // this.query = {};
      this.endPeroid = '';
      this.peroidStr = '';
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      // this.query = params;
      // this.page.currentPage = 1
      // this.onLoad(this.page, params);
      if(this.peroidStr === '' || this.endPeroid === ''){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      this.getLedgerList()
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    //
    onLoad(page, params = {}) {
      // this.loading = true;
      this.getgetAccountSets()
      getPeroidList()
        .then(res => {
          var list = res.data.data.records;
          if(list != null && list.length >0) {
            for (var i=0;i<list.length;i++) {
              const item = list[i];
              const p = item.voucherPeriod + '';
              const str = p.substring(0,4) + '年第' + p.substring(4,6) + '期';
              const it = {label:str,value:p};
              this.peroidList.push(it);
            }
          }
      });
    },
    async getgetAccountSets(){
      await getAccountSetsList()
        .then(res => {
          this.accountSetsList = res.data.data.records;
          if(this.accountSetsList!=null && this.accountSetsList.length == 1) {
            this.accountSetsValue = this.accountSetsList[0].id;
          }
      });
    },
    getLedgerList(){
      this.loading = true;
      let params = {}
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.accountSetsId = this.accountSetsValue;
      getForSubjectCountByMonth(params).then(res => {
        console.log(res.data.data)
        this.data = res.data.data;
        this.deptName = this.data[0].deptName//食堂名称
        this.tableDate = this.data[0].tableDate//日期
        this.userName = this.userInfo.nickname//制表人
        this.createUserName = this.data[0].createUserName//食堂负责人
        this.loading = false;
        this.selectionClear();
      });
    },
    exportTable(){
      let that = this;
      this.loading = true;
      if(this.peroidStr === '' || this.endPeroid === ''){
        this.$message.warning('请先选择月份');
        this.loading = false;
        return;
      }
      if(this.accountSetsValue === ''){
        this.$message.warning('请先选择账套');
        this.loading = false;
        return;
      }

      var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
      xhr.open(
        "get",
        "/api/service/rabbit-finance/financialSubject/downloadForSubjectCount?endPeroid="+this.endPeroid+"&accountSetsId="+this.accountSetsValue+"&userName="+this.userInfo.nickname,
        true
      );
      xhr.responseType = "blob";
      xhr.onload = function() {
        that.loading = false;
        const url = window.URL.createObjectURL(this.response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "资产负债表.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      xhr.send();
    },
    handleExport( params = {}){
      if( this.endPeroid == '' || this.endPeroid == undefined ){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      params.endPeroid = this.endPeroid;
      params.accountSetsId = this.accountSetsValue;
      processExportForSubjectCountByMonth(Object.assign(params, this.query)).then(res => {
        if (!res.data) {
          return;
        }
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel",
        }); // 构造一个blob对象来处理数据，并设置文件类型
        const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
        const a = document.createElement("a"); //创建a标签
        a.style.display = "none";
        a.href = href; // 指定下载链接
        let fileName = res.headers["content-disposition"];
        fileName = fileName.split("=")[1];
        a.download = decodeURIComponent(fileName); //指定下载文件名
        a.click(); //触发下载
        URL.revokeObjectURL(a.href); //释放URL对象
      });
    },
  }
};
</script>

<style  lang="less" scoped>
  /deep/ .table-form .el-form-item__label{
      display: none;
  }
  /deep/ .table-form .el-form-item__content{
    margin-left: 0 !important;
  }
</style>
