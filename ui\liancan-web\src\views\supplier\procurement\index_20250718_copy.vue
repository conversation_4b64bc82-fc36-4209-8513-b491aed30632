<template>
  <basicContainer>
    <el-tabs v-model="activeTab" @tab-click="handleTabClick" type="card">
      <el-tab-pane label="全部" name="">
      </el-tab-pane>
      <el-tab-pane label="待接单" name="0">
      </el-tab-pane>
      <el-tab-pane label="接单备货" name="6">
      </el-tab-pane>
      <el-tab-pane label="配送中" name="4">
      </el-tab-pane>
      <el-tab-pane label="已送达" name="3">
      </el-tab-pane>
      <el-tab-pane label="取消/拒单" name="2">
      </el-tab-pane>
    </el-tabs>

    <!-- 只使用一个表格组件 -->
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="menu">
        <!-- 根据当前tab和订单状态显示对应的操作按钮 -->
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.orderStatus == '0'"
                   @click.stop="opentForm(row)">查看处理
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.orderStatus == '6'"
                   @click.stop="openStockingForm(row)">查看处理
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.orderStatus == '3' || row.orderStatus == '4'"
                   @click.stop="openOrder(row)">查看
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.orderStatus == '2'"
                   @click.stop="openFuseOrder(row)">查看
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.orderStatus == '1'"
                   @click.stop="openDeliveredFormOrder(row)">查看
        </el-button>
      </template>
    </avue-crud>

    <!-- 对话框组件 -->
    <el-dialog title="采购订单" :visible.sync="isShow" :append-to-body="true" @close="closeForm" width="60%">
      <avue-form ref="orderForm" :option="orderFormOption" v-model="orderForm">
      </avue-form>
      <span slot="footer" class="dialog-footer">
				<el-button type="primary" @click="saveOrder">接单</el-button>
		     	<el-button type="submit" @click="fuseClose">拒单</el-button>
			</span>
    </el-dialog>

    <el-dialog title="拒单" :visible.sync="fuseShow" :append-to-body="true" @close="closeForm" width="60%">
      <avue-form ref="orderFuseForm" :option="orderreFuseOption" v-model="orderFuseForm" @submit="subFuse">
      </avue-form>
    </el-dialog>

    <el-dialog title="采购订单(已接单)" :visible.sync="orderShow" :append-to-body="true" @close="closeForm" width="60%">
      <avue-form ref="orderForm2" :option="orderFormOption" v-model="orderForm2">
      </avue-form>
      <span slot="footer" class="dialog-footer">
         <el-button type="primary" v-if="orderStatus == 4" @click="sendGoods">送货到食堂</el-button>
      </span>
    </el-dialog>

    <el-dialog title="采购订单(已拒单)" :visible.sync="orderFuseShow" :append-to-body="true" @close="closeForm" width="60%">
      <avue-form ref="orderForm1" :option="orderFormOption" v-model="orderForm1">
      </avue-form>
    </el-dialog>

    <el-dialog title="采购订单(已按期配送)" :visible.sync="orderDeliveredShow" :append-to-body="true" @close="closeForm" width="60%">
      <avue-form ref="orderDeliveredForm" :option="orderFormOption" v-model="orderDeliveredForm">
      </avue-form>
    </el-dialog>

    <!-- 接单备货处理弹窗 -->
    <el-dialog :title="stockingForm.isReadOnly ? '查看备货信息' : '接单备货处理'" :visible.sync="stockingShow" :append-to-body="true" @close="closeForm" width="80%">
      <avue-form ref="stockingForm" :option="stockingFormOption" v-model="stockingForm">
      </avue-form>
      <span slot="footer" class="dialog-footer" v-if="!stockingForm.isReadOnly && !stockingForm.isProcessed">
        <el-button type="primary" @click="confirmStocking">确定保存</el-button>
        <el-button @click="stockingShow = false">取消</el-button>
      </span>
      <span slot="footer" class="dialog-footer" v-else>
        <el-button @click="stockingShow = false">关闭</el-button>
      </span>
    </el-dialog>

  </basicContainer>
</template>
<script>
  import {getList, getDetail, add, update, remove,updateOrderStatus,sendGoods,fuseOrder,saveStockingSupplyMethod,checkSupplyMethodStatus} from "@/api/supplier/procurement/index";
  import {orderreFuseOption,orderFormOption} from "@/const/order/index";
  import {getSupplyMethodConfig} from "@/api/supplier/supplierAutoMatch";
  import {mapGetters} from "vuex";
  var DIC = {
    isPutaway: [{
      label: '纸质发票',
      value: "0"
    },{
      label: '电子发票',
      value: "1"
    }],
    orderStatus: [{
            label: '未接单',
            value: "0"
        },{
            label: '食堂已收',
            value: "1"
        },{
            label: '取消/拒单',
            value: "2"
        },{
            label: '已送达',
            value: "3"
        },{
            label: '配送中',
            value: "4"
        },{
            label: '已出库',
            value: "5"
        },{
            label: '接单备货',
            value: "6"
        },{
            label: '已退货',
            value: "7"
        },{
            label: '收货结束',
            value: "8"
        }],
    isBuy: [{
      label: '未确认',
      value: "0"
    },{
      label: '已确认',
      value: "1"
    }]
  }
  export default {
    data() {
      return {
        activeTab: '', // 当前激活的tab，默认为全部
        orderStatus:'',
        form: {},
        query: {},
        orderForm:{},
        orderFuseForm:{},
        orderForm2:{},
        orderForm1:{},
        orderDeliveredForm:{},
        stockingForm:{}, // 接单备货表单
        currentOrderRow: null, // 当前处理的订单行数据
        showGeneratePendingOrderButton: false, // 是否显示生成待下单按钮
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        orderId:undefined,
        fuseShow:false,
        orderShow:false,
        orderFuseShow:false,
        orderDeliveredShow:false,
        stockingShow:false, // 接单备货弹窗显示状态
        isShow: false,
        orderreFuseOption: orderreFuseOption,
        orderFormOption: orderFormOption,
        // stockingFormOption 改为计算属性
        option: {
        /*  height:'auto',
          calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menuWidth: 130,
          labelWidth: 100,
          column: [
            {
              label: "订单号",
              prop: "id",
              type: "input",
              disabled:true,
              search: true
            },
            {
              label: "客户单位",
              prop: "deptParentName",
              type: "input",
              disabled:true
            },
            {
              label: "食堂名称",
              prop: "deptName",
              type: "input",
            },
            {
              label: "食堂名称",
              prop: "canteenName",
              type: "input",
              disabled:true,
              hide:true,
            },
            {
              label: "食堂名称",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan/order/supplier/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
              hide:true
            },
            {
              label: "订单号",
              prop: "id",
              type: "input",
            },
            {
              label: "采购内容",
              prop: "goodsName",
              type: "input",
            },
            {
              label: "要求配送日期",
              prop: "sendTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
            },
            {
              label: "配送地址",
              prop: "site",
              type: "input",
              width: 200,
            },
 /*           {
              label: "采购方",
              prop: "deptName",
              type: "input",
              disabled:true,
            },*/
            {
              label: "联系人",
              prop: "userName",
              type: "input",
              disabled:true,
            },
            {
              label: "联系人电话",
              prop: "phone",
              type: "input",
            },
            {
              label: "总价",
              prop: "money",
              type: "number",
              display: false,
              width: 100,
            },
            {
              label: "订单状态",
              prop: "orderStatus",
              type: "select",
              dicData: DIC.orderStatus,
            },
            {
              label: "下单时间",
              prop: "createTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              display: false,
              hide: true,
            },
            {
              label: "商品种类数",
              prop: "totalQuantity",
              type: "input",
              //type: 'radio',
              slot: true,
              //align: 'center',
              display: false,
              hide: true,
            },
            {
              label: "收货状态",
              prop: "receiveStatus",
              type: "input",
              display: false,
              hide: true,
            },
            {
              label: "收货时间",
              prop: "receiveTime",
              type: "input",
              display: false,
              hide: true,
            },
            {
              label: "订单入库状态",
              prop: "putStatus",
              type: "input",
              display: false,
              hide: true,
            },
            {
              label: "采购商品",
              prop: 'goodsList',
              type: 'dynamic',
              hide: true,
              span: 24,
              children: {
                align: 'center',
                headerAlign: 'center',
                width: '100%',
                addBtn: false,
                delBtn: false,
                index:true,
                showSummary: true,
                sumColumnList: [
                  {
                    name: 'subtotal',
                    type: 'sum'
                  },
                ],
                column: [{
                  label: 'id',
                  prop: 'id',
                  type: 'input',
                  hide: true,
                  display: false,
                  showColumn: false,
                },
                  {
                    label: "商品",
                    prop: "goodsId",
                    type: "select",
                    dicFlag: false,
                    dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",//todo
                    props: {
                      label: "name",
                      value: "id"
                    },
                  },
                  /*{
                      label: "供应商",
                      prop: "supplierId",
                      type: "input",
                      editDisabled: true,
                  },*/
                  {
                    label: "单价",
                    prop: "price",
                    type: "input",
                  },
                  {
                    label: "采购数量",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "计量单位",
                    prop: "unit",
                    type: "select",
                    dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                    props: {
                      label: "dictValue",
                      value: "dictKey"
                    },
                  },
                  {
                    label: "小计",
                    prop: "subtotal",
                    type: "number",
                    precision:2,
                    mock:{
                      type:'number',
                      max:1,
                      min:2,
                      precision:2
                    },
                    //minRows: 0,
                  },
                ]
              },
              rules: [{
                required: true,
                message: '请选择商品',
                trigger: 'blur'
              }]
            },
          ]
        },
        data: [],
      };
    },
    created() {
      // initStockingFormOption 已改为计算属性，无需在此调用
    },
    mounted() {
      // 初始化时加载全部数据
      this.onLoad(this.page);
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.order_add, false),
          viewBtn: this.vaildData(this.permission.order_view, false),
          delBtn: this.vaildData(this.permission.order_delete, false),
          editBtn: this.vaildData(this.permission.order_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      // 接单备货表单配置（计算属性）
      stockingFormOption() {
        const isReadOnly = this.stockingForm.isReadOnly || false;
        return {
          labelWidth: 120,
          submitBtn: false, // 隐藏提交按钮
          emptyBtn: false,  // 隐藏清空按钮
          column: [
            {
              label: "订单信息",
              prop: "orderInfo",
              type: "input",
              disabled: true,
              span: 24
            },
            {
              label: "采购商品",
              prop: 'goodsList',
              type: 'dynamic',
              span: 24,
              children: {
                align: 'center',
                headerAlign: 'center',
                width: '100%',
                addBtn: false,
                delBtn: false,
                index: true,
                cellBtn: false,  // 禁用单元格操作按钮
                menu: false,     // 禁用菜单列
                column: [
                  {
                    label: "商品名称",
                    prop: "goodsName",
                    type: "input",
                    disabled: true,
                    readonly: true,
                    width: 150
                  },
                  {
                    label: "采购数量",
                    prop: "quantity",
                    type: "input",
                    disabled: true,
                    readonly: true,
                    width: 100
                  },
                  {
                    label: "单位",
                    prop: "unit",
                    type: "input",
                    disabled: true,
                    readonly: true,
                    width: 80
                  },
                  {
                    label: "商品供货方式",
                    prop: "supplyMethod",
                    type: "select",
                    dicData: [
                      { label: "自营仓库出货配送", value: "1" },
                      { label: "转供应商直发客户", value: "2" }
                    ],
                    width: 180,
                    disabled: (row) => isReadOnly || row.supplyMethodDisabled || false,
                    rules: isReadOnly ? [] : [{
                      required: true,
                      message: '请选择供货方式',
                      trigger: 'change'
                    }]
                  }
                ]
              }
            }
          ]
        };
      }
    },
    methods: {


      // tab切换处理
      handleTabClick(tab) {
        this.activeTab = tab.name;
        if (tab.name === '') {
          // 全部tab，不设置orderStatus过滤
          delete this.query.orderStatus;
        } else {
          // 特定状态tab，设置orderStatus过滤
          this.query.orderStatus = tab.name;
        }
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },

      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        // 合并查询参数，this.query中已经包含了正确的orderStatus
        const queryParams = Object.assign({}, params, this.query);
        getList(page.currentPage, page.pageSize, queryParams).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      //打开下单弹窗
      opentForm(row) {
        this.orderFormOption.column[9].display = false
        this.orderId = row.id;
        this.orderForm = row;
        this.isShow = true
        this.orderStatus = row.orderStatus;
      },
      openOrder(row){
        this.orderFormOption.column[9].display = false
        this.orderForm2 = row;
        this.orderShow = true;
        this.orderStatus = row.orderStatus;
      },
      openFuseOrder(row){
        this.orderFormOption.column[9].display = true
        this.orderForm1 = row;
        this.orderFuseShow = true;
        this.orderStatus = row.orderStatus;
      },
      openDeliveredFormOrder(row){
        this.orderFormOption.column[9].display = false
        this.orderDeliveredForm = row;
        this.orderDeliveredShow = true;
        this.orderStatus = row.orderStatus;
      },
      // 打开接单备货处理弹窗
      openStockingForm(row) {
        this.orderId = row.id;
        this.currentOrderRow = row; // 保存当前订单行数据

        // 检查是否已完成备货处理
        const isStockingCompleted = row.supplyMatchStatus === '1';

        this.stockingForm = {
          orderInfo: `订单号：${row.id} | 食堂：${row.deptName} | 配送时间：${row.sendTime}`,
          goodsList: row.goodsList || [],
          isReadOnly: isStockingCompleted, // 如果已完成备货，设为只读
          isProcessed: false // 初始化处理状态
        };

        // 如果商品列表为空，需要获取订单详情
        if (!row.goodsList || row.goodsList.length === 0) {
          this.getOrderDetail(row.id);
        } else {
          // 如果已有商品列表，先检查供货方式配置状态
          this.checkSupplyMethodStatusAndLoad().then(() => {
            // 数据加载完成后显示弹窗
            this.$nextTick(() => {
              this.stockingShow = true;
            });
          });
        }
      },
      // 获取订单详情（包含商品列表）
      getOrderDetail(orderId) {
        getDetail(orderId).then(res => {
          const orderData = res.data.data;
          this.stockingForm.goodsList = orderData.goodsList || [];

          // 获取订单详情后，检查供货方式配置状态
          this.checkSupplyMethodStatusAndLoad().then(() => {
            // 数据加载完成后显示弹窗
            this.$nextTick(() => {
              this.stockingShow = true;
            });
          });
        }).catch(error => {
          console.error('获取订单详情失败:', error);
          // 即使出错也要显示弹窗
          this.$nextTick(() => {
            this.stockingShow = true;
          });
        });
      },

      // 检查供货方式配置状态并加载
      async checkSupplyMethodStatusAndLoad() {
        try {
          // 先检查订单详情中是否已保存供货方式
          const statusResponse = await checkSupplyMethodStatus(this.orderId);

          if (statusResponse.data.code === 200 && statusResponse.data.data) {
            const { allConfigured, goodsSupplyMethodMap, configuredGoods } = statusResponse.data.data;

            if (allConfigured) {
              // 所有商品都已配置供货方式，表示已处理完成
              // 不显示提示信息，静默处理

              // 设置商品的供货方式为已保存的值
              for (let goods of this.stockingForm.goodsList) {
                if (goodsSupplyMethodMap[goods.goodsId]) {
                  goods.supplyMethod = goodsSupplyMethodMap[goods.goodsId];
                  goods.supplyMethodDisabled = true; // 已配置的设为不可编辑
                  goods.hasConfig = true;
                } else {
                  goods.supplyMethod = '';
                  goods.supplyMethodDisabled = false;
                  goods.hasConfig = false;
                }
              }

              // 设置为已处理状态，隐藏保存按钮
              this.stockingForm.isProcessed = true;

            } else if (configuredGoods > 0) {
              // 部分商品已配置，静默设置已配置的商品状态
              // 设置商品的供货方式状态
              for (let goods of this.stockingForm.goodsList) {
                if (goodsSupplyMethodMap[goods.goodsId]) {
                  goods.supplyMethod = goodsSupplyMethodMap[goods.goodsId];
                  goods.supplyMethodDisabled = true; // 已配置的设为不可编辑
                  goods.hasConfig = true;
                } else {
                  goods.supplyMethod = '';
                  goods.supplyMethodDisabled = false;
                  goods.hasConfig = false;
                }
              }

              // 继续查询购销配对配置
              await this.loadSupplyMethodConfig();

            } else {
              // 没有商品配置供货方式，查询购销配对配置
              this.$message.info('商品尚未配置供货方式');
              await this.loadSupplyMethodConfig();
            }
          } else {
            // 查询失败，继续查询购销配对配置
            console.error('检查供货方式配置状态失败:', statusResponse.data.msg);
            await this.loadSupplyMethodConfig();
          }

        } catch (error) {
          console.error('检查供货方式配置状态失败:', error);
          // 出错时继续查询购销配对配置
          await this.loadSupplyMethodConfig();
        }

        return Promise.resolve();
      },

      // 加载供货方式配置
      async loadSupplyMethodConfig() {
        try {
          // 检查是否为供应商下单（deptId为0）
          if (this.stockingForm.deptId === 0) {
            this.$message.error('供应商的购销配对功能暂未开放');
            return;
          }

          const response = await getSupplyMethodConfig(this.orderId);

          if (response.data.code === 200 && response.data.data) {
            const { hasConfig, allConfigured, goodsSupplyTypeMap, message } = response.data.data;

            if (!hasConfig) {
              // 没有配送线路配置，所有商品需要手动选择
              this.$message.warning(message);
              for (let goods of this.stockingForm.goodsList) {
                goods.supplyMethod = '';
                goods.supplyMethodDisabled = false;
                goods.hasConfig = false;
              }
            } else {
              // 有配送线路配置，根据购销配对记录设置
              for (let goods of this.stockingForm.goodsList) {
                if (goodsSupplyTypeMap[goods.goodsId] !== undefined) {
                  // 有购销配对配置，设置供货方式且不可编辑
                  // supplyType: 0-自营仓库出货配送, 1-转供应商直发客户
                  // supplyMethod: "1"-自营仓库出货配送, "2"-转供应商直发客户
                  goods.supplyMethod = goodsSupplyTypeMap[goods.goodsId] === 0 ? "1" : "2";
                  goods.supplyMethodDisabled = true;
                  goods.hasConfig = true;
                } else {
                  // 无购销配对配置，需要手动选择
                  goods.supplyMethod = '';
                  goods.supplyMethodDisabled = false;
                  goods.hasConfig = false;
                }
              }

              if (!allConfigured) {
                this.$message.warning('部分商品没有购销配对配置，请手动选择供货方式');
              }
            }
          } else {
            // 查询失败，所有商品需要手动选择
            for (let goods of this.stockingForm.goodsList) {
              goods.supplyMethod = '';
              goods.supplyMethodDisabled = false;
              goods.hasConfig = false;
            }
          }

          // 强制更新表单数据
          this.$set(this.stockingForm, 'goodsList', [...this.stockingForm.goodsList]);

        } catch (error) {
          console.error('加载供货方式配置失败:', error);
          if (error.response && error.response.data && error.response.data.msg) {
            this.$message.error(error.response.data.msg);
          } else {
            this.$message.error('加载供货方式配置失败');
          }

          // 查询失败时，默认需要用户手动选择
          for (let goods of this.stockingForm.goodsList) {
            goods.supplyMethod = '';
            goods.supplyMethodDisabled = false;
            goods.hasConfig = false;
          }
          this.$set(this.stockingForm, 'goodsList', [...this.stockingForm.goodsList]);
        }
      },



      // 确认接单备货处理
      confirmStocking() {
        this.$refs.stockingForm.validate((valid) => {
          if (valid) {
            // 检查是否所有商品都已选择供货方式
            const unselectedGoods = this.stockingForm.goodsList.filter(item => !item.supplyMethod);
            if (unselectedGoods.length > 0) {
              this.$message.error('请为所有商品选择供货方式');
              return;
            }

            // 分析商品供货方式
            const selfOperatedGoods = this.stockingForm.goodsList.filter(item => item.supplyMethod === '1'); // 自营仓库出货配送
            const supplierDirectGoods = this.stockingForm.goodsList.filter(item => item.supplyMethod === '2'); // 转供应商直发客户

            let message = '';
            if (selfOperatedGoods.length > 0 && supplierDirectGoods.length > 0) {
              message = `将处理 ${selfOperatedGoods.length} 个自营商品和 ${supplierDirectGoods.length} 个供应商直发商品`;
            } else if (selfOperatedGoods.length > 0) {
              message = `将处理 ${selfOperatedGoods.length} 个自营商品，仅更新状态`;
            } else if (supplierDirectGoods.length > 0) {
              message = `将处理 ${supplierDirectGoods.length} 个供应商直发商品，生成待下单记录`;
            }

            this.$confirm(message + '，确认处理？', '确认处理', {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }).then(() => {
              // 调用后端接口保存供货方式并处理
              const saveData = {
                orderId: this.orderId,
                goodsList: this.stockingForm.goodsList.map(item => ({
                  id: item.orderDetailId,
                  goodsId: item.goodsId,
                  supplyMethod: item.supplyMethod
                }))
              };

              saveStockingSupplyMethod(saveData).then(res => {
                if (res.data.code === 200) {
                  this.$message({
                    type: "success",
                    message: res.data.msg || "接单备货处理完成!"
                  });
                  this.stockingShow = false;
                  this.onLoad(this.page);
                } else {
                  this.$message.error(res.data.msg || '处理失败');
                }
              }).catch(error => {
                console.error('接单备货处理失败:', error);
                this.$message.error('处理失败');
              });
            }).catch(() => {
              // 用户取消
            });
          } else {
            this.$message.warning('请完善商品供货方式信息');
          }
        });
      },
      fuseClose(){
        this.fuseShow = true;
      },
      //关闭下单弹窗
      closeForm: function() {
        this.isShow = false;
        this.fuseShow = false;
        this.orderShow = false;
        this.orderFuseShow = false;
        this.orderDeliveredShow = false;
        this.stockingShow = false;
   /*     this.onLoad(this.page);*/
        if (this.$refs.orderForm) this.$refs.orderForm.resetForm()
        if (this.$refs.orderFuseForm) this.$refs.orderFuseForm.resetForm()
        if (this.$refs.orderForm2) this.$refs.orderForm2.resetForm()
        if (this.$refs.orderForm1) this.$refs.orderForm1.resetForm()
        if (this.$refs.orderDeliveredForm) this.$refs.orderDeliveredForm.resetForm()
        if (this.$refs.stockingForm) this.$refs.stockingForm.resetForm()
      },
      //关闭下单弹窗
      saveOrder(){
        updateOrderStatus(this.orderForm.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "接单成功!"
          });
        }, error => {
          window.console.log(error);
        });
        this.isShow = false
      },
      sendGoods(){
        sendGoods(this.orderForm2.id).then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          window.console.log(error);
        });
        this.orderShow = false
      },
      //允许采购
      updateIsBuy: function() {
        this.isShow = false
      },
      subFuse(row){
        row.id = this.orderId;
        fuseOrder(row).then(() => {
          this.isShow = false
          this.fuseShow = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "拒单成功!"
          });
        }, error => {
          window.console.log(error);
        });
      },
    }
  };
</script>

<style lang="scss" scoped>
 /deep/ .el-dialog__footer{
      margin-top: -50px;
}
</style>
