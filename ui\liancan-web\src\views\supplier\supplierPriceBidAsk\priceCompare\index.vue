<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @refresh-change="searchReset"
                   @on-load="onLoad">
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="isShowExplain">对比说明</el-button>
            </template>
        </avue-crud>
        <el-dialog title="查看报价"
                   :visible.sync="isShowView"
                   v-if="isShowView"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeViewForm"
                   width="50%">
            <div class="show_div">
                <div>-采购单价与征询报价比较结果：</div>
                <div style="margin-left: 30px;">偏高：最低采购单价 - 最低报价 = 差价（1元（含）及以上）</div>
                <div style="margin-left: 30px;">略高：最低采购单价 - 最低报价 = 差价（在0.1元 至 1元）：</div>
                <div style="margin-left: 30px;">相近：最低采购单价 - 最低报价 = 差价（在-0.1元（含）至 0.1元（含）之间，不含0元）</div>
                <div style="margin-left: 30px;">相同：最低采购单价 - 最低报价 = 差价（0元）：</div>
                <div style="margin-left: 30px;">略低：最低采购单价 - 最低报价 = 差价（-0.1元 至 -1元）</div>
                <div style="margin-left: 30px;">偏低：最低采购单价 - 最低报价 = 差价（-1元（含）及以下）：</div>
            </div>
        </el-dialog>
    </basic-container>
</template>

<script>
const DIC = {
    timeRange: [
        {
            label: '近15天',
            value: '0'
        },
        {
            label: '近30天',
            value: '1'
        },
        {
            label: '近90天',
            value: '2'
        },
        {
            label: '近半年',
            value: '3'
        },
        {
            label: '近一年',
            value: '4'
        },
    ],
    priceType: [
        {
            label: '偏高',
            value: '0'
        },
        {
            label: '略高',
            value: '1'
        },
        {
            label: '相近',
            value: '2'
        },
        {
            label: '相同',
            value: '3'
        },
        {
            label: '略低',
            value: '4'
        },
        {
            label: '偏低',
            value: '5'
        },
    ]
}
import { getPriceCompare } from "@/api/supplier/supplierPriceAsk";
export default {
    data() {
        return {
            data: [],
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            form: {},
            query: {},
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 200,
                tip: false,
                border: true,
                menu: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                align: "center",
                column: [
                    {
                        label: "时间范围",
                        prop: "searchTimeRange",
                        type: 'select',
                        dicData: DIC.timeRange,
                        hide: true,
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "publicShopName",
                        type: 'input',
                        search: true,
                        searchLabelWidth: 100,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: 'input'
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: 'input'
                    },
                    {
                        label: "现时使用采购单价",
                        prop: "purchasePrice",
                        type: 'input'
                    },
                    {
                        label: "统计期间采购单价",
                        display:false,
                        children: [
                            {
                                label: "最低",
                                prop: "minPurchasePrice",
                                type: 'input',
                            },
                            {
                                label: "最高",
                                prop: "maxPurchasePrice",
                                type: 'input'
                            },
                            {
                                label: "平均",
                                prop: "averagePurchasePrice",
                                type: 'input'
                            }
                        ]
                    },
                    {
                        label: "统计期间征询报价",
                        display:false,
                        children: [
                            {
                                label: "最低",
                                prop: "minBidPrice",
                                type: 'input'
                            },
                            {
                                label: "最高",
                                prop: "maxBidPrice",
                                type: 'input'
                            },
                            {
                                label: "平均",
                                prop: "averageBidPrice",
                                type: 'input'
                            }
                        ]
                    },
                    {
                        label: "采购单价与征询报价",
                        display:false,
                        children: [
                            {
                                label: "最低价对比",
                                prop: "minCompare",
                                type: 'select',
                                dicData: DIC.priceType,
                            },
                            {
                                label: "最高价对比",
                                prop: "maxCompare",
                                type: 'select',
                                dicData: DIC.priceType,
                            },
                            {
                                label: "平均价对比",
                                prop: "averageCompare",
                                type: 'select',
                                dicData: DIC.priceType,
                            }
                        ]
                    },
                    {
                        label: "现时使用采购单价与征询报价对比",
                        prop: "purchaseAndBidCompare",
                        type: 'select',
                        dicData: DIC.priceType,
                    },
                ]
            },
            isShowView: false,



        }
    },
    methods: {
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done()
        },
        searchReset() {
            this.query = {}
            this.page.currentPage = 1;
            this.onLoad(this.page, this.query);
        },
        currentChange(current) {
            this.page.currentPage = current;
            this.onLoad(this.page, this.query);
        },
        sizeChange(size) {
            this.page.pageSize = size;
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getPriceCompare(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            })
        },
        isShowExplain() {
            this.isShowView = true;
        },
        closeViewForm() {
            this.isShowView = false;
        },

    },
}
</script>

<style lang="scss" scoped>
.show_div {
    width: 100%;
    display: flex;
    flex-direction: column;
    font-size: 16px;
    color: rgb(48, 49, 51);
    line-height: 1.76923;

}
</style>
