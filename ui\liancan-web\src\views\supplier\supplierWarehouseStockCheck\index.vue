<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="addStockCheckHandler" >新增</el-button>
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="removeStockCheckHandler" >删除</el-button>
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="startStockCheckHandler" >开始盘点</el-button>
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="inputStockCheckHandler" >录入结果</el-button>
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="dealResultStockCheckHandler" >盈亏处理</el-button>
                <el-button type="primary"  icon="el-icon-download" size="small" @click="printOrderHandler" >导出</el-button>
                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>
        <!-- 打开新增页面 开始 -->
        <el-dialog title="新增"
                   :visible.sync="isShowStockCheck"
                   v-if="isShowStockCheck"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm" width="60%"  style="height: 90%;">
            <el-form ref="formAdd" label-width="80px">
                <el-form-item label="单据日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-date-picker type="date"
                                        placeholder="选择日期"
                                        :picker-options="pickerOptions"
                                        v-model="businessDate"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;"></el-date-picker>
                    </el-col>
                    <el-col :span="8"></el-col>
                    <el-col :span="2">任务名称</el-col>
                    <el-col :span="6">
                        <el-input
                            ref="userName"
                            v-model="taskName"
                            type="text"
                            clearable="true">
                        </el-input>
                    </el-col>
                </el-form-item>
                <el-form-item label="盘点人" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-select v-model="salesman" placeholder="请选择" style="width: 100%;">
                            <el-option
                                v-for="item in salesmanList"
                                :key="item.id"
                                :label="item.contactName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="8"></el-col>
                    <el-col :span="2">盘点仓库</el-col>
                    <el-col :span="6">
                        <el-select v-model="warehouseId" placeholder="请选择" style="width: 100%;">
                            <el-option
                                v-for="item in warehouseList"
                                :key="item.id"
                                :label="item.warehouseName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-form-item>
                <el-form-item label="盘点商品" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="4">
                        <el-button type="primary" size="small" @click="selectGoodHandler">盘点仓库中指定商品</el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button type="success" size="small" @click="selectOrderHandler">盘点仓库中全部商品</el-button>
                    </el-col>
                </el-form-item>
            </el-form>
            <div style="height: 250px;overflow-y: scroll;">
                <avue-crud :option="stockCheckOption"
                           :table-loading="stockCheckLoading"
                           :data="stockCheckData"
                           :page="stockCheckPage"
                           :before-open="beforeOpenStockCheck"
                           v-model="stockCheckForm"
                           ref="crudStockCheck"
                           @row-update="rowUpdateStockCheck"
                           @row-save="rowSaveStockCheck"
                           @row-del="rowDelStockCheck"
                           @search-change="searchChangeStockCheck"
                           @search-reset="searchResetStockCheck"
                           @selection-change="selectionChangeStockCheck"
                           @current-change="currentChangeStockCheck"
                           @size-change="sizeChangeStockCheck"
                           @cell-click="handleRowClick"
                           @on-load="stockCheckOnLoad">
                    <template slot="menu" slot-scope="{row,index}">
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-edit"
                            v-if="!row.$cellEdit"
                            @click="rowCellStockCheck(row,index)"
                        >修改</el-button>
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-check"
                            v-if="row.$cellEdit"
                            @click="rowSaveStockCheck(row,index)"
                        >保存</el-button>
                        <el-button
                            type="text"
                            size="mini"
                            icon="el-icon-delete"
                            @click="deleteRowStockCheck(row,index)">删除
                        </el-button>
                    </template>
                </avue-crud>
            </div>
            <div>
                盘点总额:{{totalAmt}}元
            </div>
            <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddForm">取消</el-button>
                    <el-button type="primary" @click="saveSelectHandle">保存</el-button>
                </span>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 公共商品选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowSelectGoods"
                   v-if="isShowSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeSelectForm" width="60%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crudSelectGoods"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 商品选择 结束 -->
        <!-- 打开详情页面 开始 -->
        <el-dialog title="详情"
                   :visible.sync="isShowStockCheckDetail"
                   v-if="isShowStockCheckDetail"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeShowForm" width="60%"  style="height: 90%;">
            <el-row type="flex" class="row-bg" justify="right">
                <el-col :span="24" style="height: 100%;" align="right">
                    <el-button type="primary" icon="el-icon-printer" size="small" v-print="print" >打印</el-button>
                </el-col>
            </el-row>
            <div id="printArea">
                <el-row type="flex" class="row-bg" justify="left" style="height: 60px;">
                    <el-col :span="24">
                        <div style="width: 100%;height:100%;text-align:center;"><h1>盘点单</h1></div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left" style="margin-top: 45px;">
                    <el-col :span="2">
                        <div class="head-label">单据日期</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.businessDate}}</div>
                    </el-col>
                    <el-col :span="2">
                        <div class="head-label">盘点人</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.salesmanName}}</div>
                    </el-col>
                    <el-col :span="2">
                        <div class="head-label">单据号</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.code}}</div>
                    </el-col>
                    <el-col :span="2">
                        <div class="head-label">盘点仓库</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.warehouseName}}</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">登记人</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.createUserName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">登记时间</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
                </el-row>
                <div style="height: 600px;overflow-y: scroll;margin-top: 20px;">
                    <el-table
                        :data="stockCheckData"
                        stripe
                        border
                        fit
                        show-summary
                        :header-cell-style="tableHeaderStyle"
                        :row-style="tableDetailStyle"
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column property="rowId" label="序号" align="center"></el-table-column>
                        <el-table-column property="name" label="商品名称" align="center"></el-table-column>
                        <el-table-column property="unitName" label="计量单位" align="center"></el-table-column>
<!--                        <el-table-column property="warehouseName" label="盘点仓库" align="center"></el-table-column>-->
                        <el-table-column property="stockQty" label="库存数量" align="center"></el-table-column>
                        <el-table-column property="qty" label="盘点数量" align="center"></el-table-column>
                        <el-table-column property="suplusQty" label="盘盈数量" align="center"></el-table-column>
                        <el-table-column property="lossQty" label="盘亏数量" align="center"></el-table-column>
<!--                        <el-table-column property="price" label="盘点单价" align="center"></el-table-column>-->
<!--                        <el-table-column property="amt" label="盘点金额" align="center"></el-table-column>-->
                    </el-table>
                </div>
            </div>
        </el-dialog>
        <!-- 打开详情页面 结束 -->

        <!-- 打开录入页面 开始 -->
        <el-dialog title="录入盘点结果"
                   :visible.sync="isShowInputStockCheck"
                   v-if="isShowInputStockCheck"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="inputStockCloseForm" width="60%"  style="height: 90%;">
            <el-form ref="formAdd" label-width="80px">
                <el-form-item label="单据日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-input
                            ref="businessDate"
                            v-model="businessDate"
                            type="text"
                            disabled="true"
                            clearable="true">
                        </el-input>
                    </el-col>
                    <el-col :span="8"></el-col>
                    <el-col :span="2">任务名称</el-col>
                    <el-col :span="6">
                        <el-input
                            ref="taskName"
                            v-model="taskName"
                            type="text"
                            disabled="true"
                            clearable="true">
                        </el-input>
                    </el-col>
                </el-form-item>
                <el-form-item label="盘点人" prop="region" disabled="true" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-select v-model="salesman" placeholder="请选择" disabled="true" style="width: 100%;">
                            <el-option
                                v-for="item in salesmanList"
                                :key="item.id"
                                :label="item.contactName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-col>
                    <el-col :span="8"></el-col>
                    <el-col :span="2">盘点仓库</el-col>
                    <el-col :span="6">
                        <el-select v-model="warehouseId" placeholder="请选择" disabled="true" style="width: 100%;">
                            <el-option
                                v-for="item in warehouseList"
                                :key="item.id"
                                :label="item.warehouseName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-form-item>
                <el-form-item label="盘点商品" :rules="[{ required: true, message: '', trigger: 'change' }]">
                </el-form-item>
            </el-form>
            <div style="height: 250px;overflow-y: scroll;">
                <avue-crud :option="inputStockCheckOption"
                           :table-loading="inputStockCheckLoading"
                           :data="inputStockCheckData"
                           :page="inputStockCheckPage"
                           :before-open="beforeOpenInputStockCheck"
                           v-model="inputStockCheckForm"
                           ref="crudInputStockCheck"
                           @row-update="rowUpdateInputStockCheck"
                           @row-save="rowSaveInputStockCheck"
                           @row-del="rowDelInputStockCheck"
                           @search-change="searchChangeInputStockCheck"
                           @search-reset="searchResetInputStockCheck"
                           @selection-change="selectionChangeInputStockCheck"
                           @current-change="currentChangeInputStockCheck"
                           @size-change="sizeChangeInputStockCheck"
                           @cell-click="handleInputStockCheckRowClick"
                           @on-load="inputStockCheckOnLoad">
                    <template slot="menu" slot-scope="{row,index}">
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-edit"
                            v-if="!row.$cellEdit"
                            @click="rowCellInputStockCheck(row,index)"
                        >修改</el-button>
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-check"
                            v-if="row.$cellEdit"
                            @click="rowSaveInputStockCheck(row,index)"
                        >保存</el-button>
<!--                        <el-button-->
<!--                            type="text"-->
<!--                            size="mini"-->
<!--                            icon="el-icon-delete"-->
<!--                            @click="deleteRowInputStockCheck(row)">删除-->
<!--                        </el-button>-->
                    </template>
                </avue-crud>
            </div>
            <div>
                盘点总额:{{inputStockCheckTotalAmt}}元
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="inputStockCheckCloseForm">取消</el-button>
                <el-button type="primary" @click="inputStockSaveSelectHandle" v-if="this.editType == 'input'">保存</el-button>
                <el-button type="primary" @click="inputStockDealSelectHandle" v-if="this.editType == 'deal'">立即生成盘盈单/盘亏单</el-button>
            </span>
        </el-dialog>
        <!-- 打开录入页面 结束 -->
    </basic-container>
</template>

<script>
import {getList, getDetail, add, update, exportList, remove, start, deal} from "@/api/supplier/supplierStockCheck";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {
    getSupplierCustomerList,
    getSalesmanList,
    getGoodsList,
    getWarehouseList,
    stockLoad
} from "@/api/supplier/supplier";
var DIC = {
    orderStatus: [{
        label: '未开始',
        value: "0"
    },{
        label: '盘点中',
        value: "1"
    },{
        label: '待处理',
        value: "2"
    },{
        label: '已结束',
        value: "3"
    },],

}
export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            inputStockCheckTotalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            createUserName:'',
            salesmanName:'',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: true,
                align: "center",
                menu: true,
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            disabledDate(time) {
                                return time.getTime() < Date.now();
                            }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "任务名称",
                        prop: "taskName",
                        type: "input",
                        width: 180,
                        search: true,
                    },
                    {
                        label: "盘点仓库",
                        prop: "warehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "盘点商品",
                        prop: "goodsName",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "采购商品",
                        prop: "goodGoodId",
                        type: "select",
                        search: true,
                        props: {
                            label: 'name',
                            value: 'goodGoodId'
                        },
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        hide: true,
                    },
                    {
                        label: '盘点人',
                        prop: 'salesman',
                        type:'select',
                        search: true,
                        dicUrl: `/api/rabbit-supplier/user-list`,
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        multiple:true
                    },
                    {
                        label: "状态",
                        prop: "orderStatus",
                        type: "select",
                        dicData: DIC.orderStatus,
                        search: true,
                    },
                    {
                        label: "关联盘盈单",
                        prop: "sourceSurplusCode",
                        type: "input",
                        display: false
                    },
                    {
                        label: "关联盘亏单",
                        prop: "sourceLossCode",
                        type: "input",
                        display: false
                    },
                    {
                        label: "登记人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                        display: false
                    },
                    {
                        label: "登记时间",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                        display: false
                    },
                ]
            },
            data: [],
            isShowStockCheck: false,
            isShowSelectGoods: false,
            isShowInputStockCheck: false,

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },

            //新增盘点单弹窗参数 start
            pickerOptions: {
                // disabledDate(time) {
                //     return time.getTime() > Date.now();
                // }
            },
            isShowStockCheckDetail: false,
            supplierList: [],
            salesmanList: [],
            warehouseList: [],
            supplierCustomerId: '',
            businessDate: '',
            salesman: '',
            warehouseId: '',
            taskName: '',
            supplierValue: {},
            queryStockCheck: {},
            rowStockCheck: {},
            stockCheckLoading: true,
            stockCheckData: [],
            stockCheckPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            stockCheckForm: {},
            stockCheckOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "库存数量",
                        prop: "stockQty",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        formatter:(val)=>{
                            if(val.stockQty===0) {
                                return '0';
                            }
                            return amtFilters(val.stockQty);
                        },
                    },
                    {
                        label: "盘点数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "盘盈数量",
                        prop: "suplusQty",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        formatter:(val)=>{
                            if(val.suplusQty===0) {
                                return '0';
                            }
                            return amtFilters(val.suplusQty);
                        },
                    },
                    {
                        label: "盘亏数量",
                        prop: "lossQty",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        formatter:(val)=>{
                            if(val.lossQty===0) {
                                return '0';
                            }
                            return amtFilters(val.lossQty);
                        },
                    },
                    {
                        label: "盘点单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        hide: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "盘点金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        hide: true,
                        rules: [{
                            required: false,
                            message: "请输入盘点金额",
                            trigger: "blur"
                        }],
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            //新增盘点单弹窗参数 end

            //选择商品弹窗参数 start
            queryGoodsList: {},
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择一类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
            //选择商品弹框参数 end


            //录入盘点单弹窗参数 start
            isInputShowStockCheckDetail: false,
            queryInputStockCheck: {},
            rowInputStockCheck: {},
            inputStockCheckLoading: true,
            inputStockCheckData: [],
            inputStockCheckPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            inputStockCheckForm: {},
            inputStockCheckOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "库存数量",
                        prop: "stockQty",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        formatter:(val)=>{
                            if(val.stockQty===0) {
                                return '0';
                            }
                            return amtFilters(val.stockQty);
                        },
                    },
                    {
                        label: "盘点数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "盘盈数量",
                        prop: "suplusQty",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        formatter:(val)=>{
                            if(val.suplusQty===0) {
                                return '0';
                            }
                            return amtFilters(val.suplusQty);
                        },
                    },
                    {
                        label: "盘亏数量",
                        prop: "lossQty",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        formatter:(val)=>{
                            if(val.lossQty===0) {
                                return '0';
                            }
                            return amtFilters(val.lossQty);
                        },
                    },
                    {
                        label: "盘点单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                        hide: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "盘点金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        hide: true,
                        rules: [{
                            required: false,
                            message: "请输入盘点金额",
                            trigger: "blur"
                        }],
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            //录入盘点单弹窗参数 end

        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});

        //加载仓库
        getWarehouseList()
            .then(res => {
                this.warehouseList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});

    },
    watch: {
        //计算合计金额
        "stockCheckData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt','suplusQty','lossQty','stockQty'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.stockCheckData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = amtFilters(iTotalAmt);
        },
        //新增
        addStockCheckHandler () {
            this.form = {};
            this.businessDate = '';//new Date();
            this.supplierCustomerId = '';
            this.salesman = '';
            this.warehouseId = '';
            this.editType = 'add';
            this.isShowStockCheck = true;
        },
        //删除
        removeStockCheckHandler () {
            this.editType = 'del';
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        //开始盘点
        startStockCheckHandler () {
            this.editType = 'start';
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("一旦开始盘点操作，到结束盘点之前，期间不能选择盘点仓库中本次要进行数量盘点的商品，来进行入库、出库等会导致商品库存数量发生变化的全部操作。因此请在适当时间内进行库存盘点操作，并尽快完成！您确认开始进行本次库存盘点么?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return start(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        //录入结果
        inputStockCheckHandler () {
            this.editType = 'input';
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.isShowInputStockCheck = true;
        },
        //盈亏处理
        dealResultStockCheckHandler () {
            this.editType = 'deal';
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.isShowInputStockCheck = true;
        },
        closeAddForm() {
            if (this.stockCheckData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowStockCheck = false;
                    });
            }else{
                this.isShowStockCheck = false;
            }
        },
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        closeShowForm(){
            this.isShowStockCheckDetail = false;
        },

        viewRow(row,index) {
            this.editType = 'view';
            this.form = row;
            this.stockCheckOnLoad(this.stockCheckPage)
            this.isShowStockCheckDetail = true;
        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //主界面基本方法 end

        //选择商品基本方法 start
        selectionClearGoodsList() {

        },
        searchChange2(params, done) {
            this.queryGoodsList = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        searchReset2() {
            this.queryGoodsList = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange2(list) {

        },
        currentChange2(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        goodsListOnLoad(page) {
            var params = {};
            this.goodsListLoading = true;
            params.warehouseId = this.warehouseId;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.queryGoodsList)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.length;
                this.goodsListData = data;
                this.goodsListLoading = false;
                this.selectionClearGoodsList();
            });
        },
        addGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.stockCheckRefreshData(row);
            this.isShowSelectGoods = false
        },
        //选择商品基本方法 end

        //新增采购盘点单基本方法 start
        selectionClearStockCheck() {

        },
        saveSelectHandle() {
            //保存前先检查数据
            //检测单据日期
            //检测供应商
            //业务员
            if (this.businessDate === undefined || this.businessDate === '') {
                this.$message.warning("请选择单据日期");
                return;
            }
            if (this.taskName === undefined || this.taskName === '') {
                this.$message.warning("请填写任务名称");
                return;
            }
            if (this.salesman === undefined || this.salesman === '') {
                this.$message.warning("请选择盘点人");
                return;
            }
            if (this.warehouseId === undefined || this.warehouseId === '') {
                this.$message.warning("请选择盘点仓库");
                return;
            }
            //如果当前保存为空，则不提示
            if (this.stockCheckData.length==0) {
                this.$message.warning("请选择商品")
                return;
            }

            var isSave = true;
            this.stockCheckData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                item.amt = qty*price;
            });

            if (!isSave) {
                return;
            }

            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.form.businessDate = this.businessDate;
                    this.form.taskName = this.taskName;
                    this.form.salesman = this.salesman;
                    this.form.warehouseId = this.warehouseId;

                    if (this.editType === 'edit') {
                        this.stockCheckLoading = true;
                        update(this.form,this.stockCheckData).then((res) => {
                            this.stockCheckLoading = false;
                            if (res.data.code == 200) {
                                this.onLoad(this.page);
                                this.$message({
                                    type: "success",
                                    message: "操作成功!"
                                });
                                this.isShowStockCheck = false;
                            }else{
                                this.$message({
                                    type: "error",
                                    message: "保存失败:"+res.data.message,
                                });
                            }
                        }, error => {
                            window.console.log(error);
                        });
                    }else {
                        this.stockCheckLoading = true;
                        add(this.form,this.stockCheckData).then((res) => {
                            this.stockCheckLoading = false;
                            if (res.data.code == 200) {
                                this.onLoad(this.page);
                                this.$message({
                                    type: "success",
                                    message: "操作成功!"
                                });
                                this.isShowStockCheck = false;
                            }else{
                                this.$message({
                                    type: "error",
                                    message: "保存失败:"+res.data.message,
                                });
                            }
                        }, error => {
                            window.console.log(error);
                        });
                    }
                })
                .then(() => {
                    this.isShowStockCheck = false;
                });
        },
        selectGoodHandler() {
            if (this.warehouseId == '') {
                this.$message({
                    type: "error",
                    message: "请选择盘点仓库",
                });
            }else{
                    this.isShowSelectGoods = true;
            }
        },
        selectOrderHandler() {
            if (this.warehouseId == '') {
                this.$message({
                    type: "error",
                    message: "请选择盘点仓库",
                });
            }else{
                var params = {};
                this.goodsListLoading = true;
                params.warehouseId = this.warehouseId;
                getGoodsList(this.goodsListPage.currentPage, this.goodsListPage.pageSize, Object.assign(params, this.queryGoodsList)).then(res => {
                    const goodList = res.data.data;
                    console.log("========>"+JSON.stringify(goodList));
                    this.goodsListLoading = false;
                    //加载数据到列表
                    goodList.forEach(row => {
                        this.addGoods(row);
                    });
                });
            }
        },
        beforeOpenStockCheck(done, type) {
            done();
        },
        searchChangeStockCheck(params, done) {
            this.queryStockCheck = params;
            this.stockCheckPage.currentPage = 1
            this.stockCheckOnLoad(this.stockCheckPage, params);
            done();
        },
        searchResetStockCheck() {
            this.queryStockCheck = {};
            this.stockCheckOnLoad(this.stockCheckPage);
        },
        selectionChangeStockCheck(list) {

        },
        currentChangeStockCheck(currentPage) {
            this.stockCheckPage.currentPage = currentPage;
        },
        sizeChangeStockCheck(pageSize) {
            this.stockCheckPage.pageSize = pageSize;
        },
        stockCheckOnLoad(page, params = {}) {
            console.log("1=======>",this.form);
            this.stockCheckLoading = true;
            if (this.form.id!==undefined && this.form.id!=='') {
                getDetail(this.form.id).then(res => {
                    console.log("2=======>",res);
                    const data = res.data.data;
                    this.businessDate = data.businessDate;
                    this.supplierCustomerId = data.supplierCustomerId;
                    this.salesman = data.salesman;
                    this.createUserName = data.createUserName;
                    this.salesmanName = data.salesmanName;
                    this.warehouseId = data.warehouseId;
                    this.stockCheckData = data.detailList;
                    this.stockCheckPage.total = this.stockCheckData.length;
                    var row = 0;
                    this.stockCheckData.forEach((item) => {
                        row++;
                        item.rowId = row;
                        item.$cellEdit = false;
                    });
                    this.stockCheckLoading = false;
                    this.selectionClearStockCheck();
                });
            }else{
                this.stockCheckPage.total = 0;
                this.stockCheckData = [];
                this.stockCheckData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.stockCheckLoading = false;
                this.selectionClearStockCheck();
            }
        },
        async stockCheckRefreshData(row) {
            console.log("1=======>",row);
            row.warehouseId = "";
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            //加载库存
            var qty = row.qty;
            var price = row.price;
            var goodsId = row.goodGoodId;
            var stockAmount = 0;
            await stockLoad(row.warehouseId,goodsId).then(res => {
                stockAmount = res.data.data.stockAmount;
                price = res.data.data.costPrice;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
            var amt = qty*price;
            row.amt = amt;
            row.price = price;
            row.stockQty = stockAmount;
            this.stockCheckData.push(row);
        },
        deleteRowStockCheck(row,index) {
            this.stockCheckData.splice(index,1);
        },
        //新增采购盘点单基本方法 end

        //新增采购盘点行编辑方法 start

        rowCellStockCheck(row, index) {
            this.rowStockCheck= row;
            this.$refs.crudStockCheck.rowCell(row, index)
            // this.$refs.crudStockCheck.rowCancel(row, row.$index);
        },
        async rowSaveStockCheck(row, index) {
            //修改stockCheckData对应的row
            //计算对应行的采购金额
            var qty = row.qty;
            var price = row.price;
            var goodsId = row.id;
            var stockAmount = 0;
            await stockLoad(row.warehouseId,goodsId).then(res => {
                stockAmount = res.data.data.stockAmount;
                price = res.data.data.costPrice;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
            var amt = qty*price;
            row.amt = amt;
            row.price = price;
            row.stockQty = stockAmount;
            var dif = qty - stockAmount;
            if(dif>0){
                row.suplusQty = dif;
                row.lossQty = 0;
            }else{
                row.suplusQty = 0;
                row.lossQty = Math.abs(dif);
            }
            this.stockCheckData[index] = row;
            // this.$refs.crudStockCheck.rowCell(row, index)
            row.$cellEdit = false;
            this.$refs.crudStockCheck.rowCellUpdate();
            //合计所有的行
            this.summaryAmt();
        },
        rowCancelEditStockCheck(row, index) {
            // this.$refs.crudEx.rowCancel(row, index);
            this.onLoad(this.page,this.query);
        },
        rowUpdateStockCheck(row, index, loading, done) {
            done()
        },
        rowDelStockCheck(row) {
        },
        handleRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["warehouseId", "qty", "price", "amt"].includes(column.property)) {
                    this.$refs.crudStockCheck.rowCell(row, row.$index);
                }
            }
        },
        printOrderHandler(params = {}) {
            this.$confirm("是否导出盘点单数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                params.isSupplyFlag = 1
                exportList(Object.assign(params, this.query)).then(res => {
                    const blob = new Blob([res.data]);
                    const fileName = '盘点单记录.xlsx';
                    const linkNode = document.createElement('a');

                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();

                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
            });
        },
        //新增盘点单行编辑方法 end



        //录入盘点单基本方法 start
        inputStockSaveSelectHandle() {
            //保存前先检查数据
            //检测单据日期
            //检测供应商
            //业务员
            if (this.businessDate === undefined || this.businessDate === '') {
                this.$message.warning("请选择单据日期");
                return;
            }
            if (this.taskName === undefined || this.taskName === '') {
                this.$message.warning("请填写任务名称");
                return;
            }
            if (this.salesman === undefined || this.salesman === '') {
                this.$message.warning("请选择盘点人");
                return;
            }
            if (this.warehouseId === undefined || this.warehouseId === '') {
                this.$message.warning("请选择盘点仓库");
                return;
            }
            //如果当前保存为空，则不提示
            if (this.inputStockCheckData.length==0) {
                this.$message.warning("请选择商品")
                return;
            }

            var isSave = true;
            this.inputStockCheckData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                if (qty===0) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,库存数量不正确");
                    return;
                }
                item.amt = qty*price;
            });

            if (!isSave) {
                return;
            }

            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.form.businessDate = this.businessDate;
                    this.form.taskName = this.taskName;
                    this.form.salesman = this.salesman;
                    this.form.warehouseId = this.warehouseId;
                    this.inputStockCheckLoading = true;
                    update(this.form,this.inputStockCheckData).then((res) => {
                        this.inputStockCheckLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShowInputStockCheck = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                })
                .then(() => {
                    this.isShowInputStockCheck = false;
                });
        },
        inputStockDealSelectHandle() {
            //保存前先检查数据
            //检测单据日期
            //检测供应商
            //业务员
            if (this.businessDate === undefined || this.businessDate === '') {
                this.$message.warning("请选择单据日期");
                return;
            }
            if (this.taskName === undefined || this.taskName === '') {
                this.$message.warning("请填写任务名称");
                return;
            }
            if (this.salesman === undefined || this.salesman === '') {
                this.$message.warning("请选择盘点人");
                return;
            }
            if (this.warehouseId === undefined || this.warehouseId === '') {
                this.$message.warning("请选择盘点仓库");
                return;
            }
            //如果当前保存为空，则不提示
            if (this.inputStockCheckData.length==0) {
                this.$message.warning("请选择商品")
                return;
            }

            var isSave = true;
            this.inputStockCheckData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                if (qty===0) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,库存数量不正确");
                    return;
                }
                item.amt = qty*price;
            });

            if (!isSave) {
                return;
            }

            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.form.businessDate = this.businessDate;
                    this.form.taskName = this.taskName;
                    this.form.salesman = this.salesman;
                    this.form.warehouseId = this.warehouseId;
                    this.inputStockCheckLoading = true;
                    deal(this.form,this.inputStockCheckData).then((res) => {
                        this.inputStockCheckLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShowInputStockCheck = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                })
                .then(() => {
                    this.isShowInputStockCheck = false;
                });
        },
        beforeOpenInputStockCheck(done, type) {
            done();
        },
        searchChangeInputStockCheck(params, done) {
            this.queryInputStockCheck = params;
            this.inputStockCheckPage.currentPage = 1
            this.inputStockCheckOnLoad(this.inputStockCheckPage, params);
            done();
        },
        searchResetInputStockCheck() {
            this.queryInputStockCheck = {};
            this.inputStockCheckOnLoad(this.inputStockCheckPage);
        },
        selectionChangeInputStockCheck(list) {

        },
        currentChangeInputStockCheck(currentPage) {
            this.inputStockCheckPage.currentPage = currentPage;
        },
        sizeChangeInputStockCheck(pageSize) {
            this.inputStockCheckPage.pageSize = pageSize;
        },
        inputStockCheckOnLoad(page, params = {}) {
            if(this.selectionList==undefined || this.selectionList.length == 0){
                return;
            }
            this.form.id = this.selectionList[0].id;
            console.log("1======>",this.form);
            this.inputStockCheckLoading = true;
            if (this.form.id!==undefined && this.form.id!=='') {
                getDetail(this.form.id).then(res => {
                    console.log("2=======>",res);
                    const data = res.data.data;
                    this.businessDate = data.businessDate;
                    this.supplierCustomerId = data.supplierCustomerId;
                    this.salesman = data.salesman;
                    this.createUserName = data.createUserName;
                    this.salesmanName = data.salesmanName;
                    this.taskName = data.taskName;
                    this.warehouseId = data.warehouseId;
                    this.inputStockCheckData = data.detailList;
                    this.inputStockCheckPage.total = this.inputStockCheckData.length;
                    var row = 0;
                    this.inputStockCheckData.forEach((item) => {
                        row++;
                        item.rowId = row;
                        item.$cellEdit = false;
                    });
                    this.inputStockCheckLoading = false;
                    this.selectionClearStockCheck();
                });
            }else{
                this.inputStockCheckPage.total = 0;
                this.inputStockCheckData = [];
                this.inputStockCheckData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.inputStockCheckLoading = false;
                this.selectionClearStockCheck();
            }
        },
        inputStockCheckRefreshData(row) {
            console.log("1=======>",row);
            row.warehouseId = "";
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            this.inputStockCheckData.push(row);
        },
        deleteRowInputStockCheck(row) {
        },
        //新增采购盘点单基本方法 end

        //新增采购盘点行编辑方法 start

        rowCellInputStockCheck(row, index) {
            this.rowInputStockCheck= row;
            this.$refs.crudInputStockCheck.rowCell(row, index)
        },
        async rowSaveInputStockCheck(row, index) {
            //修改stockCheckData对应的row
            //计算对应行的采购金额
            var qty = row.qty;
            var price = row.price;
            var goodsId = row.goodGoodId;
            var stockAmount = 0;
            await stockLoad(row.warehouseId,goodsId).then(res => {
                stockAmount = res.data.data.stockAmount;
                price = res.data.data.costPrice;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
            var amt = qty*price;
            row.amt = amt;
            row.price = price;
            row.stockQty = stockAmount;
            var dif = stockAmount - qty;
            if(dif>0){
                row.suplusQty = dif;
                row.lossQty = 0;
            }else{
                row.suplusQty = 0;
                row.lossQty = Math.abs(dif);
            }
            this.inputStockCheckData[index] = row;
            row.$cellEdit = false;
            this.$refs.crudInputStockCheck.rowCellUpdate();
            //合计所有的行
            this.inputStockCheckSummaryAmt();
        },
        rowCancelEditInputStockCheck(row, index) {
            // this.$refs.crudEx.rowCancel(row, index);
            this.onLoad(this.page,this.query);
        },
        rowUpdateInputStockCheck(row, index, loading, done) {
            done()
        },
        rowDelInputStockCheck(row) {
        },
        handleInputStockCheckRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["warehouseId", "qty", "price", "amt"].includes(column.property)) {
                    this.$refs.crudStockCheck.rowCell(row, row.$index);
                }
            }
        },
        inputStockCheckCloseForm() {
            if (this.inputStockCheckData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowInputStockCheck = false;
                    });
            }else{
                this.isShowInputStockCheck = false;
            }
        },
        inputStockCheckSummaryAmt() {
            var iTotalAmt = 0;
            this.inputStockCheckData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.inputStockCheckTotalAmt = amtFilters(iTotalAmt);
        },
        //录入盘点单基本方法 end
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
