<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.top_up_logging_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exports">导出</el-button>
      </template>
      <template slot="payStatus" slot-scope="{row}">
        <el-tag v-if="row.payStatus == 'PAYFAIL'" type="danger">支付失败</el-tag>
        <el-tag v-if="row.payStatus == 'PAID'" type="success">支付成功</el-tag>
        <el-tag v-if="row.payStatus == 'WAITPAY'" type="blue">等待回调</el-tag>
        <el-tag v-if="row.payStatus == 'CANCELPAY'" type="warning">支付取消</el-tag>
      </template>
      <template slot="menuLeft">
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >统计时间：{{this.startDate}} - {{this.endDate}}
        </a>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="rechargeDetails(row)">详情
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="充值详情" :visible.sync="rechargeDetailsVisible" width="60%" left :append-to-body="true" @close="rechargeDetailsVisible = false">
      <el-row>
        <div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              姓名：{{this.rechargeForm.userName}}
            </div>
<!--            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="obj.consumType == '0'">下单类型：统缴餐</span>
              <span v-if="obj.consumType == '1'">下单类型：自选餐(自由消费)</span>
              <span v-if="obj.consumType == '2'">下单类型：自选餐(预订菜品)</span>
              <span v-if="obj.consumType == '3'">下单类型：自选餐(预订餐次)</span>
            </div>-->
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              性别：
              <span v-if="this.rechargeForm.sex == '1'">男</span>
              <span v-if="this.rechargeForm.sex == '2'">女</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              编号：{{this.rechargeForm.studentJobNo}}
            </div>
    <!--        <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              部门：{{this.rechargeForm.deptName}}
            </div>-->
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.rechargeForm.mealsType == '0'">充值账户：统缴餐钱包</span>
              <span v-if="this.rechargeForm.mealsType == '1'">充值账户：自选餐钱包</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.rechargeForm.topUpType == 'WXPAY'">充值方式：微信支付</span>
              <span v-if="this.rechargeForm.topUpType == 'CASH'">充值方式：现金支付</span>
            </div>
            <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;">
                伙食费充值金额：{{this.rechargeForm.targetAmount}}
            </div>
              <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;">
                  客户实付金额：{{this.rechargeForm.payAmount}}
              </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              手续费费率：{{this.rechargeForm.serviceChargeRate}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
                微信支付手续费：{{this.rechargeForm.serviceCharge}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
                单位实收金额：{{this.rechargeForm.actualAmount}}
            </div>
            <div style="width:15%;margin-bottom: 20px;margin-top:20px;float: left;">
              充值操作人：{{this.rechargeForm.topUpUser}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              操作人身份：{{this.rechargeForm.identity}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              充值时间：{{this.rechargeForm.createTime}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.rechargeForm.payStatus == 'WAITPAY'">支付状态：等待回调</span>
              <span v-if="this.rechargeForm.payStatus == 'PAID'">支付状态：支付成功</span>
              <span v-if="this.rechargeForm.payStatus == 'PAYFAIL'">支付状态：支付失败</span>
              <span v-if="this.rechargeForm.payStatus == 'CANCELPAY'">支付状态：支付取消</span>
            </div>
            <div style="width:38%;margin-bottom: 20px;margin-top:20px;float: left;">
              备注：{{this.rechargeForm.remark}}
            </div>
          </div>
        </div>
      </el-row>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, exportTopUpData,getRechargeDetails} from "@/api/topUp/topUpLogging";
  import {
    getDeptTree
  } from "@/api/setting/dept/systemDeptSetting";
  import {mapGetters} from "vuex";
  const DIC = {
    TOPUPTYPE:[{
      label: '微信支付',
      value: 'WXPAY'
    },{
      label: '现金支付',
      value: 'CASH'
    },{
      label: '通联支付',
      value: 'ALLIN'
    }],
    OPERATION:[{
      label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),老师改为人员(2),家长改为代订(1)
      value: 'TEACHER'
    },{
      label: '代订(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),老师改为人员(2),家长改为代订(1)
      value: 'PARENT'
    },{
      label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),老师改为人员(2),家长改为代订(1)
      value: 'STUDENT'
    },{
      label: '职工',
      value: 'UNIT'
    },{
      label: '其他',
      value: 'OTHER'
    }],
    PAYSTATUS:[{
      label: '支付失败',
      value: 'PAYFAIL'
    },{
      label: '支付成功',
      value: 'PAID'
    },{
    label: '等待回调',
      value: 'WAITPAY'
  }],
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
  }
  export default {
    props: {
        schoolId: String,
    },
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        searchForm:{},
        rechargeForm:{},
        selectionList: [],
        startDate:undefined,
        endDate:undefined,
        rechargeDetailsVisible:false,
        option: {
       /*   height:'auto',*/
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          editBtn: false,
          delBtn: false,
          addBtn: false,
          viewBtn: false,
          selection: false,
          searchSpan:100,
          showSummary: true,
          sumColumnList: [
            {
              name: "targetAmount",
              type: "sum"
            },
            {
              name: "payAmount",
              type: "sum"
            },
            {
              name: "serviceCharge",
              type: "sum"
            },
            {
              name: "actualAmount",
              type: "sum"
            }
          ],
          column: [
            {
              label: "id",
              prop: "id",
              type: "input",
              hide: true
            },
            {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              search: true,
              sortable:true,
            },
            {
              label: "性别",
              prop: "sex",
              type: "select",
              dicData: DIC.sex,
              hide:true,
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
              search: true,
              sortable:true,
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              width: 150,
              overHidden: true,
              sortable:true,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              search: true,
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "充值账户",
              prop: "mealsType",
              type: "select",
              search:true,
              sortable:true,
              dicData:[
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ]
            },
            {
              label: "充值方式",
              prop: "topUpType",
              type: "select",
              dicData: DIC.TOPUPTYPE,
              search: true,
              sortable:true,
            },
            {
              label: "支付状态",
              prop: "payStatus",
              type: "select",
              dicData: DIC.PAYSTATUS,
              search: true,
              slot:true,
              sortable:true,
            /*  hide:true,*/
            },
            {
              label: "开始金额",
              prop: "startAmount",
              type: "input",
              hide:true,
              sortable:true,
            },
            {
              label: "结束金额",
              prop: "endAmount",
              type: "input",
              hide:true,
              sortable:true,
            },
            {
              label: "开始时间",
              prop: "startDateTime",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDateTime",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
/*            {
              label: '充值时间',
              prop: 'date',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "伙食费充值金额",//充值金额
              prop: "targetAmount",
              sortable:true,
            },
            {
              label: "手续费费率",
              prop: "serviceChargeRate",
              type: "input",
              sortable:true,
                hide: true,
            },
            {
              label: "客户实付金额",
              prop: "payAmount",
              sortable:true,
            },
            {
              label: "微信支付手续费",//手续费
              prop: "serviceCharge",
              type: "input",
              sortable:true,
            },
            {
              label: "单位实收金额",//实收金额
              prop: "actualAmount",
              type: "number",
              sortable:true,
            },
            {
              label: "充值操作人",
              prop: "topUpUser",
              type: "input",
              search:true,
              sortable:true,
            },
            {
              label: "操作人身份",
              prop: "identity",
              type: "input",
              hide:true,
            },
            {
              label: "操作人身份",
              prop: "rechargeOperationType",
              type: "select",
              dicData: DIC.OPERATION,
              search: true,
            },
            {
              label: "充值时间",
              prop: "createTime",
              type: "input",
              width:160,
              sortable:true,
              /*hide:true,*/
            },
      /*      {
              label: "充值单号",
              prop: "orderId",
              type: "input",
              width:160,
            },*/
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.topUpLogging_add, false),
          viewBtn: this.vaildData(this.permission.topUpLogging_view, false),
          delBtn: this.vaildData(this.permission.topUpLogging_delete, false),
          editBtn: this.vaildData(this.permission.topUpLogging_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
/*      getDeptTree().then(res => {
        const index = this.$refs.crud.findColumnIndex("deptId");
        this.option.column[index].dicData = res.data.data;
      });*/
      this.getMonthStartEnd();
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
      if (this.userInfo.userType === 'school'){
        this.option.column[1].hide = true
        this.option.column[2].hide = true
        this.option.column[1].search = false
        this.option.column[2].search = false
      /*  this.option.printBtn = true;*/
      }else if (this.userInfo.userType === 'education'){
        this.option.column[2].search = false;
        this.option.column[2].search = false;
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
        this.option.column[7].search = false;
      }else if (this.userInfo.userType === 'jiWei'){
        this.option.column[2].search = false;
        this.option.column[2].hide = true;
        this.option.column[1].hide = true;
        this.option.column[1].search = false;
        this.option.column[7].search = false;
      }else if (this.userInfo.userType === 'canteen'){
        this.option.column[1].hide = true
        this.option.column[2].hide = true
        this.option.column[1].search = false
        this.option.column[2].search = false
      }
    },
    methods: {
      getMonthStartEnd(){
        var startDate = new Date().getFullYear()+' 01-01'
        var endDate = new Date().getFullYear()+' 12-31'
        this.startDate = startDate;
        this.endDate = endDate;
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.searchForm ={};
        this.getMonthStartEnd();
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        if (params.startDateTime != '' && params.startDateTime != null && params.startDateTime != undefined){
          if (params.endDateTime == '' || params.endDateTime == null || params.endDateTime == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDateTime != '' && params.endDateTime != null && params.endDateTime != undefined){
          if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDateTime);
            var endDateTime = new Date(params.endDateTime);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
          this.getMonthStartEnd();
        }else {
          this.startDate = params.startDateTime;
          this.endDate = params.endDateTime;
        }
        this.query = params;
        this.searchForm = params;
        this.page.currentPage = 1
  /*      if (params.date != '' && params.date != null && params.date != undefined) {
          params.startDateTime = params.date[0];
          params.endDateTime = params.date[1];
          this.startDate = params.date[0];
          this.endDate = params.date[1];
        }*/
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.canteenId = this.schoolId;
        }
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      exports(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出充值明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportTopUpData(this.searchForm, this.ids).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值明细报表.xls';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      rechargeDetails(row){
        getRechargeDetails(row.id).then(res => {
          this.rechargeForm = res.data.data;
        });
        this.rechargeDetailsVisible = true;
      }
    }
  };
</script>

<style>
</style>
