<template>
  <basicContainer>
    <avue-crud :option="allOption"
               :data="allData"
               :page="allPage"
               v-model="monthForm"
               :table-loading="loading"
               ref="monthForm"
               @search-change="searchChangeMonth"
               @search-reset="searchResetMonth"
               @selection-change="selectionChangeMonth"
               @current-change="currentChangeMonth"
               @size-change="sizeChangeMonth"
               @on-load="onLoadAll">
<!--      <template slot="menuLeft">-->

<!--      </template>-->
      <template slot-scope="{row}" slot="menu">
        <el-button type="text" icon="el-icon-view" size="small" plain @click.stop="viewDetail(row)">查看 </el-button>
      </template>
    </avue-crud>

    <el-dialog title='盘点详情' :visible.sync="dateDetailsVisible" width="70%" left :append-to-body="true" @close="tongjiaocan">
      <el-form ref="verificationDataForm" :inline="true" :model="verificationDataForm" label-width="80px">
        <el-row style="padding-top: 30px;">
          <el-row :span="24">
            <el-form-item label="名称">
              <el-input v-model="verificationDataForm.name" style="width:320px" :disabled="role == 'auditCheck' && aution == 'edit' && schedule == 1"></el-input>
            </el-form-item>
            <el-col :span="24">
              <a style="color: red" type="danger" size="small" icon="el-icon-delete" plain> 盘点情况 </a>
            </el-col>
            <el-col :span="24">
              <p style="color: red" type="danger" size="small" icon="el-icon-delete" plain>
                1、如果本次盘点实际数量少于盘点时库存数量，则务必更改“本次盘点实际数量”的具体数值。更改后 </p>
              <p style="color: red" type="danger" size="small" icon="el-icon-delete" plain>
                2、若产生报废数量，则必须要录入报废原因，并拍照证明（上传照片） </p>
            </el-col>
          </el-row>

          <el-table :data="verificationDataForm.detailList"  border style="width: 100%;" @cell-click="tabClick" :row-class-name="tableRowClassName">
            <el-table-column prop="businessName" label="商品名称" align="center" width="120" style="background: #fafafa;"> </el-table-column>
            <el-table-column prop="brand" label="品牌" align="center" width="120"> </el-table-column>
            <el-table-column prop="unit" align="center" label="计量单位" width="120"> </el-table-column>
            <el-table-column prop="inventoryNumber" align="center" label="盘点时库存数量" width="120"> </el-table-column>
            <el-table-column align="center" label="本次盘点实际数量" width="120">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.realityInventoryNumber" style="width:90px" :min="0" :controls="false" @input='changeNumber(scope.row)' placeholder="请输入" :disabled="role == 'auditCheck' && aution == 'edit'  && schedule == 1"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column align="center"  label="报废数量" width="120">
              <template slot-scope="scope">
                <el-input-number v-model="scope.row.scrapBusinessNumber" :min="0" style="width:90px" :controls="false" placeholder="请输入" :disabled="role == 'auditCheck' && aution == 'edit'  && schedule == 1"></el-input-number>
              </template>
            </el-table-column>
            <el-table-column align="center"  label="报废原因" width="380">
              <template slot-scope="scope">
                <el-input v-model="scope.row.remark" placeholder="请输入" :disabled="role == 'auditCheck' && aution == 'edit'  && schedule == 1"></el-input>
              </template>
            </el-table-column>
            <el-table-column align="center"  label="拍照证明" width="200">
              <template slot-scope="scope">
                <div v-if="isAttachment" style="height:25%;width:100%;">
                  <el-upload class="upload-demo" action="/api/service/rabbit-resource/oss/endpoint/put-file" :headers="uploadHeaders"
                             :file-list="scrapImages" :on-success="handleUploadSuccess" :on-remove="handleUploadRemove"
                             :before-upload="beforeUpload" :on-preview="openPreview" v-if="!viewFlag">
                    <el-button size="mini" type="primary" @click="uploadData(scope)" >点击上传</el-button>
                  </el-upload>
                </div>

                <div v-else v-for="(item, index) in scope.row.scrapImage" :key="index" style="width: 100%;margin-bottom:10px">
                  <el-link type="primary" @click="openPreviewByView(item)">{{item.name}}</el-link>
                </div>

              </template>
            </el-table-column>
          </el-table>

          <!-- 盘点审核人或者查看详情时可以看到此项  -->
          <el-row :span="24" style="margin-top: 20px;" v-if="(role == 'auditCheck' || aution =='view') && schedule == 1">
            <el-form-item label="审核备注">
              <el-input v-model="verificationDataForm.auditRemark" style="width:320px" :disabled="aution =='view'"></el-input>
            </el-form-item>
          </el-row>

        </el-row>
      </el-form>
      <div style="text-align: center;margin-top: 20px;">
        <div v-if="aution =='edit'">
          <el-button type="primary" v-if="role == 'auditCheck' && schedule == 1" @click="auditData(3)">审核通过</el-button>
          <div v-else>
            <el-button type="primary" @click="addData(0)">保存</el-button>
            <el-button type="primary" @click="addData(1)">保存并提交审核</el-button>
          </div>
        </div>
        <div v-else>
          <el-button type="primary" @click="closeDetail()">关闭</el-button>
        </div>
      </div>
    </el-dialog>

    <el-dialog  append-to-body :visible.sync="imageDialog" @close="closeImage">
      <el-image  :src="fileUrl">
      </el-image>
    </el-dialog>
  </basicContainer>
</template>

<script>
import {getList,startCheck,saveData,getDetail} from "@/api/liancan/storageCount";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      role: '',
      aution: '',
      schedule: '',
      loading: true,
      dateDetailsVisible:false,
      query: {},
      searchForm1:{},
      monthForm:{},
      allPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      allOption:{
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        column: [
          {
            label: "盘点单号",
            prop: "id",
            type: "input",
          },
          {
            label: '名称',
            prop: 'name',
            type:'input',
          },
          {
            label: "盘点商品种类",
            prop: "businessNumber",
            type: "input",
          },
          {
            label: "报废商品种类",
            prop: "scrapBusinessNumber",
            type: "input",
          },
          {
            label: "报废总金额",
            prop: "scrapBusinessAmount",
            type: "input",
          },
          {
            label: "盘点人",
            prop: "operator",
            type: "input",
            search:true,
          },
          {
            label: "盘点时间",
            prop: "createTime",
            type: "input",
          },
          {
            label: "进度",
            prop: "schedule",
            type: "select",
            dicData: [
              {
                label: "盘点中",
                value: 0
              },
              {
                label: "盘点完成待审核",
                value: 1
              },
              {
                label: "盘点取消",
                value: 2
              },
              {
                label: "盘点完成",
                value: 3
              }
            ],
          },
          {
            label: "盘点开始时间",
            prop: "startDate",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd 00:00:00",
            searchLabelWidth:110,
            search: true,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "盘点结束时间",
            prop: "endDate",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd 23:59:59",
            searchLabelWidth:110,
            search:true,
            hide:true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
        ]
      },
      allData: [],
      verificationDataForm: {},
      isAttachment: false,
      // 上传接口头部信息
      uploadHeaders: {
        Authorization: ''
      },
      fileVOList: [],
      uploadDataList:[],
      imageDialog: false,
      fileUrl: '',
      imageId: '',
      viewFlag: false,
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
  },
  created() {
    // 设置上传接口头部信息
    const access_token = JSON.parse(localStorage.getItem("rabbit-liancan-userInfo")).content.access_token
    this.uploadHeaders = {
      'Authorization':'Bearer ' + access_token,
    };
  },
  methods: {
    searchChangeMonth(params, done) {
      this.searchForm1 = params;
      this.query = params;
      this.allPage.currentPage = 1
      this.onLoadAll(this.allPage, params);
      done();
    },
    searchResetMonth() {
      this.query = {};
      this.searchForm1 = {};
      this.onLoadAll(this.allPage);
    },
    selectionChangeMonth(list) {
      this.selectionList = list;
    },
    currentChangeMonth(currentPage){
      this.allPage.currentPage = currentPage;
    },
    sizeChangeMonth(pageSize){
      this.allPage.pageSize = pageSize;
    },
    selectionClearMonth() {
      this.selectionList = [];
      this.$refs.monthForm.toggleSelection();
    },
    onLoadAll(page, params = {}){
      this.loading = true;
      // this.query.auditStatus = 1;
      this.query.deptId = this.schoolId;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.allPage.total = data.total;
        this.allData = data.records;
        this.loading = false;
        this.selectionClearMonth();
      });
    },
    detailCommon(row){
      getDetail(row.id).then(res=>{
        var detailList = res.data.data.detailList;
        detailList.forEach(ele=>{
          if(ele.scrapImage != null && ele.scrapImage != ''){
            var images = ele.scrapImage.split(",");
            var imageList = [];
            for(var i = 0 ; i < images.length ; i++){
              imageList.push({'name':'img_'+i+1,'link':images[i]})
            }
            ele.scrapImage = imageList;
          }
        })

        res.data.data.detailList = detailList;

        this.verificationDataForm = res.data.data;
      })
    },
    viewDetail(row){
      this.detailCommon(row);
      this.aution = 'view';
      this.schedule = row.schedule;
      this.dateDetailsVisible = true;
    },
    //值改变事件，用于自动添加报废数量
    changeNumber(row){
      row.scrapBusinessNumber = row.inventoryNumber - row.realityInventoryNumber;
    },
    //上传成功回调
    handleUploadSuccess: function(response, file, fileVOList) {
      var imageUrl = file.response.data.link;
      let proof = {
        fileId: file.uid,
        name: file.name,
        url: imageUrl,
        type: this.fileType
      }
      this.fileVOList.push(proof);

      var isAdd = true;
      this.uploadDataList.forEach(ele => {
        if(ele.id == this.imageId){
          ele.url = ele.url+","+imageUrl;
          isAdd = false;
        }
      });
      if(isAdd){
        var imageData = {'id':this.imageId,'url':imageUrl};
        this.uploadDataList.push(imageData);
      }
    },
    //删除文件回调
    handleUploadRemove: function(file, fileVOList) {
      var url = file.response.data.link;
      console.log("file",file.response.data.link);
      var fileId = file.fileId;
      for (var item in this.fileVOList) {
        if (this.fileVOList[item].fileId == fileId) {
          this.fileVOList.splice(item, 1);
        }
      }
      this.uploadDataList.forEach(ele=>{
        var imageList = ele.url.split(",");
        imageList.forEach(img=>{
          if(img == url){
            if(imageList.length > 1){
              for(var i = 0; i < imageList.length; i++) {
                if(imageList[i] == url) {
                  imageList.splice(i, 1);
                  break;
                }
              }
            }else{
              img = '';
            }
          }
        })
        ele.url = imageList.toString();
      })
    },
    //上传前回调
    beforeUpload: function(file) {
      var str = file.name;
      str = str.substring(str.lastIndexOf("\.") + 1, str.length);
      var reStr = this.selectType(str);
      if (reStr == "NO") {
        this.$message.error('文件格式错误');
        return false;
      } else {
        this.fileType = reStr;
        return true;
      }
    },
    openPreview: function(item) {
      if (item.raw.type == "image/png" || item.raw.type == "image/jpg" || item.raw.type == "image/jpeg") {
        this.imageDialog = true;
        this.fileUrl = item.response.data.link;
      } else {
        //window.open('/admin/file/download/' + this.tenant + '/' + item.fileId, '_blank')
      }
    },
    uploadData:function(data){
      this.imageId = data.row.id;
    },
    openPreviewByView:function(item){
      this.imageDialog = true;
      this.fileUrl = item.link;
    },
    auditData(type){
      this.$confirm("重要提示：审核通过后，本次库存盘点将自动结束！若本次盘点中，有商品报废了，则其库存数量将会减少相应的报废数量。盘点结束后，可恢复进行入库、出库等操作。您确认要审核通过本次库存盘点么？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          //如果是提交审核，直接将图片处理为字符串
          this.verificationDataForm.detailList.forEach(ele=>{
            var imgStr = ele.scrapImage.toString();
            ele.scrapImage = imgStr;
          });
          this.verificationDataForm.schedule = type;
          saveData(this.verificationDataForm).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.dateDetailsVisible = false;
          }, error => {
            this.$message({
              type: "error",
              message: "操作失败!"+error
            });
          });
        });
    },
    addData(type){
      //处理图片
      this.verificationDataForm.detailList.forEach(ele=>{
        this.uploadDataList.forEach(img=>{
          if(ele.id == img.id){
            if(img.url != ''){
              ele.scrapImage = img.url;
            }
          }
        })
      })

      this.verificationDataForm.schedule = type;
      saveData(this.verificationDataForm).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.dateDetailsVisible = false;
      }, error => {
        this.$message({
          type: "error",
          message: "操作失败!"+error
        });
      });
    },
    closeDetail(){
      this.dateDetailsVisible = false;
    },
    closeImage:function(){
      this.imageDialog = false;
    },





  }
}
</script>

<style scoped>

</style>
