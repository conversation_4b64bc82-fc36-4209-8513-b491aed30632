<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="addOrderHandler" >新增</el-button>
<!--                <el-button type="primary"  icon="el-icon-plus" size="small" @click="exportOrderHandler" >导出</el-button>-->
<!--                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>-->
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>

        <el-dialog title="新增"
                   :visible.sync="isShowAdd"
                   v-if="isShowAdd"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm" width="60%"  style="height: 90%;">
            <el-form ref="formAdd" label-width="80px">
                <el-form-item label="单据日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-date-picker type="date"
                                        placeholder="选择日期"
                                        :picker-options="pickerOptions"
                                        v-model="businessDate"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;"></el-date-picker>
                    </el-col>
                </el-form-item>
                <el-form-item label="退料员" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-select v-model="salesman" placeholder="请选择" style="width: 100%;">
                            <el-option
                                v-for="item in receiverList"
                                :key="item.id"
                                :label="item.contactName"
                                :value="item.id">
                            </el-option>
                        </el-select>
                    </el-col>
                </el-form-item>
                <el-form-item label="订单商品" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="3">
                        <el-button type="primary" size="small" @click="selectGoodHandler">从商品库中选择</el-button>
                    </el-col>
                    <el-col :span="3">
                        <el-button type="primary" size="small" @click="relateInOrder">关联领料单</el-button>
                    </el-col>
                </el-form-item>

            </el-form>
            <div style="height: 350px;overflow-y: scroll;">
                <avue-crud :option="orderOption"
                           :table-loading="orderLoading"
                           :data="orderData"
                           :page="orderPage"
                           :before-open="beforeOpenOrder"
                           v-model="orderForm"
                           ref="crudOrder"
                           @row-update="rowUpdateOrder"
                           @row-save="rowSaveOrder"
                           @row-del="rowDelOrder"
                           @search-change="searchChangeOrder"
                           @search-reset="searchResetOrder"
                           @selection-change="selectionChangeOrder"
                           @current-change="currentChangeOrder"
                           @size-change="sizeChangeOrder"
                           @cell-click="handleRowClick"
                           @on-load="orderOnLoad">
                    <template slot="menu" slot-scope="{row,index}">
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-edit"
                            v-if="!row.$cellEdit"
                            @click="rowCellOrder(row,index)"
                        >修改</el-button>
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-check"
                            v-if="row.$cellEdit"
                            @click="rowSaveOrder(row,index)"
                        >保存</el-button>
                        <el-button
                            type="text"
                            size="mini"
                            icon="el-icon-delete"
                            @click="deleteRowOrder(row,index)">删除
                        </el-button>
                    </template>
                </avue-crud>
            </div>
            <div>

                订单总额: <font color="red">{{sumAmt}}</font>元
            </div>
            <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddForm">取消</el-button>
                    <el-button type="primary" @click="saveSelectHandle">保存</el-button>
                </span>
        </el-dialog>

        <goods ref="goods" @choiceClick="refreshData"/>
        <order ref="order"/>
      <in-order ref="inOrder" @choiceClick="refreshInData"/>
    </basic-container>
</template>

<script>
import {getList, getDetail, add} from "@/api/supplier/supplierProduceReturnOrder";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSalesmanList,stockLoad} from "@/api/supplier/supplier";
import goods from "../common/goods.vue";
import order from "./order.vue";
import inOrder from "./inOrder.vue";
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            editType: 'add',
            data:[],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                searchMenuSpan: 4, //搜索按钮长度
                // searchSpan:24,      //搜索框长度  最大长度24
                // searchLabelWidth: 120, //搜索框标题宽度 默认80
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() < Date.now();
                            // }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "物料名称",
                        prop: "goodsName",
                        type: "input",
                        width: 180,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "物料名称",
                        prop: "goodGoodId",
                        type: "select",
                        hide: true,
                        search: true,
                        props: {
                            label: 'name',
                            value: 'goodGoodId'
                        },
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "退料成本总额",
                        prop: "totalAmt",
                        type: "number",
                        labelWidth: 140,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: false,
                            message: "请输入成本总额",
                            trigger: "blur"
                        } ],
                        formatter: (row,value,label,column) => {
                            return amtFilters(value);
                        },
                        precision: 2,
                        display: false
                    },
                    {
                        label: '退料人',
                        prop: 'salesman',
                        type:'select',
                        search: true,
                        dicUrl: `/api/rabbit-supplier/user-list`,
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        multiple:true
                    },
                    {
                        label: "退料仓库",
                        prop: "warehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "登记人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                        display: false
                    },
                    {
                        label: "登记时间",
                        prop: "createTime",
                        type: "datetime",
                        format: 'yyyy-MM-dd HH:mm:ss',
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                        display: false
                    },
                ]
            },
            isShowAdd: false,
            //新增订单单弹窗参数 start
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            orderLoading: true,
            orderData:[],
            orderPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            orderForm:{},
            businessDate: '',
            relateFlag:'N',
            stockFlag:0,
            sumAmt:0.00,
            salesman:'',
            rowOrder: {},
            receiverList:[],
            orderOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品名称",
                        prop: "goodName",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "退料仓库",
                        prop: "warehouseId",
                        type: "select",
                        cell: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "退料数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "成本单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "退料金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',

                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },

        };
    },
    components:{
        order,
        goods,
        inOrder,
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.receiverList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    methods: {
        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        rowCellOrder(row, index) {
            this.rowOrder= row;
            this.$refs.crudOrder.rowCell(row, index);
            this.$refs.crudOrder.rowCancel(row, row.$index);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //增加
        addOrderHandler () {
            this.form = {};
            this.editType = 'add';
            this.isShowAdd = true;
            this.summaryAmt();
        },
        //关闭新增窗口
        closeAddForm() {
            if (this.orderData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowAdd = false;
                    });
            }else{
                this.isShowAdd = false;
            }
        },
        rowUpdateOrder(row, index, loading, done) {
            done()
        },
        async rowSaveOrder(row, index) {
            var qty = row.qty;
            var price = row.price;
            var goodsId = row.id;
            if(row.relateFlag ==='Y'){
                goodsId = row.goodsId;
            }
            await stockLoad(row.warehouseId,goodsId).then(res => {
                // var stockAmount = res.data.data.stockAmount;
                price = res.data.data.costPrice;
                // if(stockAmount - qty < 0){
                //     this.$message.warning("订单数量不能超过库存数量,当前"+stockAmount+",请求"+qty);
                //     this.stockFlag = 1;
                // }
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
            if( this.stockFlag === 1) {
                return;
            }
            row.goodName = row.name;
            row.amt = qty * price;
            row.price = price;
            row.goodsId = goodsId;
            this.orderData[index] = row;
            row.$cellEdit = false;
            this.$refs.crudOrder.rowCellUpdate();
            //合计所有的行
            this.summaryAmt(row);
        },
        orderOnLoad(page, params = {}) {
            this.orderLoading = false;
            if (this.form.id!==undefined && this.form.id!=='') {
                getDetail(this.form.id).then(res => {
                    const data = res.data.data;
                    this.businessDate = data.businessDate;
                    //this.salesman = data.salesman;
                    this.orderData = data.detailList;
                    this.orderPage.total = this.orderData.length;
                    this.orderData.forEach((item) => {
                        item.$cellEdit = false;
                    });
                    this.orderLoading = false;
                    // this.selectionClearOrder();
                });
            }else{
                this.orderPage.total = 0;
                this.orderData = [];
                this.orderData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.orderLoading = false;
                // this.selectionClearOrder();
            }
        },

        selectGoodHandler() {
            this.$refs.goods.isShowGoods= true;
        },
        relateInOrder(){
            this.$refs.inOrder.isShowInOrder= true;
        },
        refreshData(row) {
            if(row.warehouseId == '0'){
                row.warehouseId = '';
            }
            row.goodName = row.name;
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            row.relateFlag = "N";
            this.orderData.push(row);
        },
        refreshInData(row) {
            getDetail(row.id).then(res => {
                const data = res.data.data;
                console.log("==========>",data);
                data.detailList.forEach((item) => {
                    item.relateFlag = "Y";
                    this.orderData.push(item);
                    item.$cellEdit = false;
                });
            });
        },
        summaryAmt() {
            var iTotalAmt = 0;
            this.orderData.forEach((item,index)=>{
                iTotalAmt += item.totalAmt;
            });
            this.sumAmt = amtFilters(iTotalAmt);
        },
        deleteRowOrder(row) {
            this.orderData.splice(row.index,1);
            this.summaryAmt();
        },
        saveSelectHandle(){
            if (this.businessDate === undefined || this.businessDate === '') {
                this.$message.warning("请选择单据日期");
                return;
            }
            if (this.salesman === undefined || this.salesman === '') {
                this.$message.warning("请选择退料员");
                return;
            }
            if (this.orderData.length==0) {
                this.$message.warning("请选择商品");
                return;
            }
            var isSave = true;
            this.orderData.forEach((item,index)=>{
                var warehouseId = item.warehouseId;
                var qty = item.qty;
                if (warehouseId==='') {
                    isSave = false;
                    this.$message.warning("第"+index+"行,未选择入库仓库");
                    return;
                }else if (qty===0) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,订单数量不正确");
                    return;
                }
            });
            if (!isSave) {
                return;
            }
            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                    this.form.businessDate = this.businessDate;
                    this.form.salesman = this.salesman;
                    this.orderLoading = true
                    console.log(this.orderData)
                    add(this.form,this.orderData).then((res) => {
                        this.orderLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShowAdd = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                }).then(() => {
                this.isShowAdd = false;
            });

        },
        viewRow(row,index) {
            this.$refs.order.isShowOrderDetail = true;
            this.$refs.order.orderOnLoad(row);
        },

    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
