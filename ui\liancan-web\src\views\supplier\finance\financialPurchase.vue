<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-setting"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.ifVoucher == 0"
                   @click.stop="createVoucher(row)">生成凭证
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getWarehousingOrderList} from "@/api/liancan/order";
import {createPurchaseVoucher} from "@/api/supplier/finance/financialVoucher";
import {dateFormat} from "@/util/date";
import {mapGetters} from "vuex";
var DIC = {
  isBuy: [{
    label: '未确认',
    value: "0"
  },{
    label: '确认通过',
    value: "1"
  },{
    label: '确认拒绝',
    value: "2"
  }],
  orderStatus: [
    { label: '未配送', value: "0" },
    { label: '已按期配送', value: "1" },
    { label: '取消/拒单', value: "2" },
    { label: '接单未配送', value: "3" }
  ]
}
export default {
  data() {
    return {
      loading: true,
      form: {},
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: true,
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            display: false,
            hide: true,
          },
          {
            label: "所属食堂",
            prop: "deptId",
            type: "tree",
            //  multiple: true,
            dicData: [],
            props: {
              label: "title"
            },
            search:false,
            hide:true,
            /*       rules: [{
                     required: true,
                     message: "请选择所属部门",
                     trigger: "click"
                   }]*/
          },
          {
            label: "所属食堂",
            prop: "deptName",
            type: "input",
            display: false,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            // dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
            dicUrl: "/api/service/rabbit-supplier/supplierCompany/all/list",
            props: {
              label: "name",
              value: "id"
            },
            disabled: true,
            search:true,
          },
          {
            label: "商品",
            prop: "goodsName",
            type: "input",
            display: false,
          },
          {
            label: "价格",
            prop: "price",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            display: false,
          },
          {
            label: "验收数量",
            prop: "accepterQuantity",
            type: "input",
            display: false,
          },
          {
            label: "实际验收小计",
            prop: "subtotal",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            display: false,
          },
          {
            label: "订单状态",
            prop: "orderStatus",
            type: "select",
            dicData: DIC.orderStatus,
            disabled: true,
            search:true,
          },
          {
            label: "送货地址",
            prop: "site",
            type: "input",
            disabled: true,
          },
          {
            label: "编码",
            prop: "financialCode",
            type: "input",
            sortable:true,
          },
          {
            label: "订单日期",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
          },
          {
            label: "电话",
            prop: "phone",
            type: "input",
            hide: true,
            disabled: true,
          },
          {
            label: "联系人",
            prop: "userName",
            type: "input",
            hide: true,
            disabled: true,
          },
        ]
      },
      data: [],
    }
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.financial_purchase_add, false),
        viewBtn: this.vaildData(this.permission.financial_purchase_view, false),
        delBtn: this.vaildData(this.permission.financial_purchase_delete, false),
        editBtn: this.vaildData(this.permission.financial_purchase_edit, false)
      }
    }
  },
  methods: {
    beforeOpen(done, type) {
      done();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.orderStatus = 1;
      getWarehousingOrderList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getWarehousingOrderList ===== ', res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    createVoucher(row) {
      this.$confirm("确定生成凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        let param = {
          yanshouOrRukuFlag: 'yanshou',
          billCode: row.financialCode,
          billNo: row.goodsId,
          canteenId: row.deptId
        }
        return createPurchaseVoucher(param);
      }).then(
        () => {
          this.loading = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        },
        error => {
          this.loading = false;
          this.$message.error(error);
        }
      );
    }
  }
}
</script>

<style scoped>

</style>
