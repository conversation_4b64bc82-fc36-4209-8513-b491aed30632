<template>
  <div class="charge-container" id="chargeContainer">
    <div class="charge">
      <el-row type="flex" class="row-bg" justify="center">
        <h1 class="charge-title">记账凭证</h1>
      </el-row>

      <div class="charge-header">
        <div>
          凭证字
          <el-select style="width: 80px;" v-model="voucher.word">
            <el-option
              v-for="item in wordList"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>

        </div>

        <div>
          凭证号
          <el-input type="number" style="width: 80px;" min="1" v-model="voucher.no"></el-input>
        </div>

        <div>
          日期
          <el-date-picker type="date" style="width: 145px;"
                          :picker-options="expireTimeOption"
                          v-model="voucher.date" @change="changeVoucherNo"></el-date-picker>
        </div>
        <div style="float: right;position: relative;">
          附单据
          <el-input style="width: 80px;" readonly="readonly" v-model="voucher.bill"></el-input>
          张
          <el-button type="primary" @click="showAttachmentDialog" size="small" plain>管理</el-button>
                    <i class="el-icon-info"></i>
                    <div class="tip-box">
                      <table class="tip-box-table" border="1">
                        <tr style="background-color: #f5f4f4;">
                          <td>操作</td>
                          <td>快捷键</td>
                        </tr>
                        <tr>
                          <td>选择列表</td>
                          <td>F7</td>
                        </tr>
                        <tr>
                          <td>自动平衡</td>
                          <td>=</td>
                        </tr>
                        <tr>
                          <td>新增</td>
                          <td>F4</td>
                        </tr>
                        <tr>
                          <td>保存</td>
                          <td>Ctrl+S</td>
                        </tr>
                        <tr>
                          <td>保存并审核</td>
                          <td>F10</td>
                        </tr>
                        <tr>
                          <td>保存并新增</td>
                          <td>F11</td>
                        </tr>
                        <tr>
                          <td>复制上一行</td>
                          <td>//</td>
                        </tr>
                        <tr>
                          <td>复制上一行摘要</td>
                          <td>..</td>
                        </tr>
                        <tr>
                          <td>金额自动切换借贷方</td>
                          <td>空格键</td>
                        </tr>
                        <tr>
                          <td>单据头与分录快速切换</td>
                          <td>Tab</td>
                        </tr>
                      </table>
                    </div>
        </div>
      </div>
      <table class="charge-table" border="1">
        <tr>
          <td width="6%">操作</td>
          <td :width="isAuxiliary?'14%':'22%'">摘要</td>
          <td :width="isAuxiliary?'15%':'22%'">会计科目</td>
          <td width="14%" v-if="isAuxiliary">辅助核算</td>
          <td width="50%">
            <table style="height: 50px;">
              <tr style="border-bottom: 1px solid #bab9b9;">
                <td width="50%" style="border-right: 1px solid #bab9b9;">借方金额</td>
                <td width="50%">贷方金额</td>
              </tr>
              <tr>
                <td style="border-right: 1px solid #bab9b9;">
                  <table class="debtor-lender-table" style="height: 100%;">
                    <tr>
                      <td>亿</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>万</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>元</td>
                      <td>角</td>
                      <td>分</td>
                    </tr>
                  </table>
                </td>
                <td>
                  <table class="debtor-lender-table" style="height: 100%;">
                    <tr>
                      <td>亿</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>万</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>元</td>
                      <td>角</td>
                      <td>分</td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr v-for="(item,index) in list" v-bind:key="item">
          <td>
            <div @click="addList" class="charge-table-icon">
              <i class="el-icon-plus" style="color: orangered;"></i>
            </div>
            <div @click="removeList(index)" class="charge-table-icon">
              <i class="el-icon-close" style="color: #4a90e2;"></i>
            </div>
          </td>
          <td>
            <div @click="showInput(index,'main')" v-if="!item.isShowMainInput" class="main-subject">
              <textarea v-model="item.main"></textarea>
            </div>
            <div class="main-subject" v-if="item.isShowMainInput">
              <!--<input type="text" v-model="item.main" @blur="hideInput(index,'main')" v-focus @keyup="keyupEvents(index,$event,1)">-->
              <textarea v-model="item.main" @blur="hideInput(index,'main')" v-focus
                        @keyup="keyupEvents(index,$event,1)"></textarea>
            </div>
            <i v-if="item.isShowMainInput" class="el-icon-more special-elements"
               @click="selectionList(index,'main')"></i>
          </td>
          <td>
            <div @click="showInput(index,'subject')" v-if="!item.isShowSubjectInput" class="main-subject">
              <textarea v-model="item.subject.name" readonly="readonly"></textarea>
            </div>
            <div class="main-subject" v-if="item.isShowSubjectInput">
              <textarea v-model="item.subject.name" @blur="hideInput(index,'subject')" v-focus
                        @keyup="keyupEvents(index,$event,2)" readonly="readonly"></textarea>
            </div>
            <i v-if="item.isShowSubjectInput" class="el-icon-more special-elements"
               @click="selectionList(index,'subject')"></i>
          </td>
          <td>
            <table>
              <tr>
                <td width="50%" style="border-right: 1px solid #bab9b9;">
                  <table class="debtor-tbale debtor-lender-table">
                    <tr @click="showInput(index,'debtor')" v-if="!item.isShowDebtorInput"
                        :class="item.debtor*1<0?'tr-negative':''">
                      <td v-for="debtor in item.debtorList" v-bind:key="debtor">{{ debtor }}</td>
                    </tr>

                    <tr v-if="item.isShowDebtorInput">
                      <input type="number" @blur="hideInput(index,'debtor')" @keyup="debtorInputKeyUp(index,$event,3)"
                             v-model="item.debtor" maxlength="12"
                             v-focus>
                    </tr>
                  </table>
                </td>
                <td width="50%">
                  <table class="lender-tbale debtor-lender-table">
                    <tr v-if="!item.isShowLenderInput" @click="showInput(index,'lender')"
                        :class="item.lender*1<0?'tr-negative':''">
                      <td v-for="lender in item.lenderList" v-bind:key="lender">{{ lender }}</td>
                    </tr>

                    <tr v-if="item.isShowLenderInput">
                      <input type="number" @blur="hideInput(index,'lender')" @keyup="lenderInputKeyUp(index,$event,4)"
                             v-model="item.lender" maxlength="12"
                             v-focus>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td :colspan="isAuxiliary?4:3" style="text-align: left;padding-left: 10px;">合计：</td>
          <td>
            <table>
              <tr>
                <td width="50%" style="border-right: 1px solid #bab9b9;">
                  <table class="debtor-tbale debtor-lender-table">
                    <tr>
                      <td v-for="debtor in debtorTotalListComputed" v-bind:key="debtor">{{ debtor }}</td>
                    </tr>
                  </table>
                </td>
                <td width="50%">
                  <table class="lender-tbale debtor-lender-table">
                    <tr>
                      <td v-for="lender in lenderTotalListComputed" v-bind:key="lender">{{ lender }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <el-row>
        <el-col :span="4">
          <div style="height: 50px;line-height: 50px;">制单人：{{this.uInfo.nickname}}</div>
        </el-col>
        <el-col :span="12"></el-col>
        <!-- <el-col :span="2">
          <div style="height: 50px;line-height: 50px;">审核人：</div>
        </el-col>
        <el-col :span="10">
          <div style="height: 50px;line-height: 50px;">
            <el-select v-model="auditUser" placeholder="请选择">
              <el-option
                v-for="item in auditUsersList"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </div>
        </el-col> -->
        <el-col :span="8"></el-col>
      </el-row>
      <div style="text-align: right;">
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="preservation(2)">保存</el-button>
      </div>
    </div>
        <el-dialog title="会计科目" modal="false" append-to-body="true" width="30%" :visible.sync="dialogSubjectVisible">
          <el-tabs type="border-card" v-model="tabsValue">
            <el-tab-pane label="资产类" name="assets">
              <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText">
              </el-input>
              <el-tree
                class="filter-tree"
                :data="subjectDataT1000"
                :props="defaultProps"
                node-key="id"
                show-checkbox
                default-expand-all
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                ref="tree">
              </el-tree>
            </el-tab-pane>
            <el-tab-pane label="负债类" name="debt">
              <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText">
              </el-input>
              <el-tree
                class="filter-tree"
                :data="subjectDataT2000"
                :props="defaultProps"
                node-key="id"
                show-checkbox
                default-expand-all
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                ref="tree">
              </el-tree>
            </el-tab-pane>
            <el-tab-pane label="净资产类" name="netAssets">
              <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText">
              </el-input>
              <el-tree
                class="filter-tree"
                :data="subjectDataT3000"
                :props="defaultProps"
                node-key="id"
                show-checkbox
                default-expand-all
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                ref="tree">
              </el-tree>
            </el-tab-pane>
            <el-tab-pane label="收入类" name="income">
              <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText">
              </el-input>
              <el-tree
                class="filter-tree"
                :data="subjectDataT4000"
                :props="defaultProps"
                node-key="id"
                show-checkbox
                default-expand-all
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                ref="tree">
              </el-tree>
            </el-tab-pane>
            <el-tab-pane label="费用类" name="fee">
              <el-input
                placeholder="输入关键字进行过滤"
                v-model="filterText">
              </el-input>
              <el-tree
                class="filter-tree"
                :data="subjectDataT5000"
                :props="defaultProps"
                node-key="id"
                show-checkbox
                default-expand-all
                :filter-node-method="filterNode"
                @node-click="handleNodeClick"
                ref="tree">
              </el-tree>
            </el-tab-pane>
          </el-tabs>
          <span slot="footer" class="dialog-footer">
            <el-button @click="closeSubjectDialog">取消</el-button>
<!--            <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="edit">确认</el-button>-->
          </span>
        </el-dialog>

<!--    tab页加tree-->
    <el-dialog title="凭证附件管理"
               append-to-body
               :visible.sync="attachmentShow"
               width="60%">
      <el-upload
        ref="uploadRef"
        action="/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan"
        list-type="picture-card"
        :file-list="attachmentList.attachmentFileList"
        :before-upload="handleBeforeUpload"
        :on-success="handleSuccess"
        :on-preview="handlePictureCardPreview"
        :on-remove="handleRemove">
        <i class="el-icon-plus"></i>
      </el-upload>
      <el-dialog :visible.sync="attachmentList.dialogVisible">
        <img width="100%" :src="attachmentList.dialogImageUrl" alt="">
      </el-dialog>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="attachmentShow = false">取 消</el-button>
        <el-button type="primary"
                   @click="saveAttachmentHandle">确 定</el-button>
      </span>
    </el-dialog>
    <div v-loading="loading" class="dashboard-container"></div>
  </div>
</template>

<script>
import Vue from 'vue';
import {mapGetters, mapState} from "vuex";
import {
  addObj,
  getObj,
  putObj,
  getWordList,
  getMainList,
  getSubjectList,
  getAuditUserList,
  getVoucherNo,
  getVoucherBase,
  getFinancialPeriod,
} from '@/api/supplier/finance/voucher';
import {openAccount} from "@/api/supplier/finance/accountSets";
import {collatingData, parseNumber} from "@/api/supplier/finance/financialUtils";
import * as FinancialUtils from "@/api/supplier/finance/financialUtils";

Vue.directive('focus', {
  // 当绑定元素插入到 DOM 中。
  inserted: function (el) {
    // 聚焦元素
    el.focus();
  }
});

export default {
  name: "voucherForm",
  props: ['id'],
  components: {
    'main-selector': () => import('@/components/finance/mainSelector'),
    'subject-selector': () => import('@/components/finance/subjectSelector'),
    'auxiliary-selector': () => import('@/components/finance/auxiliarySelector'),
  },
  watch:{
    $route(a){
      console.log(a)
      this.init();
    }
  },
  data() {
    return {
      loadingCount:0,
      sourceType:'',
      sourceBillIds:'',
      Constant: {},
      filterText: '',
      subjectData: [],
      defaultProps: {
        children: 'children',
        label: 'displaySubjectName'
      },
      tabsValue: "assets",
      imgType: ['jpg','jpeg','png','bmp','gif'],
      attachmentList: {
        dialogImageUrl: '',
        dialogVisible: false,
        attachmentFileList: [],
      },
      attachmentShow: false,
      uInfo:{},
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      curUser: {name: ''},
      auditUsersList: [],
      auditUser: '',
      voucher: {
        word: '记', no: 1, date: new Date(), bill: 0, lastPeriod:''
      },
      expireTimeOption: {},
      list: [
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        }
      ],
      debtorTotal: 0,
      debtorTotalList: ['', '', '', '', '', '', '', '', 0, 0, 0],
      lenderTotal: 0,
      lenderTotalList: ['', '', '', '', '', '', '', '', 0, 0, 0],
      wordList: [],
      mainList: [],
      // subjectList: [],
      dialogMainVisible: false,
      dialogSubjectVisible: false,
      dialogAuxiliaryVisible: false,
      dialogMainIndex: 0,
      dialogSubjectIndex: 0,
      dialogAuxiliaryIndex: 0,
      isAuxiliary: false,
      billNo: '',
      accountSetsId: '',
    }
  },
  created() {
    document.addEventListener('keydown', this.handleEvent);
    this.uInfo = this.userInfo;
    this.changeVoucherNo();
    getWordList().then(response => {
      this.wordList = response.data.data;
    }).catch(err=>{
      console.log("getWordList: ",err);
    }).finally((fin)=>{
    });
    // getMainList().then(response => {
    //   this.mainList = response.data.data;
    // });
    this.accountSetsId = this.$route.query.accountSetsId;
    getAuditUserList(this.accountSetsId).then(response => {
      this.auditUsersList = response.data.data;
      if(this.auditUsersList.length>0)
        this.auditUser = this.auditUsersList[0].id
    }).catch(err=>{
      console.log("getAuditUserList: ",err);
    }).finally((fin)=>{
    });
    getFinancialPeriod().then(response => {
      var constant1 = response.data.data;
      if(constant1!=null && constant1 !=undefined) {
        var enaleTime = constant1.enaleTime;//帐套启用年月
        var startTime = constant1.startTime;//当前记帐年月
        if(startTime!=null && startTime != undefined && startTime.length >0) {
          var startTimeDate = startTime + "-01";
          this.Constant.startTime = startTimeDate;
        }
      }
    }).catch(err=>{
      console.log("getFinancialPeriod: ",err);
    }).finally((fin)=>{
    });
    this.init();
    getSubjectList().then(response => {
      // this.subjectList = response.data.data;
      this.subjectData = response.data.data;
      console.log("subjectData==========over");
    }).catch(err=>{
      console.log("getSubjectList: ",err);
    }).finally((fin)=>{
      this.loadingCount = 99;
    });
  },
  mounted() {

  },
  updated() {
    //给特定区域添加ID   绑定onmousedown 事件
    var chargeContainer = document.getElementById('chargeContainer');
    var outDiv = chargeContainer.getElementsByClassName('special-elements');
    for (var i = 0; i < outDiv.length; i++) {
      outDiv[i].onmousedown = function (e) {
        //现代浏览器阻止默认事件
        if (e && e.preventDefault)
          e.preventDefault();
        //IE阻止默认事件
        else
          window.event.returnValue = false;
        return false;
      }
    }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    debtorTotalListComputed: {
      get() {
        return this.debtorTotalList;
      },
      set(value) {
        this.debtorTotalList = value;
      }
    },
    lenderTotalListComputed: {
      get() {
        return this.lenderTotalList;
      },
      set(value) {
        this.lenderTotalList = value;
      }
    },
    loading() {
      if(this.loadingCount == 99) {
        return false;
      }
      return true;
    },
    subjectDataT1000() {
      return this.subjectData.T1000;
    },
    subjectDataT2000() {
      return this.subjectData.T2000;
    },
    subjectDataT3000() {
      return this.subjectData.T3000;
    },
    subjectDataT4000() {
      return this.subjectData.T4000;
    },
    subjectDataT5000() {
      return this.subjectData.T5000;
    },
  },
  methods: {
    handleNodeClick(data) {
      console.log(data);
      //判断非最后节点不能选择
      if(data.disabled) {
        this.$message.error('不能选择上级节点');
        return;
      }
      var sub = {
        number: data.id,
          name: data.displaySubjectNameAll,
          code: data.subjectCode,
          detailJson: ''
      };
      this.list[this.dialogSubjectIndex].subject = sub;
      this.dialogSubjectVisible = false;
      this.judgeIsAuxiliary();
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    saveAttachmentHandle() {
      // console.log(this.attachmentList.attachmentFileList.length);
      this.voucher.bill = this.attachmentList.attachmentFileList.length;
      this.attachmentShow = false;
    },
    handleBeforeUpload(file,fileList) {
      let extension = file.name.substring(file.name.lastIndexOf('.')+1);
      let isImg = this.imgType.includes(extension);
      let size = file.size/1024;
      if(isImg){
        if(size>500) {
          this.$message.error('图片文件大小不得超过500KB');
        }
      }else {
        this.$message.error('请上传${(}this.imgType.join(“/”)}格式图片');
      }
    },
    handleSuccess(res,file,fileList) {
      this.attachmentList.attachmentFileList = fileList;
    },
    handleRemove(file, fileList) {
      this.attachmentList.attachmentFileList = fileList;
    },
    handlePictureCardPreview(file) {
      this.attachmentList.dialogImageUrl = file.url;
      this.attachmentList.dialogVisible = true;
    },
    showAttachmentDialog() {
      this.attachmentShow = true;
    },
    changeVoucherNo() {
      const bus_data = this.formatDate("yyyy-MM-dd", this.voucher.date);
      getVoucherBase(bus_data).then(response => {
        this.voucher.no = response.data.data.no;
        this.voucher.lastPeriod = response.data.data.lastPeriod;
      }).catch(err=>{
        console.log("changeVoucherNo: ",err);
      }).finally((fin)=>{
      });
    },
    judgeIsAuxiliary() {
      var flag = false;
      this.isAuxiliary = flag;
    },
    openMainDialog() {
      this.dialogMainVisible = true
    },
    closeMainDialog(main) {
      if (!main.isTrusted) {
        this.list[this.dialogMainIndex].main = main;
      }
      this.dialogMainVisible = false
    },
    openSubjectDialog() {
      if(this.loadingCount!=99) {
        this.$message.error('正在加载会计科目，请稍侯');
        return;
      }
      this.dialogSubjectVisible = true
    },
    closeSubjectDialog(sub) {
      if (!sub.isTrusted) {
        this.list[this.dialogSubjectIndex].subject = sub;
      }
      this.dialogSubjectVisible = false;
      this.judgeIsAuxiliary();
    },
    openAuxiliaryDialog() {
      this.dialogAuxiliaryVisible = true
    },
    closeAuxiliaryDialog(aux) {
      if (!aux.isTrusted) {
        this.list[this.dialogAuxiliaryIndex].auxiliary = aux;
      }
      this.dialogAuxiliaryVisible = false;
    },
    clearAuxiliary(index, e) {
      this.list[index].auxiliary = '';
    },

    handleEvent(event) {
      //console.log(event);
      if (window.location.hash == '#/general_ledger/voucher_entry') {
        if (event.keyCode === 83 && event.ctrlKey) {
          //console.log('拦截到83+ctrl');
          this.preservation(2);
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 115) {
          //console.log('拦截到115');//F4
          this.addList();
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 121) {
          //console.log('拦截到121');//F10
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 122) {
          //console.log('拦截到122');//F11
          event.preventDefault();
          event.returnValue = false;
          return false;
        }
      } else {
        //需要销毁事件 防止全局生效
        //document.removeEventListener('keydown', this.handleEvent);
      }

    },
    showInput(index, type) {
      for (var i in this.list) {
        this.list[i].isShowDebtorInput = false;
        this.list[i].isShowLenderInput = false;
        this.list[i].isShowMainInput = false;
        this.list[i].isShowSubjectInput = false;

        if (i == index && type == 'debtor') {
          this.list[index].isShowDebtorInput = true;
        } else if (i == index && type == 'lender') {
          this.list[index].isShowLenderInput = true;
        } else if (i == index && type == 'main') {
          this.list[index].isShowMainInput = false;
        } else if (i == index && type == 'subject') {
          this.list[index].isShowSubjectInput = true;
        }
      }
    },
    hideInput(index, type) {
      var inx = 0;
      if (type == 'debtor') {
        //判断是否有小数点
        inx = this.list[index].debtor.indexOf('.');
        if (inx != -1 && this.list[index].debtor.length - 1 != inx) {
          this.list[index].debtor = (this.list[index].debtor * 1).toFixed(2);
        }
        this.list[index].debtorList = collatingData(this.list[index].debtor, this.list[index].debtorList);
        this.list[index].isShowDebtorInput = false;
      } else if (type == 'lender') {
        //判断是否有小数点
        inx = this.list[index].lender.indexOf('.');
        if (inx != -1 && this.list[index].lender.length - 1 != inx) {
          this.list[index].lender = (this.list[index].lender * 1).toFixed(2);
        }
        this.list[index].lenderList = collatingData(this.list[index].lender, this.list[index].lenderList);
        this.list[index].isShowLenderInput = false;
      } else if (type == 'main') {
        this.list[index].isShowMainInput = false;
      } else if (type == 'subject') {
        this.list[index].isShowSubjectInput = false;
      }

      this.calcDebtorTotal();
      this.calcLenderTotal();
    },
    voluationInput(index, type, val) {
      if (type == 'main') {
        this.list[index].main = val;
        this.list[index].isShowMainInput = false;
      } else if (type == 'subject') {
        this.list[index].subject.number = val.number;
        this.list[index].subject.name = val.name;
        this.list[index].subject.code = val.code;
        this.list[index].subject.detailJson = val.detailJson;
        this.list[index].isShowSubjectInput = false;
      }
      this.judgeIsAuxiliary();
    },
    selectionList(index, type) {
      //console.log('弹出选择列表');
      if (type == 'main') {
        this.dialogMainIndex = index;
        this.openMainDialog();
      } else if (type == 'subject') {
        this.dialogSubjectIndex = index;
        this.openSubjectDialog();
      } else if (type == 'auxiliary') {
        this.dialogAuxiliaryIndex = index;
        this.openAuxiliaryDialog();
      }
    },

    keyupEvents(index, e, remaind) {
      if (e.keyCode == 37) {
        //console.log('拦截到37');//左
        this.keyboardEvents('left', index * 4 + remaind);
        return;
      } else if (e.keyCode == 38) {
        //console.log('拦截到38');//上
        this.keyboardEvents('up', index * 4 + remaind);
        return;
      } else if (e.keyCode == 39) {
        //console.log('拦截到39');//右
        this.keyboardEvents('right', index * 4 + remaind);
        return;
      } else if (e.keyCode == 40) {
        //console.log('拦截到40');//下
        this.keyboardEvents('down', index * 4 + remaind);
        return;
      } else if (e.keyCode == 13) {
        //console.log('拦截到13');//enter键
        this.keyboardEvents('enter', index * 4 + remaind);
        return;
      } else if (e.keyCode == 118) {
        //console.log('拦截到118');//F7
        if (remaind == 1) {
          this.selectionList(index, 'main');
        } else if (remaind == 2) {
          this.selectionList(index, 'subject');
        }
        return;
      }

      var main = this.list[index].main;
      var subject = this.list[index].subject.name;
      if (index - 1 >= 0) {
        if (main.indexOf('//') != -1 || subject.indexOf('//') != -1) {
          this.list[index].main = this.list[index - 1].main;
          this.list[index].subject = this.list[index - 1].subject;
          this.list[index].debtor = this.list[index - 1].debtor;
          this.list[index].debtorList = this.list[index - 1].debtorList;
          this.list[index].lender = this.list[index - 1].lender;
          this.list[index].lenderList = this.list[index - 1].lenderList;
          this.list[index].auxiliary = this.list[index - 1].auxiliary;
          this.calcDebtorTotal();
          this.calcLenderTotal();
        }
        if (main.indexOf('..') != -1) {
          this.list[index].main = this.list[index - 1].main;
        }
      }
      //判断是否显示辅助核算
      this.judgeIsAuxiliary();
    },
    keyboardEvents(type, number) {
      var total = this.list.length * 4;
      if (type == 'enter') {
        // number++;
        return;
      } else if (type == 'left' && number - 1 > 0) {
        number--;
      } else if (type == 'right' && number + 1 <= total) {
        number++;
      } else if (type == 'up' && number - 4 > 0) {
        number = number - 4;
      } else if (type == 'down' && number + 4 <= total) {
        number = number + 4;
      }
      if (type == 'enter' && number > total) {
        this.addList();
      }
      var index = parseInt(number / 4);
      var remaind = number % 4;
      if (remaind == 1) {
        this.showInput(index, 'main');
      } else if (remaind == 2) {
        this.showInput(index, 'subject');
      } else if (remaind == 3) {
        this.showInput(index, 'debtor');
      } else if (remaind == 0) {
        this.showInput(index - 1, 'lender');
      }
    },
    debtorInputKeyUp(index, e, remaind) {
      if (e.keyCode === 187) {
      } else if (e.keyCode === 32) {
      } else if ((e.keyCode >= 37 && e.keyCode <= 40) || e.keyCode == 13) {
        this.hideInput(index,'debtor');
        return;
      }
    },

    lenderInputKeyUp(index, e, remaind) {
      if (e.keyCode === 187) {
      } else if (e.keyCode === 32) {
      } else if ((e.keyCode >= 37 && e.keyCode <= 40) || e.keyCode == 13) {
        this.hideInput(index,'lender');
        return;
      }
    },
    calcDebtorTotal() {
      try{
        if(this.list==null||this.list.length==0) {
          return;
        }
        var debtorTotal = 0;
        for (var i = 0; i< this.list.length;i++) {
          if (this.list[i].debtor != null && this.list[i].debtor != '') {
            debtorTotal += (this.list[i].debtor) * 1;
          }
        }
        // console.log(this.list);
        this.debtorTotal = debtorTotal;
        debtorTotal = debtorTotal + '';
        // console.log('1'+this.debtorTotalList);
        // flag是为了防止异常，当输入小数时，再输入0,this.debtorTotalList处理会异常
        var flag = 0
        var tmpArr = collatingData(debtorTotal, this.debtorTotalList)
        for(var i = 0;i<11;i++){
          if(tmpArr[i] > 0){
            flag = 1
            break;
          }
        }
        if(flag == 0){
          this.debtorTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0]
        } else {
          this.debtorTotalList = tmpArr;
        }
      }catch (e) {
        console.log(e);
      }
    },
    calcLenderTotal() {
      try {
        if(this.list==null||this.list.length==0) {
          return;
        }
        var lenderTotal = 0;
        for (var i=0;i<this.list.length;i++) {
          if (this.list[i].lender != null && this.list[i].lender != '') {
            lenderTotal += (this.list[i].lender) * 1;
          }
        }
        // console.log(lenderTotal);
        this.lenderTotal = lenderTotal;
        lenderTotal = lenderTotal + '';
        // var lenderTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        // flag是为了防止异常，当输入小数时，再输入0,this.lenderTotalList处理会异常
        var flag = 0
        var tmpArr = collatingData(lenderTotal, this.lenderTotalList)
        for(var i = 0;i<11;i++){
          if(tmpArr[i] > 0){
            flag = 1
            break;
          }
        }
        if(flag == 0){
          this.lenderTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0]
        } else {
          this.lenderTotalList = tmpArr;
        }
      }catch (e) {
        console.log(e);
      }
    },
    addList() {
      const tmpMain = ''
      if(this.list.length > 0){
        tmpMain = this.list[this.list.length-1].main
      }
      // console.log(tmpMain)
      var obj = {
        main: tmpMain,
        isShowMainInput: false,
        subject: {
          number: '',
          name: '',
          code: '',
          detailJson: ''
        },
        isShowSubjectInput: false,
        debtor: '',
        debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
        isShowDebtorInput: false,
        lender: '',
        lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
        isShowLenderInput: false,
        isAuxiliary: false,
        auxiliary: ''
      };
      this.list.push(obj);
    },
    removeList(index) {
      if (this.list.length <= 2) {
        this.$message.error('至少保存两行');
        return;
      }
      this.list.splice(index, 1);
      this.calcDebtorTotal();
      this.calcLenderTotal();
    },

    checkListData() {
      if ((this.debtorTotal != 0 || this.lenderTotal != 0) && this.lenderTotal * 1 != this.debtorTotal * 1) {
        this.$message.error('凭证借贷不平衡，请检查');
        return;
      }
      var mainFlag = true;
      var arr = [];
      for (var i in this.list) {
        //多行可以只有一个金额
        if (this.list[i].main != null && this.list[i].main != '') {
          mainFlag = false;
        }
        //如果填写一个金额必须填写了会计科目
        if ((this.list[i].subject.name == null || this.list[i].subject.name.trim() == '')
          && ((this.list[i].debtor != null && this.list[i].debtor != '' && parseFloat(this.list[i].debtor) != 0)
            || (this.list[i].lender != null && this.list[i].lender != '' && parseFloat(this.list[i].lender) != 0)) ) {
          this.$message.error('第' + (i * 1 + 1) + '行中会计科目为空，请填写了再提交');
          return;
        }
        //检查会计科目为必填
        if (this.list[i].subject.name == null || this.list[i].subject.name.trim() == '') {
          // this.$message.error('第' + (i * 1 + 1) + '行中的会计科目为必填项，请填写了再提交');
          // return;
          continue;
        }
        //如果填写了会计科目必须填写一个金额
        if ((this.list[i].debtor == null || this.list[i].debtor == '' || parseFloat(this.list[i].debtor) == 0)
          && (this.list[i].lender == null || this.list[i].lender == '' || parseFloat(this.list[i].lender) == 0)) {
          this.$message.error('第' + (i * 1 + 1) + '行中借方金额、贷方金额必须填一个，请填写了再提交');
          return;
        }

        arr.push(this.list[i]);
      }
      if (mainFlag) {
        this.$message.error('必须填写一个摘要，请填写了再提交');
        return;
      }
      if(arr==null || arr.length < 2 ) {
        this.$message.error('至少要填写两行数据');
        return;
      }
      //2022-10-13需求， 审核人可以留空，但审核时候不能是创建人自己
      // if (this.auditUser == null || this.auditUser.trim().length==0) {
      //   this.$message.error('请选择审核人');
      //   return;
      // }

      var list = [];
      debugger;
      for (var a = 0; a < arr.length; a++) {
        //过滤未填写的行
        // if ((arr[a].subject.name == null || arr[a].subject.name == '')&&((arr[a].debtor == null || arr[a].debtor == '') && (arr[a].lender == null || arr[a].lender == ''))) {
        //   continue;
        // }
        var obj = {
          explanation: arr[a].main,
          accountNumber: arr[a].subject.number,
          accountName: arr[a].subject.name,
          accountCode: arr[a].subject.code,
          auxiliaryType: arr[a].subject.detailJson,
          itemObjectKeys: arr[a].auxiliary,
          basicDebitAmount: arr[a].debtor,
          basicCreditAmount: arr[a].lender
        };
        list.push(obj);
      }
      var attList = [];
      if(this.attachmentList.attachmentFileList!=null && this.attachmentList.attachmentFileList.length>0) {
        for (var j=0;j<this.attachmentList.attachmentFileList.length;j++) {
          var item = this.attachmentList.attachmentFileList[j];
          let extension = item.name.substring(item.name.lastIndexOf('.')+1);
          let fsize = item.size;
          attList.push({name:item.name,url:item.url,type:extension,size:fsize});
        }
      }
      var reqObj = {
        id: this.id,
        billNo: this.billNo,
        billStatus: 2,
        auditUser: this.auditUser,
        voucherType: this.voucher.word,
        voucherNumber: this.voucher.no,
        year: this.formatDate("yyyy-MM-dd", this.voucher.date),
        creditTotal: this.lenderTotal,
        debitTotal: this.debtorTotal,
        attachments: this.voucher.bill,
        list: list,
        attachmentList:attList,
      };


      return reqObj;
    },
    formatDate(fmt, date) {
      if (typeof date === 'string') {
        date = new Date(date);
      }

      var o = {
        "M+": date.getMonth() + 1,                 //月份
        "d+": date.getDate(),                    //日
        "h+": date.getHours(),                   //小时
        "m+": date.getMinutes(),                 //分
        "s+": date.getSeconds(),                 //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds()             //毫秒
      };
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    },
    preservation(status) {
      var reqObj = this.checkListData();
      reqObj.sourceType = this.sourceType;
      reqObj.sourceBillIds = this.sourceBillIds;
      // console.log(reqObj);
      if (reqObj) {
        this.$confirm("是否确定保存？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            addObj(reqObj).then(response => {
              if (response.data.code == '200') {
                this.observable.notifyObservers('test')

                this.$notify({
                  title: '成功',
                  message: '创建成功',
                  type: 'success',
                  duration: 2000
                });
                //清空缓存
                this.$router.push({ query: {} });
                this.init();
                this.$refs.uploadRef.clearFiles();
              } else {
                this.$notify({
                  title: '失败',
                  message: response.data.message,
                  type: 'warning',
                  duration: 2000
                })
              }
            });
          })
      }
    },
    onSubmit() {
      var reqObj = this.checkListData();
      //console.log(reqObj);

      if (reqObj) {
        putObj(this.id, reqObj).then(response => {
          if (response.data.code == '200') {

            this.$notify({
              title: '成功',
              message: '修改成功',
              type: 'success',
              duration: 2000
            });

            this.$emit('submitSuccess', response);

          } else {
            this.$notify({
              title: '失败',
              message: response.data.message,
              type: 'warning',
              duration: 2000
            })
          }
        });
      }
    },
    setVoucherData(data) {
      this.voucher = {word: data.voucherType, no: data.voucherNumber, date: data.year, bill: data.attachments};
      this.list = [];
      var isAuxiliary = false;
      for (var i = 0; i < data.list.length; i++) {
        var debtor = data.list[i].basicDebitAmount == 0 ? '' : data.list[i].basicDebitAmount + '';
        var debtorList = debtor == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(debtor, ['', '', '', '', '', '', '', '', 0, 0, 0]);
        var lender = data.list[i].basicCreditAmount == 0 ? '' : data.list[i].basicCreditAmount + '';
        var lenderList = lender == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(lender, ['', '', '', '', '', '', '', '', 0, 0, 0]);
        if (data.list[i].itemObjectKeys != null && data.list[i].itemObjectKeys != '') {
          isAuxiliary = true;
        }
        var obj = {
          main: data.list[i].explanation,
          isShowMainInput: false,
          subject: {
            number: data.list[i].accountNumber,
            name: data.list[i].accountName,
            code: data.list[i].accountCode,
            detailJson: data.list[i].auxiliaryType
          },
          isShowSubjectInput: false,
          debtor: debtor,
          debtorList: debtorList,
          isShowDebtorInput: false,
          lender: lender,
          lenderList: lenderList,
          isShowLenderInput: false,
          isAuxiliary: data.list[i].itemObjectKeys == '' ? false : true,
          auxiliary: data.list[i].itemObjectKeys
        };
        this.list.push(obj);
      }
      this.debtorTotal = data.debitTotal + '';
      this.debtorTotalList = collatingData(this.debtorTotal, ['', '', '', '', '', '', '', '', 0, 0, 0]);
      this.lenderTotal = data.creditTotal + '';
      this.lenderTotalList = collatingData(this.lenderTotal, ['', '', '', '', '', '', '', '', 0, 0, 0]);
      this.dialogMainVisible = false;
      this.dialogSubjectVisible = false;
      this.dialogAuxiliaryVisible = false;
      this.dialogMainIndex = 0;
      this.dialogSubjectIndex = 0;
      this.dialogAuxiliaryIndex = 0;
      this.isAuxiliary = isAuxiliary;
      this.billNo = data.billNo;
    },
    init() {
      try {
        this.id = this.$route.query.id;
        this.sourceType = this.$route.query.sourceType;
        this.sourceBillIds = this.$route.query.sourceBillIds;
        var listQuery = this.$route.query.listQuery;
        const self = this;
        if (this.id !== '' && this.id !== undefined) {
          getObj(this.id)
            .then(response => {
              const data = response.data;
              self.setVoucherData(data);
            });
        }else if(listQuery !== null && listQuery !== undefined && listQuery.length >0) {
          console.log(listQuery);
          const data = {};
          data.voucherType = '记';
          data.voucherNumber = this.voucher.no;
          data.year = new Date();
          data.attachments=0;
          data.list = listQuery;
          data.debitTotal = 0;
          data.creditTotal = 0;
          for(var i=0;i<listQuery.length;i++) {
            var item = listQuery[i];
            data.debitTotal = FinancialUtils.parseNumber(data.debitTotal) + FinancialUtils.parseNumber(item.debtor);
            data.creditTotal = FinancialUtils.parseNumber(data.creditTotal) +  FinancialUtils.parseNumber(item.lender);
          }
          data.billNo = '';

          this.voucher = {word: data.voucherType, no: data.voucherNumber, date: data.year, bill: data.attachments};
          this.list = listQuery;
          var isAuxiliary = false;
          this.debtorTotal = data.debitTotal + '';
          this.debtorTotalList = collatingData(this.debtorTotal, ['', '', '', '', '', '', '', '', 0, 0, 0]);
          this.lenderTotal = data.creditTotal + '';
          this.lenderTotalList = collatingData(this.lenderTotal, ['', '', '', '', '', '', '', '', 0, 0, 0]);
          this.dialogMainVisible = false;
          this.dialogSubjectVisible = false;
          this.dialogAuxiliaryVisible = false;
          this.dialogMainIndex = 0;
          this.dialogSubjectIndex = 0;
          this.dialogAuxiliaryIndex = 0;
          this.isAuxiliary = isAuxiliary;
          this.billNo = data.billNo;

        } else {
          this.voucher.word = '记';
          // this.voucher.no = 1;
          this.changeVoucherNo();
          this.voucher.date = new Date();
          this.voucher.bill = 0;
          this.list = [
            {
              main: '',
              isShowMainInput: false,
              subject: {
                number: '',
                name: '',
                code: '',
                detailJson: ''
              },
              isShowSubjectInput: false,
              debtor: '',
              debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowDebtorInput: false,
              lender: '',
              lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowLenderInput: false,
              isAuxiliary: false,
              auxiliary: ''
            },
            {
              main: '',
              isShowMainInput: false,
              subject: {
                number: '',
                name: '',
                code: '',
                detailJson: ''
              },
              isShowSubjectInput: false,
              debtor: '',
              debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowDebtorInput: false,
              lender: '',
              lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowLenderInput: false,
              isAuxiliary: false,
              auxiliary: ''
            },
            {
              main: '',
              isShowMainInput: false,
              subject: {
                number: '',
                name: '',
                code: '',
                detailJson: ''
              },
              isShowSubjectInput: false,
              debtor: '',
              debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowDebtorInput: false,
              lender: '',
              lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowLenderInput: false,
              isAuxiliary: false,
              auxiliary: ''
            },
            {
              main: '',
              isShowMainInput: false,
              subject: {
                number: '',
                name: '',
                code: '',
                detailJson: ''
              },
              isShowSubjectInput: false,
              debtor: '',
              debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowDebtorInput: false,
              lender: '',
              lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
              isShowLenderInput: false,
              isAuxiliary: false,
              auxiliary: ''
            }
          ];
          this.debtorTotal = 0;
          this.debtorTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
          this.lenderTotal = 0;
          this.lenderTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
          this.dialogMainVisible = false;
          this.dialogSubjectVisible = false;
          this.dialogAuxiliaryVisible = false;
          this.dialogMainIndex = 0;
          this.dialogSubjectIndex = 0;
          this.dialogAuxiliaryIndex = 0;
          this.isAuxiliary = false;
        }

        this.expireTimeOption = {
          disabledDate(date) {
            //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
            const period_date = new Date(self.voucher.lastPeriod);//this.voucher.lastPeriod
            const startTime = new Date(self.Constant.startTime);
            if(period_date.getTime() > startTime.getTime()) {
              return (
                date.getTime() <= period_date.getTime()- 8.64e7
              );
            }else {
              return (
                date.getTime() <= startTime.getTime()- 8.64e7
              );
            }
          }
        };
      }catch (err) {
        console.log("init",err);
      }finally {

      }
    }
  }
}
</script>

<style scoped>
table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  padding: 0;
  margin: 0;
}

table td {
  text-align: center;
  table-layout: fixed;
  padding: 0px;
  position: relative;
}

.main-subject {
  position: relative;
  height: 60px;
  line-height: 30px;
  text-align: left;
}

.charge-container {
  padding: 10px;
  font-size: 14px;
  color: #444;
  font-weight: 400;
  background-color: white;
}

.charge {
  width: 1124px;
  margin: auto;
}

.charge-header {
  margin-bottom: 10px;
}

.charge-header > div {
  display: inline-block;
  margin-right: 15px;
}

.tip-box {
  width: 330px;
  padding: 10px;
  position: absolute;
  top: 27px;
  right: -15px;
  z-index: 1005;
  background-color: #fff;
  box-shadow: 0 0 6px rgba(170, 170, 170, .73);
  display: none;
}

.tip-box-table tr {
  height: 25px;
}

.el-icon-info {
  font-size: 18px;
  margin-left: 30px;
  cursor: pointer;
}

.el-icon-info:hover + .tip-box {
  display: inline-block;
}

.el-icon-more {
  position: absolute;
  top: 22px;
  right: 10px;
  z-index: 2;
  color: #666;
  cursor: pointer;
  font-size: 16px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0px;
  float: left;
  padding: 5px 0;
  margin: 2px 0 0;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
}

.suggest-list {
  width: 100%;
  height: auto;
  z-index: 1015;
  min-width: inherit;
  display: block;
  overflow: hidden;
  border: none;
  box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
}

.suggest-list .item-list {
  max-height: 375px;
  overflow: auto;
  list-style: none;
  margin: 0px;
  padding: 0px;
}

.item-list li {
  display: flex;
  padding: 0 10px;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.item-list li:hover {
  background: #ecebeb;
}

.item-list li.hover {
  background: #ecebeb;
}

.charge-table, .tip-box-table {
  border: 1px solid #bab9b9;
}

.charge-table, .debtor-tbale, .lender-tbale > tr {
  height: 60px;
}

.charge-table > tr:first-child {
  height: 50px;
}

.td-auxiliary-dis {
  background-color: #f7f7f7;
}

.auxiliary-accounting {
  height: 60px;
  overflow: auto;
  padding: 15px 0 0 30px;
}

.auxiliary-accounting:before {
  content: "+";
  font-size: 30px;
  color: #4a90e2;
  cursor: pointer;
  padding: 0 11px;
  position: absolute;
  top: 0;
  left: 0;
  line-height: 60px;
}

.auxiliary-single {
  display: flex;
  float: left;
  height: 28px;
  line-height: 28px;
  margin-right: 5px;
  cursor: pointer;
  background: #eee;
  padding: 0 8px;
  border-radius: 2px;
}

.auxiliary-single span {
  max-width: 90px;
  overflow: hidden;
  height: 28px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.auxiliary-single i {
  color: red;
  margin: 8px 0 8px 7px;
  font-size: 10px;
  visibility: hidden;
}

.auxiliary-single:hover i {
  visibility: inherit;
}

.charge-table-icon {
  cursor: pointer;
  display: inline-block;
}

.debtor-lender-table tr td {
  width: 9%;
  border-right: 1px solid #bab9b9;
}

.debtor-lender-table tr td:nth-child(3) {
  border-right-color: rgba(74, 144, 226, .5);
}

.debtor-lender-table tr td:nth-child(6) {
  border-right-color: rgba(74, 144, 226, .5);
}

.debtor-lender-table tr td:nth-child(9) {
  border-right-color: rgba(226, 106, 74, .5);
}

.debtor-lender-table tr td:last-child {
  border-right: none;
}

.tr-negative {
  color: red;
}

.charge-table input, select {
  width: 100%;
  height: 60px;
}

.charge-table textarea {
  width: 100%;
  height: 60px;
  padding: 9px 14px 9px 10px;
  overflow: auto;
  resize: none;
  border: none;
  border-radius: 0px;
  margin: 0;
  color: #444;
  box-sizing: border-box;
}
</style>
