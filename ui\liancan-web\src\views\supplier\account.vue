<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
<!--      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-add"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="openAccount(row)">查看
        </el-button>
      </template>-->
    </avue-crud>
    <el-dialog title="中标公告" :visible.sync="accountVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <avue-form ref="accountForm" :option="accountOption" v-model="accountForm">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getSupplierAccountList,getAccountDetail} from "@/api//supplier/supplierAccount";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        accountForm:{},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        accountVisible:false,
        accountOption:{
          height:'auto',
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          addBtn:false,
          delBtn:false,
          selection: true,
          column: [
            {
              label: "企业名称",
              prop: "name",
              type: "input",
              span: 24,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "企业性质",
              prop: "companyNature",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "管理人姓名",
              prop: "contactName",
              type: "input",
              span: 24,
            },
            {
              label: "身份证号",
              prop: "identityId",
              type: "input",
              span: 24,
            },
            {
              label: "手机号(账号)",
              prop: "account",
              type: "input",
              span: 24,
            },
          ]
        },
        option: {
          height:'auto',
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          editBtn:false,
          addBtn:false,
          delBtn:false,
          selection: true,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '省份',
              prop: 'province',
              type: 'select',
              props: {
                label: 'regionName',
                value: 'id'
              },
              cascaderItem: ['city', 'area'],
              search:true,
              hide: true,
              viewDisplay: false,
              dicUrl: `/api/rabbit-system/region/getProvince`,
              rules: [
                {
                  required: true,
                  message: '请选择省份',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '城市',
              prop: 'city',
              type: 'select',
              props: {
                label: 'regionName',
                value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
              rules: [
                {
                  required: true,
                  message: '请选择城市',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '地区',
              prop: 'area',
              type: 'select',
              props: {
                label: 'regionName',
                value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
              rules: [
                {
                  required: true,
                  message: '请选择地区',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: "企业名称",
              prop: "name",
              type: "input",
              span: 24,
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "地址",
              prop: "address",
              type: "input",
              span: 24,
              addDisplay: false,
              editDisplay: false,
              hide: true,
            },
            {
              label: "企业性质",
              prop: "companyNature",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "管理人姓名",
              prop: "contactName",
              type: "input",
              span: 24,
            },
            {
              label: "身份证号",
              prop: "identityId",
              type: "input",
              span: 24,
              hide: true,
            },
            {
              label: "手机号(账号)",
              prop: "account",
              type: "input",
              span: 24,
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
/*          addBtn: this.vaildData(this.permission.supplier_account_add, false),
          viewBtn: this.vaildData(this.permission.supplier_account_view, false),
          delBtn: this.vaildData(this.permission.supplier_account_delete, false),
          editBtn: this.vaildData(this.permission.supplier_account_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getAccountDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getSupplierAccountList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      openAccount(row){
        this.accountForm = row;
        this.accountVisible = true;
      }
    }
  };
</script>

<style>
</style>
