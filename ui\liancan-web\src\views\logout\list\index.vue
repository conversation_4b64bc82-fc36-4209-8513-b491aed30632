<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.logout_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exports">导出</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, exportLogoutData} from "@/api/logout/logoutLogging";
  import {mapGetters} from "vuex";
  const DIC = {
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
    BALANCETYPE:[{
      label: '已退现金,余额清零',
      value: '1'
    },{
      label: '未退现金,余额清零',
      value: '2'
    }],
    REFUNDTYPE:[{
      label: '已退现金,待退款清零',
      value: '1'
    },{
      label: '未退现金,待退款清零',
      value: '2'
    }],
  }
  export default {
    props: {
        schoolId: String,
    },
    data() {
      return {
        form: {},
        query: {},
        searchForm: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          editBtn: false,
          delBtn: false,
          addBtn: false,
          viewBtn: false,
          menu: false,
          selection: false,
          searchSpan:130,
          column: [
            {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              search: true
            },
        /*    {
              label: "性别",
              prop: "sex",
              type: "select",
              dicData: DIC.sex,
              search: true
            },*/
            {
              label: "学号/工号",
              prop: "studentJobNo",
              type: "input",
              search: true
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              search: true,
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
     /*       {
              label: "伙食费钱包号",
              prop: "walletNumber",
              type: "input",
              width:160,
            },*/
            {
              label: "注销操作人",
              prop: "logoutUser",
              type: "input",
            },
            {
              label: "注销时间",
              prop: "createTime",
              type: "input",
              width:150,
            },
            {
              label: "钱包余额处理方式",
              prop: "balanceType",
              type: "select",
              dicData: DIC.BALANCETYPE,
              search: true
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
  /*          {
              label: '注销时间',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "统缴餐钱包余额",
              prop: "money",
              type: "input",
            },
            {
              label: "自选餐钱包余额",
              prop: "money2",
              type: "input",
            },
 /*           {
              label: "注销时统缴餐待退款金额",
              prop: "refundMoney",
              type: "number",
              precision:2,
              width:190,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
            },
            {
              label: "统缴餐待退款注销处理方式",
              prop: "refundType",
              type: "select",
              dicData: DIC.REFUNDTYPE,
              search: true,
              width:190,
            },*/
            {
              label: "注销单号",
              prop: "id",
              type: "input",
              width:170,
            },
          ]
        },
        data: [],
        unitSelect: false
      };
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.logoutLogging_add, false),
          viewBtn: this.vaildData(this.permission.logoutLogging_view, false),
          delBtn: this.vaildData(this.permission.logoutLogging_delete, false),
          editBtn: this.vaildData(this.permission.logoutLogging_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
      if (this.userInfo.userType === 'school'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
        this.option.printBtn = true;
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
      }else if (this.userInfo.userType === 'education'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
        this.option.column[5].search =false;
        this.option.column[1].search =false;
        this.option.column[1].hide = false;
      }else if (this.userInfo.userType === 'jiWei'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
        this.option.column[5].search =false;
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
      }else if (this.userInfo.userType === 'canteen'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
        this.option.printBtn = true;
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
      }
/*      this.option.column[0].search = this.unitSelect;
      this.option.column[4].search = !this.unitSelect;
      this.option.column[0].hide = !this.unitSelect;*/
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm = params;
        this.query = params;
/*        if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.startDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
        }*/
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.unitId = this.schoolId;
        }
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      exports(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出注销明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportLogoutData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '注销明细报表.xls';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      }
    }
  };
</script>

<style>
</style>
