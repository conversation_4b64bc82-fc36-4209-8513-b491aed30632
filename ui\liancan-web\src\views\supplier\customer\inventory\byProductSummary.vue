<template>
    <basic-container>
        <avue-crud
            :defaults.sync="defaults"
            :option="option"
            :table-loading="loading"
            :data="data"
            :page="page"
            v-model="form"
            ref="crud"
            :before-open="beforeOpen"
            @search-change="searchChange"
            @search-reset="searchReset"
            @current-change="currentChange"
            @size-change="sizeChange"
            @on-load="onLoad">
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-download" size="small" @click="printOrderHandler" >导出</el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {mapGetters} from "vuex";
import {getCanteenStockByProductList, exportCanteenStockByProductList} from "@/api/supplier/supplierInventoryPublicSet";
export default {
    props: ['deptId','deptName','canteenId','canteenName'],
    data() {
        return {
            defaults: {},
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            data: [],
            selectionList: [],
            option: {
                height:'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                viewBtn: false,
                menu: false,
                addBtn: false,
                selection: false,
                labelWidth: 150,
                align: 'center',
                column: [
                    {
                        label: "客户名称",
                        prop: "deptName",
                        type: "input",
                    },
                    {
                        label: "客户名称",
                        prop: "deptId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-liancan/supplierInventoryPublicSet/dept/select-all",
                        props: {
                            label: "deptName",
                            value: "id"
                        },
                        span: 24,
                        filterable: true,
                        searchFilterable: true,
                        search: true,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "purchaseId",
                        type: "select",
                        search: true,
                        dicUrl: "/api/service/rabbit-liancan/biddingType/purchaseType",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['purchaseType'],
                    },
                    {
                        label: "二类",
                        prop: "purchaseType",
                        type: "select",
                        search:true,
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-liancan/biddingType/ingredientsType/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        rules: [{
                            required: true,
                            message: "请选择招标大类",
                            trigger: "blur"
                        }],
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['commodityType'],
                    },
                    {
                        label: "商品名称",
                        prop: "commodityType",
                        type: "select",
                        search: true,
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/allDict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        hide: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "goodsName",
                        type: "input",
                        search: true,
                    },
                    {
                        label: "图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                        propsHttp: {
                            res: 'data',
                            url: 'link',
                        },
                        hide: true,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "netWeight",
                        type: "input",
                    },
                    {
                        label: "库存数量",
                        prop: "quantity",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "input",
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "select",
                        dicData: [
                            {
                                label: "正常",
                                value: '1'
                            },
                            {
                                label: "停用",
                                value: '0'
                            }
                        ],
                    },
                ]
            },
        }
    },
    computed: {
        ...mapGetters(["permission"]),
    },
    methods: {
        beforeOpen(done, type) {

            if (["edit", "view"].includes(type)) {
                this.form.deptId = this.deptId;
                this.form.deptName = this.deptName;
                this.form.canteenId  = this.canteenId;
                this.form.canteenName = this.canteenName;
            }
            console.log("11========>"+JSON.stringify(this.form))
            done();
        },
        currentChange(currentPage){
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize){
            this.page.pageSize = pageSize;
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        async onLoad(page, params = {}) {
            this.loading = true;
            if(this.deptId != null && this.deptId != ''){
                // params.deptId = this.deptId;
                // params.deptName = this.deptName;
                params.deptId  = this.canteenId;
                params.deptName = this.canteenName;
            }
            var res = await getCanteenStockByProductList(page.currentPage, page.pageSize, Object.assign(params, this.query));
            console.log("22=======>"+res);
            if (res.data.code === 200) {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            }else{
                this.$message.error('刷新失败')
            }
        },
        reLoad(){
            console.log("55=======>"+this.canteenId+","+this.canteenName);
            this.query = {};
            var params = {};
            params.schoolId = this.canteenId;
            params.deptId  = this.canteenId;
            params.deptName = this.canteenName;
            this.onLoad(this.page,params);
        },
        printOrderHandler(params = {}){
            this.$confirm("是否导出客户库存-按商品查看记录?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                if(this.deptId != null && this.deptId != ''){
                    // params.deptId = this.deptId;
                    // params.deptName = this.deptName;
                    params.deptId  = this.canteenId;
                    params.deptName = this.canteenName;
                }
                exportCanteenStockByProductList(Object.assign(params, this.query)).then(res => {
                    const blob = new Blob([res.data]);
                    const fileName = '客户库存-按商品查看记录.xlsx';
                    const linkNode = document.createElement('a');

                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();

                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
            });
        },
    }
}
</script>

<style scoped>

</style>
