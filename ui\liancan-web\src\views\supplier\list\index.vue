<template>
  <basicContainer>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="companyName" slot-scope="{row}">
        <a href="javascript:void(0)" style="color: #409EFF;" @click="hanbleOpenDateils(row)">{{row.companyName}}</a>
      </template>
    </avue-crud>
    <el-dialog :title="`${this.supplierName}`" :visible.sync="supplierDateilsVisible" :append-to-body="true" @close="supplierDateilsClose" width="60%">
     <!-- <el-row>
        <div>
          <div class="flex">
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              企业名称：{{this.supplierDateilsForm.companyName}}
            </div>
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              地址：{{this.supplierDateilsForm.address}}
            </div>
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.supplierDateilsForm.companyNature == '1'">企业性质：私营企业</span>
              <span v-if="this.supplierDateilsForm.companyNature == '2'">企业性质：国营企业</span>
              <span v-if="this.supplierDateilsForm.companyNature == '3'">企业性质：中外合资企业</span>
              <span v-if="this.supplierDateilsForm.companyNature == '4'">企业性质：外资企业</span>
              <span v-if="this.supplierDateilsForm.companyNature == '5'">企业性质：他</span>
            </div>
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              联系人：{{this.supplierDateilsForm.contactsName}}
            </div>
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              联系人手机：{{this.supplierDateilsForm.mobile}}
            </div>
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              管理员姓名：{{this.supplierDateilsForm.adminName}}
            </div>
            <div style="width: 30%;margin-bottom: 20px;margin-top:20px;float: left;">
              手机号码(账号)：{{this.supplierDateilsForm.adminMobile}}
            </div>
          </div>
        </div>
      </el-row>-->
      <avue-form ref="supplierDateilsForm" :option="supplierDateilsOption" v-model="supplierDateilsForm">
      </avue-form>

      <div>供应商资质:</div>
      <avue-form ref="qualificationForm" :option="qualificationOption" v-model="qualificationForm">
      </avue-form>

      <div>供应商品或服务:</div>
      <avue-crud :option="goodsOption"
                 :data="goodsData"
                 :page="goodsPage"
                 :permission="permissionList"
                 v-model="goodsForm"
                 ref="goodsForm"
                 @search-change="searchChangeGoods"
                 @search-reset="searchResetGoods"
                 @selection-change="selectionChangeGoods"
                 @current-change="currentChangeGoods"
                 @size-change="sizeChangeGoods"
                 @on-load="onLoadGoods">
        <template slot="url" slot-scope="scope">
          <img :src="scope.row.url" min-width="40" height="50" @click="queryImg(scope.row.url)">
        </template>
      </avue-crud>
      <div>中标项目列表:</div>
      <avue-crud :option="biddingOption"
                 :data="biddingData"
                 :page="biddingPage"
                 :permission="permissionList"
                 v-model="biddingForm"
                 ref="biddingForm"
                 @search-change="searchChangeBidding"
                 @search-reset="searchResetBidding"
                 @selection-change="selectionChangeBidding"
                 @current-change="currentChangeBidding"
                 @size-change="sizeChangeBidding"
                 @on-load="onLoad1">
        <template slot="biddingAnnounce" slot-scope="{row}">
          <a href="javascript:void(0)" style="color: #409EFF;" @click="biddingNotice(row.id)">查看</a>
        </template>
        <template slot="biddingStatus" slot-scope="{row}">
          <span v-if="row.biddingStatus == 1">--</span>
          <el-tag v-if="row.biddingStatus == 2" size="medium" type="danger">未中标</el-tag>
          <el-tag v-if="row.biddingStatus == 3" size="medium" type="success">已中标</el-tag>
        </template>
        <template slot="biddingMessage" slot-scope="{row}">
          <span v-if="row.biddingStatus == 1">暂无</span>
          <span v-if="row.biddingStatus == 2">查看</span>
          <a  v-if="row.biddingStatus == 3" href="javascript:void(0)" style="color: #409EFF;" @click="myBiddingNotice(row)">查看</a>
        </template>
      </avue-crud>
    </el-dialog>
    <el-dialog title="招标公告" :visible.sync="biddingNoticVisible" :append-to-body="true" @close="biddingNoticFormClose" width="60%">
      <avue-form ref="biddingNoticForm" :option="biddingNoticOption" v-model="biddingNoticForm">
      </avue-form>
      <avue-crud :option="fileListOption1"
                 :table-loading="fileTableLoadings"
                 :data="fileListData1"
                 v-model="fileListForm1"
                 ref="fileListForm1">
        <template slot="url" slot-scope="{row}">
          <a :href="`${row.url}`" style="color: #409EFF;" target="_blank">{{row.name}}</a>
        </template>
      </avue-crud>
      <avue-crud ref="biddingForm" v-model="biddingForm" :option="shopInformationOption" :data="shopInformationsData" @on-load="loadMenus" :page="shopInformationPage"
                 :table-loading="menuTableLoadings">
      </avue-crud>
    </el-dialog>
    <el-dialog title="中标公告" :visible.sync="myBiddingNoticVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <avue-form ref="myBiddingNoticForm" :option="myBiddingNoticOption" v-model="myBiddingNoticForm">
      </avue-form>
      <avue-crud :option="fileListOption1"
                 :table-loading="fileTableLoadings"
                 :data="fileListData2"
                 v-model="fileListForm2"
                 ref="fileListForm2">
        <template slot="url" slot-scope="{row}">
          <a :href="`${row.url}`" style="color: #409EFF;" target="_blank">{{row.name}}</a>
        </template>
      </avue-crud>
      <avue-crud :option="myBiddingNoticListOption"
                 :table-loading="myBiddingNoticListTableLoading"
                 :data="myBiddingNoticListData"
                 :page="myBiddingNoticListPage"
                 :before-open="beforeOpen"
                 v-model="myBiddingNoticListForm"
                 ref="myBiddingNoticListForm"
                 @search-change="mySearchChange"
                 @search-reset="mySearchReset"
                 @selection-change="mySelectionChange"
                 @current-change="myCurrentChange"
                 @size-change="mySizeChange"
                 @on-load="myBiddingNoticListLoad">
        <template slot="menu" slot-scope="scope">
          <el-button size="mini" type="text" @click="details(scope.row)">查看
          </el-button>
        </template>
      </avue-crud>
      <div>
        <span>评标会现场图片:</span>
        <span v-for="(item,index) in imageUrl">
        <img :src="item" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item)" class="avatar">
        </span>
      </div>
      <!--   <avue-form ref="biddingNoticForm22" :option="biddingNoticOption22" v-model="biddingNoticForm22">
         </avue-form>-->
      <!--      <avue-crud ref="myBiddingNoticListForm" v-model="myBiddingNoticListForm" :option="myBiddingNoticListOption" :data="myBiddingNoticListData" :page="myBiddingNoticListPage"
                       :table-loading="myBiddingNoticListTableLoadings">
            </avue-crud>-->
    </el-dialog>
    <el-dialog title="采购详情" :visible.sync="detailsVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <avue-form ref="detailsForm" :option="detailsOption" v-model="detailsForm">
      </avue-form>
      <avue-crud :option="fileListOption"
                 :table-loading="fileTableLoadings"
                 :data="fileListData"
                 v-model="fileListForm"
                 ref="fileListForm">
        <template slot="url" slot-scope="{row}">
          <a :href="`${row.url}`" style="color: #409EFF;" target="_blank">{{row.name}}</a>
        </template>
      </avue-crud>
      <!--      <avue-crud ref="myBiddingNoticListForm" v-model="myBiddingNoticListForm" :option="myBiddingNoticListOption" :data="myBiddingNoticListData" :page="myBiddingNoticListPage"
                       :table-loading="myBiddingNoticListTableLoadings">
            </avue-crud>-->
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="40%" height="30%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basicContainer>
</template>
<script>
  import {
    getSupplierGoodsList,
    getBiddingSupplierList,
    getGoodsList
  } from "@/api/backendBidding/bidding";
  import {getList,
    getAllBiddingList,
    getMyBiddingList,
    getDetail,
    add,
    update,
    remove,
    getShopInformation,
    getSuccessfulBidding,
    getQualifications,
    getQualificationsById,
    saveOrUpdateSupperlier,
    biddingById,
    shortList,
    detailedList,
    getFileById,
    downloadFile} from "@/api/backendBidding/bidding";
  import {myBiddingNoticOption,myBiddingNoticListOption,detailsOption,biddingOption,shopInformationOption1} from "@/const/bidding/index";
  import {mapGetters} from "vuex";
  export default {
    props: {
        schoolId: String,
    },
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        myBiddingNoticListTableLoading:true,
        menuTableLoading:true,
        goodsTableLoading:true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        biddingPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        myBiddingNoticListPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        goodsPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        supplierId:undefined,
        selectionList: [],
        biddingData:[],
        imageUrl:[],
        fileListData:[],
        myBiddingNoticListData:[],
        shopInformationsData:[],
        goodsData:[],
        orderFuseForm:{},
        supplierDateilsForm:{},
        qualificationForm:{},
        myBiddingNoticForm:{},
        myBiddingNoticListForm:{},
        detailsForm:{},
        biddingForm:{},
        fileListForm:{},
        biddingNoticForm:{},
        fileListForm1:{},
        fileListForm2:{},
        goodsForm:{},
        orderId:undefined,
        myBiddingId:undefined,
        fuseShow:false,
        orderShow:false,
        orderFuseShow:false,
        supplierDateilsVisible:false,
        myBiddingNoticVisible:false,
        biddingNoticVisible:false,
        dialogVisible:false,
        supplierName:undefined,
        dialogImageUrl:undefined,
        biddingTableLoading:true,
        detailsVisible:false,
        myBiddingNoticOption:myBiddingNoticOption,
        myBiddingNoticListOption:myBiddingNoticListOption,
        detailsOption:detailsOption,
        biddingNoticOption:biddingOption,
        shopInformationOption:shopInformationOption1,
        goodsOption:{
          menu:false,
          /*          height:'auto',*/
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          header: false,
          index: true,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: true,
          column: [
            {
              label: "招标类型",
              prop: "purchaseType",
              type: "select",
              addDisplay: false,
              search: true,
              dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict",
              props: {
                label: "name",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择招标类型",
                trigger: "blur"
              }],
              cascaderItem: ['commodityType'],
              cascaderChange: true
            },
            {
              label: "招标大类",
              prop: "commodityType",
              type: "input",
            },
            {
              label: "商品",
              prop: "shopName",
              type: "input",
            },
            {
              label: '图片',
              prop: 'url',
              type: 'upload',
              dataType: 'String',
              listType: 'picture-img',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              tip: '只能上传jpg/png文件，且大小不超过 500KB',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              rules: [{
                required: false,
                message: '请上传图片',
                trigger: 'click'
              }],
              slot: true,
              //formslot: true,
              span: 24,
            },
            /*            {
                          label: "图片",
                          prop: "url",
                          type: "input",
                        },*/
          ]
        },
        fileListOption: {
          menu:false,
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: true,
          menuBtn: false,
          column: [
            {
              label: "合同",
              prop: "url",
              type: 'url',
              slot:true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
          ]
        },
        fileListOption1: {
          menu:false,
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: true,
          menuBtn: false,
          column: [
            {
              label: "附件",
              prop: "url",
              type: 'url',
              slot:true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
          ]
        },
        biddingOption: {
          menu:false,
          /*    height:'auto',*/
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          header: false,
          selection: true,
          column: [
            {
              label: "招标类型",
              prop: "purchaseType",
              type: "select",
              addDisplay: false,
              search: true,
              dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict",
              props: {
                label: "name",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择招标类型",
                trigger: "blur"
              }],
            },
            {
              label: "项目名称",
              prop: "entryName",
              type: "input",
              align: 'center',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "采购预算",
              prop: "money",
              type: "number",
              align: 'center',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "采购单位",
              prop: "biddingUnit",
              type: "input",
              align: 'center',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "中标时间",
              prop: "biddingTime",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              span: 24,
            },
            {
              label: '招标公告',
              span: 24,
              prop: 'biddingAnnounce',
              slot: true,
              align: 'center',
              // hide: true,
              addDisplay: false,
              editDisplay: false,
              minWidth: 60,
            },
            {
              label: "中标公告",
              prop: "biddingMessage",
              type: "input",
              slot: true,
              align: 'center',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "中标情况",
              prop: "biddingStatus",
              type: "input",
              slot: true,
              align: 'center',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
          ]
        },
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menuWidth: 130,
          labelWidth: 100,
          menu:false,
          column: [
            {
              label: "企业名称",
              prop: "companyName",
              type: "input",
              search:true,
              slot:true,
            },
            {
              label: "企业性质",
              prop: "companyNature",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
              props: {
                label: "dictValue",
                value: "dictKey"
              },

            },
            {
              label: "联系人",
              prop: "contactsName",
              type: "input",
            },
            {
              label: "联系人手机",
              prop: "mobile",
              type: "input",
            },
            {
              label: "地址",
              prop: "address",
              type: "input",
            },
            {
              label: "中标供应商品或服务",
              prop: "goodName",
              type: "input",
            },
          ]
        },
        supplierDateilsOption: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menuWidth: 130,
          labelWidth: 100,
          menu:false,
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: "企业名称",
              prop: "companyName",
              type: "input",
              search:true,
              disabled:true,
              display:false,
            },
            {
              label: "地址",
              prop: "address",
              type: "input",
              disabled:true,
            },
            {
              label: "企业性质",
              prop: "companyNature",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              disabled:true,

            },
            {
              label: "联系人",
              prop: "contactsName",
              type: "input",
              disabled:true,
            },
            {
              label: "联系人手机",
              prop: "mobile",
              type: "input",
              disabled:true,
            },
            {
              label: "管理员姓名",
              prop: "adminName",
              type: "input",
              disabled:true,
            },
            {
              label: "手机号码(账号)",
              prop: "adminMobile",
              type: "input",
              disabled:true,
            },
            /*          {
                        label: "中标供应商品或服务",
                        prop: "goodName",
                        type: "input",
                        disabled:true,
                      },*/
          ]
        },
        qualificationOption :{
          height:'auto',
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          labelWidth: 150,
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              span: 24,
              display:false
            },
            {
              label: "法人代表",
              prop: "legalRepresentative",
              type: "input",
              span: 24,
              disabled: true,
            },
            {
              label: "法人身份证号",
              prop: "corporateIdentity",
              type: "input",
              span: 24,
              disabled: true,
            },
            {
              label: "企业办公电话",
              prop: "officePhone",
              type: "input",
              disabled: true,
            },
            {
              label: "企业网站",
              prop: "enterpriseWebsite",
              type: "input",
              disabled: true,
            },
            {
              label: "企业联系人",
              prop: "contactsName",
              type: "input",
              disabled: true,
            },
            {
              label: "企业联系人电话",
              prop: "mobile",
              type: "input",
              disabled: true,
            },
            {
              label: "营业执照",
              prop: "businessUrl",
              type: 'upload',
              listType: 'picture-img',
              span: 8,
              hide: true,
            },
            {
              label: "食品经营许可证",
              prop: "foodBusinessUrl",
              type: 'upload',
              listType: 'picture-img',
              span: 8,
              hide: true,
            },
            {
              label: "食品生产许可证",
              prop: "foodProductionUrl",
              type: 'upload',
              listType: 'picture-img',
              span: 8,
              hide: true,
            },
            {
              label: "食品流通许可证",
              prop: "foodCirculationUrl",
              type: 'upload',
              listType: 'picture-img',
              span: 8,
              hide: true,
            },
            {
              label: "其他证件",
              prop: "otherDocumentsUrl",
              type: 'upload',
              listType: 'picture-img',
              span: 8,
              hide: true,
            },
          ]
        },
        data: [],
        isShow: false,
        orderForm: {},
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          /*         addBtn: this.vaildData(this.permission.order_add, false),
                   viewBtn: this.vaildData(this.permission.order_view, false),
                   delBtn: this.vaildData(this.permission.order_delete, false),
                   editBtn: this.vaildData(this.permission.order_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchResetBidding() {
        this.query = {};
        this.onLoad1(this.biddingPage);
      },
      /*      searchReset1() {
              this.query = {};
              this.onLoad1(this.biddingPage);
            },*/
      mySearchReset() {
        this.query = {};
        this.myBiddingNoticListLoad(this.myBiddingNoticListPage);
      },
      searchResetGoods() {
        this.query = {};
        this.onLoadGoods(this.goodsPage);
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      searchChangeBidding(params, done) {
        this.query = params;
        this.biddingPage.currentPage = 1
        this.onLoad1(this.biddingPage, params);
        done();
      },
      /*      searchChange1(params, done) {
              this.query = params;
              this.biddingPage.currentPage = 1
              this.onLoad1(this.biddingPage, params);
              done();
            },*/
      mySearchChange(params, done) {
        this.query = params;
        this.myBiddingNoticListPage.currentPage = 1
        this.myBiddingNoticListLoad(this.page, params);
        done();
      },
      mySearchChange(params, done) {
        this.query = params;
        this.myBiddingNoticListPage.currentPage = 1
        this.myBiddingNoticListLoad(this.myBiddingNoticListPage, params);
        done();
      },
      searchChangeGoods(params, done) {
        this.query = params;
        this.goodsPage.currentPage = 1
        this.onLoadGoods(this.goodsPage, params);
        done();
      },
      mySelectionChange(list) {
        this.selectionList = list;
      },
      mySelectionClear() {
        this.selectionList = [];
        this.$refs.myBiddingNoticListForm.toggleSelection();
      },
      myCurrentChange(currentPage){
        this.myBiddingNoticListPage.currentPage = currentPage;
      },
      mySizeChange(pageSize){
        this.myBiddingNoticListPage.pageSize = pageSize;
      },
      /*      sizeChangeGoods(pageSize){
              this.myBiddingNoticListPage.pageSize = pageSize;
            },*/
      sizeChangeBidding(pageSize){
        this.biddingPage.pageSize = pageSize;
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChange1(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      selectionClearGoods11() {
        this.selectionList = [];
        this.$refs.goodsForm.toggleSelection();
      },
      selectionChangeGoods(list) {
        this.selectionList = list;
      },
      selectionChangeBidding(list){
        this.selectionList = list;
      },
      selectionClear1() {
        this.selectionList = [];
        this.$refs.biddingForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeBidding(currentPage){
        this.biddingPage.currentPage = currentPage;
      },
      /*      currentChange1(currentPage){
              this.biddingPage.currentPage = currentPage;
            },*/
      currentChangeGoods(currentPage){
        this.goodsPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeBidding(pageSize){
        this.biddingPage.pageSize = pageSize;
      },
      /*      sizeChange1(pageSize){
              this.biddingPage.pageSize = pageSize;
            },*/
      sizeChangeGoods(pageSize){
        this.goodsPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.companyId = this.schoolId;
        }
        getSupplierGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoad1(page, params = {}) {
        /*     this.biddingTableLoading = true;*/
        /*        this.query.successfulSupplier = this.supplierId*/
        getBiddingSupplierList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.supplierId).then(res => {
          const data = res.data.data;
          this.biddingPage.total = data.total;
          this.biddingData = data.records;
          /*          this.biddingTableLoading = false;*/
          this.selectionClear1();
        });
      },
      myBiddingNoticListLoad(page, params = {}) {
        this.myBiddingNoticListTableLoading = true;
        /*   this.query =[]*/
   /*     this.query.id = this.myBiddingId;*/
        detailedList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.myBiddingId).then(res => {
          const data = res.data.data;
          this.myBiddingNoticListPage.total = data.total;
          this.myBiddingNoticListData = data.records;
          this.myBiddingNoticListTableLoading = false;
          this.selectionClearGoods();
        });
      },
      onLoadGoods(page, params = {}) {
        /*        this.goodsTableLoading = true;*/
        /*        this.query.successfulSupplier = this.supplierId*/
        getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.supplierId).then(res => {
          const data = res.data.data;
          this.goodsPage.total = data.total;
          this.goodsData = data.records;
          /*          this.goodsTableLoading = false;*/
          this.selectionClearGoods11();
        });
      },
      onloadForQualification(){
        getQualificationsById(this.supplierId).then(res => {
          this.qualificationForm = res.data.data
        });
      },
      hanbleOpenDateils(row){
        this.supplierId = row.companyId;
        this.supplierName = row.companyName;
        this.supplierDateilsForm = row;
        this.onLoad1(this.biddingPage)
        this.onLoadGoods(this.goodsPage)
        //查询供应商资质
        this.onloadForQualification()
        this.supplierDateilsVisible = true;
      },
      myBiddingNotice(row){
        this.myBiddingId = row.id;
        getDetail(row.id).then(res => {
          this.myBiddingNoticForm = res.data.data;
        });
        this.myBiddingNoticListLoad(this.myBiddingNoticListPage)
        this.imageUrl =  row.fileUrlList
        this.myBiddingNoticForm.fileUrlList = row.fileUrlList;
        getFileById(row.id).then(res => {
          this.fileListData2 = res.data.data
        });
        this.myBiddingNoticVisible  = true
      },
      queryImg(images){
        this.dialogImageUrl = images;
        this.dialogVisible = true;
      },
      details(row){
        console.log(">>>>>>>>>>>>>>>>>>",JSON.stringify(row))
        this.detailsForm = row;
        getFileById(row.id).then(res => {
          this.fileListData = res.data.data
        });
        this.detailsVisible = true;
      },
      biddingNotice(id){
        console.info("=============="+id);
        getDetail(id).then(res => {
          this.biddingNoticForm = res.data.data;
        });
        getShopInformation(id).then(res => {
          this.shopInformationsData = res.data.data.records;
          this.shopInformationPage.total = res.data.data.total;
        });
        getFileById(id).then(res => {
          this.fileListData1 = res.data.data
        });
        this.biddingNoticVisible = true;
      },
      supplierDateilsClose(){
        this.biddingPage.currentPage = 1;
        this.goodsPage.currentPage = 1;
        this.biddingData = [];
        this.goodsData = [];
        this.supplierDateilsVisible = false;
      }
    }
  };
</script>

<style>
</style>
