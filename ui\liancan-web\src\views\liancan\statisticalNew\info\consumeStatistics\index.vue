<template>
  <basic-container>
    <div class="all-mess">
      <div class="mess-header">
        <div
          :class="{acitve:activeIdx==index}"
          v-for="(item,index) in messList"
          :key="index"
          @click="menuClick(index)"
        >
          {{item}}
        </div>
      </div>
      <div class="mess-content" v-if="activeIdx == 0">
        <avue-crud :option="monthOption"
                  :data="monthData"
                  :page="monthPage"
                  :table-loading="monthLoading"
                  :before-open="beforeOpen"
                  v-model="monthForm"
                  ref="monthForm"
                  @search-change="searchChangeMonth"
                  @search-reset="searchResetMonth"
                  @selection-change="selectionChangeMonth"
                  @current-change="currentChangeMonth"
                  @size-change="sizeChangeMonth"
                  @on-load="onLoadMonth">
          <template slot="menuLeft">
    <!--        <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>-->
            <a style="color: red" type="danger"
              size="small"
              icon="el-icon-delete"
              plain
            >备注：当天的消费统计数据需到凌晨后统计。
            </a>
          </template>
          <!-- <template slot-scope="{row}" slot="menu">
            <el-button type="text"
                      icon="el-icon-view"
                      size="small"
                      plain
                      @click.stop="openDetails2(row)">查看
            </el-button>
          </template> -->
          <el-date-picker
            v-model="month"
            type="month"
            placeholder="选择月">
          </el-date-picker>
        </avue-crud>
      </div>

      <div class="mess-content" v-if="activeIdx == 1">
        <avue-crud :option="yearOption"
                  :data="yearData"
                  :page="yearPage"
                  :table-loading="yearLoading"
                  :before-open="beforeOpen"
                  v-model="yearForm"
                  ref="yearForm"
                  @search-change="searchChangeMonth"
                  @search-reset="searchResetMonth"
                  @selection-change="selectionChangeMonth"
                  @current-change="currentChangeMonth"
                  @size-change="sizeChangeMonth"
                  @on-load="onLoadYear">
          <template slot="menuLeft">
    <!--        <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>-->
            <a style="color: red" type="danger"
              size="small"
              icon="el-icon-delete"
              plain
            >备注：当天的消费统计数据需到凌晨后统计。
            </a>
          </template>
          <!-- <template slot-scope="{row}" slot="menu">
            <el-button type="text"
                      icon="el-icon-view"
                      size="small"
                      plain
                      @click.stop="openDetails2(row)">查看
            </el-button>
          </template> -->
          <el-date-picker
            v-model="month"
            type="month"
            placeholder="选择月">
          </el-date-picker>
        </avue-crud>
      </div>
    </div>

    <el-dialog :title="this.title" :visible.sync="dateDetailsVisible" width="70%" left :append-to-body="true" @close="tongjiaocan">
      <el-row style="padding-top: 30px;">
        <div v-if="isShow1" style="margin-bottom: 30px;">{{context}}：{{this.diningDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 营业网点：{{this.businessOutletsName}}</div>
        <div v-if="isShow2" style="margin-bottom: 30px;">起止日期：{{this.diningDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 营业网点：{{this.businessOutletsName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;窗口组：{{this.windowGroupName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;窗口：{{this.windowName}}</div>
        <el-table
          :data="dateConsumData"
          border
          style="width: 100%;">
          <el-table-column
            prop="status"
            label="名称"
            align="center"
            width="180" style="background: #fafafa;">
          </el-table-column>
          <el-table-column
            prop="mealIncomeBalance1"
            label="统缴餐"
            align="center"
            width="180">
          </el-table-column>
          <el-table-column
            prop="freeConsumBalance1"
            align="center"
            label="自选餐(自由消费)">
          </el-table-column>
          <el-table-column
            prop="orderDishesBalance1"
            align="center"
            label="自选餐(预订菜品)">
          </el-table-column>
          <el-table-column
            prop="orderMealsBalance1"
            align="center"
            label="自选餐(预订餐次)">
          </el-table-column>
          <el-table-column
            prop="totalBalance"
            align="center"
            label="合计">
          </el-table-column>
        </el-table>
      </el-row>
    </el-dialog>

  </basic-container>
</template>

<script>
import {getDetail, getMonthList, getConsumDetailsByCanteen, getYearList} from "@/api/queryStatistics/businessIncomeStatistics";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      activeIdx: 0,
      messList: [ '按月份统计', '按年份统计' ],
      monthLoading: true,
      yearLoading: true,
      query: {},
      form: {},
      monthForm:{},
      monthPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      yearForm:{},
      yearPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      monthOption:{
        /*          height:'auto',*/
        /*    calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        menu: false,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'mealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'freeConsumBalance1',
            type: 'sum'
          },
          {
            name: 'orderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'orderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'noMealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          /*       {
                   label: "单位",
                   prop: "unitId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search:true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },
                 {
                   label: "学校",
                   prop: "schoolId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search:true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },*/
          {
            label: '月份',
            prop: 'monthDate',
            type:'month',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM',
            valueFormat: 'yyyy-MM',
            hide:true,
          },
          {
            label: "月份",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "所属单位",
            prop: "schoolId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: false,
            sortable:true,
          },
          {
            label: "食堂名称",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/dict",
            props: {
              label: "deptName",
              value:"id"
            },
            search: false,
            sortable:true,
          },
          // {
          //   label: "营业网点",
          //   prop: "businessOutletName",
          //   type: "input",
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          //   sortable:true,
          // },
          // {
          //   label: "营业网点",
          //   prop: "businessOutletId",
          //   type: "select",
          //   span: 24,
          //   rules: [{
          //     required: true,
          //     message: "请输入营业网点",
          //     trigger: "blur"
          //   }],
          //   search:true,
          //   hide:true,
          //   dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select?canteenIds=" + this.schoolId,
          //   props: {
          //     label: "name",
          //     value: "id"
          //   },
          //   cascaderItem: ['windowGroupId'],
          //   cascaderChange: true
          // },
          // {
          //   label: "窗口组",
          //   prop: "windowGroupId",
          //   type: "select",
          //   span: 24,
          //   hide:true,
          //   rules: [{
          //     required: true,
          //     message: "请输入窗口组",
          //     trigger: "blur"
          //   }],
          //   dicFlag: false,
          //   dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
          //   props: {
          //     label: "name",
          //     value: "id"
          //   },
          //   cascaderItem: ['windowId'],
          //   cascaderChange: true
          // },
          // {
          //   label: "窗口号",
          //   prop: "windowId",
          //   type: "select",
          //   span: 24,
          //   hide:true,
          //   rules: [{
          //     required: true,
          //     message: "请输入窗口号",
          //     trigger: "blur"
          //   }],
          //   dicFlag: false,
          //   multiple:true,
          //   dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
          //   props: {
          //     label: "number",
          //     value: "id"
          //   },
          // },
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "mealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(自由消费)",
                prop: "freeConsumBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订菜品)",
                prop: "orderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "orderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: '未取餐(含自动扣费/退款)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "noMealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(预订菜品)",
                prop: "noOrderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "noOrderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      yearOption:{
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        menu: false,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'mealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'freeConsumBalance1',
            type: 'sum'
          },
          {
            name: 'orderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'orderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'noMealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          /*       {
                   label: "单位",
                   prop: "unitId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search:true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },
                 {
                   label: "学校",
                   prop: "schoolId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search:true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },*/
          {
            label: '年份',
            prop: 'monthDate',
            type:'month',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM',
            valueFormat: 'yyyy-MM',
            hide:true,
          },
          {
            label: "年份",
            prop: "createTime",
            type: "datetime",
            format: "yyyy",
            valueFormat: "yyyy",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "所属单位",
            prop: "schoolId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: false,
            sortable:true,
          },
          {
            label: "食堂名称",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/dict",
            props: {
              label: "deptName",
              value:"id"
            },
            search: false,
            sortable:true,
          },
          // {
          //   label: "营业网点",
          //   prop: "businessOutletName",
          //   type: "input",
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          //   sortable:true,
          // },
          // {
          //   label: "营业网点",
          //   prop: "businessOutletId",
          //   type: "select",
          //   span: 24,
          //   rules: [{
          //     required: true,
          //     message: "请输入营业网点",
          //     trigger: "blur"
          //   }],
          //   search:true,
          //   hide:true,
          //   dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select?canteenIds=" + this.schoolId,
          //   props: {
          //     label: "name",
          //     value: "id"
          //   },
          //   cascaderItem: ['windowGroupId'],
          //   cascaderChange: true
          // },
          // {
          //   label: "窗口组",
          //   prop: "windowGroupId",
          //   type: "select",
          //   span: 24,
          //   hide:true,
          //   rules: [{
          //     required: true,
          //     message: "请输入窗口组",
          //     trigger: "blur"
          //   }],
          //   dicFlag: false,
          //   dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
          //   props: {
          //     label: "name",
          //     value: "id"
          //   },
          //   cascaderItem: ['windowId'],
          //   cascaderChange: true
          // },
          // {
          //   label: "窗口号",
          //   prop: "windowId",
          //   type: "select",
          //   span: 24,
          //   hide:true,
          //   rules: [{
          //     required: true,
          //     message: "请输入窗口号",
          //     trigger: "blur"
          //   }],
          //   dicFlag: false,
          //   multiple:true,
          //   dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
          //   props: {
          //     label: "number",
          //     value: "id"
          //   },
          // },
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "mealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(自由消费)",
                prop: "freeConsumBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订菜品)",
                prop: "orderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "orderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: '未取餐(含自动扣费/退款)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "noMealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(预订菜品)",
                prop: "noOrderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "noOrderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      monthData:[],
      yearData:[],
      searchForm1:{},
      selectionList: [],
      isShow1: true,
      isShow2: true,
      title: '',
      context: '',
      diningDate: '',
      businessOutletsName: '',
      windowGroupName: '',
      windowName: '',
      dateConsumData:[],
      dateDetailsVisible: false,
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    menuClick(idx) {
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.monthPage.currentPage = 1;
        this.onLoadMonth(this.monthPage);
      }
      if (idx == 1){
        this.yearPage.currentPage = 1;
        this.onLoadYear(this.yearPage);
      }
    },
    searchChangeMonth(params, done) {
      this.searchForm1 = params;
      this.query = params;
      this.monthPage.currentPage = 1
      this.onLoadMonth(this.monthPage, params);
      done();
    },
    searchResetMonth() {
      this.query = {};
      this.searchForm1 = {};
      this.onLoadMonth(this.monthPage);
    },
    selectionChangeMonth(list) {
      this.selectionList = list;
    },
    currentChangeMonth(currentPage){
      this.monthPage.currentPage = currentPage;
    },
    sizeChangeMonth(pageSize){
      this.monthPage.pageSize = pageSize;
    },
    onLoadMonth(page, params = {}) {
      this.monthLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getMonthList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.monthPage.total = data.total;
        this.monthData = data.records;
        this.monthLoading = false;
        // this.selectionClearMonth();
      });
    },
    onLoadYear(page, params = {}) {
      this.monthLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getYearList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.yearPage.total = data.total;
        this.yearData = data.records;
        this.yearLoading = false;
        // this.selectionClearMonth();
      });
    },
    openDetails2(row){
      this.isShow2 = false;
      this.title = "按月份统计";
      this.context = "月份";
      this.diningDate = row.createTime;
      this.businessOutletsName = row.businessOutletName;
      getConsumDetailsByCanteen(row.businessOutletId,row.canteenId,row.createTime,"2",null,null,null).then(res => {
        this.dateConsumData = res.data.data;
      });
      this.dateDetailsVisible = true;
    },
  }
}
</script>

<style lang="scss" scoped>
.all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      /deep/ div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20rpx;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
