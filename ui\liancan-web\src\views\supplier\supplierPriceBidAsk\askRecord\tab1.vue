<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @refresh-change="searchReset"
                   @on-load="onLoad">
            <template slot="menu" slot-scope="scope">
                <el-button type="text" size="mini" icon="el-icon-view" @click="viewHandle(scope.row)">查看报价
                </el-button>
            </template>
        </avue-crud>
        <!-- 查看报价 开始 -->
        <el-dialog title="查看报价"
                   :visible.sync="isShowView"
                   v-if="isShowView"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeViewForm"
                   width="70%">
            <avue-crud :option="option2"
                       :table-loading="loading2"
                       :data="data2"
                       :page="page2"
                       v-model="form2"
                       ref="crud2"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @refresh-change="searchReset2">
            </avue-crud>
        </el-dialog>
        <!-- 查看报价 结束 -->
    </basic-container>
</template>

<script>
import { getList, getAllBidList } from "@/api/supplier/supplierPriceAsk";
export default {
    data() {
        return {
            data: [],
            loading: false,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            form: {},
            query: {},
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 200,
                tip: false,
                border: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                align: "center",
                column: [
                    {
                        label: "标题",
                        prop: "title",
                        type: 'input'
                    },
                    {
                        label: "询价商品",
                        prop: "goodsTotal",
                        type: 'input'
                    },
                    {
                        label: "截止日期",
                        prop: "deadLine",
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                    },
                    {
                        label: "询价状态",
                        prop: "status",
                        type: 'select',
                        dicData: [
                            { label: '进行中', value: '0' },
                            { label: '已结束', value: '1' }
                        ],
                        search: true
                    },
                    {
                        label: "报价供货商（家）",
                        prop: "bidCount",
                    },
                    {
                        label: "编辑人",
                        prop: "updateUserName",
                        search: true
                    },
                    {
                        label: "编辑时间",
                        prop: "updateTime",
                        type: "input",
                    }
                ]
            },
            // 查看报价
            selectAskId: '',
            isShowView: false,
            data2: [],
            loading2: false,
            page2: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            form2: {},
            query2: {},
            option2: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 200,
                tip: false,
                border: true,
                menu: false,
                addBtn: false,
                align: "center",
                column: [
                    {
                        label: "商品子项名称",
                        prop: "goodsName",
                        type: 'input',
                        search: true,
                        searchLabelWidth: 120,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: 'input'
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: 'input'
                    },
                    {
                        label: "所需数量",
                        prop: "needNum",
                        type: 'input'
                    },
                    {
                        label: "报价",
                        prop: "bidPrice",
                        type: 'input'
                    },
                    {
                        label: "报价有效截止日期",
                        prop: "deadLine",
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                    },
                    {
                        label: "报价状况",
                        prop: "status",
                        type: 'select',
                        dicData: [
                            { label: '进行中', value: '0' },
                            { label: '已结束', value: '1' }
                        ],
                    },
                    {
                        label: "报价单位名称",
                        prop: "deptId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/supplierCompany/all/list",
                        props: {
                            label: "name",
                            value:"id"
                        },
                    },
                    {
                        label: "联系电话",
                        prop: "phone",
                        type: 'input'
                    },
                    {
                        label: "报价人",
                        prop: "bidUserName",
                        type: 'input'
                    },
                ]
            },

        };
    },
    methods: {
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done()
        },
        searchReset() {
            this.query = {}
            this.page.currentPage = 1;
            this.onLoad(this.page, this.query);
        },
        currentChange(current) {
            this.page.currentPage = current;
            this.onLoad(this.page, this.query);
        },
        sizeChange(size) {
            this.page.pageSize = size;
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            })
        },
        // 查看报价
        viewHandle(row) {
            this.selectAskId = row.id;
            this.isShowView = true;
            this.onLoad2(this.page2, this.query2);
        },
        closeViewForm() {
            this.isShowView = false;
        },
        searchChange2(params, done) {
            this.query2 = params;
            this.page2.currentPage = 1;
            this.onLoad2(this.page2, params);
            done()
        },
        searchReset2() {
            this.query2 = {}
            this.page2.currentPage = 1;
            this.onLoad2(this.page2, this.query2);
        },
        currentChange2(current) {
            this.page2.currentPage = current;
            this.onLoad2(this.page2, this.query2);
        },
        sizeChange2(size) {
            this.page2.pageSize = size;
            this.onLoad2(this.page2, this.query2);
        },
        onLoad2(page, params = {}) {
            this.loading2 = true;
            params.askId = this.selectAskId;
            getAllBidList(page.currentPage, page.pageSize, Object.assign(params, this.query2)).then(res => {
                const data = res.data.data;
                this.page2.total = data.total;
                this.data2 = data.records;
                this.loading2 = false;
            })
        },
    }
};
</script>

<style scoped>

</style>
