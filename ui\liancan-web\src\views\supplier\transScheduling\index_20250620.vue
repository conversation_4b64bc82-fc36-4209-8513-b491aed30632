<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="status" slot-scope="scope">
                <el-tag v-if="scope.row.status == '0'" size=" medium" type="primary">未开始</el-tag>
                <el-tag v-if="scope.row.status == '1'" size="medium" type="danger">执行中</el-tag>
                <el-tag v-if="scope.row.status == '2'" size="medium" type="danger">已结束</el-tag>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {getList, add,update,remove,loadDetailList} from "@/api/supplier/supplierTransScheduling";
import {mapGetters} from "vuex";
export default {
    data() {
        return {
            form: {
                detailEntityList: []
            },
            query: {},
            loading: true,
            data:[],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                refreshBtn: false,
                searchMenuSpan: 4, //搜索按钮长度
                tip: false,
                border: true,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                index: true,
                viewBtn: true,
                selection: true,
                labelWidth: 150,
                dialogWidth: 900,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "开始时间",
                        type: 'datetime',
                        prop: 'startDate',
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                        hide: true,
                        search: true,
                        rules: [{
                            required: true,
                            message: '请选择开始时间',
                            trigger: 'blur'
                        }]
                    },
                    {
                        label: "结束时间",
                        type: 'datetime',
                        prop: 'endDate',
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                        hide: true,
                        search: true,
                        rules: [{
                            required: true,
                            message: '请选择开始时间',
                            trigger: 'blur'
                        }]
                    },
                    {
                        label: "标题",
                        prop: "title",
                        type: "input",
                        search: true
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "select",
                        dicData: [{
                            value: "0",
                            label: "未开始"
                        }, {
                            value: "1",
                            label: "执行中"
                        }, {
                            value: "2",
                            label: "已结束"
                        }],
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        slot: true,
                        search: true
                    },
                    {
                        label: '配送路线',
                        prop: 'detailEntityList',
                        type: 'dynamic',
                        span: 24,
                        children: {
                            align: 'center',
                            headerAlign: 'center',
                            column: [
                                 {
                                label: '路线名称',
                                prop: "lineId",
                                type: 'select',
                                dicUrl: "/api/service/rabbit-supplier/deliveryLine-list",
                                props: {
                                    label: "lineName",
                                    value: "id"
                                   },
                                },
                                {
                                    label: "配送车俩",
                                    prop: "carId",
                                    type: "select",
                                    dicUrl: "/api/service/rabbit-supplier/deliveryCar-list",
                                    props: {
                                        label: "carName",
                                        value: "id"
                                    },
                                },
                                {
                                    label: "驾驶员",
                                    prop: "driverId",
                                    type: "select",
                                    dicUrl: "/api/service/rabbit-supplier/user-list",
                                    props: {
                                        label: "contactName",
                                        value: "id"
                                    },
                                },
                                {
                                    label: "跟车员",
                                    prop: "followerId",
                                    type: "select",
                                    dicUrl: "/api/service/rabbit-supplier/user-list",
                                    props: {
                                        label: "contactName",
                                        value: "id"
                                    },
                                },
                            ]
                        }
                    },
                    {
                        label: "编辑日期",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd",
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                ]
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        permissionList() {
            return {
                addBtn: this.vaildData(this.permission.supplier_delivery_car_add, false),
                viewBtn: this.vaildData(this.permission.supplier_delivery_car_view, false),
                delBtn: this.vaildData(this.permission.supplier_delivery_car_delete, false),
                editBtn: this.vaildData(this.permission.supplier_delivery_car_edit, false)
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
    },
    methods: {
        rowSave(row, loading, done) {
            add(row).then(res => {
                loading();
                this.onLoad(this.page);
                if (res.data.success) {
                    this.$message({
                        type: "success",
                        message: res.data.message
                    });
                } else {
                    this.$message({
                        type: "error",
                        message: res.data.message
                    });
                }
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //主界面基本方法 start
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                this.form.detailEntityList = []
                loadDetailList(this.form.id).then(res => {
                    res.data.data.map(item => {
                        this.form.detailEntityList.push({
                            lineId: item.lineId,
                            carId:item.carId,
                            driverId:item.driverId,
                            followerId:item.followerId,
                        })
                    })
                });

            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },

    }
};
</script>

<style scoped>
.avue-crud__pagination {
    display: none;
}
</style>
