<template>
  <basic-container>
    <el-tabs type="border-card" v-model="activeName">
<!--      //==充值取现消费收入===========================================================================-->
      <el-tab-pane label="充值取现消费收入" name="first">
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="curd"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
          <template slot="infoForm" slot-scope="scope">
            <avue-crud
              :option="infoOption"
              :table-loading="loading"
              :page="pageSub"
              :before-open="beforeOpenSub"
              v-model="formSub"
              ref="curdSub"
              @row-update="rowUpdateSub"
              @row-save="rowSaveSub"
              @row-del="rowDelSub"
              @search-change="searchChangeSub"
              @search-reset="searchResetSub"
              :data="infoData"
              @on-load="onLoadSub">
            </avue-crud>
          </template>
        </avue-crud>
      </el-tab-pane>
<!--      商品对应科目======================================================-->
      <el-tab-pane label="商品对应科目" name="second">
        <avue-crud
          :data="goodCategoryData"
          :option="goodCategoryOption"
          :page="pageGood"
          :before-open="beforeOpenCategory"
          v-model="formGoodCategory"
          ref="curdCategory"
          @row-update="rowUpdateGoodCategory"
          @row-save="rowSaveGoodCategory"
          @search-change="searchChangeGoodCategory"
          @search-reset="searchResetGoodCategory"
          @on-load="onLoadGoodCategory">
        </avue-crud>
      </el-tab-pane>
<!--      商品对应科目======================================================-->
      <el-tab-pane label="费用对应科目" name="third">费用对应科目</el-tab-pane>
<!--      商品对应科目======================================================-->
      <el-tab-pane label="其他对应科目" name="fourth">其他对应科目</el-tab-pane>
    </el-tabs>
  </basic-container>
</template>
<script>
import {mapGetters} from "vuex";
import {getList,getGoodCategoryList, getDetails, add, update, remove, addSub, updateSub, removeSub,addGoodCategory} from "@/api/supplier/finance/financialBillConfig";
import {getAccountSets, getSubjectTree, getSubjectTreeByaccountSets} from "@/api/supplier/finance/financialUtils";
var DIC = {
  // 余额方向
  balanceDirection: [{
    label: '借',
    value: 0
  },{
    label: '贷',
    value: 1
  }],
}

// let accountSetsId =0;
// let canteenId = 0;
let baseSubjectUrl = '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId/';
let baseSubjectUrl_category = '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId/';
export default {
  data(){
    return {
      canteenId: 0,
      accountSetsId: 0,
      category_canteenId: 0,
      category_accountSetsId: 0,
      activeName: "first",
      form:{},
      formSub:{},
      formGoodCategory:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      pageSub: {
        pageSize: 1000,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      info:[],
      data:[],
      option:{
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        addBtn: true,
        menu:true,
        refreshBtn: false,
        searchBtn: false,
        columnBtn: false,
        addRowBtn: false,
        cellBtn: false,
        selection: true,
        align: 'center',
        column: [{
          label: 'id',
          prop: 'id',
          hide: true,
          addDisplay: false,
          editDisplay: false,
          viewDisplay: false,
        },
          {
            label: "食堂",
            prop: "canteenId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            cascaderItem: ['accountSetsId'],
            cascaderItemIndex:1,
            props: {
              label: "deptName",
              value:"id"
            },
            nodeClick:(data)=>{
              getAccountSets(data.id).then(res=>{
                this.canteenId = data.id;
                this.form.canteenId = data.id;

                const column2 = this.goodCategoryOption.column[2];
                column2.dicData = res.data.data;
                this.onLoad(this.page,this.form);
              });
            }
          },
          {
            label: "帐套",
            prop: "accountSetsId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: `/api/rabbit-supplier/finance/financialAccountSets/accountSetsList/scope?canteenId={{key}}`,
            props: {
              label: "accountName",
              value: "id"
            },
            nodeClick:(data)=>{
              this.accountSetsId = data.id;
              this.form.accountSetsId = data.id;
              this.onLoad(this.page,this.form);
            }
          },
          {
            label: '编码',
            prop: 'billCode',
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            rules: [{
              required: true,
              message: "编码",
              trigger: "blur"
            }],
          },
          {
            label: '模块',
            prop: 'modular',
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            rules: [{
              required: true,
              message: "模块",
              trigger: "blur"
            }],
          },
          {
            label: '操作者',
            prop: 'operator',
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            rules: [{
              required: true,
              message: "操作者",
              trigger: "blur"
            }],
          },
          {
            label: '动作名称',
            prop: 'billAction',
            rules: [{
              required: true,
              message: "动作名称",
              trigger: "blur"
            }],
          },
          {
            label: '描述',
            prop: 'billDescribe',
            rules: [{
              required: true,
              message: "描述",
              trigger: "blur"
            }],
          },
          {
          labelWidth:0,
          label: '',
          prop: 'info',
          span:24,
          hide:true,
          formslot:true,
        }]
      },
      infoOption:{
        column: [{
          label: 'id',
          prop: 'id',
          hide:true,
          viewDisplay: false,
          addDisplay: false,
          editDisplay: false,
        },
          {
            label: '主键id',
            prop: 'billId',
            hide: true,
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '编码',
            prop: 'billCode',
            search: true,
            disabled: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
          },
          {
            label: '生成凭证描述',
            prop: 'billSubjectDesc',
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
          },
          {
            label: '凭证摘要',
            prop: 'billVoucherSummary',
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
          },
          {
            label: '技术实现',
            prop: 'realization',
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
          },
          {
            label: '分组',
            prop: 'billGroup',
            dataType: "number",
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            rules: [{
              required: true,
              message: "分组",
              trigger: "blur"
            }],
          },
          {
            label: '次序',
            prop: 'billSort',
            dataType: "number",
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            rules: [{
              required: true,
              message: "次序",
              trigger: "blur"
            }],
          },
          {
            label: '科目',
            prop: 'subjectId',
            type: "tree",
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            dicUrl: '',
            props: {
              label: "subjectName",
              value: "id"
            },
            rules: [{
              required: true,
              message: "科目",
              trigger: "blur"
            }],
          },
          {
            label: '余额方向(0借1贷)',
            prop: 'balanceDirection',
            type: "select",
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            dicData: DIC.balanceDirection,
            rules: [{
              required: true,
              message: "余额方向",
              trigger: "blur"
            }],
          }]
      },
      pageGood: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      goodCategoryData:[],
      goodCategoryOption: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        selection: false,
        align: "center",
        addBtn: true,
        viewBtn:false,
        delBtn:false,
        editBtn: false,
        addRowBtn:false,
        cellBtn:true,
        cancelBtn:true,
        searchBtn:false,
        refreshBtn:false,
        columnBtn:false,


        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "食堂",
            prop: "canteenId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            cascaderItem: ['accountSetsId'],
            cascaderItemIndex:1,
            props: {
              label: "deptName",
              value:"id"
            },
            nodeClick:(data)=>{
              this.category_canteenId = data.id;
              this.formGoodCategory.canteenId = data.id;
              getAccountSets(data.id).then(res=>{
                const column3 = this.goodCategoryOption.column[3];
                // column3.dicUrl = '/api/service/rabbit-supplier/finance/financialAccountSets/accountSetsList/scope?canteenId='+data.id;
                column3.dicData = res.data.data;
                this.onLoadGoodCategory(this.pageGood,this.form);
              });
            }
          },
          {
            label: "帐套",
            prop: "accountSetsId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: `/api/rabbit-supplier/finance/financialAccountSets/accountSetsList/scope?canteenId={{key}}`,
            cascaderItem: ['inSubjectId','outSubjectId'],
            cascaderItemIndex:1,
            props: {
              label: "accountName",
              value: "id"
            },
            nodeClick:(data)=>{
              this.category_accountSetsId = data.id;
              this.formGoodCategory.accountSetsId = data.id;
              getSubjectTreeByaccountSets(data.id).then(res => {
                // baseSubjectUrl_category = '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId?accountSetsId='+data.id;
                var arr = res.data.data;
                const column6 = this.goodCategoryOption.column[6];
                column6.dicData = arr;

                const column7 = this.goodCategoryOption.column[7];
                column7.dicData = arr;

                this.onLoadGoodCategory(this.pageGood,this.form);
              });
            }
          },
          {
            label: "商品大类",
            prop: "biddingTypeId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan/biddingType/dict",
            props: {
              label: "name",
              value: "id"
            },
            search: true,
          },
          {
            label: "物资类型",
            prop: "materialType",
            type: "input",
            rules: [{
              required: true,
              message: "请输入商品名称",
              trigger: "blur"
            }],
            search: true
          },
          {
            label: '入库科目',
            prop: 'inSubjectId',
            type: "tree",
            search: true,
            cell: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            // dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId?accountSetsId={{key}}',
            dicData:[],
            props: {
              label: "subjectName",
              value: "id"
            },
          },
          {
            label: '出库科目',
            prop: 'outSubjectId',
            type: "tree",
            search: true,
            cell: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            // dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId?accountSetsId={{key}}',
            dicData:[],
            props: {
              label: "subjectName",
              value: "id"
            },
          },
        ],

      },
      //
    }
  },
  created() {

  },
  computed:{
    infoData(){
      if(this.info != null && this.info.length >0) {
        return this.info;
      }
      return this.form.info || []
    },
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
  },
  mounted() {
    this.$refs.crud.dicInit('cascader');
    this.$refs.curdCategory.dicInit('cascader');
  },
  methods: {
    beforeOpen(done, type) {
      if (["add","edit", "view"].includes(type)) {
        // getDetail(this.form.id).then(res => {
        //   this.form = res.data.data;
        // });
        // console.log(type);
        if(type=="add") {
          this.form = {};
          this.info = [];
        }else if (type=="edit"){
          baseSubjectUrl = '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId?accountSetsId='+this.accountSetsId;
          const column8 = this.infoOption.column[8];
          column8.dicUrl = baseSubjectUrl;

          const column4 = this.goodCategoryOption.column[4];
          column4.dicUrl = baseSubjectUrl;
          const column5 = this.goodCategoryOption.column[5];
          column4.dicUrl = baseSubjectUrl;
        }
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      // this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    rowSave(row, loading, done) {
      const info = row.info;
      if(info==null || info==undefined||info=='') {
        row.info = [];
      }
      if(this.info!=null&&this.info.length>0) {
        row.info = this.info;
      }
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      this.form = row;
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    rowCellCategory(row,index) {
      this.$refs.curdCategory.rowCell(row,index);
    },
    rowCellUpdateCategory(row,index,done) {
      // this.$refs.curdCategory.rowCell(row,index);
      this.$message.success(JSON.stringify(row));
    },
    rowCellCancelCategory(row,index) {
      row.$cellEdit = false;
      this.$refs.curdCategory.refreshTable();
    },
    onLoad(page, params = {}) {
      this.info = [];
      this.loading = true;
      // if((params.accountSetsId==null||params.accountSetsId==undefined||params.accountSetsId==0)||(params.canteenId==null||params.canteenId==undefined||params.canteenId==0)) {
      //   this.loading = false;
      //   return;
      // }
      baseSubjectUrl = '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId?accountSetsId='+this.accountSetsId;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },

    beforeOpenCategory(done, type) {
      if (["add","edit", "view"].includes(type)) {
        // baseSubjectUrl = '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/accountSetsId?accountSetsId='+this.category_accountSetsId;
        // const column6 = this.goodCategoryOption.column[6];
        // column6.dicUrl = baseSubjectUrl;
        //
        // const column7 = this.goodCategoryOption.column[7];
        // column7.dicUrl = baseSubjectUrl;
      }
      done();
    },

    onLoadGoodCategory(page, params = {}) {
      getGoodCategoryList(page.currentPage, page.pageSize, Object.assign(params, this.queryGoodCategory)).then(res => {
        const data = res.data.data;
        this.pageGood.total = data.total;
        this.goodCategoryData = data.records;
        this.goodCategoryData.forEach((item) => {
          item.$cellEdit = false;
        });
        this.loading = false;
      });
    },

    // 子框设置
    onLoadSub(page, billid) {
      // if(billid !=undefined && billid !=null && billid != '') {
      //
      // }else {
      //
      // }
      billid = this.form.id;
      if(billid!=undefined && billid > 0) {
        this.loading = true;
        getDetails(billid).then(res => {
          this.form.info = res.data.data;
          this.loading = false;
        });
      }
    },
    beforeOpenSub(done, type) {
      if (["add", "edit", "view"].includes(type)) {
        console.log(this.formSub);
      }
      done();
    },
    rowSaveSub(row, loading, done) {
      if(row!=null) {
        if(this.form.id != null && this.form.id != undefined && this.form.id != '') {
          row.billId = this.form.id;
          row.billCode = this.form.billCode;
          addSub(row).then(() => {
            loading();
            this.onLoadSub(this.pageSub,this.formSub.id);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          }, error => {
            done();
            window.console.log(error);
          });
        }else{
          this.info.push(row);
        }

      }
      loading();
      done();
    },
    rowUpdateSub(row, index, loading, done) {
      updateSub(row).then(() => {
        loading();
        this.onLoadSub(this.pageSub,this.formSub.id);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDelSub(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return removeSub(row.id);
        })
        .then(() => {
          this.onLoadSub(this.pageSub,this.formSub.id);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    searchResetSub() {
      this.querySub = {};
      this.onLoadSub(this.pageSub,this.formSub.id);
    },
    searchChangeSub(params, done) {
      this.querySub = params;
      this.pageSub.currentPage = 1
      this.onLoadSub(this.pageSub,this.formSub.id);
      done();
    },

    // 商品分类方法=================================================
    rowSaveGoodCategory(row, loading, done) {
      addGoodCategory(row).then(() => {
        loading();
        this.onLoadGoodCategory(this.pageGood,this.formGoodCategory.id);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdateGoodCategory(row, index, loading, done) {
      addGoodCategory(row).then(() => {
        loading();
        this.onLoadGoodCategory(this.pageGood,this.formGoodCategory.id);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        done();
        window.console.log(error);
      });
    },
    searchResetGoodCategory() {
      this.queryGoodCategory = {};
      this.onLoadGoodCategory(this.pageGood,this.formGoodCategory.id);
    },
    searchChangeGoodCategory(params, done) {
      this.queryGoodCategory = params;
      this.pageGood.currentPage = 1
      this.onLoadGoodCategory(this.pageGood,this.formGoodCategory.id);
      done();
    },
  }
}
</script>
