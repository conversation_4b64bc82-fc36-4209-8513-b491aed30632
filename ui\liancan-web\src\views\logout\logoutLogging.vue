<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exports">导出</el-button>
      </template>
      <template slot="menuLeft">
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >统计时间：{{this.startDate}} - {{this.endDate}}
        </a>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, exportLogoutData} from "@/api/logout/logoutLogging";
  import {mapGetters} from "vuex";
  const DIC = {
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
    BALANCETYPE:[{
      label: '已退现金,余额清零',
      value: '1'
    },{
      label: '未退现金,余额清零',
      value: '2'
    }],
    REFUNDTYPE:[{
      label: '已退现金,待退款清零',
      value: '1'
    },{
      label: '未退现金,待退款清零',
      value: '2'
    }],
  }
  export default {
    data() {
      return {
        form: {},
        query: {},
        searchForm: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        startDate:undefined,
        endDate:undefined,
        selectionList: [],
        option: {
        /*  height:'auto',
          calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          editBtn: false,
          delBtn: false,
          addBtn: false,
          viewBtn: false,
          menu: false,
          printBtn:true,
          selection: false,
          searchSpan: 160,
          column: [
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              search: true,
              width: 120,
            },
            {
              label: "性别",
              prop: "sex",
              type: "select",
              dicData: DIC.sex,
              search: true,
              width: 60,
            },
            {
              label: "学号/工号",
              prop: "studentJobNo",
              type: "input",
              search: true,
              width: 130,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              search:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                label: "title",
                value: "id"
              },
              width: 150,
              overHidden: true,
              rules: [{
                required: true,
                message: "请输入所属租户",
                trigger: "click"
              }]
            },
/*            {
              label: "伙食费钱包号",
              prop: "walletNumber",
              type: "input",
              width: 160,
            },*/
            {
              label: "操作人",
              prop: "logoutUser",
              type: "input",
              width: 130,
            },
            {
              label: "注销时间",
              prop: "createTime",
              type: "input",
              width: 150,
            },
            {
              label: "注销时钱包余额",
              prop: "money",
              type: "number",
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
            },
            {
              label: "钱包处理方式",
              prop: "balanceType",
              type: "select",
              dicData: DIC.BALANCETYPE,
              search: true
            },
            {
              label: "统缴餐待退款金额",
              prop: "refundMoney",
              type: "number",
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
            },
            {
              label: "统缴餐待退款处理方式",
              prop: "refundType",
              type: "select",
              dicData: DIC.REFUNDTYPE,
              search: true,
            },
            {
              label: '退款时间',
              prop: 'orderDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
            {
              label: "注销单号",
              prop: "id",
              type: "input",
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.logoutLogging_add, false),
          viewBtn: this.vaildData(this.permission.logoutLogging_view, false),
          delBtn: this.vaildData(this.permission.logoutLogging_delete, false),
          editBtn: this.vaildData(this.permission.logoutLogging_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      this.getMonthStartEnd();
    },
    methods: {
      getMonthStartEnd(){
        var startDate = new Date().getFullYear()+' 01-01'
        var endDate = new Date().getFullYear()+' 12-31'
        this.startDate = startDate;
        this.endDate = endDate;
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.getMonthStartEnd();
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.searchForm = params;
        this.query = params;
        if (params.orderDate != '' && params.orderDate != null && params.orderDate != undefined) {
          params.startDate = params.orderDate[0];
          params.endDate = params.orderDate[1];
          this.startDate = params.orderDate[0];
          this.endDate = params.orderDate[1];
        }
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      exports(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出注销明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportLogoutData(this.ids).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '注销明细报表.xls';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      }
    }
  };
</script>

<style>
</style>
