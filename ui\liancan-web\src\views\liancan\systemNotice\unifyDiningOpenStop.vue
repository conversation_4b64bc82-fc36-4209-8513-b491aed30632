<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
     <!-- <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.unifyDiningMenu_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>-->
      <template slot="menu" slot-scope="scope">
        <el-button v-if="scope.row.status === '0'" type="text" size="mini" icon="el-icon-thumb" @click.stop="publish(scope.row)">发布</el-button>
        <el-button v-if="scope.row.status === '0'" type="text" size="mini" icon="el-icon-delete" @click.stop="rowDel(scope.row)">删除</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, getDict} from "@/api/liancan/unifyDiningMenu";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
          option: {
              height:'auto',
              calcHeight: 30,
              searchShow: true,
              searchMenuSpan: 6,
              tip: false,
              border: true,
              index: true,
              viewBtn: true,
              selection: true,
              labelWidth: 150,
              dialogWidth: "70%",
              column: [
                  {
                      label: "姓名",
                      prop: "userName",
                      type: "input",
                      span: 24,
                      search:true,
                  },
                  {
                      label: '性别',
                      prop: 'sex',
                      type: "radio",
                      dicData: [
                          {
                              label: '男',
                              value: "1"
                          },
                          {
                              label: '女',
                              value: "2"
                          }
                      ],
                      search:true,
                  },
                  {
                      label: "学号/工号",
                      prop: "studentJobNo",
                      type: "input",
                      search:true,
                  },
                  {
                      label: "班级",
                      prop: "deptName",
                      type: "input",
                  },
                  {
                      label: "人员属性",
                      prop: "attribute",
                      type: "select",
                      dicUrl: '/api/service/rabbit-liancan/userRole/dict',
                      props: {
                          res: "data",
                          label: "name",
                          value: "id"
                      },
                  },
                  {
                      label: "人员照片",
                      prop: "avatar",
                      type: 'upload',
                      listType: 'picture-img',
                      action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                      propsHttp: {
                          res: 'data',
                          url: 'link',
                      },
                      span:24,
                      slot: true,
                  },
                  {
                      label: "人员状态",
                      prop: "status",
                      type: "radio",
                      dicUrl: "/api/service/rabbit-system/dict/dictionary?code=start_stop_status",
                      search:true,
                      props: {
                          label: "dictValue",
                          value: "dictKey"
                      },
                  },
                  {
                      label: "用餐类别",
                      prop: "mealsType",
                      type: "select",
                      dicUrl: '/api/service/rabbit-liancan/diningType/dict',
                      props: {
                          label: "name",
                          value: "id"
                      },
                      search:true,
                  },
                  {
                      label: "统缴餐缴费计划",
                      prop: "paymentPlanId",
                      type: "input",
                  },
                  {
                      label: "统缴餐状态",
                      prop: "unifyDiningStatus",
                      type: "radio",
                      dicData: [
                          {
                              label: '正常',
                              value: "0"
                          },
                          {
                              label: '停餐',
                              value: "1"
                          }
                      ],
                  },
                  {
                      label: "统缴餐余额",
                      prop: "remainingSum",
                      type: "number",
                  },
              ]
          },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.unifyDiningMenu_add, false),
          viewBtn: this.vaildData(this.permission.unifyDiningMenu_view, false),
          delBtn: this.vaildData(this.permission.unifyDiningMenu_delete, false),
          editBtn: this.vaildData(this.permission.unifyDiningMenu_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    watch: {
        'form.outletsId'() {
            let outletsId = this.form.outletsId;
            if (outletsId != null && outletsId != '') {
                this.getDict(outletsId);
            }
        }
    },
    methods: {
      getDict(outletsId) {
          getDict(1,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("brekker");
              this.option.column[index].dicData = res.data.data;
          });
          getDict(2,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("lunch");
              this.option.column[index].dicData = res.data.data;
          });
          getDict(3,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("supper");
              this.option.column[index].dicData = res.data.data;
          });
          getDict(4,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("night");
              this.option.column[index].dicData = res.data.data;
              /*DIC.night.forEach(s => {
                  DIC.night.$remove(s);
              })*/
          });
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      publish(row) {
          row.status = '1';
          update(row).then(() => {
              this.onLoad(this.page);
              this.$message({
                  type: "success",
                  message: "操作成功!"
              });
          }, error => {
              window.console.log(error);
          });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          //this.getDict(this.form.outletsId);
          /*getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });*/
        }
          if (["add"].includes(type)) {
              const index = this.$refs.crud.findColumnIndex("brekker");
              this.option.column[index].dicData = [];
              const index2 = this.$refs.crud.findColumnIndex("lunch");
              this.option.column[index2].dicData = [];
              const index3 = this.$refs.crud.findColumnIndex("supper");
              this.option.column[index3].dicData = [];
              const index4 = this.$refs.crud.findColumnIndex("night");
              this.option.column[index4].dicData = [];
          }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
        /*if(){

        }*/
      }
    }
  };
</script>

<style>
</style>
