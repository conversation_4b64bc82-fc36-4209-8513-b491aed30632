<template>
  <basic-container>
    <div class="search_container">
      <el-form :inline="true" :model="dataForm">
        <el-form-item label="被检单位">
          <el-select v-model="dataForm.deptId" placeholder="被检单位" clearable filterable @change="getDataList()">
            <el-option v-for="item in canteenList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label>
          <el-select v-model="dataForm.checkItem" placeholder="检测项目" clearable filterable @change="getDataList()">
            <el-option v-for="item in dictList" :key="item.dictValue" :label="item.dictValue" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="时间" style="margin-left: 30px;">
          <el-date-picker v-model="dataForm.createTimeStart" type="date" placeholder="时间起" value-format="yyyy-MM-dd" clearable style="width:140px" />
          至
          <el-date-picker v-model="dataForm.createTimeEnd" type="date" placeholder="时间止" value-format="yyyy-MM-dd" clearable style="width:140px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()" icon="el-icon-search">查询</el-button>
          <el-button @click="getDataList('empty')" icon="el-icon-delete">清空</el-button>
          <!-- <el-button   type="primary" icon="el-icon-plus">新增</el-button>
          <el-button  type="danger" icon="el-icon-delete">批量删除</el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="avue-empty" v-show="!hasData" style="margin-top:150px;margin-bottom: 800px;"><div class="avue-empty__image" style="height: 50px;"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAxKSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgIDxlbGxpcHNlIGZpbGw9IiNGNUY1RjUiIGN4PSIzMiIgY3k9IjMzIiByeD0iMzIiIHJ5PSI3Ii8+CiAgICA8ZyBmaWxsLXJ1bGU9Im5vbnplcm8iIHN0cm9rZT0iI0Q5RDlEOSI+CiAgICAgIDxwYXRoIGQ9Ik01NSAxMi43Nkw0NC44NTQgMS4yNThDNDQuMzY3LjQ3NCA0My42NTYgMCA0Mi45MDcgMEgyMS4wOTNjLS43NDkgMC0xLjQ2LjQ3NC0xLjk0NyAxLjI1N0w5IDEyLjc2MVYyMmg0NnYtOS4yNHoiLz4KICAgICAgPHBhdGggZD0iTTQxLjYxMyAxNS45MzFjMC0xLjYwNS45OTQtMi45MyAyLjIyNy0yLjkzMUg1NXYxOC4xMzdDNTUgMzMuMjYgNTMuNjggMzUgNTIuMDUgMzVoLTQwLjFDMTAuMzIgMzUgOSAzMy4yNTkgOSAzMS4xMzdWMTNoMTEuMTZjMS4yMzMgMCAyLjIyNyAxLjMyMyAyLjIyNyAyLjkyOHYuMDIyYzAgMS42MDUgMS4wMDUgMi45MDEgMi4yMzcgMi45MDFoMTQuNzUyYzEuMjMyIDAgMi4yMzctMS4zMDggMi4yMzctMi45MTN2LS4wMDd6IiBmaWxsPSIjRkFGQUZBIi8+CiAgICA8L2c+CiAgPC9nPgo8L3N2Zz4K" alt=""></div><p class="avue-empty__desc">暂无数据</p></div>
    <div id="capture">
      <div class="chartContainer" >
          <div class="bd1 chartContainer2" style="height: 150px;width:150px;">
            <div id="ringChart1" class="ringchart"></div>
            <div>检测率</div>
            <div>已检测：{{jcTotal}}</div>
            <div>已采样：{{cyTotal}}</div>
          </div>
          <div class="bd1 chartContainer2" style="height: 150px;width:150px;">
            <div id="ringChart2" class="ringchart"></div>
            <div>合格率</div>
            <div>&nbsp;</div>
            <div>合格：{{hgTotal}}</div>
          </div>
          <div class="bd1 chartContainer2" style="height: 150px;width:150px;">
            <div id="ringChart3" class="ringchart"></div>
            <div>不合格率</div>
            <div>&nbsp;</div>
            <div>不合格：{{bhgTotal}}</div>
          </div>
          <div class="bd1 chartContainer2" style="height: 150px;width:150px;">
            <div id="ringChart4" class="ringchart"></div>
            <div>不合格(疑似)率</div>
            <div>&nbsp;</div>
            <div>不合格(疑似)：{{bhgysTotal}}</div>
          </div>
      </div>
      <div id="chartColumn" style="width: 100%; height: 400px;margin-top:100px;margin-bottom: 500px;"></div>
    </div>


  </basic-container>
</template>
<style>
  .bd1{border: 0px solid #f00;}
  .chartContainer{display: flex;}
  .chartContainer2{margin: 30px 50px 0px 50px;text-align: center;}
  .ringchart{width:150px;height: 150px;}
</style>
<script>
import request from "@/router/axios";
import echarts from "echarts";
import { dateFormat } from "@/util/date";
export default {
  data() {
    return {
      chartColumn: null,
      canteenList: [],
      hasData:false,
      dictList:[],
      jcTotal:0,
      cyTotal:0,
      hgTotal:0,
      bhgTotal:0,
      bhgysTotal:0,
      dataForm: {
        deptId: "",
        createTimeStart: dateFormat(
          new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
          "yyyy-MM-dd"
        ),
        createTimeEnd: dateFormat(new Date(), "yyyy-MM-dd")
      }
    };
  },
  mounted() {
    //this.drawLine();
    this.getCanteenList();
    this.getDataList();
    console.log('aaaa..===')
   // this.getDictList();
   setTimeout(() => {
     //this.test3();
   }, 3000);
  },
  methods: {
    test3(){
      html2canvas(document.querySelector("#capture")).then(canvas => {
          //document.body.appendChild(canvas)
          var pageData = canvas.toDataURL('image/jpeg', 1.0);
          this.saveImage(pageData.replace("image/jpeg", "image/octet-stream"),"合格率统计.jpeg");
      });
    },
    saveImage(data, filename){
        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = data;
        save_link.download = filename;
        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);
    },
    getData(url,params,callbackFun) {
      request({
        url: url,
        params: {...params},
        method: "get"
      }).then(
        res => {
          callbackFun(res);
        },
        error => { }
      );
    },
    getCanteenList() {
      request({
        url: "/api/service/rabbit-system/dept/getDeptAndSubDept?deptCategory=4",
        method: "get"
      }).then(
        res => {
          console.log(res, " res  getCanteenList ... ");
          this.canteenList = res.data.data;
        },
        error => { }
      );
    },getDictList() {
      request({
        url: "/api/service/rabbit-system/dict/dictionary?code=fast_check_item",
        method: "get"
      }).then(
        res => {
          console.log(res, " res  getDictList ... ");
          this.dictList = res.data.data;
          console.log(this.dictList,'this.dictList...')
        },
        error => { }
      );
    },
    getDataList(action) {
      if (action === "empty") {
        for (const key in this.dataForm) {
          if (this.dataForm.hasOwnProperty(key)) {
            this.dataForm[key] = "";
          }
        }
      }
      let params = this.dataForm;
      this.getData('/api/service/rabbit-supplier/foodcheck/statPassPercent',params,(res)=>{
        //console.log('res 11111 =',res);
          let data = res.data.data;
          if(data&&data.length>0){
            this.hasData=true;
            document.getElementById('chartColumn').style.display='block';
          }else{
            this.hasData=false;
            document.getElementById('chartColumn').style.display='none';
          }
          let xAxisData = [];
          let passData = [];
          let unpassData = [];
          let unpassYsData=[];
          if(data){
          data.forEach(e => {
            xAxisData.push(e.checkDate);
            passData.push(e.hgRate);
            unpassData.push(e.bhgRate);
            unpassYsData.push(e.bhgysRate);
          });
          }
          this.calcRingChartData(data);
          this.drawLine(xAxisData, passData, unpassData,unpassYsData);
      })
    },
    calcRingChartData(dataArray){
      let da=dataArray;
      if(!da){return ;}
      let ts=this;
      let a1=['jcTotal','cyTotal','hgTotal','bhgTotal','bhgysTotal'];
      a1.forEach(e1=>{ts[e1]=0;});
      da.forEach(e=>{
        a1.forEach(e1=>{
          ts[e1]+=e[e1];
        })
      })
      this.drawRingChart((ts.jcTotal/ts.cyTotal*100).toFixed(2),'ringChart1');
      this.drawRingChart((ts.hgTotal/ts.jcTotal*100).toFixed(2),'ringChart2');
      this.drawRingChart((ts.bhgTotal/ts.jcTotal*100).toFixed(2),'ringChart3');
      this.drawRingChart((ts.bhgysTotal/ts.jcTotal*100).toFixed(2),'ringChart4');
    },
    drawRingChart(dataValue,renderId){
        // 基于准备好的dom，初始化echarts
       // let dataValue=88;
        let wgWarnDataList=[{color:'#80ccff',value:dataValue, name: 'aaaaaaa',label:{padding:[0,0,0,0]}},{color:'#eee',value:(100-dataValue), name:'other',label:{show:false}}];
        let mychart3 = echarts.init(document.getElementById(renderId));
        var option = {
                backgroundColor:'',
                series : [
                    {
                        name: '访问来源',
                        type: 'pie',
                        radius: ['60%','85%'],
                        silent: true, /*鼠标放上去不显示效果*/
                        data:wgWarnDataList,
                        // data:[
                        //     {value:30, name:'耗材',color:'#01BE7D'},
                        //     {value:30, name:'其它',color:'#8E2EFA'},
                        //     {value:30, name:'水嘴消毒',color:'#ECA100'},
                        //     {value:30, name:'水质',color:'#1FB8F1'},
                        // ],
                        //roseType: 'angle',
                        itemStyle: {
                            normal: {
                                shadowBlur: 100,
                                shadowColor: 'rgba(0, 0, 0, 0)',
                                //fontSize: fontSizeFun(isPc == 'n' ? 0.2 : 0.15),//柱状图底部字体大小
                                label:{
                                    formatter:function(p){return p.data.value+'%'},
                                  //  formatter:function(p){return p.data.name+'\n'+p.data.value+'%'},
                                    //textStyle:{color:'#fff',fontSize:fontSizeFun(isPc == 'n' ? 0.2 : 0.24)},//柱状图底部字体大小
                                    position:'center'
                                },
                                labelLine:{
                                    length2:80//第二段引线长度
                                    ,show:false
                                },
                                color:function(p){
                                    return p.data.color||'#fff';
                                }
                            }
                        }
                    }
                ]
            };

        // 使用刚指定的配置项和数据显示图表。
        mychart3.setOption(option);
    },
    drawLine(xAxisData, passData, unpassData,unpassYsData) {
      this.chartColumn = echarts.init(document.getElementById("chartColumn"));
      let getData = function () {
        let yData1 = [];
        xAxisData.forEach(e => {
          yData1.push(parseInt(Math.random() * 100));
        });
        return yData1;
      };
      this.chartColumn.setOption({
        title: { text: "合格率统计" },
        tooltip: {
          trigger: "axis"
        },
        legend: {
          data: ["合格率","不合格率","不合格率(疑似)"]
        },
        grid: {
          left: "1%",
          right: "3%",
          bottom: "1%",
          containLabel: true
        },
        toolbox: { feature: { saveAsImage: {} } },
        xAxis: {
          type: "category",
          //boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: "value"
        },
        series: [
            {
            name: "合格率",
            type: "line",
            //stack: '总量',
            itemStyle: { color: "#00ff00" },
            data: passData
          },
          {
            name: "不合格率",
            type: "line",
            //stack: '总量',
            itemStyle: { color: "#ff0000" },
            data: unpassData
          },
          {
            name: "不合格率(疑似)",
            type: "line",
            //stack: '总量',
            itemStyle: { color: "#66ccff" },
            data: unpassYsData
          }
        ]
      },true);
      this.chartColumn.resize();
    }
  }
};
</script>

<style>
.el-row {
  margin-bottom: 20px;
  font-size: medium;
}
</style>
