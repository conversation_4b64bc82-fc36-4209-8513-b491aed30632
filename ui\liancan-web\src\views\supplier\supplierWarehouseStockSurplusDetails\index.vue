<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-download" size="small" @click="printOrderHandler" >导出</el-button>
                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {detailList, getDetail, add, update, exportDetailList} from "@/api/supplier/supplierStockSuplus";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";

var DIC = {
    billTypes: [{
        label: '全部',
        value: "0"
    },{
        label: '盘亏单',
        value: "1"
    },{
        label: '盘盈单',
        value: "2"
    },],
}
export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                menu: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            disabledDate(time) {
                                return time.getTime() < Date.now();
                            }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "单据类型",
                        prop: "billType",
                        type: "select",
                        dicData: DIC.billTypes,
                        search: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                        search: true,
                    },
                    {
                        label: "商品名称",
                        prop: "goodGoodId",
                        type: "select",
                        hide: true,
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                        search: false,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "仓库",
                        prop: "warehouseId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "入库成本金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            data: [],
            isShowStockIn: false,
            isShowSelectGoods: false,
            isShowSelectOrder: false,

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "stockInData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.stockInData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },
        addStockInHandler () {
            this.form = {};
            this.businessDate = '';//new Date();
            this.supplierCustomerId = '';
            this.salesman = '';
            this.editType = 'add';
            this.isShowStockIn = true;
        },
        closeAddForm() {
            if (this.stockInData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowStockIn = false;
                    });
            }else{
                this.isShowStockIn = false;
            }
        },
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        closeShowForm(){
            this.isShowStockInDetail = false;
        },

        viewRow(row,index) {
            this.editType = 'view';
            this.form = row;
            this.stockInOnLoad(this.stockInPage)
            this.isShowStockInDetail = true;
        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            detailList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        printOrderHandler(params = {}){
            this.$confirm("是否导出盘盈明细数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                params.isSupplyFlag = 1
                exportDetailList(Object.assign(params, this.query)).then(res => {
                    const blob = new Blob([res.data]);
                    const fileName = '盘盈明细记录.xlsx';
                    const linkNode = document.createElement('a');

                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();

                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
            });
        },
        //主界面基本方法 end
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
