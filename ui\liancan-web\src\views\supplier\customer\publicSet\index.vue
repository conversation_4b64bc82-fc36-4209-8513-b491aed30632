<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="isPublic" slot-scope="{row}">
        <el-tag v-if="row.isPublic == '1'" type="success">公开</el-tag>
        <el-tag v-if="row.isPublic == '0'" type="danger">隐藏</el-tag>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, exportDataToExcel} from "@/api/supplier/supplierInventoryPublicSet";
import {mapGetters} from "vuex";
var DIC = {
    isUse: [{
        label: '正常',
        value: "1"
    },{
        label: '停用',
        value: "0"
    },],


}
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      searchForm: {

      },
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        selection: true,
        viewBtn:true,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
              dicUrl: "/api/service/rabbit-liancan/supplierInventoryPublicSet/all/listForEvaluationBidding?evaluationBiddingFlag=1",
            props: {
                label: "name",
                value: "id"
            },
            showClose: true,
            search: true,
          rules: [{
              required: true,
              message: "请选择供应商",
              trigger: "blur"
          }],
          },
          {
            label: "向其公开实时库存",
            prop: "isPublic",
              type: "radio",
              slot:true,
              search: true,
              dicData: [
                  {
                      label: '公开',
                      value: "1"
                  },
                  {
                      label: '隐藏',
                      value: "0"
                  },
              ],
              value: "0",
          },
          {
            label: "最后一次操作人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: true,
          },
          {
            label: "最后一次操作时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            hide: true,
          },
        ]
      },
    }
  },
  computed: {
    ...mapGetters(["permission", 'userInfo']),
    permissionList() {
      return {
      };
    },
    ids() {
      let ids = [];
      this.personnelSelectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },

  created() {
  },
  methods: {
    rowSave(row, loading, done) {
      row.type = "1";
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.type = "1";
      // this.query.companyId = "1553993875767554050";
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
      /////////////////////////////////////////
      //导出
      exportExcel(){
          exportDataToExcel(Object.assign(this.form)).then(res => {
              if (!res.data) {
                  return;
              }
              const blob = new Blob([res.data], {
                  type: "application/vnd.ms-excel",
              }); // 构造一个blob对象来处理数据，并设置文件类型
              const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
              const a = document.createElement("a"); //创建a标签
              a.style.display = "none";
              a.href = href; // 指定下载链接
              let fileName = res.headers["content-disposition"];
              fileName = fileName.split("=")[1];
              a.download = decodeURIComponent(fileName); //指定下载文件名
              a.click(); //触发下载
              URL.revokeObjectURL(a.href); //释放URL对象
          });
      },
  }
}
</script>
