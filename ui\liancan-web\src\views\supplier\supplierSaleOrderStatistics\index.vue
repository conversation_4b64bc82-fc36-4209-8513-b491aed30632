<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary"  icon="el-icon-download" size="small" @click="exportOrderData">导出</el-button>
<!--          <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>-->
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {saleOrderStatistical,saleOrderStatisticalExport} from "@/api/supplier/supplierSaleOrder";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        searchFrom:{},
        dialogVisible:false,
        dialogImageUrl:undefined,
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          menu:false,
          align: 'center',

          column: [
              {
                  label: "统计时间段",
                  prop: "queryRange",
                  type: "input",
              },
              {
                  label: "所属单位",
                  prop: "parentDeptId",
                  type: "select",
                  dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                  props: {
                      label: "deptName",
                      value:"id"
                  },
                  addDisplay: false,
                  editDisplay: false,
                  viewDisplay: false,
                  display:false,
                  // search: true,
              },
              {
                  label: "食堂名称",
                  prop: "deptId",
                  type: "select",
                  dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                  props: {
                      label: "deptName",
                      value:"id"
                  },
                  addDisplay: false,
                  editDisplay: false,
                  viewDisplay: false,
                  display:false,
                  // search: true,
              },
              {
                  label: "取消/拒单",
                  prop: "orderCancel",
                  type: "number",
              },
              {
                  label: "配送中",
                  prop: "orderProcess",
                  type: "number",
              },
              {
                  label: "配送中订单总金额",
                  prop: "orderProcessAmt",
                  type: "number",
              },
              {
                  label: "已送达",
                  prop: "orderArrive",
                  type: "number",
              },
              {
                  label: "已送达订单总金额",
                  prop: "orderArriveAmt",
                  type: "number",
              },
              {
                  label: "食堂已收",
                  prop: "orderReceive",
                  type: "number",
              },
              {
                  label: "食堂已收订单总金额",
                  prop: "orderReceiveAmt",
                  type: "number",
              },
            {
              label: "开始时间",
              prop: "startTime",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              hide: true,
              search: true,
              display: false,
              showColumn:false,
            },
            {
              label: "结束时间",
              prop: "endTime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              type: "date",
              hide: true,
              search: true,
              display: false,
              showColumn:false,
            },
          ]
        },
        showSummary: true,
        sumColumnList: [
          {
            name: 'supplierQuantity',
            type: 'sum'
          },
          {
            name: 'orderQuantity',
            type: 'sum'
          },
          {
            name: 'moneySum',
            type: 'sum'
          }
        ],
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    },
    methods: {
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchFrom = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
          saleOrderStatistical(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      exportOrderData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          BACKGROUND: 'rgba(0, 0, 0, 0.7)'
        });
        this.searchFrom.status = "1";
        saleOrderStatisticalExport(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '销售订单统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
