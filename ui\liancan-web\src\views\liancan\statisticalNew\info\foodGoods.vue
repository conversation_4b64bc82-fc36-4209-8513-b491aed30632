<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="small" icon="el-icon-view" @click="openDialog(scope.row)">查看
        </el-button>
      </template>
    </avue-crud>

    <el-dialog title="详情" :visible.sync="detailVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <span class="food-title">食材基本信息</span>
      <div class="food-view">
        <div  class="row-table">
          <div class="row-table-first">食材名称</div><div class="row-table-second">{{pageParams.name}}</div>
          <div class="row-table-first">品牌</div><div class="row-table-second">{{pageParams.brand}}</div>
          <div class="row-table-first">计量单位</div><div class="row-table-second">{{pageParams.unit}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">折合净重</div><div class="row-table-second">{{pageParams.netWeight}}</div>
          <div class="row-table-first">保质期</div><div class="row-table-second">{{ pageParams.shelfLife || '无' }}</div>
          <div class="row-table-first">规格</div><div class="row-table-second">无</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">生产许可</div><div class="row-table-second">{{ pageParams.production  || '无' }}</div>
          <div class="row-table-first">产地</div><div class="row-table-second">{{ pageParams.origin }}</div>
          <div class="row-table-first">质量等级</div><div class="row-table-second">{{ pageParams.qualityLevel }}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">质量标准</div><div class="row-table-second">{{ pageParams.qualityStandard  || '无' }}</div>
          <div class="row-table-first">贮存方法</div><div class="row-table-second">{{ pageParams.storage  || '无' }}</div>
          <div class="row-table-first">备注</div><div class="row-table-second">{{ pageParams.remark  || '无' }}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">状态</div><div class="row-table-second">{{ pageParams.status }}</div>
          <div class="row-table-first">新增时间：</div><div class="row-table-second" style="width: 56.2%;">{{ pageParams.createTime }}</div>
        </div>
      </div>
      <span class="food-title">食材供应商</span>
      <div style="margin-top: 10px;">
        <div>
          <table class="table-b">
            <tr class="table-h">
              <td>供应商名称</td>
              <td>合同有效期至</td>
              <td>合同文件</td>
              <td>状态</td>
            </tr>
            <tr v-for="type in agreementInfo">
              <td>{{ type.supplierName }}</td>
              <td>{{ type.inDate }}</td>
              <td v-if="type.agreement"><img :src="type.agreement" style="width: 100px;height: 80px;" @click="openPreviewByView(type.agreement)"></td>
              <td v-else></td>
              <td>{{ agreementStatus[type.status] }}</td>
            </tr>
          </table>
        </div>
      </div>
    </el-dialog>

    <el-dialog  append-to-body :visible.sync="imageDialog" @close="closeImage">
      <el-image  :src="fileUrl">
      </el-image>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getGoodsAgreementInfo} from "@/api/liancan/schoolGoods";
import {mapGetters} from "vuex";
let DIC = {
  status: [{
    label: '停用',
    value: "0"
  }, {
    label: '正常',
    value: "1"
  }],
};
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      pageParams: {},
      detailVisible: false,
      imageDialog: false,
      fileUrl: '',
      option: {
        // height:'auto',
        // calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        align: "center",
        column: [
          {
            label: "所属食堂",
            prop: "deptId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            hide: true,
          },
          {
            label: "商品类型",
            prop: "type",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品类型",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
            props: {
              label: "name",
              value: "id"
            },
            filterable: true,
            searchFilterable: true,
            cascaderItem: ['bidding'],
            editDisabled:true,
          },
          {
            label: "食材大类",
            prop: "biddingQuery",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品大类",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            filterable: true,
            searchFilterable: true,
            cascaderItem: ['biddingTypeIdQuery'],
            search: true,
            dicFlag: false,
            overHidden: true,
            display:false,
          },
          {
            label: "食材小类",
            prop: "biddingTypeIdQuery",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品小类",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            search: true,
            display:false,
          },
          {
            label: "食材名称",
            prop: "name",
            type: "input",
            rules: [{
              required: true,
              message: "请输入商品名称",
              trigger: "blur"
            }],
            search: true,
          },
          {
            label: "商品类型",
            prop: "typeQuery",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品类型",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
            props: {
              label: "name",
              value: "id"
            },
            filterable: true,
            searchFilterable: true,
            cascaderItem: ['biddingQuery'],
            search: true,
            display:false,
            hide: true,
          },
          {
            label: "商品大类",
            prop: "bidding",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品大类",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            filterable: true,
            searchFilterable: true,
            cascaderItem: ['biddingTypeId'],
            dicFlag: false,
            overHidden: true,
            hide:true,
          },
          {//4
            label: "商品小类",
            prop: "biddingTypeId",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品小类",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            hide:true,
          },
          {//6
            label: "品牌",
            prop: "brand",
            type: "input",
          },
          {//5
            label: "计量单位",
            prop: "unit",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请选择计量单位",
              trigger: "blur"
            }],
          },
          {//7
            label: "每一计量单位折合净重为",
            labelWidth:180,
            prop: "netWeight",
            type: "input",
            rules: [{
              required: false,
              message: "请输入净含量",
              trigger: "blur"
            }],
            addDisplay: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.status,
          },
          // {//8
          //   label: "保质期",
          //   prop: "shelfLife",
          //   type: "input",
          //   rules: [{
          //     required: false,
          //     message: "请输入保质期",
          //     trigger: "blur"
          //   }],
          //   addDisplay: true,
          //   hide:true,
          // },
          // {//9
          //   label: "生产许可",
          //   prop: "production",
          //   type: "input",
          //   rules: [{
          //     required: false,
          //     message: "请输入生产许可",
          //     trigger: "blur"
          //   }],
          //   addDisplay: true,
          //   hide:true,
          // },
          // {//10
          //   label: "产地",
          //   prop: "origin",
          //   type: "input",
          //   rules: [{
          //     required: false,
          //     message: "请输入产地",
          //     trigger: "blur"
          //   }],
          //   addDisplay: true,
          //   hide:true,
          // },
          // {//11
          //   label: "质量等级",
          //   prop: "qualityLevel",
          //   type: "input",
          //   rules: [{
          //     required: false,
          //     message: "请输入质量等级",
          //     trigger: "blur"
          //   }],
          //   addDisplay: true,
          //   hide:true,
          // },
          // {//12
          //   label: "质量标准",
          //   prop: "qualityStandard",
          //   type: "input",
          //   rules: [{
          //     required: false,
          //     message: "请输入质量标准",
          //     trigger: "blur"
          //   }],
          //   addDisplay: true,
          //   hide:true,
          // },
          // {
          //   label: "生产厂家",
          //   prop: "manufacturer",
          //   type: "input",
          //   rules: [{
          //     required: true,
          //     message: "请输入生产厂家",
          //     trigger: "blur"
          //   }],
          // },
          // {
          //   label: "商品图片",
          //   prop: "imgUrl",
          //   type: 'upload',
          //   listType: 'picture-img',
          //   propsHttp: {
          //     res: 'data',
          //     url: 'link'
          //   },
          //   tip: '只能上传jpg/png文件，大小不超过 500KB',
          //   action: '/api/service/rabbit-resource/oss/endpoint/put-file',
          //   rules: [{
          //     required: false,
          //     message: '请上传图片',
          //     trigger: 'click'
          //   }],
          //   slot: true,
          //   span: 24,
          //   hide: true,
          // },
        ]
      },
      data: [],
      agreementInfo: [],
      agreementStatus: ['正常', '失效'],
    }
  },
  created(){
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.schoolGoods_add, false),
        viewBtn: this.vaildData(this.permission.schoolGoods_view, false),
        delBtn: this.vaildData(this.permission.schoolGoods_delete, false),
        editBtn: this.vaildData(this.permission.schoolGoods_edit, false)
      };
    },
  },
  watch: {
    'form.type'() {
      let type = this.form.type;
      //食材
      if(type == "1314471996665397249" || type == "1366636175794667521"){
        // if(type == "1315498192049037313"){
        this.option.column.forEach(then=>{
          if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
            then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
            then.addDisplay = true
          }
        })
      }
      //燃料
      if(type == "1316561056074649602" || type == "1366636571036516354"){
        // if(type == "1315613115907416066"){
        this.option.column.forEach(then=>{
          if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
            then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
            then.addDisplay = false;
          }
        })
      }
    },
  },
  methods: {
    openDialog(row) {
      getGoodsAgreementInfo(row.id).then(res => {
        console.log('getGoodsAgreementInfo ============ ', res)
        if (res.data.code == 200) {
          this.agreementInfo = res.data.data
        }
        this.pageParams = row;
        this.detailVisible = true;
      });
    },
    beforeOpen(done, type) {
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.deptId = this.schoolId;
      this.query.type = '1314471996665397249';
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    openPreviewByView: function(item) {
      this.imageDialog = true;
      this.fileUrl = item;
    },
    closeImage() {
      this.imageDialog = false;
    }
  }
}
</script>

<style scoped>
.food-title {
  font-size: 18px;
  font-weight: bold;
  color: #000000;
}
.food-view {
  display: flex;
  flex-direction: column;
}
.food-row {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  margin: 10px 0;
}
.food-item {
  width: 50%;
}
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}

</style>
