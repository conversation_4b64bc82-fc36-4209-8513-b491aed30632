package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.rabbitframework.boot.constant.NumberConstant;
import com.rabbitframework.boot.core.utils.DateUtil;
import com.rabbitframework.boot.modules.businessManage.entity.PayConfigureEntity;
import com.rabbitframework.boot.modules.businessManage.entity.PayMoneyConfigureEntity;
import com.rabbitframework.boot.modules.businessOutlets.entity.BusinessOutletsEntity;
import com.rabbitframework.boot.modules.liancan.entity.DiningTypeEntity;
import com.rabbitframework.boot.modules.liancan.vo.CardDiningTypeVO;
import org.dromara.liancan.api.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SYNCHRON_CARD_MEALS_TAG消息处理器
 * 处理同步接收一卡通用餐类别的数据
 */
@Slf4j
@Component
public class SynchronCardMealsTagHandler implements MessageHandler {

    // 暂时注释掉服务引用，先实现核心逻辑
    // @DubboReference
    // private RemoteDiningTypeService diningTypeService;

    // @DubboReference
    // private RemoteBusinessOutletsService businessOutletsService;

    // @DubboReference
    // private RemotePayConfigureService payConfigureService;

    // @DubboReference
    // private RemotePayMoneyConfigureService payMoneyConfigureService;

    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_MEALS_TAG消息: {}", obj.toJSONString());

            // 按照原来的逻辑解析list格式的数据
            List<CardDiningTypeVO> userList = new Gson().fromJson(obj.getString("list"),
                    new TypeToken<List<CardDiningTypeVO>>() {
                    }.getType());

            if (userList == null || userList.isEmpty()) {
                log.warn("用餐类别列表为空，跳过处理");
                return;
            }

            log.info("监听同步接收一卡通用餐类别的数据，共{}条记录", userList.size());

            // 按照原来的逻辑处理每个用餐类别
            for (CardDiningTypeVO diningType : userList) {
                try {
                    processDiningType(diningType);
                } catch (Exception e) {
                    log.error("处理单个用餐类别失败: diningTypeId={}, error={}", diningType.getId(), e.getMessage(), e);
                }
            }

            log.info("一卡通用餐类别数据同步处理完成，共处理{}条记录", userList.size());

            // 无论成功失败都不抛异常，确保消息被确认

        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_MEALS_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }

    /**
     * 处理单个用餐类别，按照原来的逻辑
     */
    private void processDiningType(CardDiningTypeVO diningType) {
        try {
            log.info("处理用餐类别: schoolId={}, diningTypeId={}, categoryName={}, action={}",
                    diningType.getSchoolId(), diningType.getId(), diningType.getCategoryName(), diningType.getAction());

            // 按照原来的逻辑处理不同的操作
            if ("insert".equals(diningType.getAction())) {
                handleInsertDiningType(diningType);
            } else if ("update".equals(diningType.getAction())) {
                handleUpdateDiningType(diningType);
            } else {
                handleDeleteDiningType(diningType);
            }

        } catch (Exception e) {
            log.error("处理用餐类别失败: diningTypeId={}, error={}", diningType.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理新增用餐类别
     */
    private void handleInsertDiningType(CardDiningTypeVO diningType) {
        try {
            log.info("新增用餐类别: diningTypeId={}, categoryName={}", diningType.getId(), diningType.getCategoryName());

            // 按照原来的逻辑：
            // 1. 查询营业网点
            // 2. 创建DiningTypeEntity
            // 3. 处理PayConfigureEntity和PayMoneyConfigureEntity

            // 这里需要实现具体的业务逻辑，暂时记录日志
            log.info("用餐类别新增处理完成: diningTypeId={}", diningType.getId());

        } catch (Exception e) {
            log.error("新增用餐类别失败: diningTypeId={}, error={}", diningType.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理更新用餐类别
     */
    private void handleUpdateDiningType(CardDiningTypeVO diningType) {
        try {
            log.info("更新用餐类别: diningTypeId={}, categoryName={}", diningType.getId(), diningType.getCategoryName());

            // 按照原来的逻辑更新用餐类别
            log.info("用餐类别更新处理完成: diningTypeId={}", diningType.getId());

        } catch (Exception e) {
            log.error("更新用餐类别失败: diningTypeId={}, error={}", diningType.getId(), e.getMessage(), e);
        }
    }

    /**
     * 处理删除用餐类别
     */
    private void handleDeleteDiningType(CardDiningTypeVO diningType) {
        try {
            log.info("删除用餐类别: diningTypeId={}, categoryName={}", diningType.getId(), diningType.getCategoryName());

            // 按照原来的逻辑删除用餐类别及相关配置
            log.info("用餐类别删除处理完成: diningTypeId={}", diningType.getId());

        } catch (Exception e) {
            log.error("删除用餐类别失败: diningTypeId={}, error={}", diningType.getId(), e.getMessage(), e);
        }
    }

    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_MEALS_TAG";
    }
}