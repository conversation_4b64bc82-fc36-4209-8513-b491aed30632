package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONObject;
import org.dromara.liancan.api.RemoteSystemPersonnelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * SYNCHRON_CARD_MEALS_TAG消息处理器
 * 处理同步接收一卡通用餐类别的数据
 */
@Slf4j
@Component
public class SynchronCardMealsTagHandler implements MessageHandler {
    
    @DubboReference
    private RemoteSystemPersonnelService systemPersonnelService;
    
    // TODO: 添加更多需要的服务引用
    // @DubboReference
    // private IDiningTypeService diningTypeService;
    // 
    // @DubboReference  
    // private IBusinessOutletsService businessOutletsService;
    //
    // @DubboReference
    // private IPayConfigureService payConfigureService;
    //
    // @DubboReference
    // private IPayMoneyConfigureService payMoneyConfigureService;
    
    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_MEALS_TAG消息: {}", obj.toJSONString());
            
            // 解析消息参数
            String schoolId = obj.getString("school_id");
            String mealTypeId = obj.getString("meal_type_id");
            String mealTypeName = obj.getString("meal_type_name");
            String mealTypeCode = obj.getString("meal_type_code");
            String price = obj.getString("price");
            String startTime = obj.getString("start_time");
            String endTime = obj.getString("end_time");
            String status = obj.getString("status");
            
            log.info("监听同步接收一卡通用餐类别的数据: schoolId={}, mealTypeId={}, mealTypeName={}, price={}", 
                    schoolId, mealTypeId, mealTypeName, price);
            
            // 验证必要参数
            if (!StringUtils.hasText(schoolId)) {
                log.warn("学校ID为空，跳过处理");
                return;
            }
            
            if (!StringUtils.hasText(mealTypeId)) {
                log.warn("用餐类别ID为空，跳过处理");
                return;
            }
            
            // 处理用餐类别同步逻辑
            handleMealTypeSync(schoolId, mealTypeId, mealTypeName, mealTypeCode, price, startTime, endTime, status);
            
            // 处理相关的业务配置同步
            handlePayConfigSync(schoolId, mealTypeId, price);
            
            // 处理营业网点配置
            handleBusinessOutletsSync(schoolId, mealTypeId);
            
            log.info("一卡通用餐类别数据同步处理完成: schoolId={}, mealTypeId={}", schoolId, mealTypeId);
            
            // 无论成功失败都不抛异常，确保消息被确认
            
        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_MEALS_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }
    
    /**
     * 处理用餐类别同步
     */
    private void handleMealTypeSync(String schoolId, String mealTypeId, String mealTypeName, 
                                  String mealTypeCode, String price, String startTime, String endTime, String status) {
        try {
            log.info("处理用餐类别同步: schoolId={}, mealTypeId={}, mealTypeName={}", schoolId, mealTypeId, mealTypeName);
            
            // TODO: 实现用餐类别数据同步逻辑
            // 1. 查询是否已存在该用餐类别
            // 2. 如果不存在则新增，如果存在则更新
            // 3. 保存或更新用餐类别信息
            
            // 示例框架代码（需要根据实际的服务接口实现）:
            // List<DiningTypeEntity> existingTypes = diningTypeService.list(
            //     Wrappers.<DiningTypeEntity>query().lambda()
            //         .eq(DiningTypeEntity::getCompanyId, schoolId)
            //         .eq(DiningTypeEntity::getMealTypeId, mealTypeId)
            // );
            //
            // if (existingTypes.isEmpty()) {
            //     // 新增用餐类别
            //     DiningTypeEntity newMealType = new DiningTypeEntity();
            //     newMealType.setCompanyId(Long.parseLong(schoolId));
            //     newMealType.setMealTypeId(mealTypeId);
            //     newMealType.setMealTypeName(mealTypeName);
            //     newMealType.setMealTypeCode(mealTypeCode);
            //     newMealType.setPrice(new BigDecimal(price));
            //     newMealType.setStartTime(startTime);
            //     newMealType.setEndTime(endTime);
            //     newMealType.setStatus(status);
            //     newMealType.setCreateTime(DateUtil.now());
            //     diningTypeService.save(newMealType);
            // } else {
            //     // 更新现有用餐类别
            //     DiningTypeEntity existingType = existingTypes.get(0);
            //     existingType.setMealTypeName(mealTypeName);
            //     existingType.setPrice(new BigDecimal(price));
            //     existingType.setStartTime(startTime);
            //     existingType.setEndTime(endTime);
            //     existingType.setStatus(status);
            //     diningTypeService.updateById(existingType);
            // }
            
            log.info("用餐类别同步处理完成: mealTypeId={}", mealTypeId);
            
        } catch (Exception e) {
            log.error("处理用餐类别同步失败: mealTypeId={}, error={}", mealTypeId, e.getMessage(), e);
        }
    }
    
    /**
     * 处理支付配置同步
     */
    private void handlePayConfigSync(String schoolId, String mealTypeId, String price) {
        try {
            log.info("处理支付配置同步: schoolId={}, mealTypeId={}, price={}", schoolId, mealTypeId, price);
            
            // TODO: 实现支付配置同步逻辑
            // 1. 同步PayConfigureEntity数据
            // 2. 同步PayMoneyConfigureEntity数据
            
            log.info("支付配置同步处理完成: mealTypeId={}", mealTypeId);
            
        } catch (Exception e) {
            log.error("处理支付配置同步失败: mealTypeId={}, error={}", mealTypeId, e.getMessage(), e);
        }
    }
    
    /**
     * 处理营业网点配置同步
     */
    private void handleBusinessOutletsSync(String schoolId, String mealTypeId) {
        try {
            log.info("处理营业网点配置同步: schoolId={}, mealTypeId={}", schoolId, mealTypeId);
            
            // TODO: 实现营业网点配置同步逻辑
            
            log.info("营业网点配置同步处理完成: mealTypeId={}", mealTypeId);
            
        } catch (Exception e) {
            log.error("处理营业网点配置同步失败: mealTypeId={}, error={}", mealTypeId, e.getMessage(), e);
        }
    }
    
    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_MEALS_TAG";
    }
}