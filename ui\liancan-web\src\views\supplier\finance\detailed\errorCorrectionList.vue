<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.error_correction_details_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportDetailsData">导出</el-button>
      </template>
      <template slot="menuLeft">
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >统计时间：{{this.startDate}} - {{this.endDate}}
        </a>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-setting"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.ifVoucher == 0"
                   @click.stop="createVoucher(row)">生成凭证
        </el-button>
      </template>
      <template slot="menuRight">
        <el-button class="filter-item" size="small" type="primary" icon="el-icon-finished" @click="genVoucherBatch">一键生成凭证</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove,exportDetailsData} from "@/api/businessManage/errorCorrectionDetails";
import {createErrorCorrectionVoucher} from "@/api/supplier/finance/financialVoucher";
import {
  getDeptTree2
} from "@/api/system/dept";
import {mapGetters} from "vuex";
import {getErrorCorrectionDetailsList} from "@/api/supplier/finance/financialBill";
const DIC = {
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
  ERRORSTATUS:[
    {
      label: '少收钱',
      value: "1"
    },
    {
      label: '多收钱',
      value: "2"
    }
  ],
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      searchForm:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      startDate:undefined,
      endDate:undefined,
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: false,
        menu: true,
        addBtn: false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "单位",
            prop: "unitId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search:true,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }]
          },
          {
            label: "学校",
            prop: "schoolId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search:true,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }]
          },
          // {
          //   label: "所属食堂",
          //   prop: "canteenId",
          //   type: "tree",
          //   //  multiple: true,
          //   dicData: [],
          //   props: {
          //     label: "title"
          //   },
          //   search:true,
          //   hide:true,
          // },
          // {
          //   label: "所属食堂",
          //   prop: "canteenName",
          //   type: "input",
          //   width:100,
          //   sortable:true,
          // },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search:true,
            sortable:true,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            sortable:true,
          },
          {
            label: "编码",
            prop: "financialCode",
            type: "input",
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            width:100,
            addDisplay:false,
            editDisplay:false,
            viewDisplay:false,
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          {
            label: "食堂",
            prop: "canteenName",
            type: "input",
            overHidden: true,
            sortable:true,
          },
          {
            label: "食堂",
            prop: "canteenId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
          },
          {
            label: "消费单号",
            prop: "consumNo",
            type: "input",
            sortable:true,
          },
          {
            label: "消费类型",
            prop: "type",
            type: "select",
            search:true,
            dicData: [
              {
                label: "自由消费",
                value: "0"
              },
              {
                label: "统缴餐",
                value: "1"
              }
            ],
          },
          {
            label: "错误类型",
            prop: "status",
            type: "radio",
            dicData: DIC.ERRORSTATUS,
            search: true,
            sortable:true,
            rules: [{
              required: true,
              message: "请选择错误类型",
              trigger: "blur"
            }],
          },
          {
            label: "开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          /*            {
                        label: '纠错时间',
                        prop: 'orderDate',
                        type:'datetime',
                        searchSpan:6,
                        searchRange:true,
                        search:true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        hide:true,
                      },*/
          {
            label: "纠错前下单金额",
            prop: "orderMoney",
            type: "number",
            precision:2,
            width:120,
            sortable:true,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
          },
          {
            label: "纠错后实收金额",
            prop: "paidMoney",
            type: "number",
            precision:2,
            width:120,
            sortable:true,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
          },
          {
            label: "纠错差额",
            prop: "differenceMoney",
            type: "number",
            precision:2,
            sortable:true,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
          },
          {
            label: "纠错人",
            prop: "errorUserName",
            type: "input",
            search: false,
            sortable:true,
          },
          {
            label: "纠错时间",
            prop: "createTime",
            type: "date",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            rules: [{
              required: true,
              message: "请输入通知日期",
              trigger: "click"
            }],
            sortable:true,
          },
          {
            label: "纠错单号",
            prop: "errorCorrectionNo",
            type: "input",
            sortable:true,
          },
          {
            label: "纠错备注",
            prop: "errorRemark",
            type: "input",
            overHidden: true,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.error_correction_details_add, false),
        viewBtn: this.vaildData(this.permission.error_correction_details_view, false),
        delBtn: this.vaildData(this.permission.error_correction_details_delete, false),
        editBtn: this.vaildData(this.permission.error_correction_details_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    // getDeptTree2(this.schoolId,null).then(res => {
    //   const index = this.$refs.crud.findColumnIndex("canteenId");
    //   this.option.column[index].dicData = res.data.data;
    // });
    this.getMonthStartEnd();
    this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    if (this.userInfo.userType === 'school'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
      this.option.printBtn = true;
      this.option.column[2].search = false;
      this.option.column[2].hide = true;
    }else if (this.userInfo.userType === 'education'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;

      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.column[8].search = false;
    }else if (this.userInfo.userType === 'jiWei'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;

      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.column[8].search = false;
    }else if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[4].hide = true;
      this.option.column[3].search = false;
      this.option.column[1].hide = true;
      this.option.printBtn = true;
      this.option.column[2].search = false;
      this.option.column[2].hide = true;
    }
  },
  methods: {
    genVoucherBatch() {
      this.$message({
        type: "warning",
        message: "功能开发中",
      });
    },
    getMonthStartEnd(){
      var startDate = new Date().getFullYear()+' 01-01'
      var endDate = new Date().getFullYear()+' 12-31'
      this.startDate = startDate;
      this.endDate = endDate;
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.searchForm = {};
      this.getMonthStartEnd();
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
        this.getMonthStartEnd();
      }else {
        this.startDate = params.startDate;
        this.endDate = params.endDate;
      }
      params.beginDate = params.startDate;
      params.endDate = params.endDate;
      this.searchForm.beginDate = params.startDate;
      this.searchForm.endDate = params.endDate;
      this.query = params;
      this.searchForm = params;
      this.page.currentPage = 1
      /*        if (params.orderDate != '' && params.orderDate != null && params.orderDate != undefined) {
                params.beginDate = params.orderDate[0];
                params.endDate = params.orderDate[1];
                this.startDate = params.orderDate[0];
                this.endDate = params.orderDate[1];
                this.searchForm.beginDate = params.orderDate[0];
                this.searchForm.endDate = params.orderDate[1];
              }*/
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(params.canteenId == null && params.canteenId){
        if(this.schoolId != null && this.schoolId != ''){
          params.unitId = this.schoolId;
        }
      }
      getErrorCorrectionDetailsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getErrorCorrectionDetailsList ======== ', res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    exportDetailsData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出下方明细数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportDetailsData(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '消费纠错明细报表.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    createVoucher(row) {
      this.$confirm("确定生成凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        let param = {
          billCode: row.financialCode,
          billNo: row.id,
          canteenId: row.canteenId
        }
        return createErrorCorrectionVoucher(param);
      }).then(
        () => {
          this.loading = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        },
        error => {
          this.loading = false;
          this.$message.error(error);
        }
      );
    }
  }
};
</script>

<style>
</style>
