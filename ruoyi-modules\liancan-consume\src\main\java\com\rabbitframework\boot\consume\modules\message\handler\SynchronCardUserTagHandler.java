package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.dromara.liancan.api.RemoteSystemPersonnelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.concurrent.*;

/**
 * SYNCHRON_CARD_USER_TAG消息处理器
 * 处理同步接收一卡通用户的数据（包含多线程批处理）
 */
@Slf4j
@Component
public class SynchronCardUserTagHandler implements MessageHandler {
    
    @DubboReference
    private RemoteSystemPersonnelService systemPersonnelService;
    
    // 线程池配置
    private static final int CORE_POOL_SIZE = 5;
    private static final int MAXIMUM_POOL_SIZE = 10;
    private static final long KEEP_ALIVE_TIME = 60L;
    private static final int QUEUE_CAPACITY = 100;
    
    // 创建线程池
    private final ThreadPoolExecutor executor = new ThreadPoolExecutor(
        CORE_POOL_SIZE,
        MAXIMUM_POOL_SIZE,
        KEEP_ALIVE_TIME,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(QUEUE_CAPACITY),
        new ThreadFactory() {
            private int counter = 0;
            @Override
            public Thread newThread(Runnable r) {
                return new Thread(r, "CardUserSync-" + (++counter));
            }
        },
        new ThreadPoolExecutor.CallerRunsPolicy()
    );
    
    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_USER_TAG消息: {}", obj.toJSONString());
            
            // 解析消息参数
            String schoolId = obj.getString("school_id");
            String batchId = obj.getString("batch_id");
            String totalCount = obj.getString("total_count");
            String currentBatch = obj.getString("current_batch");
            JSONArray userDataArray = obj.getJSONArray("user_data");
            
            log.info("监听同步接收一卡通用户的数据: schoolId={}, batchId={}, totalCount={}, currentBatch={}, userCount={}", 
                    schoolId, batchId, totalCount, currentBatch, userDataArray != null ? userDataArray.size() : 0);
            
            // 验证必要参数
            if (!StringUtils.hasText(schoolId)) {
                log.warn("学校ID为空，跳过处理");
                return;
            }
            
            if (userDataArray == null || userDataArray.isEmpty()) {
                log.warn("用户数据为空，跳过处理");
                return;
            }
            
            // 使用多线程批处理用户数据
            processBatchUserData(schoolId, batchId, userDataArray);
            
            log.info("一卡通用户数据同步处理完成: schoolId={}, batchId={}, processedCount={}", 
                    schoolId, batchId, userDataArray.size());
            
            // 无论成功失败都不抛异常，确保消息被确认
            
        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_USER_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }
    
    /**
     * 批处理用户数据（多线程）
     */
    private void processBatchUserData(String schoolId, String batchId, JSONArray userDataArray) {
        try {
            // 将用户数据分组，每组处理一定数量的用户
            int batchSize = 50; // 每批处理50个用户
            int totalUsers = userDataArray.size();
            int batchCount = (totalUsers + batchSize - 1) / batchSize;
            
            log.info("开始多线程批处理用户数据: totalUsers={}, batchSize={}, batchCount={}", totalUsers, batchSize, batchCount);
            
            // 创建CountDownLatch来等待所有批次完成
            CountDownLatch latch = new CountDownLatch(batchCount);
            
            // 提交批处理任务
            for (int i = 0; i < batchCount; i++) {
                final int batchIndex = i;
                final int startIndex = i * batchSize;
                final int endIndex = Math.min(startIndex + batchSize, totalUsers);
                
                executor.submit(new BatchCardSynchronTask(
                    schoolId, batchId, batchIndex, 
                    userDataArray.subList(startIndex, endIndex),
                    latch, systemPersonnelService
                ));
            }
            
            // 等待所有批次完成，最多等待5分钟
            boolean completed = latch.await(5, TimeUnit.MINUTES);
            if (!completed) {
                log.warn("批处理用户数据超时: schoolId={}, batchId={}", schoolId, batchId);
            } else {
                log.info("批处理用户数据完成: schoolId={}, batchId={}, processedBatches={}", schoolId, batchId, batchCount);
            }
            
        } catch (Exception e) {
            log.error("批处理用户数据失败: schoolId={}, batchId={}, error={}", schoolId, batchId, e.getMessage(), e);
        }
    }
    
    /**
     * 批量卡同步任务（内部类）
     */
    private static class BatchCardSynchronTask implements Runnable {
        private final String schoolId;
        private final String batchId;
        private final int batchIndex;
        private final List<Object> userDataBatch;
        private final CountDownLatch latch;
        private final RemoteSystemPersonnelService systemPersonnelService;
        
        public BatchCardSynchronTask(String schoolId, String batchId, int batchIndex, 
                                   List<Object> userDataBatch, CountDownLatch latch, 
                                   RemoteSystemPersonnelService systemPersonnelService) {
            this.schoolId = schoolId;
            this.batchId = batchId;
            this.batchIndex = batchIndex;
            this.userDataBatch = userDataBatch;
            this.latch = latch;
            this.systemPersonnelService = systemPersonnelService;
        }
        
        @Override
        public void run() {
            try {
                log.info("开始处理用户批次: schoolId={}, batchId={}, batchIndex={}, userCount={}", 
                        schoolId, batchId, batchIndex, userDataBatch.size());
                
                int successCount = 0;
                int failCount = 0;
                
                // 处理批次中的每个用户
                for (Object userDataObj : userDataBatch) {
                    try {
                        JSONObject userData = (JSONObject) userDataObj;
                        boolean result = processUser(schoolId, userData);
                        if (result) {
                            successCount++;
                        } else {
                            failCount++;
                        }
                    } catch (Exception e) {
                        failCount++;
                        log.error("处理单个用户失败: batchIndex={}, error={}", batchIndex, e.getMessage());
                    }
                }
                
                log.info("用户批次处理完成: schoolId={}, batchIndex={}, successCount={}, failCount={}", 
                        schoolId, batchIndex, successCount, failCount);
                
            } catch (Exception e) {
                log.error("批次处理失败: schoolId={}, batchIndex={}, error={}", schoolId, batchIndex, e.getMessage(), e);
            } finally {
                latch.countDown();
            }
        }
        
        /**
         * 处理单个用户数据
         */
        private boolean processUser(String schoolId, JSONObject userData) {
            try {
                String cardUserId = userData.getString("card_user_id");
                String userName = userData.getString("user_name");
                String userType = userData.getString("user_type");
                String cardNo = userData.getString("card_no");
                String deptId = userData.getString("dept_id");
                String status = userData.getString("status");
                
                // TODO: 实现具体的用户数据处理逻辑
                // 1. 查询用户是否已存在
                // 2. 如果不存在则新增，如果存在则更新
                // 3. 同步用户的一卡通信息
                
                // 示例框架代码（需要根据实际的服务接口实现）:
                // String existingUser = systemPersonnelService.getPersonnelByCardUserId(cardUserId, Long.parseLong(schoolId));
                // if (existingUser == null || "[]".equals(existingUser)) {
                //     // 新增用户
                //     String newUserJson = buildUserJson(schoolId, cardUserId, userName, userType, cardNo, deptId, status);
                //     return systemPersonnelService.savePersonnel(newUserJson);
                // } else {
                //     // 更新用户
                //     String updateUserJson = buildUpdateUserJson(existingUser, cardUserId, userName, userType, cardNo, deptId, status);
                //     return systemPersonnelService.updatePersonnel(updateUserJson);
                // }
                
                // 临时返回true，表示处理成功
                return true;
                
            } catch (Exception e) {
                log.error("处理单个用户数据失败: cardUserId={}, error={}", 
                        userData.getString("card_user_id"), e.getMessage());
                return false;
            }
        }
    }
    
    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_USER_TAG";
    }
    
    /**
     * 销毁方法，关闭线程池
     */
    public void destroy() {
        if (executor != null && !executor.isShutdown()) {
            executor.shutdown();
            try {
                if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }
}