package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.rabbitframework.boot.modules.personnel.entity.SystemPersonnelEntity;
import com.rabbitframework.boot.modules.personnel.vo.CardUserVO;
import org.dromara.liancan.api.RemoteSystemPersonnelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SYNCHRON_CARD_USER_TAG消息处理器
 * 处理同步接收一卡通用户的数据（包含多线程批处理）
 */
@Slf4j
@Component
public class SynchronCardUserTagHandler implements MessageHandler {

    @DubboReference
    private RemoteSystemPersonnelService systemPersonnelService;

    // 密码编码器
    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_USER_TAG消息: {}", obj.toJSONString());

            // 按照原来的逻辑解析list格式的数据
            List<CardUserVO> userList = new Gson().fromJson(obj.getString("list"),
                    new TypeToken<List<CardUserVO>>() {
                    }.getType());

            if (userList == null || userList.isEmpty()) {
                log.warn("用户列表为空，跳过处理");
                return;
            }

            Long companyId = userList.get(0).getSchoolId();
            log.info("监听同步接收一卡通用户的数据，共{}条记录，学校ID: {}", userList.size(), companyId);

            // 查询现有的人员数据
            List<SystemPersonnelEntity> systemPersonnelEntityList = systemPersonnelService
                    .list(Wrappers.<SystemPersonnelEntity>query().lambda()
                            .eq(SystemPersonnelEntity::getIsDel, 0)
                            .eq(SystemPersonnelEntity::getStatus, "1")
                            .eq(SystemPersonnelEntity::getCompanyId, companyId));

            Map<Long, SystemPersonnelEntity> sMap = new HashMap<>();
            if (!systemPersonnelEntityList.isEmpty()) {
                sMap = systemPersonnelEntityList.stream()
                        .collect(Collectors.toMap(SystemPersonnelEntity::getId, Function.identity()));
            }

            // 按照原来的逻辑使用多线程批处理
            processBatchUserDataOriginal(userList, companyId, sMap);

            log.info("一卡通用户数据同步处理完成，共处理{}条记录", userList.size());

            // 无论成功失败都不抛异常，确保消息被确认

        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_USER_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }

    /**
     * 按照原来的逻辑批处理用户数据
     */
    private void processBatchUserDataOriginal(List<CardUserVO> userList, Long companyId,
            Map<Long, SystemPersonnelEntity> sMap) {
        try {
            log.info("开始处理用户数据同步: companyId={}, userCount={}", companyId, userList.size());

            // 按照原来的逻辑处理用户数据
            List<SystemPersonnelEntity> resultList = processUserList(userList, companyId, sMap);

            // 批量保存或更新
            if (!resultList.isEmpty()) {
                systemPersonnelService.saveOrUpdateBatch(resultList);
                log.info("成功保存用户数据: count={}", resultList.size());
            }

        } catch (Exception e) {
            log.error("批处理用户数据失败: companyId={}, error={}", companyId, e.getMessage(), e);
        }
    }

    /**
     * 处理用户列表，按照原来BatchCardSynchronThread的逻辑
     */
    private List<SystemPersonnelEntity> processUserList(List<CardUserVO> userList, Long companyId,
            Map<Long, SystemPersonnelEntity> sMap) {
        List<Long> userIds = new ArrayList<>();
        LinkedList<SystemPersonnelEntity> list = new LinkedList<>();

        userList.forEach(user -> {
            SystemPersonnelEntity systemPersonnelEntity = new SystemPersonnelEntity();
            SystemPersonnelEntity existingEntity = sMap.get(user.getId());

            if (user.getAction().equals("insert") || user.getAction().equals("update")) {
                if (!userIds.contains(user.getId())) {
                    systemPersonnelEntity.setId(user.getId());
                    systemPersonnelEntity.setUserName(user.getName());

                    // 设置性别
                    if (user.getSex() != null) {
                        if (user.getSex().equals(0)) {
                            systemPersonnelEntity.setSex("1"); // 男
                        } else {
                            systemPersonnelEntity.setSex("2"); // 女
                        }
                    }

                    // 设置密码
                    if (existingEntity != null) {
                        if (existingEntity.getPassword() == null || existingEntity.getPassword().isEmpty()) {
                            systemPersonnelEntity.setPassword(ENCODER.encode("123456"));
                        }
                    } else {
                        systemPersonnelEntity.setPassword(ENCODER.encode("123456"));
                    }

                    // 设置学号/工号
                    if (user.getIdNumber() != null && !user.getIdNumber().isEmpty()) {
                        systemPersonnelEntity.setStudentJobNo(user.getIdNumber());
                    }

                    // 设置部门ID
                    if (user.getDeptId() != null) {
                        systemPersonnelEntity.setDeptId(user.getDeptId().toString());
                    }

                    // 设置人员类型
                    if ("student".equals(user.getUserType())) {
                        systemPersonnelEntity.setPersonnelType("1"); // 学生
                    } else {
                        systemPersonnelEntity.setPersonnelType("2"); // 老师
                    }

                    systemPersonnelEntity.setCompanyId(user.getSchoolId());

                    // 设置用餐类型
                    if (user.getCategoryId() != null) {
                        systemPersonnelEntity.setMealsType(user.getCategoryId());
                    }

                    systemPersonnelEntity.setStatus("1"); // 启用状态
                    systemPersonnelEntity.setCreateTime(new Date());
                    systemPersonnelEntity.setUpdateTime(new Date());
                    userIds.add(user.getId());
                    list.add(systemPersonnelEntity);
                }
            } else {
                // 删除操作
                systemPersonnelEntity.setId(user.getId());
                systemPersonnelEntity.setUserName(user.getName());
                systemPersonnelEntity.setCompanyId(user.getSchoolId());
                systemPersonnelEntity.setStatus("2"); // 禁用状态
                systemPersonnelEntity.setUpdateTime(new Date());
                list.add(systemPersonnelEntity);
            }
        });

        return list;
    }

    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_USER_TAG";
    }

}