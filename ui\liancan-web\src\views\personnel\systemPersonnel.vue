<template>
  <basic-container>
    <div>
      <div class="all-mess">
        <div class="mess-header">
          <div
            :class="{acitve:activeIdx==index}"
            v-for="(item,index) in messList"
            :key="index"
            @click="menuClick(index)"
          >
            {{item}}
          </div>
        </div>
        <div class="mess-content" v-if="activeIdx == 0">
          <avue-crud :option="option"
                     :table-loading="loading"
                     :data="data"
                     :page="page"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="form"
                     ref="crud"
                     @row-update="rowUpdate"
                     @row-save="rowSave"
                     @search-change="searchChange"
                     @search-reset="searchReset"
                     @selection-change="selectionChange"
                     @current-change="currentChange"
                     @size-change="sizeChange"
                     @on-load="onLoad">
            <template slot="menuLeft">
              <el-button type=""
                         size="small"
                         icon="el-icon-delete"
                         plain
                         v-if="permission.system_personnel_delete && this.modeType2 != '1' && this.modeType3 == '1'"
                         @click="handleDelete">删 除
              </el-button>
            </template>
            <template slot="menu" slot-scope="{row}">
              <el-button   v-if="permission.system_personnel_delete && modeType2 != '1' && modeType3 == '1'" size="mini" icon="el-icon-delete" type="text" @click="delPersonnel(row)">删除
              </el-button>
              <el-button v-if="permission.system_personnel_edit && row.balanceStatus == '1' && row.modeType != '3'" size="mini" icon="el-icon-edit" type="text" @click="editPersonnel(row)">编辑
              </el-button>
              <el-button v-if="permission.system_personnel_edit && row.balanceStatus == '0' && row.modeType != '3'" size="mini" icon="el-icon-edit" type="text" @click="editPersonnel2(row)">编辑
              </el-button>
              <el-button v-if="permission.system_personnel_reset_password" size="mini" icon="el-icon-refresh" type="text" @click="reset(row)">重置密码
              </el-button>
<!--              <el-button v-if="row.cardStatus == '0' && row.modeType != '3'" size="mini" icon="el-icon-error" type="text" @click="reportTheLoss(row)">挂失-->
<!--              </el-button>-->
<!--              <el-button v-if="row.cardStatus == '1' && row.modeType != '3'" size="mini" icon="el-icon-success" type="text" @click="uncoupling(row)">解挂-->
<!--              </el-button>-->
            </template>
            <template slot="cardStatus" slot-scope="{row}">
              <el-tag v-if="row.cardStatus == '0'" size="medium" type="success">未开卡</el-tag>
              <el-tag v-if="row.cardStatus == '1'" size="medium" type="error">正常</el-tag>
              <el-tag v-if="row.cardStatus == '2'" size="medium" type="error">已挂失</el-tag>
              <el-tag v-if="row.cardStatus == '3'" size="medium" type="error">异常</el-tag>
              <el-tag v-if="row.cardStatus == '4'" size="medium" type="error">过期</el-tag>
            </template>
            <template slot="menuLeft">
              <!--        <el-button class="filter-item" size="small" type="warning" @click="patchReset(row)">批量重置密码
                      </el-button>-->
              <el-button :disabled = "isShowTea" v-if="permission.system_personnel_synchron_teacher && this.agencyType == '0' && this.modeType == '2'" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="synchronSchoolTeacher">{{isShowTea == true?numberTea:'同步云校老师数据'}}</el-button>
              <el-button :disabled = "isShowStu" v-if="permission.system_personnel_synchron_student && this.agencyType == '0' && this.modeType == '2'" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="synchronSchoolStudent">{{isShowStu == true?numberStu:'同步云校学生数据'}}</el-button>
              <el-button v-if="permission.system_personnel_import && this.modeType2 != '1'" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="downPersonnelData">导入</el-button>
              <el-button v-if="permission.system_personnel_export" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportPersonnelData">导出</el-button>
              <el-button v-if="permission.system_personnel_batch_editor && this.modeType4 == '1'" class="filter-item" size="small" type="warning" icon="el-icon-edit" @click="batchEditPersonnel">批量编辑</el-button>
              <el-button v-if="permission.system_personnel_batch_reset_password" class="filter-item" size="small" type="warning" icon="el-icon-refresh" @click="batchReset">批量重置密码</el-button>
              <el-button v-if="permission.system_personnel_batch_editor && this.modeType4 == '1'" class="filter-item" size="small" type="warning" icon="el-icon-edit" @click="importDialogShow">批量编辑用餐类别导入</el-button>
              <el-button v-if="permission.system_personnel_batch_editor && this.modeType4 == '1'" class="filter-item" size="small" type="danger" icon="el-icon-edit" @click="clickSynchronAmount">同步金额</el-button>
              <el-button :disabled = "isShowCard" v-if="isShow" class="filter-item" size="small" type="warning" icon="el-icon-download" @click="synchronCard">{{isShowCard == true?numberCard:'同步一卡通用户数据'}}</el-button>
            </template>
            <template slot="avatar" slot-scope="{row}">
              <el-image  style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
            </template>
          </avue-crud>
        </div>
        <div class="mess-content" v-if="activeIdx == 1">

          <avue-crud :option="option"
                     :table-loading="loading"
                     :data="data"
                     :page="page"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="form"
                     ref="crud"
                     @row-update="rowUpdate"
                     @row-save="rowSave"
                     @search-change="searchChange"
                     @search-reset="searchReset"
                     @selection-change="selectionChange"
                     @current-change="currentChange"
                     @size-change="sizeChange"
                     @on-load="onLoad">
            <template slot="menu" slot-scope="{row}">
              <el-button v-if="permission.system_personnel_edit && row.balanceStatus == '1' && row.modeType != '3'" size="mini" icon="el-icon-edit" type="text" @click="editPersonnel(row)">编辑
              </el-button>
              <el-button v-if="permission.system_personnel_edit && row.balanceStatus == '0' && row.modeType != '3'" size="mini" icon="el-icon-edit" type="text" @click="editPersonnel2(row)">编辑
              </el-button>
            </template>
            <template slot="menuLeft">
              <el-button v-if="permission.system_personnel_export" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportPersonnelData">导出</el-button>
              <el-button v-if="permission.system_personnel_batch_editor && this.modeType4 == '1'" class="filter-item" size="small" type="warning" icon="el-icon-edit" @click="batchEditPersonnel">批量编辑</el-button>
              <a style="color: red" type="danger"
                 size="small"
                 icon="el-icon-delete"
                 plain
              >说明：此类人员虽然是在本食堂的服务范围之内，但其用餐类别可能未设置（空白），或者是其他食堂的用餐类别，以致于不能在本食堂中消费，需将其用餐类别更改为本食堂的用餐类别
              </a>
            </template>
          </avue-crud>
        </div>
      </div>
    </div>
    <el-dialog title="批量编辑" :visible.sync="batchEditVisible" :append-to-body="true" @close="batchEditClose" width="60%">
      <avue-form ref="editForm" :option="editOption" v-model="editForm" @submit="batchUpdatePersonnel">
        <template slot-scope="scope" slot="urlList">
          <div><span style="color: red;">重要提示：批量编辑操作会同时更改所选人员的相关信息，风险较高，请务必认真仔细操作，避免影响人员用餐。</span></div>
        </template>
      </avue-form>
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <!-- 同步云校数据-->
    <el-dialog :visible.sync="schoolVisible" @close="schoolVisible=false" width="30%" height="40%" :append-to-body="true">
      <avue-form ref="schoolForm" :option="schoolOption" v-model="schoolForm" @submit="batchUpdatePersonnel">

      </avue-form>
    </el-dialog>
    <!-- 添加用户数据-->
    <el-dialog title="编辑" :visible.sync="editditVisible" :append-to-body="true" @close="batchEditClose" width="60%">
      <avue-form ref="editPersonnelForm" :option="editoption" v-model="editPersonnelForm" @submit="editPersonnelDate"> <template slot-scope="scope" slot="menuForm">
        <el-button icon="el-icon-delete" @click="closeData1">清空</el-button>
      </template>
        <template slot-scope="scope" slot="urlList">
          <div>
            <!--            <p style="line-height:0; margin-bottom:5px;"><span class="&#45;&#45;mb&#45;&#45;rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:20px; text-decoration:none;">1、如果本人员目前进行统缴餐用餐，那么</span><span class="&#45;&#45;mb&#45;&#45;rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:20px; text-decoration:none;">更改用餐类别后，该人员将从明天开始，按照更改后的用餐类别进行统缴用餐，请认真确认后再更改用餐类别。</span></p>-->
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:20px; text-decoration:none;letter-spacing:5px">【用餐类别变更说明】</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:45px; text-decoration:none;letter-spacing:5px">1、变更用餐类别将直接改变当前人员的每日可用餐次，请务必按照实际需要，谨慎更改。</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:45px; text-decoration:none;letter-spacing:5px">2、更改用餐类别后，该人员将从当天最近一个餐次开始，按照更改后的用餐类别进行统缴餐用餐。例如，在上午9点更改用餐类别，该人员将从当天中餐开始，按照更改后的用餐类别进行统缴餐用餐。</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:45px; text-decoration:none;letter-spacing:5px">3、更改用餐类别后，如果可用餐次比更改前的要多，每日餐费将增加，比如从原本的早中餐类别（只吃早餐、中餐）更改为早中晚餐类别（早餐、中餐、晚餐都吃），需提醒该人员及时充值，避免发生餐费不足导致无法用餐的情况。</span></p>
          </div>
        </template>
      </avue-form>
    </el-dialog>
    <!-- 添加用户数据-->
    <el-dialog title="编辑" :visible.sync="editditVisible2" :append-to-body="true" @close="batchEditClose" width="60%">
      <avue-form ref="editPersonnelForm2" :option="editoption2" v-model="editPersonnelForm2" @submit="editPersonnelDate">
        <template slot-scope="scope" slot="menuForm">
          <el-button icon="el-icon-delete" @click="closeData2">清空</el-button>
        </template>
        <template slot-scope="scope" slot="urlList">
          <div>
            <!--            <p style="line-height:0; margin-bottom:5px;"><span class="&#45;&#45;mb&#45;&#45;rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:20px; text-decoration:none;">1、如果本人员目前进行统缴餐用餐，那么</span><span class="&#45;&#45;mb&#45;&#45;rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:20px; text-decoration:none;">更改用餐类别后，该人员将从明天开始，按照更改后的用餐类别进行统缴用餐，请认真确认后再更改用餐类别。</span></p>-->
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:20px; text-decoration:none;letter-spacing:5px">【用餐类别变更说明】</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:45px; text-decoration:none;letter-spacing:5px">1、变更用餐类别将直接改变当前人员的每日可用餐次，请务必按照实际需要，谨慎更改。</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:45px; text-decoration:none;letter-spacing:5px">2、更改用餐类别后，该人员将从当天最近一个餐次开始，按照更改后的用餐类别进行统缴餐用餐。例如，在上午9点更改用餐类别，该人员将从当天中餐开始，按照更改后的用餐类别进行统缴餐用餐。</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:45px; text-decoration:none;letter-spacing:5px">3、更改用餐类别后，如果可用餐次比更改前的要多，每日餐费将增加，比如从原本的早中餐类别（只吃早餐、中餐）更改为早中晚餐类别（早餐、中餐、晚餐都吃），需提醒该人员及时充值，避免发生餐费不足导致无法用餐的情况。</span></p>
          </div>
        </template>
      </avue-form>
    </el-dialog>
    <!-- 添加用户数据-->
    <el-dialog title="重置密码" :visible.sync="resetVisible" :append-to-body="true" @close="resetClose" width="50%">
      <avue-form ref="resetForm" :option="resetOption" v-model="resetForm" @submit="resetPassWord">

      </avue-form>
    </el-dialog>
    <el-dialog title="导入" :visible.sync="importDialogVisible" width="70%" @close="closeDow"
               :append-to-body="true">
      <avue-form ref="downForm" :option="option1" v-model="downForm" :upload-preview="uploadPreview"
                 :upload-error="uploadError" :upload-delete="uploadDelete" :upload-before="uploadBefore" :upload-after="uploadAfter" @submit="uploadFile">
        <template slot="attachmentsd" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">表头：
            姓名、性别、编号、部门、人员类别、用餐类别、一卡通卡号、民族、人员属性
          </el-button>
        </template>
        <template slot="attachments" slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleExcel()" style="font-size:15px;">点击下载基础信息模板
          </el-button>
        </template>
        <template slot="excleXls" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">文件后缀名必须为xls （即Excel格式），文件大小不得大于1MB
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="导入" :visible.sync="importForMealCategoryDialogVisible" width="70%" @close="closeDow"
               :append-to-body="true">
      <avue-form ref="downForMealCategoryForm" :option="exportOption" v-model="downForMealCategoryForm" :upload-preview="uploadPreview"
          :upload-error="uploadForMealCategoryError" :upload-delete="uploadForMealCategoryDelete" :upload-before="uploadForMealCategoryBefore"
          :upload-after="uploadForMealCategoryAfter" @submit="uploadFileForMealCategory">
        <template slot="attachmentsd" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">表头：
            姓名、编号、用餐类别名称
          </el-button>
        </template>
        <template slot="attachments" slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleForMealCategoryExcel()" style="font-size:15px;">点击下载模板
          </el-button>
        </template>
        <template slot="excleXls" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">文件后缀名必须为xls （即Excel格式），文件大小不得大于1MB
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList,
  getDetail,
  add,
  update,
  remove,
  exportPersonnelData,
  batchUpdatePersonnel,
  synchronSchoolTeacher,
  synchronSchoolStudent,
  resetPassword,
  downPersonnelTemplate,
  uploadFile,
  synchronCard,
  editCardStatus,
  isExistRecharConsum,
  logout,
  downTemplateForMealCategory,
  uploadFileForMealCategory,
  batchForMealCategoryUpdate,
  synchronAmount
} from "@/api/personnel/systemPersonnel";
import {
  getDeptTree
} from "@/api/setting/dept/systemDeptSetting";
import {
  getDeptAgencyType,
} from "@/api/system/dept";
import {mapGetters} from "vuex";
import website from '@/config/website';
const DIC = {
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
  VAILD: [{
    label: '人员(1)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '1'
  },{
    label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '2'
  },
  ],
  VAILD2: [{
    label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '2'
  },
  ],
  SAGE: [{
    label: '小学生',
    value: '0'
  },{
    label: '初中生',
    value: '1'
  },{
    label: '高中生',
    value: '2'
  }],
  PERSONNEL:[{
    label: '停用',
    value: '0'
  },{
    label: '启用',
    value: '1'
  }, {
    label: '注销',
    value: '2'
  },{
    label: '锁定',
    value: '3'
  }],
  OPEN:[{
    label: '是',
    value: '1'
  }]
}
export default {
  data() {
    return {
      uploadFilesForMealCategoryList:[],
      downForMealCategoryForm:{},
      importForMealCategoryDialogVisible:false,
      activeIdx: 0,
      messList: [ '用餐人员', '用餐类别异常人员'],
      form: {},
      abnormalForm:{},
      query: {},
      importForm:{},
      searchForm: {},
      editForm:{},
      editPersonnelForm:{},
      editPersonnelForm2:{},
      resetForm:{},
      downForm:{},
      loading: true,
      abnormalLoading:true,
      importDialogVisible:false,
      batchEditVisible:false,
      dialogVisible:false,
      schoolVisibleb:false,
      resetVisible:false,
      id:undefined,
      page: {
        pageSizes: [ 10, 20, 30, 40, 50, 100, 500, 1000, 2000, 5000],
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      abnormalPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      dept: {"canteenType": ''},
      agencyType:undefined,
      canteenType:undefined,
      modeType:undefined,
      modeType2:undefined,
      modeType3:undefined,
      modeType4:undefined,
      selectionList: [],
      uploadFilesList:[],
      dialogImageUrl:undefined,
      fileName:undefined,
      userId:undefined,
      userIds:undefined,
      isShowTea:undefined,
      isShowStu:undefined,
      isShowCard:undefined,
      numberTea:undefined,
      numberStu:undefined,
      numberCard:undefined,
      codeTimeTea:undefined,
      codeTimeStu:undefined,
      codeTimeCard:undefined,
      categoryStatus:undefined,
      editditVisible:false,
      editditVisible2:false,
      isShow:true,
      option1: {
        labelWidth: 250,
        card: true,
        /*submitBtn: false,
        emptyBtn: false,*/
        group: [{
          label: '下载人员开户信息导入表',
          prop: 'group1',
          column: [{
            prop: 'attachmentsd',
            hide: true,
            formslot: true
          },
            {
              prop: 'attachments',
              span: 24,
              hide: true,
              formslot: true
            },
          ]
        },
          {
            label: '上传填好的人员开户信息表',
            prop: 'group2',
            column: [{
              prop: "excleXls",
              hide: true,
              formslot: true
            },
              {
                label: '上传',
                prop: 'fileList',
                type: 'upload',
                loadText: '附件上传中，请稍等',
                span: 24,
                dataType: 'array',
                hide:true,
                limit:1,
                propsHttp: {
                  res: 'data',
                  url:'originalName'
                },
                rules: [{
                  required: true,
                  message: "请上传文件在提交",
                  trigger: "blur"
                }],
                action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              },
            ]
          },
        ]
      },
      editoption:{
        /*       height:'auto',
               calcHeight: 30,*/
        /*         searchShow: true,
                 searchMenuSpan: 6,*/
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        editBtn:false,
        labelWidth: 190,
        dialogWidth: "70%",
        emptyBtn:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display:false,
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            span: 24,
            search:true,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }]
          },
          {
            label: "编号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            rules: [{
              required: true,
              message: "请输入编号",
              trigger: "blur"
            }],
          },
          {
            label: "用餐类别",
            prop: "mealsType1",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display:false,
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            search:true,
            hide:true,
            display:true,
            dicUrl: "/api/service/rabbit-liancan/diningType/dict",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              res: "data",
              label: "title",
              value:"id"
            },
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: '请选择部门',
              trigger: 'blur'
            }],
          },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD,
            value:'1',
            search:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelType2',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD2,
            value:'2',
            search:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span:24,
            slot: true,
            /*rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],*/
          },
          {
            label: "人员状态",
            prop: "status",
            type: "radio",
            dicData: [{
              label: "启用",
              value: '1'
            },
            ],
            value:'1',
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请选择人员状态",
              trigger: "blur"
            }],
          },
          {
            label: "民族",
            prop: "nation",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
          {
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },
        ]
      },
      editoption2:{
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        editBtn:false,
        labelWidth: 190,
        dialogWidth: "70%",
        emptyBtn:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display:false,
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            span: 24,
            search:true,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }]
          },
          {
            label: "编号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            rules: [{
              required: true,
              message: "请输入编号",
              trigger: "blur"
            }],
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            search:true,
            display:true,
            dicUrl: "/api/service/rabbit-liancan/diningType/dict",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              res: "data",
              label: "title",
              value:"id"
            },
            rules: [{
              required: true,
              message: '请选择部门',
              trigger: 'blur'
            }],
          },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD,
            value:'1',
            search:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelType2',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD2,
            value:'2',
            search:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },

          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span:24,
            slot: true,
            /*rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],*/
          },
          {
            label: "人员状态",
            prop: "status",
            type: "radio",
            dicData: [{
              label: "启用",
              value: '1'
            },
            ],
            value:'1',
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请选择人员状态",
              trigger: "blur"
            }],
          },
          {
            label: "民族",
            prop: "nation",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
          {
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },
        ]
      },
      option: {
        /*  height:'auto',
          calcHeight: 30,*/
        calcHeight: 127,
        height: '500',
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        editBtn:false,
        delBtn:false,
        labelWidth: 190,
        dialogWidth: "70%",
        printBtn:true,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "登录账号",
            prop: "account",
            type: "input",
            width: 100,
            addDisplay: false,
            rules: [{
              required: true,
              message: "请输入登录账号",
              trigger: "blur"
            }],
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            sortable:true,
            search:true,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],

          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
            sortable:true,
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }],
          },
          {
            label: "编号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            sortable:true,
            rules: [{
              required: true,
              message: "请输入编号",
              trigger: "blur"
            }],
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            sortable:true,
            minWidth: 120,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              label: "title",
              value: "id"
            },
            editDisplay: false,
            viewDisplay: false,
            multiple:true,
            /*slot:true,*/
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请输入部门",
              trigger: "click"
            }]
          },
          /*            {
                        label: "部门",
                        prop: "deptId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
                        props: {
                          label: "title",
                          value: "id"
                        },
                        editDisplay: false,
                        viewDisplay: false,
                        multiple:true,
                        /!*slot:true,*!/
                        search:true,
                        hide:true,
                        rules: [{
                          required: true,
                          message: "请输入部门",
                          trigger: "click"
                        }]
                      },*/
          {
            label: "用餐类别",
            sortable:true,
            prop: "mealsName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            search:true,
            hide:true,
            editDisplay: false,
            viewDisplay: false,
            dicUrl: "/api/service/rabbit-liancan/diningType/dict",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          //2023-02-03需求更改人员类别，原来1为学生,2为教职工
          // {
          //   label: '人员类别',
          //   prop: 'personnelType',
          //   type: "radio",
          //   //slot: true,
          //   dicData: DIC.VAILD,
          //   value:'1',
          //   search:true,
          //   hide:true,
          //   rules: [{
          //     required: true,
          //     message: '请选择人员类别',
          //     trigger: 'blur'
          //   }],
          //   width: 90,
          // },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD,
            search:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelType2',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD2,
            value:'2',
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelTypeName',
            type: "input",
            addDisplay:false,
            hide:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span:24,
            slot: true,
            /*rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],*/
            width: 90,
          },
          {
            label: "人员状态",
            prop: "status",
            type: "radio",
            dicData: [{
              label: "启用",
              value: '1'
            },
            ],
            value:'1',
            hide:true,
            rules: [{
              required: true,
              message: "请选择人员状态",
              trigger: "blur"
            }],
            width: 90,
          },
          /*{
            label: "人员状态",
            prop: "status1",
            type: "radio",
            dicData: DIC.PERSONNEL,
            addDisplay:false,
            editDisplay:false,
            viewDisplay:false,
            hide:true,
            rules: [{
              required: true,
              message: "请输入人员状态",
              trigger: "blur"
            }],
            width: 90,
          },*/
          // {
          //   label: "有效期",
          //   prop: "effectiveTime",
          //   type: "datetime",
          //   format: "yyyy-MM-dd",
          //   valueFormat: "yyyy-MM-dd",
          //   hide:true,
          //   viewDisplay:false,
          //   addDisplay:false,
          //   pickerOptions: {
          //     disabledDate(time) {
          //       return time.getTime() < Date.now();
          //     },
          //   },
          //   width: 90,
          // },
          {
            label: "民族",
            prop: "nation",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            hide:true,
          },

          {
            label: "卡状态",
            prop: "cardStatus",
            type: "select",
            search: true,
            slot:true,
            addDisplay: false,
            dicData: [
              {
                value: '0',
                label: '未开卡'
              },
              {
                value: '1',
                label: '正常'
              },
              {
                value: '2',
                label: '已挂失'
              },
              {
                value: '3',
                label: '异常'
              },
              {
                value: '4',
                label: '过期'
              }
            ],
          },
          {
            label: "卡序号",
            prop: "cardNum",
            type: "input",
            search: true,
          },
          {
            label: "卡号",
            prop: "cardId",
            type: "input",
            search: true,
          },
          {
            label: "生效日期",
            prop: "startDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd"
          },
          {
              label: "终止日期",
              prop: "endDate",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd"
          },
          /*       {
                   label: "开通伙食费钱包",
                   prop: "boardOpenStatuss",
                   type: "radio",
                   dicData: DIC.OPEN,
                   hide:true,
                   value:'1',
                   mock:{
                     type:'dic'
                   },
                 },*/
          /*     {
                 label: "伙食费钱包余额(元)",
                 prop: "balance",
                 type: "input",*/
          /*      disabled:true,*/
          /*              rules: [{
                          required: true,
                          message: "请输入伙食费钱包初始余额(元)",
                          trigger: "blur"
                        }],*/
          /*   },*/
          /*{
            label: "自选餐每餐次消费限额(元)",
            prop: "consumQuota",
            type: "input",
          },*/
          /*{
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },*/
        ]
      },
      abnormalOption:{
        /*  height:'auto',
          calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        editBtn:false,
        delBtn:false,
        labelWidth: 190,
        dialogWidth: "70%",
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "登录账号",
            prop: "account",
            type: "input",
            width: 100,
            addDisplay: false,
            rules: [{
              required: true,
              message: "请输入登录账号",
              trigger: "blur"
            }],
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search:true,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 100,
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }],
            width: 50,
          },
          {
            label: "编号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            rules: [{
              required: true,
              message: "请输入编号",
              trigger: "blur"
            }],
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            minWidth: 120,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              label: "title",
              value: "id"
            },
            editDisplay: false,
            viewDisplay: false,
            multiple:true,
            /*slot:true,*/
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请输入部门",
              trigger: "click"
            }]
          },
          {
            label: "用餐类别",
            prop: "mealsName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            search:true,
            hide:true,
            editDisplay: false,
            viewDisplay: false,
            dicUrl: "/api/service/rabbit-liancan/diningType/dict",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          // 2023-02-03需求更改人员类别，原来1为学生,2为教职工
          // {
          //   label: '人员类别',
          //   prop: 'personnelType',
          //   type: "radio",
          //   //slot: true,
          //   dicData: DIC.VAILD,
          //   value:'1',
          //   search:true,
          //   hide:true,
          //   rules: [{
          //     required: true,
          //     message: '请选择人员类别',
          //     trigger: 'blur'
          //   }],
          //   width: 90,
          // },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD,
            search:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelType2',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD2,
            value:'2',
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          // {
          //   label: '人员类别',
          //   prop: 'personnelTypeName',
          //   type: "input",
          //   addDisplay:false,
          //   rules: [{
          //     required: true,
          //     message: '请选择人员类别',
          //     trigger: 'blur'
          //   }],
          //   width: 90,
          // },
          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span:24,
            slot: true,
            rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],
            width: 90,
          },
          {
            label: "人员状态",
            prop: "status",
            type: "radio",
            dicData: [{
              label: "启用",
              value: '1'
            },
            ],
            value:'1',
            hide:true,
            rules: [{
              required: true,
              message: "请选择人员状态",
              trigger: "blur"
            }],
            width: 90,
          },
          /*{
            label: "人员状态",
            prop: "status1",
            type: "radio",
            dicData: DIC.PERSONNEL,
            addDisplay:false,
            editDisplay:false,
            viewDisplay:false,
            hide:true,
            rules: [{
              required: true,
              message: "请输入人员状态",
              trigger: "blur"
            }],
            width: 90,
          },*/
          {
            label: "一卡通卡号",
            prop: "cardId",
            type: "input",
          },
          {
            label: "卡状态",
            prop: "cardStatus",
            type: "select",
            slot:true,
            addDisplay: false,
            dicData: [{
              label: "正常",
              value: '0'
            },
              {
                label: "挂失",
                value: '1'
              }
            ],
          },
          {
            label: "有效期",
            prop: "effectiveTime",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide:true,
            viewDisplay:false,
            addDisplay:false,
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < Date.now();
              },
            },
            width: 90,
          },
          {
            label: "民族",
            prop: "nation",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            hide:true,
          },
          /*       {
                   label: "开通伙食费钱包",
                   prop: "boardOpenStatuss",
                   type: "radio",
                   dicData: DIC.OPEN,
                   hide:true,
                   value:'1',
                   mock:{
                     type:'dic'
                   },
                 },*/
          /*     {
                 label: "伙食费钱包余额(元)",
                 prop: "balance",
                 type: "input",*/
          /*      disabled:true,*/
          /*              rules: [{
                          required: true,
                          message: "请输入伙食费钱包初始余额(元)",
                          trigger: "blur"
                        }],*/
          /*   },*/
          /*{
            label: "自选餐每餐次消费限额(元)",
            prop: "consumQuota",
            type: "input",
          },*/
          /*{
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },*/
        ]
      },
      resetOption:{
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        header: false,
        selection: true,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
          },
          {
            label: '请输入密码',
            prop: 'password',
            type: "input",
            placeholder:'登录密码为数字或英文字母，限6-8位',
            labelWidth:100,
            rules: [{
              required: true,
              message: '请输入密码',
              trigger: 'blur'
            }]
          },
        ]
      },
      schoolOption:{
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        header: false,
        selection: true,
        column: [
          {
            label: '云校学校名称',
            prop: 'liaSchoolId',
            type: "select",
            //slot: true,
            dicUrl: "/api/service/rabbit-binding/schoolBinding/dict",
            props: {
              label: "name",
              value: "id"
            },
            rules: [{
              required: false,
              message: "请选择学校",
              trigger: "click"
            }]
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
          }]
      },
      editOption:{
        /*height:'auto',*/
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        header: false,
        selection: true,
        column: [
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              res: "data",
              label: "title",
              value:"id"
            },
            search:true,
            hide:true,
          },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD,
            search:true,
          },
          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            search:true,
            hide:true,
            display:true,
            dicUrl: "/api/service/rabbit-liancan/diningType/dict",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "民族",
            prop: "nation",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
          {
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },
        ]
      },
      exportOption: {
        labelWidth: 250,
        card: true,
        /*submitBtn: false,
        emptyBtn: false,*/
        group: [{
          label: '下载批量编辑用餐类别模板',
          prop: 'group1',
          column: [{
            prop: 'attachmentsd',
            hide: true,
            formslot: true
          },
            {
              prop: 'attachments',
              span: 24,
              hide: true,
              formslot: true
            },
          ]
        },
        {
          label: '导入批量编辑用餐类别的用餐人员',
          prop: 'group2',
          column: [
            {
              prop: "excleXls",
              hide: true,
              formslot: true
            },
            {
              label: '导入',
              prop: 'fileList',
              type: 'upload',
              loadText: '附件导入中，请稍等',
              span: 24,
              dataType: 'array',
              hide:true,
              limit:1,
              propsHttp: {
                res: 'data',
                url:'originalName'
              },
              rules: [{
                required: true,
                message: "请导入文件在提交",
                trigger: "blur"
              }],
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            },
          ]
        },
        ]
      },
      data: [],
      abnormalData:[]
    };
  },
  computed: {
    ...mapGetters(["permission", 'userInfo']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.system_personnel_add, false),
        viewBtn: this.vaildData(this.permission.system_personnel_view, false),
        delBtn: this.vaildData(this.permission.system_personnel_delete, false),
        editBtn: this.vaildData(this.permission.system_personnel_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    this.categoryStatus = "0";
    getDeptAgencyType().then(res =>{
      this.agencyType = res.data.data.agencyType;
      this.canteenType = res.data.data.canteenType;
      if (res.data.data.applicationMode === '1'){
        this.modeType = '1';
        this.modeType2 = '2';
        this.modeType3 = '1';
        this.modeType4 = '1';
        this.isShow = false;
      }else if (res.data.data.applicationMode === '2'){
        this.modeType = '2';
        this.modeType2 = '1';
        this.modeType3 = '2';
        this.modeType4 = '1';
        this.isShow = false;
        // this.option.addBtn = false;
      }else if (res.data.data.applicationMode === '3'){
        this.modeType = '1';
        this.modeType2 = '1';
        this.modeType3 = '2';
        this.modeType4 = '2';
        this.isShow = true;
        // this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        /* this.option.menu = false;*/
      }else if (res.data.data.applicationMode === '0'){
        this.modeType = '1';
        this.modeType2 = '1';
        this.modeType3 = '2';
        this.modeType4 = '2';
        this.isShow = false;
        // this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        /*          this.option.menu = false;*/
      }
      if (res.data.data.agencyType == '0' || this.dept.applicationMode === '1'){
        this.option.column[10].search = false;
      }else {
        this.option.column[9].addDisplay = false;
        this.option.column[10].addDisplay = true;
        this.option.column[9].search = false;
      }
      this.dept = res.data.data;
    })
    /*      getDeptTree().then(res => {
            const index = this.$refs.crud.findColumnIndex("deptId");
            this.option.column[index].dicData = res.data.data;
          });*/
  },
  methods: {
    menuClick(idx) {
      this.query=[];
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.query = {};
        this.categoryStatus = "0";
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
      if (idx == 1){
        this.option.printBtn= false;
        // this.option.addBtn = false;
        this.query = {};
        this.categoryStatus = "1";
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
    },
    rowSave(row, loading, done) {
      if (this.agencyType === '0'){
        row.personnelType = row.personnelType;
      }else {
        row.personnelType = row.personnelType2;
      }
      row.deptId = row.deptId[0];
      add(row).then(res => {
        loading();
        this.page.currentPage = 1;
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: res.message
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
        window.console.log(row.attribute instanceof Array)
      /*  if (row.attribute instanceof Array){
          row.attribute = row.attribute
        }else {
          row.attribute1 = row.attribute
          row.attribute = []
        }*/
      if (row.boardOpenStatus instanceof Array){
        row.boardOpenStatus = row.boardOpenStatus
      }else {
        row.boardOpenStatus1 = row.boardOpenStatus
        row.boardOpenStatus = []
      }
      row.deptId = row.deptId[0]
      /*        row.deptId = row.deptId.join(",");*/
      update(row).then(() => {
        loading();
        this.page.currentPage = 1;
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    editPersonnelDate(row,loading){
      /*        row.deptId = row.deptId.join(",");*/
      const loading1 = this.$loading({
        lock: true,
        text: '正在修改信息，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      if (this.agencyType === '0'){
        row.personnelType = row.personnelType;
      }else {
        row.personnelType = row.personnelType2;
      }
      update(row).then(res => {
        loading();
        loading1.close();
        this.onLoad(this.page);
        this.editditVisible = false;
        this.editditVisible2 = false;
        this.$message({
          type: "success",
          message: res.data.data,
          duration: 7000
        });
      }, error => {
        loading();
        loading1.close();
        window.console.log(error);
      });
    },
    delPersonnel(row) {
      isExistRecharConsum(row.id).then(res => {
        if (res.data.data === 'yes') {
          this.$confirm(res.data.message, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              logout(row.id).then(res => {
                this.page.currentPage = 1;
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "存档成功!",
                });
              }, error => {
                window.console.log(error);
              });
            })
            .catch(() => {
            });
        } else {
          this.$confirm("确定将选择数据删除?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              return remove(row.id);
            })
            .then(() => {
              this.page.currentPage = 1;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "删除成功!"
              });
            });
        }
      }, error => {
        window.console.log(error);
      });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      isExistRecharConsum(this.ids).then(res => {
        if (res.data.data === 'yes') {
          this.$confirm(res.data.message, "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              logout(this.ids).then(res => {
                this.page.currentPage = 1;
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "存档成功!",
                });
              }, error => {
                window.console.log(error);
              });
            })
            .catch(() => {
            });
        } else {
          this.$confirm("确定将选择数据删除?", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
            .then(() => {
              return remove(this.ids);
            })
            .then(() => {
              this.page.currentPage = 1;
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "操作成功!请稍后刷新页面查看结果"
              });
            });
        }
      }, error => {
        window.console.log(error);
      });
    },
    beforeOpen(done, type) {
      done();
      // 转成数组
      if (this.form.attribute != null) {
        let attribute = (this.form.attribute).split(',')
        this.form.attribute = attribute
      }
      // 转成数组
      if (this.form.boardOpenStatus != null) {
        let boardOpenStatus = (this.form.boardOpenStatus).split(',')
        this.form.boardOpenStatus = boardOpenStatus
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          const balanceColumn = this.option.column[13];
          if (this.form.boardOpenStatus.map(parseInt) == 1){
            balanceColumn.disabled = false;
          }
          if (this.form.boardOpenStatus.map(parseInt) == 0){
            balanceColumn.disabled = true;
          }
          this.form = res.data.data;
        });
      }
      getDeptAgencyType().then(res =>{
        this.agencyType = res.data.data.agencyType;
      })
      //8:显示教职工，9：显示学生或者教职工
      /*        if (this.agencyType === '0' || this.dept.applicationMode === '1'){
                this.option.column[10].display = false;
              }else {
                this.option.column[9].display = false;
              }*/
      if (res.data.data.agencyType == '0' || this.dept.applicationMode === '1'){
        this.option.column[10].search = false;
      }else {
        this.option.column[9].addDisplay = false;
        this.option.column[10].addDisplay = true;
        this.option.column[9].search = false;
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.searchForm = params;
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
      this.$refs.crud.refreshTable();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.status = "1";
      params.categoryStatus = this.categoryStatus;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    synchronSchoolTeacher(){
      const loading = this.$loading({
        lock: true,
        text: '请稍等，正在同步云校老师数据',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      synchronSchoolTeacher().then(() => {
        loading.close()
        this.onLoad(this.page);
        /*          this.$message({
                    type: "success",
                    message: "操作成功"
                  });*/
        this.$message({
          type: "success",
          message: "触发同步操作成功，同步数据需要一点时间，请稍后刷新查询数据",
          duration: 6000
        });
        this.isShowTea = true;
        let num = 30;
        this.numberTea = `${num}s`;
        this.codeTimeTea = setInterval(() => {
          if (num <= 0) {
            clearInterval(this.codeTimeTea);
            this.isShowTea = false;
            return;
          }
          num--;
          this.numberTea = `等待 ${num} s`;
        }, 1000);
      }, error => {
        loading.close()
          window.console.log(error);
      });
    },
    synchronSchoolStudent(){
      const loading = this.$loading({
        lock: true,
        text: '请稍等，正在同步云校学生数据',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      synchronSchoolStudent().then(() => {
        loading.close()
        this.onLoad(this.page);
        /*        this.$message({
                  type: "success",
                  message: "操作成功"
                });*/
        this.$message({
          type: "success",
          message: "触发同步操作成功，同步数据需要一点时间，请稍后刷新查询数据",
          duration: 6000
        });
        this.isShowStu = true;
        let num = 30;
        this.numberStu = `${num}s`;
        this.codeTimeStu = setInterval(() => {
          if (num <= 0) {
            clearInterval(this.codeTimeStu);
            this.isShowStu = false;
            return;
          }
          num--;
          this.numberStu = `等待 ${num} s`;
        }, 1000);
      }, error => {
        loading.close()
          window.console.log(error);
      });
    },
    exportPersonnelData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出人员信息数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.searchForm.status = "1";
      this.searchForm.categoryStatus = this.categoryStatus;
      exportPersonnelData(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '人员开户信息报表.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    downPersonnelData(){
      this.importDialogVisible = true;
    },
    batchEditClose(){
      this.editditVisible = false;
      this.editditVisible2 = false;
    },

    resetClose(){
      this.resetForm.password = "";
      this.resetVisible = false;
    },
    batchEditPersonnel(){
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.canteenType === '0'){
        this.editOption.column[4].display = false;
      }
      this.batchEditVisible = true;
    },
    batchReset(){
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.userId = this.ids;
      this.resetVisible = true;
    },
    batchUpdatePersonnel(row, loading){
      row.ids = this.ids;
      this.$confirm('确定要进行批量操作?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        this.batchEditVisible = false;
        const loading1 = this.$loading({
          lock: true,
          text: '正在批量修改用餐类别，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        batchUpdatePersonnel(row).then(() => {
          this.editForm.mealsType = "";
          this.editForm.deptId = "";
          this.editForm.attribute = "";
          this.editForm.consumQuota = "";
          this.editForm.status = "";
          this.editForm.personnelType = "";
          this.editForm.sex = "";
          this.editForm.nation = "";
          this.editForm.effectiveTime = "";
          this.editForm.cardId = "";
          loading();
          loading1.close();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "批量更新成功,请稍后刷新查看结果!",
            duration: 5000
          });
        }, error => {
          loading();
          loading1.close();
          this.batchEditVisible = false;
          window.console.log(error);
        });
      }).catch(() => {
        loading();
      });

    },
    handleClickPreview: function(url) {
        window.console.log(">>>>>>>>>>>>>")
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    editPersonnel(row){
      this.editPersonnelForm = row;
      getDeptAgencyType().then(res =>{
        this.agencyType = res.data.data.agencyType;
      })
      //7:显示教职工，8：显示学生或者教职工
      //6:显示教职工，7：显示学生或者教职工
      if (this.agencyType === '0'){
        this.editoption.column[7].display = true;
        this.editoption.column[8].display = false;
      }else {
        this.editoption.column[7].display = true;
        this.editoption.column[8].display = false;
      }
      if (this.canteenType === '0'){
        this.editoption.column[5].display = false;
      }
      this.editditVisible = true;
    },
    editPersonnel2(row){
      this.editPersonnelForm2 = row;
      getDeptAgencyType().then(res =>{
        this.agencyType = res.data.data.agencyType;
      })
      //6:显示教职工，7：显示学生或者教职工
      if (this.agencyType === '0'){
        this.editoption2.column[6].display = true;
        this.editoption2.column[7].display = false;
      }else {
        this.editoption2.column[7].display = true;
        this.editoption2.column[6].display = false;
      }
      if (this.canteenType === '0'){
        this.editoption2.column[4].display = false;
      }
      this.editditVisible2 = true;
    },
    reset(row){
      this.userId = row.id;
      this.resetVisible = true;
    },

    patchReset(){
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.userId = this.ids;
      this.resetVisible = true;
    },
    closeData1(){
      this.editPersonnelForm.id = "";
      this.editPersonnelForm.userName = "";
      this.editPersonnelForm.sex = "";
      this.editPersonnelForm.studentJobNo = "";
      this.editPersonnelForm.mealsType = "";
      this.editPersonnelForm.deptId = "";
      this.editPersonnelForm.personnelType ="";
      this.editPersonnelForm.attribute = "";
      this.editPersonnelForm.avatar = "";
      this.editPersonnelForm.status = "";
      this.editPersonnelForm.cardId = "";
      this.editPersonnelForm.effectiveTime = "";
      this.editPersonnelForm.nation = "";
      this.editPersonnelForm.balance  = "";
      this.editPersonnelForm.consumQuota = "";
    },
    closeData2(){
      this.editPersonnelForm2.id = "";
      this.editPersonnelForm2.userName = "";
      this.editPersonnelForm2.sex = "";
      this.editPersonnelForm2.studentJobNo = "";
      this.editPersonnelForm2.mealsType = "";
      this.editPersonnelForm2.deptId = "";
      this.editPersonnelForm2.personnelType ="";
      this.editPersonnelForm2.attribute = "";
      this.editPersonnelForm2.avatar = "";
      this.editPersonnelForm2.status = "";
      this.editPersonnelForm2.cardId = "";
      this.editPersonnelForm2.effectiveTime = "";
      this.editPersonnelForm2.nation = "";
      this.editPersonnelForm2.balance  = "";
      this.editPersonnelForm2.consumQuota = "";
    },
    resetPassWord(row,loading){
      row.ids = this.userId;
      resetPassword(row).then(res => {
        loading();
        this.resetForm.password = "";
        this.page.currentPage = 1;
        this.onLoad(this.page);
        this.resetVisible = false;
        this.$message({
          type: "success",
          message: "重置密码成功"
        });
      }, error => {
        loading();
        window.console.log(error);
      });
    },
    handleExcel(){
      downPersonnelTemplate().then(res => {
        const blob = new Blob([res.data]);
        const fileName = '人员开户信息表.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    uploadBefore(file, done, loading,column) {
      /*        console.log(">>>>>>>>>上传前的方法",file)*/
      var str = file.name;
      str = str.substring(str.lastIndexOf("\.") + 1, str.length);

      var reStr = this.selectType(str);
      if (reStr == "NO") {
        loading();
        this.$message.error('文件格式错误,只能上传"xls"格式');
        return false;
      } else {
        /*    var newFile = new File([file], '1234', { type: file.type });*/
        this.fileName = file.name
        done()
        return true;
      }
    },
    uploadError(error, column) {
    },
    uploadAfter(res, done, loading,column) {
        window.console.log("文件路径",JSON.stringify(res))
      let proof = {
        label: this.fileName,
        value: res.link
      }
      this.uploadFilesList.push(proof);
      done()
    },
    uploadPreview(file,column,done){
      done()//默认执行打开方法
    },
    uploadDelete(column,file) {
      this.uploadFilesList.splice(file.uid,1);
    },
    //判断文件类型
    selectType(type) {
      var fileList = ["xls"];
      for (var item in fileList) {
        if (fileList[item] == type) {
          return "FILE";
        }
      }
      return "NO";
    },
    closeDow(){
      this.downForm.fileList = "";
      this.uploadFilesList = [];
      this.importDialogVisible = false;
    },
    uploadFile(row,loading){
        window.console.log(">>>>>>>>>>>>>>",JSON.stringify(this.uploadFilesList[0].value))
      const loading1 = this.$loading({
        lock: true,
        text: '正在导入表格数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadFile(this.uploadFilesList[0].value).then(res => {
        if (res.data.data === 'error'){
          loading1.close()
          loading();
          this.downForm.fileList = "";
          this.uploadFilesList = [];
          this.$alert(res.data.message, '信息', {
            confirmButtonText: '确定'
          })
        }else {
          loading1.close()
          loading();
          this.downForm.fileList = "";
          this.uploadFilesList = [];
          this.$message({
            type: "success",
            message: "导入成功"
          });
          this.page.currentPage = 1;
          this.onLoad(this.page);
          this.importDialogVisible = false;
        }
      });
    },
    synchronCard(){
      synchronCard().then(() => {
        /*      this.$message({
                type: "success",
                message: "触发同步一卡通用户数据操作，请稍后刷新查询数据"
              });*/
        this.$message({
          type: "success",
          message: "触发同步操作成功，同步数据需要一点时间，请稍后刷新查询数据",
          duration: 6000
        });
        this.isShowCard = true;
        let num = 30;
        this.numberCard = `${num}s`;
        this.codeTimeCard = setInterval(() => {
          if (num <= 0) {
            clearInterval(this.codeTimeCard);
            this.isShowCard = false;
            return;
          }
          num--;
          this.numberCard = `等待 ${num} s`;
        }, 1000);
      }, error => {
          window.console.log(error);
      });
    },
    reportTheLoss(row){
      this.$confirm("确定要挂失？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          row.cardStatus = "1";
          row.userIds = row.id;
          editCardStatus(row).then(res => {
            this.page.currentPage = 1;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "挂失成功!"
            });
          }, error => {
            window.console.log(error);
          });
        })
        .catch(() => {
        });
    },
    uncoupling(row){
      this.$confirm("确定要解挂？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          row.cardStatus = "0";
          row.userIds = row.id;
          editCardStatus(row).then(res => {
            this.page.currentPage = 1;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "解挂成功!"
            });
          }, error => {
            window.console.log(error);
          });
        })
        .catch(() => {
        });
    },
    //批量编辑用餐类别导入
    importDialogShow(){
      this.importForMealCategoryDialogVisible = true;
    },
    closeForMealCategoryDow(){
      this.downForMealCategoryForm.fileList = "";
      this.uploadFilesForMealCategoryList = [];
      this.importForMealCategoryDialogVisible = false;
    },
    uploadForMealCategoryError(error, column) {
    },
    uploadForMealCategoryAfter(res, done, loading,column) {
        window.console.log("文件路径",JSON.stringify(res))
      let proof = {
        label: this.fileName,
        value: res.link
      }
      this.uploadFilesForMealCategoryList.push(proof);
      done()
    },
    uploadForMealCategoryPreview(file,column,done){
      done()//默认执行打开方法
    },
    uploadForMealCategoryDelete(column,file) {
      this.uploadFilesForMealCategoryList.splice(file.uid, 1);
    },
    //判断文件类型
    selectType(type) {
      var fileList = ["xls"];
      for (var item in fileList) {
        if (fileList[item] == type) {
          return "FILE";
        }
      }
      return "NO";
    },
    uploadFileForMealCategory(row,loading){
        window.console.log(this.downForMealCategoryForm)
      console.log(">>>>>>>>>>>>>>",JSON.stringify(this.uploadFilesForMealCategoryList[0].value))
      const loading1 = this.$loading({
        lock: true,
        text: '正在导入表格数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadFileForMealCategory(this.uploadFilesForMealCategoryList[0].value).then(res => {
          window.console.log(res)
        if (res.data.data === 'yes' || res.data.data === 'error') {
          loading();
          loading1.close();
          this.$alert(res.data.message, '提示', {
            confirmButtonText: '确定'
          });
          loading();
        } else {
          loading();
          loading1.close();
          this.$confirm('确定要对表格的用餐人员进行批量编辑用餐类别？', "提示", {
            confirmButtonText: "确定",
            cancelButtonText: "取消",
            type: "warning"
          })
          .then(() => {
              var param = { excelUrl: this.uploadFilesForMealCategoryList[0].value }
              const loading1 = this.$loading({
                lock: true,
                text: '正在批量编辑，操作需要时间，请耐心等候',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              batchForMealCategoryUpdate(param).then(() => {
                this.$message({
                  type: "success",
                  message: "操作成功!"
                });
                loading1.close()
                loading();
                this.downForMealCategoryForm.fileList = "";
                this.uploadFilesForMealCategoryList = [];
                this.$message({
                  type: "success",
                  message: "导入成功"
                });
                this.page.currentPage = 1;
                this.onLoad(this.page);
                this.importForMealCategoryDialogVisible = false;
              }, error => {
                loading1.close()
                loading();
                this.downForMealCategoryForm.fileList = "";
                this.uploadFilesForMealCategoryList = [];
                this.$alert(res.data.message, '信息', {
                  confirmButtonText: '确定'
                })
              });
            })
            .catch(() => {
              loading();
          });
        }
      });
    },
    handleForMealCategoryExcel(){
      downTemplateForMealCategory().then(res => {
        const blob = new Blob([res.data]);
        const fileName = '批量编辑用餐类别模板.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    clickSynchronAmount(){
      synchronAmount().then(res => {
        this.$message({
          type: "success",
          message: "同步成功!"
        });
      }, error => {
        window.console.log(error);
        this.$message({
          type: "success",
          message: "同步失败!"
        });
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    ::v-deep div {
      width: 180px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>
