<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.report_statistics_bidding_export" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportBiddingData">导出</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList,exportBiddingData} from "@/api/reportStatistics/bidding";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        dialogVisible:false,
        searchFrom:{},
        option: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: false,
          showSummary: true,
          menu:false,
          addBtn:false,
          align: 'center',
          /*printBtn:true,*/
          sumColumnList: [
            {
              name: "draftCount",
              type: "sum"
            },
            {
              name: "approvingCount",
              type: "sum"
            },
            {
              name: "terminationCount",
              type: "sum"
            },
            {
              name: "notStartCount",
              type: "sum"
            },
            {
              name: "signingCount",
              type: "sum"
            },
            {
              name: "evaluatingCount",
              type: "sum"
            },
            {
              name: "finishCount",
              type: "sum"
            },
            {
              name: "amount",
              type: "sum"
            },
          ],
          column: [
            /*        {
                      label: "招标类别",
                      prop: "purchaseType",
                      type: "input",
                    },*/
            {
              label: "所属食堂",
              prop: "companyId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
            },
            {
              label: "招标类别",
              prop: "purchaseType",
              type: "select",
              search: true,
              dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict",
              props: {
                label: "name",
                value: "id"
              },
            },
            {
              label: "大类",
              prop: "commodityType",
              type: "input",
            },
            {
              label: "草稿",
              prop: "draftCount"
            },
            {
              label: "审批中",
              prop: "approvingCount"
            },
            {
              label: "终止",
              prop: "terminationCount"
            },
            {
              label: "未开始",
              prop: "notStartCount"
            },
            {
              label: "报名中",
              prop: "signingCount"
            },
            {
              label: "评标中",
              prop: "evaluatingCount"
            },
            {
              label: "已结束",
              prop: "finishCount"
            },
            {
              label: "数量合计",
              prop: "amount"
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          /*          addBtn: this.vaildData(this.permission.work_personnel_add, false),
                    viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                    delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                    editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      if (this.userInfo.userType === 'canteen') {
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchFrom = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      exportBiddingData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportBiddingData(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '招标进展统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
