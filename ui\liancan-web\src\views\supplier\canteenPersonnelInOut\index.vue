<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/supplier/canteenPersonnelInOut";
  import {getRoleTree2} from "@/api/system/role";
  import {mapGetters} from "vuex";
  const DIC = {
    sex: [
      {
        label: '男',
        value: 1
      },
      {
        label: '女',
        value: 2
      }
    ]
  }

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          menu:false,
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          addBtn: false,
          selection: true,
          labelWidth: 150,
          dialogWidth: 900,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "所属部门",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
            },
            {
              label: '姓名',
              prop: 'userId',
              type: 'select',
              props: {
                label: 'realName',
                value: 'id'
              },
              dicUrl: '/api/service/rabbit-user/user-list',
              rules: [
                {
                  required: true,
                  message: '请选择食堂人员',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: "职务",
              prop: "roleId",
              multiple: true,
              type: "tree",
              dicData: [],
              props: {
                label: "title"
              },
              checkStrictly: true,
              slot: true,
              search:true,
              rules: [{
                required: true,
                message: "请选择所属角色",
                trigger: "click"
              }]
            },
            {
              label: '性别',
              search:true,
              prop: 'sex',
              type: "radio",
              dicData: DIC.sex,
              disabled: false
            },
            {
              label: "出入设备",
              prop: "deviceName",
              type: "input",
              disabled: false
            },
            {
              label: "设备SN码",
              prop: "deviceKey",
              type: "input",
              disabled: false
            },
            {
              label: "出入时间",
              search:true,
              prop: "inOutTime",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              disabled: false
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.supplier_account_add, false),
          viewBtn: this.vaildData(this.permission.supplier_account_view, false),
          delBtn: this.vaildData(this.permission.supplier_account_delete, false),
          editBtn: this.vaildData(this.permission.supplier_account_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      this.initData();
    },
    methods: {
      initData() {
        getRoleTree2('').then(res => {
          const index = this.$refs.crud.findColumnIndex("roleId");
          this.option.column[index].dicData = res.data.data;
        });
      },
      rowSave(row, loading, done) {
        add(row).then(res => {
          loading();
          this.onLoad(this.page);
          console.log(">>>>>>>>>>>>>>>>>",res.data.success)
    /*      this.$message({
            type: "success",
            message: "操作成功!"
          });*/
          if (res.data.success) {
            this.$message({
              type: "success",
              message: res.data.message
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.message
            });
          }
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
    }
  };
</script>

<style>
</style>
