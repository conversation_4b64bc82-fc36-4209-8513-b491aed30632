<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="createDateForm" slot-scope="{type,disabled}" >
        <el-date-picker
          v-model="form.createDate"
          :picker-options="expireTimeOption"
          value-format="yyyy-MM-dd"
          @change="changeCreateDate"
          type="date"
          placeholder="选择日期">
        </el-date-picker>
      </template>
      <template slot="buyVoucherCode" slot-scope="{row}" >
        <div v-if="row.buyVoucherId>0">
          <el-button  type="success" size="small"  @click.native="viewVoucher(row)" style="cursor: pointer">{{row.buyVoucherCode}}</el-button >
          <el-button type="text" icon="el-icon-delete" size="small" plain style="border: 0;background-color: transparent !important;" @click.stop="deleteVoucher(row)">删除</el-button>
        </div>
      </template>
      <template slot="frontImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.frontImg" fit="cover" @click="handleClickPreview(row.frontImg)"></el-image>
      </template>
      <template slot="backImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.backImg" fit="cover" @click="handleClickPreview(row.backImg)"></el-image>
      </template>
      <template slot="menuLeft" slot-scope="{size}">
        <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload2" @click="importData">导入</el-button>
        <el-button class="filter-item" size="small" type="success" icon="el-icon-download" @click="exportRecord">导出</el-button>
        <el-button class="filter-item" size="small" type="primary" plain icon="el-icon-c-scale-to-original" @click="generateVoucher(null)" >生成凭证</el-button>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text" icon="el-icon-turn-off" size="small" plain style="border: 0;background-color: transparent !important;" @click.stop="copyDialog(row)">复制</el-button>
        <el-button type="text" size="small"  plain icon="el-icon-c-scale-to-original" style="border: 0;background-color: transparent !important;" @click="generateVoucher(row)" >生成凭证</el-button>
      </template>
<!--      自定义-->
<!--      <template slot="originalAmt" slot-scope="{row}" >-->
<!--        <el-input-number v-model="form.originalAmt" :min="1" label="原值"></el-input-number>-->
<!--      </template>-->
<!--      <template slot="recoveryRate" slot-scope="{row}" >-->
<!--        <el-input v-model="form.recoveryRate" label="残值率"></el-input>-->
<!--      </template>-->
<!--      <template slot="expectedRecoveryAmt" slot-scope="{row}" >-->
<!--        <el-input v-model="form.expectedRecoveryAmt" label="预计残值"></el-input>-->
<!--      </template>-->

<!--      <template slot="qty" slot-scope="{row}" >-->
<!--        <el-input v-model="form.qty"></el-input>-->
<!--      </template>-->
<!--      <template slot="depreciationMethod" slot-scope="{row}" >-->
<!--        <el-input v-model="form.depreciationMethod"></el-input>-->
<!--      </template>-->
<!--      <template slot="recoverySubjectId" slot-scope="{row}" >-->
<!--        <el-input v-model="form.recoverySubjectId"></el-input>-->
<!--      </template>-->
    </avue-crud>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-dialog title="导入" :visible.sync="importDialogVisible" width="70%" @close="closeDow"
               :append-to-body="true">
      <avue-form ref="downForm" :option="option1" v-model="downForm" :upload-preview="uploadPreview"
                 :upload-error="uploadError" :upload-delete="uploadDelete" :upload-before="uploadBefore" :upload-after="uploadAfter" @submit="uploadFile">
        <template slot="attachmentsd" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">表头：
            编码、名称、类别、部门、录入期间、开始使用日期、备注、状态、折旧方法、预计使用年限、预计残值率、固定资产科目、累计折旧科目、<br /><br />折旧费用科目、资产购入科目、
            资产原值、已折旧期间数、期初累计折旧、期末累计折旧、期末净值、月折旧、期初净值、残值、购入凭证
          </el-button>
        </template>
        <template slot="attachments" slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleExcel()" style="font-size:15px;">点击下载基础信息模板
          </el-button>
        </template>
        <template slot="excleXls" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">文件后缀名必须为xls或xlsx （即Excel格式），文件大小不得大于1MB
          </el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="复制并新增资产" :visible.sync="copyVisible" @close="copyVisible=false" width="30%" height="40%" :append-to-body="true">
      <el-form ref="form1" :model="form1" label-width="180px">
        <el-form-item label="复制资产的数量">
          <el-input-number v-model="numCopy" :min="1" label="复制资产的数量"></el-input-number>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="copyVisible = false">取消</el-button>
          <el-button type="primary" @click="copyRecord()" >确定</el-button >
        </span>
      </template>
    </el-dialog>
  </basic-container>
</template>

<script>
import {formatDate,collatingData,getSubjectTree} from "@/api/supplier/finance/financialUtils";
import {getList, getDetail, add, update, remove, removeVoucher} from "@/api/supplier/finance/assetsDetails";
import {exportAssetsDetailsRecord} from "@/api/supplier/finance/exportExcel";
import {downAssetsDetailsTemplate, selectType, uploadFileAssetsDetails} from "@/api/supplier/finance/importExcel";
// import {getDeptTree} from "@/api/work/personnel/canteenDeptSetting";
import {getDeptTree} from "@/api/setting/dept/systemDeptSetting"
export default {
  data() {
    return {
      subjectTree:[],
      copyRow:{},
      copyIndex:0,
      numCopy:1,
      copyVisible:false,
      option1: {
        labelWidth: 250,
        card: true,
        group: [{
          label: '下载资产清单(卡片)',
          prop: 'group1',
          column: [{
            prop: 'attachmentsd',
            hide: true,
            formslot: true
          },
            {
              prop: 'attachments',
              span: 24,
              hide: true,
              formslot: true
            },
          ]
        },
          {
            label: '上传资产清单(卡片)',
            prop: 'group2',
            column: [{
              prop: "excleXls",
              hide: true,
              formslot: true
            },
              {
                label: '上传',
                prop: 'fileList',
                type: 'upload',
                loadText: '附件上传中，请稍等',
                span: 24,
                dataType: 'array',
                hide:true,
                limit:1,
                propsHttp: {
                  res: 'data',
                  url:'originalName'
                },
                rules: [{
                  required: true,
                  message: "请上传文件在提交",
                  trigger: "blur"
                }],
                action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              },
            ]
          },
        ]
      },
      downForm:{},
      uploadFilesList: [], //导入列表
      importDialogVisible:false,//导入
      expireTimeOption: {},
      form: {
        depreciationMethod: "平均年限法",
        createDate: '',
        qty:1,
        originalAmt:0,
        recoveryRate:0,
        expectedRecoveryAmt:0,
        // recoverySubjectId:0,
        // recoverySubjectCode:"510105",
        // recoverySubjectName:"固定资产折旧费用",
      },
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        addBtn: true,
        delBtn: true,
        editBtn: true,
        selection: true,
        align: 'center',
        showHeader:true,
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "资产编码",
            prop: "assetsCode",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            rules: [{
              required: true,
              message: "资产编码",
              trigger: "blur"
            }],
          },
          {
            label: "资产名称",
            prop: "assetsName",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            rules: [{
              required: true,
              message: "资产名称",
              trigger: "blur"
            }],
          },
          {
            label: "资产类别",
            prop: "typeId",
            type: "tree",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            dicUrl: "/api/service/rabbit-supplier/finance/financialAssetsFixedType/tree",
            props: {
              label: "typeName",
              value:"id"
            },
            rules: [{
              required: true,
              message: "类别名称",
              trigger: "blur"
            }],
            nodeClick:(data) => {
              // 折旧方式
              this.form.depreciationMethod = data.depreciationMethod;
              // 预计使用期限;
              this.form.expectedUseYear = data.expectedUseYear;
              // 固定资产科目
              this.form.fixedSubjectId = data.fixedSubjectId;
              // 累计折旧科目
              this.form.recoverySubjectId = data.recoverySubjectId;

              // 残值率
              this.form.recoveryRate = data.recoveryRate;
            }
          },
          {
            label: "录入期间",
            prop: "createPeriod",
            type: "input",
            addDisplay: true,
            addDisabled:true,
            editDisplay: true,
            editDisabled:true,
            viewDisplay: true,
            hide: true,
            search: false,
            rules: [{
              required: false,
              message: "录入期间",
              trigger: "blur"
            }],
          },

          {
            label: "使用部门",
            prop: "useDeptId",
            type: "tree",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            // dicUrl: "/api/service/rabbit-canteen/DeptSetting/tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              label: "title",
              value:"id"
            },
            rules: [{
              required: true,
              message: "使用部门",
              trigger: "blur"
            }],
          },
          {
            label: "开始使用日期",
            prop: "createDate",
            type: "date",
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            addDisplay: true,
            editDisplay: true,
            viewDisplay: false,
            search: false,
            formslot: true,
            labelWidth: 120,
            change: (data) => {
              if(data!=null && data != undefined) {
                  const strs = data.value.toString().split("-");
                  const lb = strs[0]+''+strs[1];
                  this.form.createPeriod = lb;
              }else{
                this.form.createPeriod = '';
              }
            },
            rules: [{
              required: true,
              message: "开始使用日期",
              trigger: "blur"
            }],
          },

          {
            label: "数量",
            prop: "qty",
            type: "number",
            addDisplay: true,
            addDisabled: true,
            editDisplay: true,
            editDisabled: true,
            viewDisplay: true,
            search: false,
            rules: [{
              required: false,
              message: "数量",
              trigger: "blur"
            }],
          },

          {
            label: "折旧方式",
            prop: "depreciationMethod",
            type: "input",
            addDisplay: true,
            addDisabled: true,
            editDisplay: true,
            editDisabled: true,
            viewDisplay: true,
            search: false,
            rules: [{
              required: false,
              message: "折旧方式",
              trigger: "blur"
            }],
          },
          {
            label: "预计使用期限",
            prop: "expectedUseYear",
            type: "number",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: false,
            labelWidth: 100,
            rules: [{
              required: true,
              message: "预计使用期限",
              trigger: "blur"
            }],
          },
          {
            label: "固定资产科目",
            prop: "fixedSubjectId",
            type: "tree",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            labelWidth: 100,
            dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/1601',
            // dicData:[],
            props: {
              label: "subjectName",
              value:"id"
            },
            rules: [{
              required: true,
              message: "固定资产科目",
              trigger: "blur"
            }],
          },
          {
            label: "累计折旧科目",
            prop: "recoverySubjectId",
            type: "tree",
            addDisplay: true,
            // addDisabled: true,
            editDisplay: true,
            // editDisabled: true,
            viewDisplay: true,
            labelWidth: 100,
            dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/1602',
            // dicData:[],
            props: {
              label: "subjectName",
              value:"id"
            },
            rules: [{
              required: false,
              message: "累计折旧科目",
              trigger: "blur"
            }],
          },
          {
            label: "折旧费用科目",
            prop: "feeSubjectId",
            type: "tree",
            addDisplay: true,

            editDisplay: true,
            viewDisplay: true,
            labelWidth: 100,
            dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/510105',
            // dicData:[],
            props: {
              label: "subjectName",
              value:"id"
            },
            rules: [{
              required: true,
              message: "折旧费用科目",
              trigger: "blur"
            }],
          },
          {
            label: "资产购入对方科目",
            prop: "buySubjectId",
            type: "tree",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            labelWidth: 130,
            // dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope',
            dicData:[],
            props: {
              label: "subjectName",
              value:"id"
            },
            rules: [{
              required: true,
              message: "资产购入对方科目",
              trigger: "blur"
            }],
          },
          {
            label: "原值",
            prop: "originalAmt",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: false,
            rules: [{
              required: true,
              message: "原值",
              trigger: "blur"
            }],
            change: (currentValue, oldValue)=> {
              this.calculateExpectedValue();
            },
            blur: ()=>{
              this.calculateExpectedValue();
            },
          },
          {
            label: "残值率(%)",
            prop: "recoveryRate",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: false,
            rules: [{
              required: true,
              message: "残值率",
              trigger: "blur"
            }],
            change: (currentValue, oldValue)=> {
              this.calculateExpectedValue();
            },
            blur: ()=> {
              this.calculateExpectedValue();
            },
          },
          {
            label: "预计残值",
            prop: "expectedRecoveryAmt",
            type: "input",
            addDisplay: true,
            addDisabled: true,
            editDisplay: true,
            editDisabled: true,
            viewDisplay: true,
            search: false,
            rules: [{
              required: false,
              message: "预计残值",
              trigger: "blur"
            }],
          },
          {
            label: "期初累计折旧",
            prop: "beginRecoveryAmt",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            addDisabled: true,
            editDisabled: true,
            search: false,
            labelWidth: 100,
            // rules: [{
            //   required: true,
            //   message: "期初累计折旧",
            //   trigger: "blur"
            // }],
          },

          {
            label: "期初净值",
            prop: "beginAmt",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            addDisabled: true,
            editDisabled: true,
            search: false,
            // rules: [{
            //   required: true,
            //   message: "期初净值",
            //   trigger: "blur"
            // }],
          },

          {
            label: "期末累计折旧",
            prop: "endRecoveryAmt",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            labelWidth: 100,
          },
          {
            label: "月折旧",
            prop: "monthRecoveryAmt",
            type: "input",
            addDisplay: true,
            addDisabled: true,
            editDisplay: true,
            editDisabled: true,
            viewDisplay: true,
            search: false,
            // rules: [{
            //   required: true,
            //   message: "月折旧",
            //   trigger: "blur"
            // }],
          },

          {
            label: "已折旧期间",
            prop: "haveRecoveryPeriod",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            addDisabled: true,
            editDisabled: true,
            search: false,
            // rules: [{
            //   required: true,
            //   message: "已折旧期",
            //   trigger: "blur"
            // }],
          },
          {
            label: "期末净值",
            prop: "endAmt",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // rules: [{
            //   required: true,
            //   message: "期末净值",
            //   trigger: "blur"
            // }],
          },
          {
            label: "状态",
            prop: "assetsStatus",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false
          },
          {
            label: "备注",
            prop: "remarks",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: false
          },
          {
            label: "购入凭证",
            prop: "buyVoucherCode",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            width: 150,
            // rules: [{
            //   required: true,
            //   message: "购入凭证",
            //   trigger: "blur"
            // }],
          },
          {
            label: "清理凭证",
            prop: "cleanVoucherCode",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // rules: [{
            //   required: true,
            //   message: "清理凭证",
            //   trigger: "blur"
            // }],
          },
          {
            label: "减值准备凭证",
            prop: "recoveryPreVoucherCode",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            labelWidth: 100,
            // rules: [{
            //   required: true,
            //   message: "减值准备凭证",
            //   trigger: "blur"
            // }],
          },
        ]
      },
      data: [],
    };
  },
  computed: {
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created(){
    var self = this;
    this.loading = true;

    getSubjectTree().then(res=>{
      self.subjectTree = res.data.data;
      // const column11 = this.option.column[11];
      // const column12 = this.option.column[12];
      // const column13 = this.option.column[13];
      // const column14 = this.option.column[14];
      // column11.dicData = self.subjectTree;
      // column12.dicData = self.subjectTree;
      // column13.dicData = self.subjectTree;
      // column14.dicData = self.subjectTree;

      var index_buySubjectId = this.$refs.crud.findColumnIndex("buySubjectId");
      this.option.column[index_buySubjectId].dicData = self.subjectTree;
    }).catch(err=>{}).finally(fin=>{this.loading = false;});

    // this.initData();
    this.observable.addObserver(this)
  },
  mounted() {
    this.expireTimeOption = {
      disabledDate(date) {
        //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
        const startTime = new Date();
        const firstTime = new Date(startTime.getFullYear(),startTime.getMonth(),0);
        return (
          date.getTime() <= firstTime.getTime()
        );
      }
    };
  },
  destroyed(){
    this.observable.removeObserver(this)
  },
  methods: {
    update(){
      console.log('执行刷新')
      this.onLoad(this.page);
    },
    // originalAmtHandleChange(value) {
    //
    // },
    // recoveryRateHandleChange(value) {
    //
    // },
    // expectedRecoveryAmtHandleChange(value) {
    //
    // },
    calculateExpectedValue() {
      //原值
      var originalAmt_value = this.form.originalAmt;
      //残值率
      var recoveryRate_value = this.form.recoveryRate;
      //预计残值=原值*残值率
      var expectedRecoveryAmt_value = originalAmt_value*recoveryRate_value*0.01;
      this.form.expectedRecoveryAmt = expectedRecoveryAmt_value;
      this.form.beginAmt = originalAmt_value//新增时候，原值等期初净值
    },
    // initData() {
    //   getDeptTree().then(res => {
    //     const data = res.data.data;
    //     const index = this.$refs.crud.findColumnIndex("useDeptId");
    //     this.option.column[index].dicData = data;
    //   });
    // },
    changeCreateDate(data) {
      if(data!=null && data != undefined) {
        const bus_data = formatDate("yyyy-MM-dd", data);
        const strs = bus_data.split("-");
        const lb = strs[0]+''+strs[1];
        this.form.createPeriod = lb;
      }else{
        this.form.createPeriod = '';
      }
    },
    rowSave(row, loading, done) {
      this.$confirm("资产一经保存后不可修改,请确保信息无误?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          row.depreciationMethod = "平均年限法";
          if(row.qty == undefined || row.qty == 0) {
            row.qty = 1;
          }
          // row.recoverySubjectId = 0;
          // row.recoverySubjectCode = "510105";
          // row.recoverySubjectName ="固定资产折旧费用";

          add(row).then(() => {
            loading();
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          }, error => {
            done();
            window.console.log(error);
          }).then(()=>{
            done();
          });
        })
      .then(()=>{
        done();
      }).catch(()=>{done()});
    },
    rowUpdate(row, index, loading, done) {
      this.$confirm("资产一经保存后不可修改,请确保信息无误?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          update(row).then(() => {
            loading();
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          }, error => {
            done();
            window.console.log(error);
          })
          .then(()=>{
            done();
          });
        })
      .then(()=>{
        done();
      }).catch(()=>{done()});
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      //数量指定为1，计算方法指定为"平均年限法"，累计折旧科目指定：固定资产折旧费用
      this.form.depreciationMethod = "平均年限法";
      if(this.form.qty == undefined || this.form.qty == 0) {
        this.form.qty = 1;
      }
      // this.form.recoverySubjectId = 0;
      // this.form.recoverySubjectCode = "510105";
      // this.form.recoverySubjectName ="固定资产折旧费用";
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      // this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleClickPreview: function(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    //导入导出
    exportRecord(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportAssetsDetailsRecord(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '资产清单(卡片).xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    importData(){
      this.importDialogVisible = true;
      // uploadFileAssetsDetails("");
    },
    closeDow(){
      this.downForm.fileList = "";
      this.uploadFilesList = [];
      this.importDialogVisible = false;
    },
    uploadFile(row,loading){
      console.log(">>>>>>>>>>>>>>",JSON.stringify(this.uploadFilesList[0].value))
      const loading1 = this.$loading({
        lock: true,
        text: '正在导入表格数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadFileAssetsDetails(this.uploadFilesList[0].value).then(res => {
        if (res.data.data === 'error' || res.data.code=='200'){
          loading1.close()
          loading();
          this.downForm.fileList = "";
          this.uploadFilesList = [];
          this.$alert(res.data.message, '信息', {
            confirmButtonText: '确定'
          })
        }else {
          loading1.close()
          loading();
          this.downForm.fileList = "";
          this.uploadFilesList = [];
          this.$message({
            type: "success",
            message: "导入成功"
          });
          this.page.currentPage = 1;
          this.onLoad(this.page);
          this.importDialogVisible = false;
        }
      });
    },
    handleExcel(){
      downAssetsDetailsTemplate().then(res => {
        const blob = new Blob([res.data]);
        const fileName = '资产清单(卡片)模板.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    uploadBefore(file, done, loading,column) {
      /*        console.log(">>>>>>>>>上传前的方法",file)*/
      var str = file.name;
      str = str.substring(str.lastIndexOf(".") + 1, str.length);
      var reStr = selectType(str);
      if (reStr == "NO") {
        loading();
        this.$message.error('文件格式错误,只能上传"[xls,xlsx]"格式');
        return false;
      } else {
        /*    var newFile = new File([file], '1234', { type: file.type });*/
        this.fileName = file.name
        done()
        return true;
      }
    },
    uploadError(error, column) {
      console.log("文件路径000000")
    },
    uploadAfter(res, done, loading,column) {
      console.log("文件路径",JSON.stringify(res))
      let proof = {
        label: this.fileName,
        value: res.link
      }
      this.uploadFilesList.push(proof);
      done()
    },
    uploadPreview(file,column,done){
      done()//默认执行打开方法
    },
    uploadDelete(column,file) {
      this.uploadFilesList.splice(file.uid,1);
    },
    copyDialog(row) {
      this.copyVisible = true;
      this.form = row;
    },
    //复制单据
    copyRecord() {
      this.copyVisible = false;
      // this.$refs.crud.rowEdit(this.copyRow, this.copyIndex);
      this.form.qty = this.numCopy;
      this.$refs.crud.rowAdd();
    },
    //生成凭证
    generateVoucher(row) {
      //判断是否选择了记录，是单行还是多行
      var rowList = [];
      if(row != null && row != undefined) {
        rowList.push(row);
      }else {
        rowList = this.selectionList;
      }
      if (rowList.length === 0) {
        this.$message.warning("请选择要生成凭证的固定资产");
        return;
      }
      var list = [];
      for(var i=0;i<rowList.length;i++) {//多条记录
        var it = rowList[i];
        //判断是否已经生成了凭证
        if(it.buyVoucherId!=null&&it.buyVoucherId!=undefined&&it.buyVoucherId.length>0&&it.buyVoucherId>0) {
          this.$message.warning("固定资产["+it.assetsName+"]已经生成了凭证,不能重复生成");
          return;
        }
        //判断金额不能为零
        if(it.originalAmt==null||it.originalAmt==undefined||it.originalAmt==0) {
          this.$message.warning("固定资产["+it.assetsName+"]的[原值]为0");
          return;
        }
        if(it.fixedSubjectId==null||it.fixedSubjectId==undefined||it.fixedSubjectId==0) {
          this.$message.warning("固定资产["+it.assetsName+"]未设置[固定资产科目]");
          return;
        }
        if(it.buySubjectId==null||it.buySubjectId==undefined||it.buySubjectId==0) {
          this.$message.warning("固定资产["+it.assetsName+"]未设置[资产购入对方科目]");
          return;
        }
        //判断固定资产科目不能为空
        //判断购入科目不能为空
      }
      var billIds = '';
      for(var j=0;j<rowList.length;j++) {//多条记录
        it = rowList[j];
        billIds+=it.id + ',';
        //固定资产借方
        var basicDebitAmount = it.originalAmt;//借
        var basicCreditAmount = 0;//贷
        var explanation = '购入'+it.assetsName;//摘要
        var accountNumber = it.fixedSubjectId;//subjectId
        var accountName = it.fixedSubjectCode + " " +it.fixedSubjectName;//subjectName
        var accountCode = it.fixedSubjectCode;//subjectCode
        var auxiliaryType = '';
        var itemObjectKeys = '';

        var debtor = basicDebitAmount == 0 ? '' : basicDebitAmount + '';
        var debtorList = debtor == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(debtor, ['', '', '', '', '', '', '', '', 0, 0, 0]);
        var lender = basicCreditAmount == 0 ? '' : basicCreditAmount + '';
        var lenderList = lender == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(lender, ['', '', '', '', '', '', '', '', 0, 0, 0]);
        var obj = {
          main: explanation,
          isShowMainInput: false,
          subject: {
            number: accountNumber,
            name: accountName,
            code: accountCode,
            detailJson: auxiliaryType
          },
          isShowSubjectInput: false,
          debtor: debtor,
          debtorList: debtorList,
          isShowDebtorInput: false,
          lender: lender,
          lenderList: lenderList,
          isShowLenderInput: false,
          isAuxiliary: itemObjectKeys == '' ? false : true,
          auxiliary: itemObjectKeys
        };
        list.push(obj);
        //固定资产贷方
        basicDebitAmount = 0;//借
        basicCreditAmount = it.originalAmt;//贷
        explanation = '购入'+it.assetsName;//摘要
        accountNumber = it.buySubjectId;//subjectId
        accountName = it.buySubjectCode +" "+it.buySubjectName;//subjectName
        accountCode = it.buySubjectCode;//subjectCode
        auxiliaryType = '';
        itemObjectKeys = '';

        debtor = basicDebitAmount == 0 ? '' : basicDebitAmount + '';
        debtorList = debtor == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(debtor, ['', '', '', '', '', '', '', '', 0, 0, 0]);
        lender = basicCreditAmount == 0 ? '' : basicCreditAmount + '';
        lenderList = lender == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(lender, ['', '', '', '', '', '', '', '', 0, 0, 0]);
        obj = {
          main: explanation,
          isShowMainInput: false,
          subject: {
            number: accountNumber,
            name: accountName,
            code: accountCode,
            detailJson: auxiliaryType
          },
          isShowSubjectInput: false,
          debtor: debtor,
          debtorList: debtorList,
          isShowDebtorInput: false,
          lender: lender,
          lenderList: lenderList,
          isShowLenderInput: false,
          isAuxiliary: itemObjectKeys == '' ? false : true,
          auxiliary: itemObjectKeys
        };
        list.push(obj);
      }
      this.$router.push({
        path: "/finance/financialVoucherAdd",
        query: {listQuery:list,sourceType:'1601',sourceBillIds:billIds}
      });

      //刷新页面
      this.onLoad(this.page);
    },
    viewVoucher(row) {
      this.$router.push({
        path: "/financialVoucherEdit",
        query: {id:row.buyVoucherId}
      });
    },
    deleteVoucher(row) {
      this.$confirm("确定删除凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return removeVoucher(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
  }
};
</script>
