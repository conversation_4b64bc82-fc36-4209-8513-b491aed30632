<template>
  <basic-container>
    <div v-if="isShowDialog != true" class="all-mess">
        <avue-crud :option="goodsOption"
                   :data="goodsData"
                   :page="goodsPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="goodsForm"
                   ref="goodsForm"
                   @search-change="searchChangeGoods"
                   @search-reset="searchResetGoods"
                   @current-change="currentChangeGoods"
                   @size-change="sizeChangeGoods"
                   @on-load="onGoodsLoad">
          <template slot="menu" slot-scope="scope">
            <el-button type="text" size="small" icon="el-icon-view" @click="opendialog(scope.row.canteenName, scope.row.canteenId)">查看
            </el-button>
          </template>
        </avue-crud>
    </div>
    <div v-if="isShowDialog">
      <el-row>
        <el-col span="1">
          <el-button icon="el-icon-back" @click="closeDialog">返回</el-button>
        </el-col>
        <el-col span="22">
          <div style="text-align: center;">
            <span style="padding:15px 20px;font-size:20px; font-weight:900">{{ canteenName }}</span>
          </div>
        </el-col>
        <el-col span="1">
        </el-col>
      </el-row>

      <!-- <div class="mess-header mess_list_dialog"> -->
        <div class="mess-header mess_list_dialog">
          <div class="mess_item_first"><span>基础信息</span></div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==0 }" @click="typeClick(0 )">食堂信息</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==1 }" @click="typeClick(1 )">食材信息</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==2 }" @click="typeClick(2 )">供应商信息</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==3 }" @click="typeClick(3 )">用餐人员</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==4 }" @click="typeClick(4 )">食堂职工</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==5 }" @click="typeClick(5 )">违规预警</div>
        </div>
        <div class="mess-header mess_list_dialog">
          <div class="mess_item_first"><span>食堂管理</span></div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==6 }" @click="typeClick(6 )">职工考勤</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==7 }" @click="typeClick(7 )">食堂招标</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==8 }" @click="typeClick(8 )">合同信息</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==9 }" @click="typeClick(9 )">采购订单</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==10}" @click="typeClick(10)">采购进仓</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==11}" @click="typeClick(11)">商品库存</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==12}" @click="typeClick(12)">出仓记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==13}" @click="typeClick(13)">盘点记录</div>
        </div>
        <div class="mess-header mess_list_dialog">
          <div class="mess_item_first"><span>充值消费</span></div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==14}" @click="typeClick(14)">充值记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==15}" @click="typeClick(15)">充值统计</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==16}" @click="typeClick(16)">消费记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==17}" @click="typeClick(17)">消费统计</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==18}" @click="typeClick(18)">纠错记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==19}" @click="typeClick(19)">退款记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==20}" @click="typeClick(20)">存档退款记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==21}" @click="typeClick(21)">存档退款统计</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==22}" @click="typeClick(22)">取现记录</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==23}" @click="typeClick(23)">取现统计</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==24}" @click="typeClick(24)">消费结算表</div>
        </div>
        <!-- <div class="mess-header mess_list_dialog">
          <div class="mess_item_first"><span>食堂财务</span></div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==25}" @click="typeClick(25)">财务凭证</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==26}" @click="typeClick(26)">现金日记账</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==27}" @click="typeClick(27)">存款日记账</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==28}" @click="typeClick(28)">明细账</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==29}" @click="typeClick(29)">总账</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==30}" @click="typeClick(30)">科目余额表</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==31}" @click="typeClick(31)">科目汇总表</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==32}" @click="typeClick(32)">资产负债表</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==33}" @click="typeClick(33)">收入费用表</div>
          <div class="mess_item_dialog" :class="{acitve_dialog:activeIdx2==34}" @click="typeClick(34)">固定资产</div>
        </div> -->
        <hr/>
        <!-- <div
          class="mess_item_dialog"
          :class="{acitve_dialog:activeIdx2==index}"
          v-for="(item,index) in menuList"
          :key="index" @click="typeClick(index)">
          {{item}}
        </div> -->
      <!-- </div> -->

      <canteenManage :schoolId="canteenId" v-if="idx==1"/><!-- 食堂信息 -->
      <foodGoods :schoolId="canteenId" v-if="idx==2"/><!-- 食材信息 -->
      <supplier :schoolId="this.canteenId" v-if="idx==3"/><!-- 供应商信息 -->
      <systemPersonnel :schoolId="this.canteenId" v-if="idx==4"/><!-- 用餐人员 -->
      <canteenUser :schoolId="this.canteenId" v-if="idx==5"/><!-- 食堂职工 -->
      <canteenWarn :schoolId="this.canteenId" v-if="idx==6"/><!-- 违规预警 -->
      <userAttendance :schoolId="this.canteenId" v-if="idx==7"/><!-- 职工考勤 -->
      <canteenBidding :schoolId="this.canteenId" v-if="idx==8"/><!-- 食堂招标 -->
      <!--<supplyAgreement :schoolId="this.canteenId" v-if="idx==9"/>--><!-- 供应合同 -->
      <contractManagement :schoolId="this.canteenId" v-if="idx==9"/><!-- 合同信息 -->
      <canteenOrder :schoolId="this.canteenId" v-if="idx==10"/><!-- 采购订单 -->
      <canteenAccepter :schoolId="this.canteenId" v-if="idx==11"/><!-- 采购进仓 -->
      <goodsStorage :schoolId="this.canteenId" v-if="idx==12"/><!-- 商品库存 -->
      <canteenOutStorage :schoolId="this.canteenId" v-if="idx==13"/><!-- 出仓记录 -->
      <canteenStorage :schoolId="this.canteenId" v-if="idx==14"/><!-- 盘点记录 -->
      <topUpLogging :schoolId="this.canteenId" v-if="idx==15"/><!-- 充值明细 -->
      <rechargeStatistics :schoolId="this.canteenId" v-if="idx==16"/><!-- 充值统计 -->
      <consumDetails :schoolId="this.canteenId" v-if="idx==17"/><!-- 消费明细 -->
      <consumeStatistics :schoolId="this.canteenId" v-if="idx==18"/><!-- 消费统计 -->
      <errorCorrectionDetails :schoolId="this.canteenId" v-if="idx==19"/><!-- 纠错明细 -->
      <refundTransferLogging :schoolId="this.canteenId" v-if="idx==20"/><!-- 退款明细 -->
      <archiveRefund :schoolId="this.canteenId" v-if="idx==21"/><!-- 存档退款明细 -->
      <archiveRefundSum :schoolId="this.canteenId" v-if="idx==22"/><!-- 存档退款统计 -->
      <withdrawCashLogging :schoolId="this.canteenId" v-if="idx==23"/><!-- 取现明细 -->
      <withdrawCashStatistics :schoolId="this.canteenId" v-if="idx==24"/><!-- 取现统计 -->
      <consumptionDayStatistics :schoolId="this.canteenId" v-if="idx==25"/><!-- 消费结算表 -->
      <financialVoucherList :schoolId="canteenId" v-if="idx==26"/><!-- 财务凭证 -->
      <stockCashJournal :schoolId="canteenId" v-if="idx==27"/><!-- 现金日记账 -->
      <bankDepositJournal :schoolId="canteenId" v-if="idx==28"/><!-- 存款日记账 -->
      <generalLedger :schoolId="canteenId" v-if="idx==29"/><!-- 明细账 -->
      <subjectBalanceSheet :schoolId="canteenId" v-if="idx==30"/><!-- 总账 -->
      <subjectSummarySheet :schoolId="canteenId" v-if="idx==31"/><!-- 科目余额表 -->
      <subsidiaryLedger :schoolId="canteenId" v-if="idx==32"/><!-- 科目汇总表 -->
      <financialReportsBalanceSheet :schoolId="canteenId" v-if="idx==33"/><!-- 资产负债表 -->
      <financialReportsIncomeAndPay :schoolId="canteenId" v-if="idx==34"/><!-- 收入费用表 -->
      <assetsDetails :schoolId="this.canteenId" v-if="idx==35"/><!-- 固定资产 -->
    </div>
  </basic-container>
</template>

<script>
import {getServeFoodUnitList, getCanteenUnitList, getSchoolInfo} from "@/api/liancan/statistical";
import detailVue from "@/views/liancan/statisticalNew/detail";
import canteenManage from "@/views/liancan/statisticalNew/info/canteenManage";
import foodGoods from "@/views/liancan/statisticalNew/info/foodGoods";
import supplier from "@/views/supplier/list/index.vue";
import systemPersonnel from "@/views/liancan/statisticalNew/info/systemPersonnel";
import canteenUser from "@/views/liancan/statisticalNew/info/canteenUser";
import userAttendance from "@/views/liancan/statisticalNew/info/userAttendance";
import canteenBidding from "@/views/liancan/statisticalNew/info/canteenBidding";
import canteenWarn from "@/views/liancan/statisticalNew/info/canteenWarn";
import supplyAgreement from "@/views/liancan/statisticalNew/info/supplyAgreement";
import contractManagement from "@/views/liancan/contractManagement/index.vue";
import canteenOrder from "@/views/liancan/statisticalNew/info/canteenOrder";
import canteenAccepter from "@/views/liancan/statisticalNew/info/canteenAccepter";
import goodsStorage from "@/views/liancan/goodsStorage/index.vue";
import canteenOutStorage from "@/views/liancan/statisticalNew/info/canteenOutStorage";
import canteenStorage from "@/views/liancan/statisticalNew/info/canteenStorage";
import topUpLogging from "@/views/liancan/statisticalNew/info/topUpLogging";
import rechargeStatistics from "@/views/liancan/statisticalNew/info/rechargeStatistics";
import consumDetails from "@/views/liancan/statisticalNew/info/consumDetails";
import consumeStatistics from "@/views/liancan/statisticalNew/info/consumeStatistics";
import errorCorrectionDetails from "@/views/liancan/statisticalNew/info/errorCorrectionDetails";
import refundTransferLogging from "@/views/liancan/statisticalNew/info/refundTransferLogging";
import archiveRefund from "@/views/liancan/statisticalNew/info/archiveRefund";
import archiveRefundSum from "@/views/liancan/statisticalNew/info/archiveRefundSum";
import withdrawCashLogging from "@/views/liancan/statisticalNew/info/withdrawCashLogging";
import withdrawCashStatistics from "@/views/liancan/statisticalNew/info/withdrawCashStatistics";
import consumptionDayStatistics from "@/views/consumptionStatistics/consumptionDayStatistics.vue";



import financialVoucherList from "@/views/finance/financialVoucherList.vue";
import stockCashJournal from "@/views/finance/cashierAudit/stockCashJournal.vue";
import bankDepositJournal from "@/views/finance/cashierAudit/bankDepositJournal.vue";
import generalLedger from "@/views/finance/accountBookDetails/generalLedger.vue";
import subjectBalanceSheet from "@/views/finance/accountBookDetails/subjectBalanceSheet.vue";
import subjectSummarySheet from "@/views/finance/accountBookDetails/subjectSummarySheet.vue";
import subsidiaryLedger from "@/views/finance/accountBookDetails/subsidiaryLedger.vue";
import financialReportsBalanceSheet from "@/views/finance/accountSets/financialReportsBalanceSheet.vue";
import financialReportsIncomeAndPay from "@/views/finance/accountSets/financialReportsIncomeAndPay.vue";
import assetsDetails from "@/views/finance/assetsDetails/assetsDetailsView.vue";

import {mapGetters} from "vuex";
var DIC = {
  agencyType: [
    {
      label: '学校',
      value: "0"
    },
    {
      label: '政府机关',
      value: "1"
    },
    {
      label: '事业单位',
      value: "2"
    },
    {
      label: '国企',
      value: "3"
    },
    {
      label: '其他',
      value: "4"
    },
  ],
}
export default {
  data() {
    return {
      activeIdx: 0,
      messList: [ '供餐单位', '供餐单位设立的食堂' ],
      form: {},
      goodsForm:{},
      query: {},
      goodsQuery:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      goodsPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      isShow: false,
      resOrderId: null,
      confirmBat: false,
      pageParams: {},
      detailVisible: false,
      option: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: "所属单位",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            viewDisplay: false,
            editDisplay: false,
            search: false,
            hide: true,
          },
          {
            label: "单位名称",
            prop: "deptName",
            type: "input",
            search: true,
          },
          {
            label: "单位类型",
            prop: "agencyType",
            type: "select",
            dicData: DIC.agencyType,
          },
          {
            label: "单位地址",
            prop: "address",
            type: "input",
          },
          {
            label: "管理员姓名",
            prop: "userName",
            type: "input",
          },
          {
            label: "管理员手机号",
            prop: "userPhone",
            type: "input",
          },
        ]
      },
      //index 2
      goodsOption: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: "食堂id",
            prop: "canteenId",
            type: "input",
            hide: true,
          },
          {
              label: '省份',
              prop: 'province',
              type: 'select',
              props: {
              label: 'regionName',
              value: 'id'
              },
              cascaderItem: ['city', 'area'],
              search:true,
              hide: true,
              viewDisplay: false,
              dicUrl: `/api/rabbit-system/region/getProvince`,
              rules: [
              {
                required: true,
                message: '请选择省份',
                trigger: 'blur'
              }
              ]
            },
            {
              label: '城市',
              prop: 'city',
              type: 'select',
              props: {
              label: 'regionName',
              value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
              rules: [
              {
                required: true,
                message: '请选择城市',
                trigger: 'blur'
              }
              ]
            },
            {
              label: '地区',
              prop: 'area',
              type: 'select',
              props: {
              label: 'regionName',
              value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
              rules: [
              {
                required: true,
                message: '请选择地区',
                trigger: 'blur'
              }
              ]
            },
          {
            label: "所属单位",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            viewDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "食堂名称",
            prop: "canteenName",
            search: true,
            type: "input",
          },
          {
            label: "食堂类型",
            prop: "canteenType",
            addDisplay: false,
            editDisplay: false,
            type: "select",
            dicData: [
              {
                label: "学生食堂",
                value: "1"
              },
              {
                label: "教师食堂(单位食堂)",
                value: "2"
              }
            ],
          },
          {
            label: "经营方式",
            prop: "operationMode",
            addDisplay: false,
            editDisplay: false,
            type: "select",
            dicData: [
              {
                label: "自主经营",
                value: "1"
              },
              {
                label: "承包经营",
                value: "2"
              }
            ],
          },
          {
            label: "食堂负责人",
            prop: "userName",
            type: "input",
          },
          {
            label: "负责人电话",
            prop: "userPhone",
            type: "input",
          },
          {
            label: "食堂状态",
            prop: "status",
            addDisplay: false,
            editDisplay: false,
            type: "select",
            dicData: [
              {
                label: "启用",
                value: "0"
              },
              {
                label: "停用",
                value: "1"
              }
            ],
          },
          {
            label: '建立日期',
            prop: 'createTime',
            type: 'datetime',
            overHidden: true,
            format: 'yyyy-MM-dd',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          // {
          //   label: "建立人",
          //   prop: "createUser",
          //   type: "select",
          //   dicUrl: '/api/service/rabbit-user/user-list',
          //   props: {
          //     label: "realName",
          //     value: "id"
          //   },
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          // },
          {
            label: "建立人",
            prop: "createUser",
            type: "input",
          },
          {
            label: "启用账套",
            prop: "isUse",
            addDisplay: false,
            editDisplay: false,
            type:"select",
            dicData: [
              {
                label: "未启用",
                value: 0
              },
              {
                label: "启用",
                value: 1
              }
            ],
          },
        ]
      },
      data: [],
      goodsData:[],
      isShowDialog: false,
      canteenName: null,
      canteenId: null,
      activeIdx2: 0,
      idx: 0,
    }
  },
  components: {
    'canteenManage': canteenManage,
    'foodGoods': foodGoods,
    'supplier': supplier,
    'systemPersonnel': systemPersonnel,
    'canteenUser': canteenUser,
    'userAttendance': userAttendance,
    'financialVoucherList': financialVoucherList,
    'stockCashJournal': stockCashJournal,
    'bankDepositJournal': bankDepositJournal,
    'generalLedger': generalLedger,
    'subjectBalanceSheet': subjectBalanceSheet,
    'subjectSummarySheet': subjectSummarySheet,
    'subsidiaryLedger': subsidiaryLedger,
    'financialReportsIncomeAndPay': financialReportsIncomeAndPay,
    'financialReportsBalanceSheet': financialReportsBalanceSheet,
    'canteenBidding': canteenBidding,
    'canteenWarn': canteenWarn,
    'supplyAgreement': supplyAgreement,
    'contractManagement': contractManagement,
    'canteenOrder': canteenOrder,
    'canteenAccepter': canteenAccepter,
    'goodsStorage': goodsStorage,
    'canteenOutStorage': canteenOutStorage,
    'canteenStorage': canteenStorage,
    'topUpLogging': topUpLogging,
    'rechargeStatistics': rechargeStatistics,
    'consumDetails': consumDetails,
    'consumeStatistics': consumeStatistics,
    'errorCorrectionDetails': errorCorrectionDetails,
    'refundTransferLogging': refundTransferLogging,
    'archiveRefund': archiveRefund,
    'archiveRefundSum': archiveRefundSum,
    'withdrawCashLogging': withdrawCashLogging,
    'withdrawCashStatistics': withdrawCashStatistics,
    'consumptionDayStatistics': consumptionDayStatistics,
    'assetsDetails': assetsDetails,
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
    },
    ids() {
    }
  },
  created(){
  },
  methods: {
    menuClick(idx) {
      this.query=[];
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.query = {};
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
      if (idx == 1){
        this.query = {};
        this.goodsPage.currentPage = 1;
        this.onGoodsLoad(this.goodsPage);
      }
    },
    showDetail (row) {
      getSchoolInfo(row.deptId).then(res => {
        console.log('showDetail ===== ', res)
        this.detailVisible = true;
        this.pageParams = res.data.data;
      })
    },
    beforeOpen(done, type) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchResetGoods(){
      this.goodsQuery = {};
      this.onGoodsLoad(this.goodsPage);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchChangeGoods(params, done) {
      this.goodsQuery = params;
      this.goodsPage.currentPage = 1
      this.onGoodsLoad(this.goodsPage, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    currentChangeGoods(currentPage){
      this.goodsPage.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    sizeChangeGoods(pageSize){
      this.goodsPage.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getServeFoodUnitList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getServeFoodUnitList ===== ', res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    onGoodsLoad(page, params = {}) {
      this.loading = true;
      getCanteenUnitList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getCanteenUnitList ===== ', res)
        const data = res.data.data;
        this.goodsPage.total = data.total;
        this.goodsData = data.records;
        this.loading = false;
      })
    },
    typeClick(num) {
      this.activeIdx2  = num;
      this.idx = num + 1;
    },
    opendialog(name,id){
      console.log('opendialog ==== ', name, id)
      this.canteenName = name;
      this.canteenId = id;
      this.isShowDialog = true;
      this.activeIdx2  = 0;
      this.idx = 1;
    },
    closeDialog(){
      this.isShowDialog = false;
      this.idx = -1;
    },
  }
}
</script>

<style lang="scss" scoped>
.fontsize {
  font-weight: 700;
}
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    margin-bottom: 20px;
    /deep/ div {
      width: 160px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
.mess_list_dialog {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .mess_item_first {
    margin: -14px 20px 0 0;
    font-weight: bold;
    font-size: 20px;
  }
  .mess_item_dialog {
    width: 120px;
    height: 37px;
    line-height: 35px;
    text-align: center;
    border: 1px solid #C0C0C0;
    margin: 0 10px 10px 0;
    border-radius: 12px
    /*&:hover {*/
    /*  color: #333333;*/
    /*  background: #1b16f1;*/
    /*}*/
  }
  .acitve_dialog {
    background: #409EFF;
  }
}
</style>
