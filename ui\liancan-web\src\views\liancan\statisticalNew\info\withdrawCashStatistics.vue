<template>
  <basic-container>
    <avue-crud :option="monthOption"
               :data="monthData"
               :page="monthPage"
               :table-loading="monthLoading"
               :before-open="beforeOpen"
               v-model="monthForm"
               ref="monthForm"
               @search-change="searchChangeMonth"
               @search-reset="searchResetMonth"
               @selection-change="selectionChangeMonth"
               @current-change="currentChangeMonth"
               @size-change="sizeChangeMonth"
               @on-load="onLoadMonth">
      <template slot="menuLeft">
        <el-button v-if="permission.withdraw_cash_statistics_exports && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >备注：当天的取现统计数据需到凌晨后统计。
        </a>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove,getMonthList,getYearList,getDeptList,exportDateFoodData,exportMonthFoodData,exportYearFoodData,exportDeptData} from "@/api/queryStatistics/withdrawCashStatistics";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      query: {},
      monthForm:{},
      monthPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      monthLoading:true,
      monthOption:{
        /*          height:'auto',*/
        /*    calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        addBtn:false,
        menu:false,
        selection: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'unifiedPaymentMealBalance',
            type: 'sum'
          },
          {
            name: 'optionalMealBalance',
            type: 'sum'
          },
          {
            name:'sumBalance',
            type: 'sum'
          }
        ],
        column: [
          /*          {
                      label: "单位",
                      prop: "unitId",
                      type: "select",
                      dicUrl: "/api/service/rabbit-system/dept/select",
                      props: {
                        label: "deptName",
                        value:"id"
                      },
                      hide: false,
                      search:true,
                      rules: [{
                        required: true,
                        message: "请选择部门",
                        trigger: "click"
                      }]
                    },
                    {
                      label: "学校",
                      prop: "schoolId",
                      type: "select",
                      dicUrl: "/api/service/rabbit-system/dept/select",
                      props: {
                        label: "deptName",
                        value:"id"
                      },
                      hide: false,
                      search:true,
                      rules: [{
                        required: true,
                        message: "请选择部门",
                        trigger: "click"
                      }]
                    },*/
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "月",
            prop: "monthDate",
            type: "month",
            mock:{
              type:'month',
              format:'yyyy-MM'
            },
            format:'yyyy-MM',
            valueFormat:'yyyy-MM',
            search:true,
            hide:true,
          },
          {
            label: "月份",
            prop: "date",
            type: "datetime",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          /*{
            label: "部门",
            prop: "deptId",
            type: "tree",
            //search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },*/
          {
            label: "统缴餐钱包取现金额",
            prop: "unifiedPaymentMealBalance",
            sortable:true,
          },
          {
            label: "自选餐钱包取现金额",
            prop: "optionalMealBalance",
            sortable:true,
          },
          {
            label: "合计",
            prop: "sumBalance",
            sortable:true,
          },
        ]
      },
      monthData:[],
      searchForm1:{},
      selectionList: [],
    }
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchChangeMonth(params, done) {
      this.searchForm1 = params;
      this.query = params;
      this.monthPage.currentPage = 1
      this.onLoadMonth(this.monthPage, params);
      done();
    },
    searchResetMonth() {
      this.query = {};
      this.searchForm1 = {};
      this.onLoadMonth(this.monthPage);
    },
    selectionChangeMonth(list) {
      this.selectionList = list;
    },
    currentChangeMonth(currentPage){
      this.monthPage.currentPage = currentPage;
    },
    sizeChangeMonth(pageSize){
      this.monthPage.pageSize = pageSize;
    },
    selectionClearMonth() {
      this.selectionList = [];
      this.$refs.monthForm.toggleSelection();
    },
    onLoadMonth(page, params = {}) {
      this.monthLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getMonthList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.monthPage.total = data.total;
        this.monthData = data.records;
        this.monthLoading = false;
        this.selectionClearMonth();
      });
    },



  }
}
</script>

<style scoped>

</style>
