<template>
  <div>
    <div class="all-mess">
      <div class="mess-header">
        <div
          :class="{acitve:activeIdx==index}"
          v-for="(item,index) in messList"
          :key="index"
          @click="menuClick(index)"
        >
          {{item}}
        </div>
      </div>
      <div class="mess-content" v-if="activeIdx == 0">
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
          <template slot="menuLeft">
            <el-button v-if="permission.withdraw_cash_statistics_exports && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData">导出</el-button>
            <a style="color: red" type="danger"
               size="small"
               icon="el-icon-delete"
               plain
            >备注：当天的取现统计数据需到凌晨后统计。
            </a>
          </template>
        </avue-crud>
      </div>
      <div class="mess-content" v-if="activeIdx == 1">
        <avue-crud :option="monthOption"
                   :data="monthData"
                   :page="monthPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="monthForm"
                   ref="monthForm"
                   @search-change="searchChangeMonth"
                   @search-reset="searchResetMonth"
                   @selection-change="selectionChangeMonth"
                   @current-change="currentChangeMonth"
                   @size-change="sizeChangeMonth"
                   @on-load="onLoadMonth">
          <template slot="menuLeft">
            <el-button v-if="permission.withdraw_cash_statistics_exports && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>
            <a style="color: red" type="danger"
               size="small"
               icon="el-icon-delete"
               plain
            >备注：当天的取现统计数据需到凌晨后统计。
            </a>
          </template>
        </avue-crud>
      </div>

      <div class="mess-content" v-if="activeIdx == 2">
        <avue-crud :option="yearOption"
                   :data="yearData"
                   :page="yearPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="yearForm"
                   ref="yearForm"
                   @search-change="searchChangeYear"
                   @search-reset="searchResetYear"
                   @selection-change="selectionChangeYear"
                   @current-change="currentChangeYear"
                   @size-change="sizeChangeYear"
                   @on-load="onLoadYear">
          <template slot="menuLeft">
            <el-button v-if="permission.withdraw_cash_statistics_exports && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData3">导出</el-button>
            <a style="color: red" type="danger"
               size="small"
               icon="el-icon-delete"
               plain
            >备注：当天的取现统计数据需到凌晨后统计。
            </a>
          </template>
        </avue-crud>
      </div>
      <div class="mess-content" v-if="activeIdx == 3">
        <avue-crud :option="deptOption"
                   :data="deptData"
                   :page="deptPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="deptForm"
                   ref="deptForm"
                   @search-change="searchChangeDept"
                   @search-reset="searchResetDept"
                   @selection-change="selectionChangeDept"
                   @current-change="currentChangeDept"
                   @size-change="sizeChangeDept"
                   @on-load="onLoadDept">
          <template slot="menuLeft">
            <el-button v-if="permission.withdraw_cash_statistics_exports && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData4">导出</el-button>
            <a style="color: red" type="danger"
               size="small"
               icon="el-icon-delete"
               plain
            >备注：当天的取现统计数据需到凌晨后统计。
            </a>
          </template>
        </avue-crud>
      </div>
    </div>
  </div>
</template>

<script>
  import {getList, getDetail, add, update, remove,getMonthList,getYearList,getDeptList,exportDateFoodData,exportMonthFoodData,exportYearFoodData,exportDeptData} from "@/api/queryStatistics/withdrawCashStatistics";
  import {mapGetters} from "vuex";
  export default {
    props: {
        schoolId: String,
    },
    data() {
      return {
        activeIdx: 0,
        messList: [ '按日期统计', '按月份统计', '按年份统计', '按部门统计' ],
        form: {},
        query: {},
        monthForm:{},
        yearForm:{},
        deptForm:{},
        loading: true,
        monthLoading:true,
        deptLoading:true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        monthPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        yearPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        searchForm:{},
        searchForm1:{},
        searchForm2:{},
        searchForm3:{},
        selectionList: [],
        option: {
/*          height:'auto',
          calcHeight: 30,
          searchShow: true,*/
        /*  searchShow: true,*/
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: false,
          addBtn:false,
          menu:false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'unifiedPaymentMealBalance',
              type: 'sum'
            },
            {
              name: 'optionalMealBalance',
              type: 'sum'
            },
            {
              name:'sumBalance',
              type: 'sum'
            }
          ],
          column: [
/*            {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
    /*        {
              label: '日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "日期",
              prop: "date",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              sortable:true,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              //search: true,
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "统缴餐钱包取现金额",
              prop: "unifiedPaymentMealBalance",
              sortable:true,
            },
            {
              label: "自选餐钱包取现金额",
              prop: "optionalMealBalance",
              sortable:true,
            },
            {
              label: "合计",
              prop: "sumBalance",
              sortable:true,
            },
          ]
        },
        monthOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'unifiedPaymentMealBalance',
              type: 'sum'
            },
            {
              name: 'optionalMealBalance',
              type: 'sum'
            },
            {
              name:'sumBalance',
              type: 'sum'
            }
          ],
          column: [
  /*          {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "月",
              prop: "monthDate",
              type: "month",
              mock:{
                type:'month',
                format:'yyyy-MM'
              },
              format:'yyyy-MM',
              valueFormat:'yyyy-MM',
              search:true,
              hide:true,
            },
            {
              label: "月份",
              prop: "date",
              type: "datetime",
              format: "yyyy-MM",
              valueFormat: "yyyy-MM",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              sortable:true,
            },
            /*{
              label: "部门",
              prop: "deptId",
              type: "tree",
              //search: true,
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },*/
            {
              label: "统缴餐钱包取现金额",
              prop: "unifiedPaymentMealBalance",
              sortable:true,
            },
            {
              label: "自选餐钱包取现金额",
              prop: "optionalMealBalance",
              sortable:true,
            },
            {
              label: "合计",
              prop: "sumBalance",
              sortable:true,
            },
          ]
        },
        yearOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'unifiedPaymentMealBalance',
              type: 'sum'
            },
            {
              name: 'optionalMealBalance',
              type: 'sum'
            },
            {
              name:'sumBalance',
              type: 'sum'
            }
          ],
          column: [
         /*   {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "年",
              prop: "yearDate",
              type: "year",
              mock:{
                type:'year',
                format:'yyyy'
              },
              format:'yyyy',
              valueFormat:'yyyy',
              search:true,
              hide:true,
            },
            /*{
              label: "部门",
              prop: "deptId",
              type: "tree",
              //search: true,
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },*/
            {
              label: "年份",
              prop: "date",
              type: "datetime",
              format: "yyyy",
              valueFormat: "yyyy",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              sortable:true,
            },
            {
              label: "统缴餐钱包取现金额",
              prop: "unifiedPaymentMealBalance",
              sortable:true,
            },
            {
              label: "自选餐钱包取现金额",
              prop: "optionalMealBalance",
              sortable:true,
            },
            {
              label: "合计",
              prop: "sumBalance",
              sortable:true,
            },
          ]
        },
        deptOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'unifiedPaymentMealBalance',
              type: 'sum'
            },
            {
              name: 'optionalMealBalance',
              type: 'sum'
            },
            {
              name:'sumBalance',
              type: 'sum'
            }
          ],
          column: [
    /*        {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
/*            {
              label: '日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "时间段",
              prop: "date",
              type:'input',
              sortable:true,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              search: true,
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "部门",
              prop: "deptName",
              sortable:true,
            },
            {
              label: "统缴餐钱包取现金额",
              prop: "unifiedPaymentMealBalance",
              sortable:true,
            },
            {
              label: "自选餐钱包取现金额",
              prop: "optionalMealBalance",
              sortable:true,
            },
            {
              label: "合计",
              prop: "sumBalance",
              sortable:true,
            },
          ]
        },
        data: [],
        monthData:[],
        yearData:[],
        deptData:[],
        unitSelect: false
      }
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.food_wallet_recharge_add, false),
          viewBtn: this.vaildData(this.permission.food_wallet_recharge_view, false),
          delBtn: this.vaildData(this.permission.food_wallet_recharge_delete, false),
          editBtn: this.vaildData(this.permission.food_wallet_recharge_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      const { messName } = this.$route.query
      if(messName){
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log( this.activeIdx )
      }
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
      if (this.userInfo.userType === 'education'){
        this.deptOption.column[4].search = false;
      }else if (this.userInfo.userType === 'jiWei'){
        this.deptOption.column[4].search = false;
      }/*else {
        this.option.printBtn = true;
        this.monthOption.printBtn = true;
        this.yearOption.printBtn = true;
        this.deptOption.printBtn = true;
      }*/
/*      if (this.userInfo.userType === 'school'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
        this.monthOption.column[0].search = false;
        this.monthOption.column[0].hide = true;
        this.yearOption.column[0].search = false;
        this.yearOption.column[0].hide = true;
        this.deptOption.column[0].hide = true;
        this.deptOption.column[0].search = false;
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
        this.monthOption.column[1].search = false;
        this.monthOption.column[1].hide = true;
        this.yearOption.column[1].search = false;
        this.yearOption.column[1].hide = true;
        this.deptOption.column[1].hide = true;
        this.deptOption.column[1].search = false;
      }else if (this.userInfo.userType === 'education'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
        this.monthOption.column[0].search = false;
        this.monthOption.column[0].hide = true;
        this.yearOption.column[0].search = false;
        this.yearOption.column[0].hide = true;
        this.deptOption.column[0].hide = true;
        this.deptOption.column[0].search = false;
        this.option.column[1].search = this.unitSelect;
        this.option.column[1].hide = false;
        this.monthOption.column[1].search = this.unitSelect;
        this.monthOption.column[1].hide = false;
        this.yearOption.column[1].search = this.unitSelect;
        this.yearOption.column[1].hide = false;
        this.deptOption.column[1].hide = false;
        this.deptOption.column[1].search = this.unitSelect;
        this.deptOption.column[5].search = false;
      }else if (this.userInfo.userType === 'jiWei'){
        this.option.column[0].search = this.unitSelect;
        this.option.column[0].hide = false;
        this.monthOption.column[0].search = this.unitSelect;
        this.monthOption.column[0].hide = false;
        this.yearOption.column[0].search = this.unitSelect;
        this.yearOption.column[0].hide = false;
        this.deptOption.column[0].hide = false;
        this.deptOption.column[0].search = this.unitSelect;
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
        this.monthOption.column[1].search = false;
        this.monthOption.column[1].hide = true;
        this.yearOption.column[1].search = false;
        this.yearOption.column[1].hide = true;
        this.deptOption.column[1].hide = true;
        this.deptOption.column[1].search = false;
        this.deptOption.column[5].search = false;
      }*/
/*      if (this.userInfo.userType === 'school') {
        this.option.column[0].search = this.unitSelect;
        this.option.column[0].hide = !this.unitSelect;
      }
      this.monthOption.column[0].search = this.unitSelect;
      this.yearOption.column[0].search = this.unitSelect;
      this.deptOption.column[0].search = this.unitSelect;
      this.deptOption.column[4].search = !this.unitSelect;
      this.monthOption.column[0].hide = !this.unitSelect;
      this.yearOption.column[0].hide = !this.unitSelect;
      this.deptOption.column[0].hide = !this.unitSelect;*/
    },
    methods: {
      menuClick(idx) {
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          console.log("1")
          this.query = {};
          this.page.currentPage = 1;
          this.onLoad(this.page);
        }
        if (idx == 1){
          console.log("2")
          this.query = {};
          this.monthPage.currentPage = 1;
          this.onLoadMonth(this.monthPage);
        }
        if (idx == 2){
          console.log("3")
          this.query = {};
          this.yearPage.currentPage = 1;
          this.onLoadYear(this.yearPage);
        }
        if (idx == 3){
          console.log("4")
          this.query = {};
          this.deptPage.currentPage = 1;
          this.onLoadDept(this.deptPage);
        }
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchResets(){
        this.query = {};
      },
      searchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoad(this.page);
      },
      searchResetMonth() {
        this.query = {};
        this.searchForm1 = {};
        this.onLoadMonth(this.monthPage);
      },
      searchResetYear() {
        this.query = {};
        this.searchForm2 = {};
        this.onLoadYear(this.yearPage);
      },
      searchResetDept() {
        this.query = {};
        this.searchForm3 = {};
        this.onLoadDept(this.deptPage);
      },
      searchChange(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm = params;
        this.query = params;
        this.page.currentPage = 1
/*        if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.startDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
        }*/
        this.onLoad(this.page, params);
        done();
      },
      searchChangeMonth(params, done) {
        this.searchForm1 = params;
        this.query = params;
        this.monthPage.currentPage = 1
        this.onLoadMonth(this.monthPage, params);
        done();
      },
      searchChangeYear(params, done) {
        this.searchForm2 = params;
        this.query = params;
        this.yearPage.currentPage = 1
        this.onLoadYear(this.yearPage, params);
        done();
      },
      searchChangeDept(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm3.startDate =  params.startDate;
        this.searchForm3.endDate = params.endDate;
        this.searchForm3 = params;
        this.query = params;
        this.deptPage.currentPage = 1
/*        if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.startDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
          this.searchForm3.startDate =  params.queryDate[0];
          this.searchForm3.endDate = params.queryDate[1];
        }*/
        this.onLoadDept(this.deptPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChangeMonth(list) {
        this.selectionList = list;
      },
      selectionChangeYear(list) {
        this.selectionList = list;
      },
      selectionChangeDept(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      selectionClearMonth() {
        this.selectionList = [];
        this.$refs.monthForm.toggleSelection();
      },
      selectionClearYear() {
        this.selectionList = [];
        this.$refs.yearForm.toggleSelection();
      },
      selectionClearDept() {
        this.selectionList = [];
        this.$refs.deptForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeMonth(currentPage){
        this.monthPage.currentPage = currentPage;
      },
      currentChangeYear(currentPage){
        this.yearPage.currentPage = currentPage;
      },
      currentChangeDept(currentPage){
        this.deptPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeMonth(pageSize){
        this.monthPage.pageSize = pageSize;
      },
      sizeChangeYear(pageSize){
        this.yearPage.pageSize = pageSize;
      },
      sizeChangeDept(pageSize){
        this.deptPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.unitId = this.schoolId;
        }
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoadMonth(page, params = {}) {
        this.monthLoading = true;
          if(this.schoolId != null && this.schoolId != ''){
              params.unitId = this.schoolId;
          }
        getMonthList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.monthPage.total = data.total;
          this.monthData = data.records;
          this.monthLoading = false;
          this.selectionClearMonth();
        });
      },
      onLoadYear(page, params = {}) {
        this.yearLoading = true;
          if(this.schoolId != null && this.schoolId != ''){
              params.unitId = this.schoolId;
          }
        getYearList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.yearPage.total = data.total;
          this.yearData = data.records;
          this.yearLoading = false;
          this.selectionClearYear();
        });
      },
      onLoadDept(page, params = {}) {
        this.deptLoading = true;
          if(this.schoolId != null && this.schoolId != ''){
              params.unitId = this.schoolId;
          }
        getDeptList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptPage.total = data.total;
          this.deptData = data.records;
          this.deptLoading = false;
          this.selectionClearDept();
        });
      },
      exportRechargeDetailData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDateFoodData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '取现统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportRechargeDetailData2(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportMonthFoodData(this.searchForm1).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '取现统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportRechargeDetailData3(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportYearFoodData(this.searchForm2).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '取现统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportRechargeDetailData4(){
    /*    if (this.searchForm3.queryDate != '' && this.searchForm3.queryDate != null && this.searchForm3.queryDate != undefined){*/
          const loading = this.$loading({
            lock: true,
            text: '正在导出数据，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          exportDeptData(this.searchForm3).then(res => {
            loading.close();
            const blob = new Blob([res.data]);
            const fileName = '取现统计报表.xlsx';
            const linkNode = document.createElement('a');

            linkNode.download = fileName; //a标签的download属性规定下载文件的名称
            linkNode.style.display = 'none';
            linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
            document.body.appendChild(linkNode);
            linkNode.click(); //模拟在按钮上的一次鼠标单击

            URL.revokeObjectURL(linkNode.href); // 释放URL 对象
            document.body.removeChild(linkNode);
          })
   /*     }else {
          this.$message.error('请选择导出数据的时间段并查询!');
        }*/
      }
    }
  }
</script>

<style lang="scss" scoped>
  .all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      ::v-deep div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20rpx;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
