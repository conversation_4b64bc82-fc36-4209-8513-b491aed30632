<template>
  <basic-container>
    <div class="search_container">
      <el-form :inline="true" :model="dataForm">
        <el-form-item label="被检单位">
          <el-select v-model="dataForm.deptId" placeholder="被检单位" clearable filterable @change="getDataList()">
            <el-option v-for="item in canteenList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label>
          <el-select v-model="dataForm.checkItem" placeholder="检测项目" clearable filterable @change="getDataList()">
            <el-option v-for="item in dictList" :key="item.dictValue" :label="item.dictValue" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="时间" style="margin-left: 30px;">
          <el-date-picker v-model="dataForm.createTimeStart" type="date" value-format="yyyy-MM-dd"  placeholder="时间起" clearable style="width:140px" />
          至
          <el-date-picker v-model="dataForm.createTimeEnd" type="date" value-format="yyyy-MM-dd"  placeholder="时间止" clearable style="width:140px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()" icon="el-icon-search">查询</el-button>
          <el-button @click="getDataList('empty')" icon="el-icon-delete">清空</el-button>
          <!-- <el-button   type="primary" icon="el-icon-plus">新增</el-button>
          <el-button  type="danger" icon="el-icon-delete">批量删除</el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="avue-empty" v-show="!hasData" style="margin-top:150px;margin-bottom: 800px;"><div class="avue-empty__image" style="height: 50px;"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAxKSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgIDxlbGxpcHNlIGZpbGw9IiNGNUY1RjUiIGN4PSIzMiIgY3k9IjMzIiByeD0iMzIiIHJ5PSI3Ii8+CiAgICA8ZyBmaWxsLXJ1bGU9Im5vbnplcm8iIHN0cm9rZT0iI0Q5RDlEOSI+CiAgICAgIDxwYXRoIGQ9Ik01NSAxMi43Nkw0NC44NTQgMS4yNThDNDQuMzY3LjQ3NCA0My42NTYgMCA0Mi45MDcgMEgyMS4wOTNjLS43NDkgMC0xLjQ2LjQ3NC0xLjk0NyAxLjI1N0w5IDEyLjc2MVYyMmg0NnYtOS4yNHoiLz4KICAgICAgPHBhdGggZD0iTTQxLjYxMyAxNS45MzFjMC0xLjYwNS45OTQtMi45MyAyLjIyNy0yLjkzMUg1NXYxOC4xMzdDNTUgMzMuMjYgNTMuNjggMzUgNTIuMDUgMzVoLTQwLjFDMTAuMzIgMzUgOSAzMy4yNTkgOSAzMS4xMzdWMTNoMTEuMTZjMS4yMzMgMCAyLjIyNyAxLjMyMyAyLjIyNyAyLjkyOHYuMDIyYzAgMS42MDUgMS4wMDUgMi45MDEgMi4yMzcgMi45MDFoMTQuNzUyYzEuMjMyIDAgMi4yMzctMS4zMDggMi4yMzctMi45MTN2LS4wMDd6IiBmaWxsPSIjRkFGQUZBIi8+CiAgICA8L2c+CiAgPC9nPgo8L3N2Zz4K" alt=""></div><p class="avue-empty__desc">暂无数据</p></div>
    <div id="chartColumn" style="width: 100%; height: 400px;margin-top:100px;"></div>
    <div style="text-align: center;margin-top:30px;">检测单数：{{checkCount}}</div>
    <div style="height: 500px;"></div>
  </basic-container>
</template>

<script>
import request from "@/router/axios";
import echarts from "echarts";
import { dateFormat } from "@/util/date";
export default {
  data() {
    return {
      chartColumn: null,
      canteenList: [],
      hasData:false,
      checkCount:0,
      dictList:[],
      dataForm: {
        deptId: "",
        createTimeStart: dateFormat(
          new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
          "yyyy-MM-dd"
        ),
        createTimeEnd: dateFormat(new Date(), "yyyy-MM-dd")
      }
    };
  },
  mounted() {
    //this.drawLine();
    this.getCanteenList();
    this.getDataList();
   // this.getDictList();
  },
  methods: {
    getData(url,params,callbackFun) {
      request({
        url: url,
        params: {...params},
        method: "get"
      }).then(
        res => {
          callbackFun(res);
        },
        error => { }
      );
    },
    getCanteenList() {
      request({
        url: "/api/service/rabbit-system/dept/getDeptAndSubDept?deptCategory=4",
        method: "get"
      }).then(
        res => {
          console.log(res, " res  getCanteenList ... ");
          this.canteenList = res.data.data;
        },
        error => { }
      );
    },getDictList() {
      request({
        url: "/api/service/rabbit-system/dict/dictionary?code=fast_check_item",
        method: "get"
      }).then(
        res => {
          console.log(res, " res  getDictList ... ");
          this.dictList = res.data.data;
          console.log(this.dictList,'this.dictList...')
        },
        error => { }
      );
    },
    getDataList(action) {
      if (action === "empty") {
        for (const key in this.dataForm) {
          if (this.dataForm.hasOwnProperty(key)) {
            this.dataForm[key] = "";
          }
        }
      }
      let params = this.dataForm;
      this.getData('/api/service/rabbit-supplier/foodcheck/statCheckOrder',params,(res)=>{
        //console.log('res 11111 =',res);
          let data = res.data.data;
          if(data&&data.length>0){
            this.hasData=true;
            document.getElementById('chartColumn').style.display='block';
          }else{
            this.hasData=false;
            document.getElementById('chartColumn').style.display='none';
          }
          let xAxisData = [];
          let yData = [];
          this.checkCount=0;
          if(data){
          data.forEach(e => {
            xAxisData.push(e.checkDate);
            yData.push(e.checkCount);
            this.checkCount+=e.checkCount;
          });
          }
          this.drawLine(xAxisData,  yData);
      })
    },
    drawLine(xAxisData, data) {
      this.chartColumn = echarts.init(document.getElementById("chartColumn"));
      let getData = function () {
        let yData1 = [];
        xAxisData.forEach(e => {
          yData1.push(parseInt(Math.random() * 100));
        });
        return yData1;
      };
      this.chartColumn.setOption({
        title: { text: "检测单统计" },
        tooltip: {
          trigger: "axis"
        },
        legend: {
          data: ["检测单"]
        },
        grid: {
          left: "1%",
          right: "3%",
          bottom: "1%",
          containLabel: true
        },
        toolbox: { feature: { saveAsImage: {} } },
        xAxis: {
          type: "category",
          //boundaryGap: false,
          data: xAxisData
        },
        yAxis: {
          type: "value"
        },
        series: [
          {
            name: "检测单",
            type: "line",
            //stack: '总量',
            itemStyle: { color: "#ff0000" },
            data: data
          }
        ]
      });
      this.chartColumn.resize();
    }
  }
};
</script>

<style>
.el-row {
  margin-bottom: 20px;
  font-size: medium;
}
</style>
