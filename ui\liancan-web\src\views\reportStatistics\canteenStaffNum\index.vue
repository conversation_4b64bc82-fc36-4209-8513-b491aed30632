<template>
  <basic-container>
    <div>
      <avue-crud
            :table-loading="monthLoading"
            :option="monthOption"
            :data="monthData"
            :page="monthPage"
            :permission="permissionList"
            :before-open="beforeOpen"
            v-model="monthForm"
            ref="monthForm"
            @search-change="searchChangeMonth"
            @search-reset="searchResetMonth"
            @selection-change="selectionChangeMonth"
            @current-change="currentChangeMonth"
            @size-change="sizeChangeMonth"
            @on-load="onLoadMonth">
            <template slot="menuLeft">
              <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportCanteenStaffData">导出</el-button>
            </template>
            <el-date-picker
              v-model="month"
              type="month"
              placeholder="选择月">
            </el-date-picker>
          </avue-crud>
    </div>
  </basic-container>
</template>

<script>
  import {getUnitList,getCanteenStaffList,geDiningNumberList,exportUnitNumberData,exportCanteenStaffData,exportCanteenDiningData} from "@/api/reportStatistics/cateringUnits";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        activeIdx: 0,
        messList: [ '用餐单位','用餐人数' , '食堂职工'],
        form: {},
        query: {},
        monthForm:{},
        yearForm:{},
        loading: true,
        monthLoading:true,
        yearLoading:true,
        startDate:undefined,
        stopDate:undefined,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        monthPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        yearPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        searchForm:{},
        searchForm1:{},
        searchForm2:{},
        selectionList: [],
        option: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,*/
          /*  searchShow: true,*/
          searchMenuSpan: 6,
          searchShow: false,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: false,
          addBtn:false,
          menu:false,
          showSummary: true,
          sumColumnList: [
            {
              name: "schoolNumber",
              type: "sum"
            },
            {
              name: "governmentNumber",
              type: "sum"
            },
            {
              name: "institutionNumber",
              type: "sum"
            },
            {
              name: "enterpriseNumber",
              type: "sum"
            },
            {
              name: "otherNumber",
              type: "sum"
            },
            {
              name: "totalNumber",
              type: "sum"
            },
          ],
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "单位名称",
              prop: "companyId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: true,
              fixed:true,
            },
            {
              label: "单位类型",
              prop: "agencyType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "学校",
              prop: "schoolNumber",
            },
            {
              label: "政府机关",
              prop: "governmentNumber",
            },
            {
              label: "事业单位",
              prop: "institutionNumber",
            },
            {
              label: "国企",
              prop: "enterpriseNumber",
            },
            {
              label: "其他",
              prop: "otherNumber",
            },
            {
              label: "合计",
              prop: "totalNumber",
            },
          ]
        },
        monthOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'diningNumber',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "所属单位",
              prop: "parentId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              fixed:true,
            },
            {
              label: "单位类型",
              prop: "agencyType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              search: true,
            },
            {
              label: "食堂名称",
              prop: "companyId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              fixed:true,
            },

          /*  {
              label: "单位名称",
              prop: "deptName",
              type: "input",
            },

            {
              label: "单位类型",
              prop: "schoolType",
              type: "input",
            },
            {
              label: "单位类型",
              prop: "unitType",
              type: "select",
              search:true,
              hide:true,
              dicData: [
                {
                  label: "学校",
                  value: "0"
                },
                {
                  label: "政府机关",
                  value: "1"
                },
                {
                  label: "事业单位",
                  value: "2"
                },
                {
                  label: "国企",
                  value: "3"
                },
                {
                  label: "其他",
                  value: "4"
                }
              ],
            },*/
            {
              label: "合计",
              prop: "diningNumber",
            }
          ]
        },
        yearOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name:'stuNumber',
              type: 'sum'
            },
            {
              name:'teaNumber',
              type: 'sum'
            },
            {
              name:'diningNumber',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "所属单位",
              prop: "parentId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              fixed:true,
            },
            {
              label: "单位类型",
              prop: "agencyType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              search: true,
            },
            {
              label: "食堂名称",
              prop: "companyId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              fixed:true,
            },

/*            {
              label: "单位名称",
              prop: "deptName",
              type: "input",
            },

            {
              label: "单位类型",
              prop: "schoolType",
              type: "input",
            },
            {
              label: "单位类型",
              prop: "unitType",
              type: "select",
              search:true,
              hide:true,
              dicData: [
                {
                  label: "学校",
                  value: "0"
                },
                {
                  label: "政府机关",
                  value: "1"
                },
                {
                  label: "事业单位",
                  value: "2"
                },
                {
                  label: "国企",
                  value: "3"
                },
                {
                  label: "其他",
                  value: "4"
                }
              ],
            },*/
            {
              label: "学生",
              prop: "stuNumber",
            },
            {
              label: "教职工",
              prop: "teaNumber",
            },
            {
              label: "用餐人数合计",
              prop: "diningNumber",
            }
          ]
        },
        data: [],
        monthData:[],
        yearData:[],
      }
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.food_wallet_recharge_add, false),
          viewBtn: this.vaildData(this.permission.food_wallet_recharge_view, false),
          delBtn: this.vaildData(this.permission.food_wallet_recharge_delete, false),
          editBtn: this.vaildData(this.permission.food_wallet_recharge_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      const { messName } = this.$route.query
      if(messName){
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log( this.activeIdx )
      }
      var beginDate = this.getCurrentMonthFirst();
      this.startDate = beginDate;
      var endDate = this.getCurrentMonthLast();
      this.stopDate = endDate;
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    },
    methods: {
      getCurrentMonthFirst(){
        var date = new Date();
        date.setDate(1);
        var month = parseInt(date.getMonth()+1);
        var day = date.getDate();
        if (month < 10) {
          month = '0' + month
        }
        if (day < 10) {
          day = '0' + day
        }
        return date.getFullYear() + '-' + month + '-' + day;
      },
      getCurrentMonthLast(){
        var date=new Date();
        var currentMonth=date.getMonth();
        var nextMonth=++currentMonth;
        var nextMonthFirstDay=new Date(date.getFullYear(),nextMonth,1);
        var oneDay=1000*60*60*24;
        var lastTime = new Date(nextMonthFirstDay-oneDay);
        var month = parseInt(lastTime.getMonth()+1);
        var day = lastTime.getDate();
        if (month < 10) {
          month = '0' + month
        }
        if (day < 10) {
          day = '0' + day
        }
        return date.getFullYear() + '-' + month + '-' + day;
      },
      menuClick(idx) {
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.page.currentPage = 1;
          this.onLoad(this.page);
        }
        if (idx == 2){
          this.monthPage.currentPage = 1;
          this.onLoadMonth(this.monthPage);
        }
        if (idx == 1){
          this.yearPage.currentPage = 1;
          this.onLoadYear(this.yearPage);
        }
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoad(this.page);
      },
      searchResetMonth() {
        this.query = {};
        this.searchForm1 = {};
        this.onLoadMonth(this.monthPage);
      },
      searchResetYear() {
        this.query = {};
        this.searchForm2 = {};
        this.onLoadYear(this.yearPage);
      },
      searchChange(params, done) {
        this.searchForm = params;
        this.query = params;
        this.page.currentPage = 1
        if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.beginDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
        }
        this.onLoad(this.page, params);
        done();
      },
      searchChangeMonth(params, done) {
        this.searchForm1 = params;
        this.query = params;
        this.monthPage.currentPage = 1
        this.onLoadMonth(this.monthPage, params);
        done();
      },
      searchChangeYear(params, done) {
        this.searchForm2 = params;
        this.query = params;
        this.yearPage.currentPage = 1
        this.onLoadYear(this.yearPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChangeMonth(list) {
        this.selectionList = list;
      },
      selectionChangeYear(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      selectionClearMonth() {
        this.selectionList = [];
        this.$refs.monthForm.toggleSelection();
      },
      selectionClearYear() {
        this.selectionList = [];
        this.$refs.yearForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeMonth(currentPage){
        this.monthPage.currentPage = currentPage;
      },
      currentChangeYear(currentPage){
        this.yearPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeMonth(pageSize){
        this.monthPage.pageSize = pageSize;
      },
      sizeChangeYear(pageSize){
        this.yearPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getUnitList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoadMonth(page, params = {}) {
        this.monthLoading = true;
        getCanteenStaffList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.monthPage.total = data.total;
          this.monthData = data.records;
          this.monthLoading = false;
          this.selectionClearMonth();
        });
      },
      onLoadYear(page, params = {}) {
        this.yearLoading = true;
        geDiningNumberList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.yearPage.total = data.total;
          this.yearData = data.records;
          this.yearLoading = false;
          this.selectionClearYear();
        });
      },
      exportUnitNumberData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportUnitNumberData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '供餐单位数量统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportCanteenStaffData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportCanteenStaffData(this.searchForm1).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '食堂职工统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportCanteenDiningData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportCanteenDiningData(this.searchForm2).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '食堂用餐人数统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      /deep/ div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20rpx;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
