<template>
    <basic-container>
       <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
           <template slot="baiduMapForm" slot-scope="{row,index}">
             <baidu-map class="map" :center="centerPoint"  :zoom="15" :scroll-wheel-zoom="true" >
                   <bm-polyline :path="polylinePath" stroke-color="blue"   :stroke-weight="2"   @lineupdate="updatePolylinePath" ></bm-polyline>
                 <bm-marker v-for="(item,index)  in polylinePath" :key="index+'_'" :position="item" >
                   <bm-label  :content="index+1" :labelStyle="{color: '#fff', fontSize : '12px',background:'rgba(0, 0, 0, 0)',borderColor:'rgba(0, 0, 0, 0)'}"
                             :offset="{width: 4,height:2 }"
                            />
                 </bm-marker>
               </baidu-map>
           </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {getList, add,update,remove,loadCustomerList,loadMyCompany} from "@/api/supplier/supplierDeliveryLine";
import {mapGetters} from "vuex";

export default {
    data() {
        return {
            infoWindow: {
                show: false,
                contents: ''
            },
            centerPoint: {},
            polylinePath:[],

            form: {
                customerList: []
            },
            query: {},
            loading: true,
            data:[],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                refreshBtn: false,
                searchMenuSpan: 4, //搜索按钮长度
                tip: false,
                border: true,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                index: true,
                viewBtn: true,
                selection: true,
                labelWidth: 100,
                dialogWidth: 1200,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "路线编号",
                        prop: "lineNumber",
                        type: "input",
                        rules: [{
                            required: true,
                            message: '请输入路线编号',
                            trigger: 'blur'
                        }],
                    },
                    {
                        label: "路线名称",
                        prop: "lineName",
                        type: "input",
                        search: true,
                        rules: [{
                            required: true,
                            message: '请输入路线名称',
                            trigger: 'blur'
                        }],
                    },
                    {
                        label: "配送客户",
                        prop: "customerName",
                        type: "input",
                        search: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {
                        label: '配送客户',
                        prop: 'customerList',
                        type: 'dynamic',
                        span: 24,
                        search: false,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        children: {
                            align: 'center',
                            headerAlign: 'center',
                            column: [{
                                label: '客户名称',
                                prop: "customerId",
                                type: 'select',
                                dicUrl: "/api/service/rabbit-supplier/customer-list",
                                props: {
                                    label: "customerName",
                                    value: "companyCompanyId"
                                },
                            }]
                        }
                    },
                    {
                        label: "编辑日期",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd",
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "配送路线图",
                        prop: "baiduMap",
                        type: "text",
                        span: 24,
                        formslot:true,
                        addDisplay: false,
                    },
                ]
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
    },
    methods: {
        updatePolylinePath (e) {
            this.polylinePath = e.target.getPath()
        },
        rowSave(row, loading, done) {
            try{
                console.log("1======>"+JSON.stringify(this.form));
                if(this.form==undefined || this.form.customerList==undefined || this.form.customerList.length == 0){
                    this.$message({
                        type: "error",
                        message: "请选择配送客户"
                    });
                    done();
                    return;
                }
                // row.customerIds = this.from.customerList.join(",")

                var list = [];
                this.form.customerList.forEach(e=>{
                    list.push(e.customerId);
                });
                row.customerIds = list.join(",");

                console.log("11111======>"+JSON.stringify(row));
                add(row).then(res => {
                    loading();
                    this.onLoad(this.page);
                    if (res.data.success) {
                        this.$message({
                            type: "success",
                            message: res.data.message
                        });
                    } else {
                        this.$message({
                            type: "error",
                            message: res.data.message
                        });
                    }
                    done();
                }, error => {
                    done();
                    window.console.log(error);
                });
            }catch (e){
                done();
            }
        },
        rowUpdate(row, index, loading, done) {
            console.log("2======>"+JSON.stringify(row));
            row.customerIds = "";
            row.customerList.map(item => {
                row.customerIds = row.customerIds+","+item.customerId
            })
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //主界面基本方法 start
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                this.polylinePath = []
                loadCustomerList(this.form.id).then(res => {
                    res.data.data.map(item => {
                        this.form.customerList.push({
                            customerId: item.id,
                            props:{
                                label: item.customerName,
                                value: item.id
                            },

                        })
                        this.polylinePath.push({
                            lng:item.longitude,
                            lat:item.latitude
                        })
                    })
                });
                loadMyCompany().then(res => {
                    this.centerPoint = {lng:res.data.data.longitude,lat:res.data.data.latitude}
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },

        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },

    }
};
</script>
<style scoped>
.map {
    width: 100%;
    height: 600px;
}
.avue-crud__pagination {
    display: none;
}
</style>
