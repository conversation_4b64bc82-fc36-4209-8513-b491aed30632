<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
        </avue-crud>
    </basic-container>
</template>

<script>
import {mapGetters} from "vuex";
import {getList,getActualStock,getPurchasePrice,getPurchaseDetail,getSaleDetail,getSalePrice} from "@/api/supplier/supplierQueryInfo";
var DIC = {
    billTypes: [{
        label: '全部',
        value: "0"
    },{
        label: '盘亏单',
        value: "1"
    },{
        label: '盘盈单',
        value: "2"
    },],
}
export default {
    props: {
        // 声明接收的参数
        rowData: {
            type: Object,
            default: () => ({}) // 默认值
        }
    },
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 5,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: '300px',
                searchShow: true,
                refreshBtn: true,
                columnBtn: true,
                searchBtn: true,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                menu: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                    },
                    {
                        label: "单据号",
                        prop: "code",
                        type: "input",
                        searchLabelWidth: 60,
                        search: true,
                    },
                    {
                        label: "单据类型",
                        prop: "billType",
                        type: "select",
                        slot:true,
                        dicData: DIC.billTypes,
                    },
                    {
                        label: '供应商名称',
                        prop: 'supplierCustomerId',
                        type: 'select',
                        props: {
                            label: 'customerName',
                            value: 'id'
                        },
                        dicUrl: '/api/service/rabbit-supplier/customer-list',
                        width: 100,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                        search: true,
                        width: 100,
                        searchLabelWidth: 100,
                    },
                    {
                        label: "商品子项名称",
                        prop: "goodName",
                        type: "input",
                        search: true,
                        width: 100,
                        searchLabelWidth: 100,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "采购数量",
                        prop: "purchaseNum",
                        type: "input"
                    },
                    {
                        label: "采购单价",
                        prop: "purchasePrice",
                        type: "number",
                        precision:2,
                        mock:{
                            type:'number',
                            max:1,
                            min:2,
                            precision:2
                        },
                    },
                    {
                        label: "采购金额",
                        prop: "purchaseAmt",
                        type: "number",
                        precision:2,
                        mock:{
                            type:'number',
                            max:1,
                            min:2,
                            precision:2
                        },
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                ]
            },
            data: [],
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
    },
    watch: {
    },
    mounted() {
    },
    methods: {
        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getPurchaseDetail(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
