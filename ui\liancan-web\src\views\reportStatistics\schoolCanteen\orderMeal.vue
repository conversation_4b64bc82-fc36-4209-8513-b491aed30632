<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="morDiningNumber" slot-scope="{row}">
        <a href="javascript:void(0)" style="color: #409EFF;" @click="getMorDiningNumber(row)">{{row.morDiningNumber}}</a>
      </template>
      <template slot="aftDiningNumber" slot-scope="{row}">
        <a href="javascript:void(0)" style="color: #409EFF;" @click="getAftDiningNumber(row)">{{row.aftDiningNumber}}</a>
      </template>
      <template slot="dinDiningNumber" slot-scope="{row}">
        <a href="javascript:void(0)" style="color: #409EFF;" @click="getDinDiningNumber(row)">{{row.dinDiningNumber}}</a>
      </template>
      <template slot="nigDiningNumber" slot-scope="{row}">
        <a href="javascript:void(0)" style="color: #409EFF;" @click="getNigDiningNumber(row)">{{row.nigDiningNumber}}</a>
      </template>
      <template slot="menuLeft">
        <el-button v-if="permission.order_meal_export" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportDeptSummaryData">导出</el-button>
      </template>
    </avue-crud>
    <el-dialog :title="`预订餐次订单`" :visible.sync="managementVisible" :append-to-body="true" @close="managementVisible = false" width="80%">
      <avue-crud :option="deptSummaryOption"
                 :table-loading="deptSummaryLoading"
                 :data="deptSummaryData"
                 :page="deptSummaryPage"
                 v-model="deptSummaryForm"
                 ref="deptSummaryForm"
                 @search-change="deptSummarySearchChange"
                 @search-reset="deptSummarySearchReset"
                 @selection-change="deptSummarySelectionChange"
                 @current-change="deptSummaryCurrentChange"
                 @size-change="deptSummarySizeChange"
                 @on-load="onLoadDeptSummary">
        <template slot="menuLeft">
          <el-button v-if="permission.order_meal_synchr" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="synchronOrder">同步订单</el-button>
          <el-button v-if="permission.order_meal_export" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportOrderData">导出</el-button>
        </template>
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getOrderMealList,getOrderList,getDeptUserDetailsList,synchronReservationMeal,exportOrderMealData,exportDeptUserDetailsData,exportOrderData,synchronOrder2} from "@/api/businessManage/studentUniteOrder";
  import {mapGetters} from "vuex";

  const DIC = {
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
    week: [{
      value: "1",
      label: "星期一"
    },
      {
        value: "2",
        label: "星期二"
      },
      {
        value: "3",
        label: "星期三"
      },
      {
        value: "4",
        label: "星期四"
      },
      {
        value: "5",
        label: "星期五"
      },
      {
        value: "6",
        label: "星期六"
      },
      {
        value: "7",
        label: "星期日"
      }
    ],
  }
  export default {
    data() {
      return {
        form: {},
        query: {},
        deptDetailsForm:{},
        searchOrderForm:{},
        loading: true,
        deptSummaryLoading:true,
        deptDetailsLoading:true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptSummaryPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptDetailsPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        deptSummaryData:[],
        deptDetailsData:[],
        searchFrom:{},
        deptSummaryForm:{},
        searchForm:{},
        searchDeptUserForm:{},
        dialogVisible:false,
        managementVisible:false,
        deptDetailsVisible:false,
        dialogImageUrl:undefined,
        title:undefined,
        businessOutletsName:undefined,
        payStartTime:undefined,
        payEndTime:undefined,
        serveFoodStartDate:undefined,
        serveFoodEndDate:undefined,
        isPublish:undefined,
        planId:undefined,
        deptId:undefined,
        businessOutletsId:undefined,
        dinnerDate:undefined,
        mealType:undefined,
        option: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          menu:false,
          column: [
            {
              label: "所属食堂",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: '所属网点',
              prop: 'businessOutletsName',
              type: 'input',
            },
            {
              label: '星期',
              prop: 'week',
              type: 'input',
              span: 20,
              dicData: DIC.week
            },
            {
              label: "营业网点",
              prop: "businessOutletsId",
              type: "select",
              span: 24,
              rules: [{
                required: true,
                message: "请输入营业网点",
                trigger: "blur"
              }],
              hide:true,
              search:true,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              props: {
                label: "name",
                value: "id"
              },
            },
            {
              label: '用餐时间',
              prop: 'diningTime',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
            {
              label: '用餐日期',
              prop: 'dinnerDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
            },
            {
              label: "早餐预订份数",
              prop: "morDiningNumber",
              type: "input",
              slot:true,
            },
            {
              label: "中餐预订份数",
              prop: "aftDiningNumber",
              type: "input",
              slot:true,
            },
            {
              label: "晚餐预订份数",
              prop: "dinDiningNumber",
              type: "input",
              slot:true,
            },
            {
              label: "夜餐预订份数",
              prop: "nigDiningNumber",
              type: "input",
              slot:true,
            },
            {
              label: "合计",
              prop: "totalnumber",
              type: "input"
            }
          ]
        },
        deptSummaryOption: {
       /*   height: 'auto',
          calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          indexLabel: "序号",
          viewBtn: false,
          addBtn: false,
          menu: false,
          delBtn: false,
          editBtn: false,
          labelWidth: 150,
          menuWidth: 280,
          column: [{
            label: '编号',
            prop: 'id',
            type: 'input',
            display: false,
            hide: true
          },
            {
              label: '用餐时间',
              prop: 'date',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
      /*      {
              label: '所属网点',
              prop: 'businessOutletsName',
              type: 'input',
              span: 20,
            },*/
            {
              label: '姓名',
              prop: 'studentName',
              type: 'input',
              span: 20,
              search: true,
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
              width:100,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
            },
            {
              label: "学号/工号",
              prop: "studentJobNo",
              type: "input",
              search:true,
              rules: [{
                required: true,
                message: "请输入学号/工号",
                trigger: "blur"
              }],
            },
            {
              label: '用餐日期',
              prop: 'dinnerDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
            },
            {
              label: '星期',
              prop: 'week',
              type: 'input',
              span: 20,
              dicData: DIC.week
            },
            {
              label: '餐次类型',
              prop: 'mealType',
              type: 'select',
              span: 24,
              dicData: [{
                value: '0',
                label: '早餐'
              },
                {
                  value: '1',
                  label: '中餐'
                },
                {
                  value: '2',
                  label: '晚餐'
                },
                {
                  value: '3',
                  label: '夜餐'
                }
              ]
            },
            /*           {
                         label: "开始时间",
                         prop: "startDate",
                         type: "date",
                         format: "yyyy-MM-dd",
                         valueFormat: "yyyy-MM-dd",
                         hide: true,
                         search: true,
                         display: false,
                         showColumn:false,
                       },
                       {
                         label: "结束时间",
                         prop: "endDate",
                         format: "yyyy-MM-dd",
                         valueFormat: "yyyy-MM-dd",
                         type: "date",
                         hide: true,
                         search: true,
                         display: false,
                         showColumn:false,
                       },*/
            {
              label: '本餐单价',
              prop: 'money',
              type: 'input',
              span: 20,
            },
            {
              label: '关联下单单号',
              prop: 'consumNo',
              type: 'input',
              span: 20,
            },
            {
              label: '用餐状态',
              prop: 'dinnerStatus',
              type: 'select',
              span: 24,
              search:true,
              dicData: [{
                value: '0',
                label: '待取餐'
              },
                {
                  value: '1',
                  label: '已取餐'
                },
                {
                  value: '2',
                  label: '已退订'
                }
              ]
            },
            {
              label: '取餐/退订时间',
              prop: 'takeMealTime',
              type: "date",
              format: 'yyyy-MM-dd HH:mm',
              valueFormat: 'yyyy-MM-dd HH:mm',
            },
            {
              label: '退订操作人',
              prop: 'unsubscribeUserName',
              type: 'input',
              span: 20,
            },
          ]
        },
        deptDetailsOption:{
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          menu:false,
          column: [
            {
              label: "部门",
              prop: "deptName",
              type: "input",
            },
            {
              label: '姓名',
              prop: 'userName',
              type: 'input',
              search:true,
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
            },
            {
              label: '学号/工号',
              prop: 'studentJobNo',
              type: 'input',
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              search:true,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: '伙食费',
              prop: 'balance',
              type: 'input',
            },
            {
              label: '已订餐',
              prop: 'diningStatus',
              type: 'input',
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          /*        addBtn: this.vaildData(this.permission.work_personnel_add, false),
                  viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                  delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                  editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchForm = params;
        if (params.diningTime != '' && params.diningTime != null && params.diningTime != undefined) {
          params.startDate = params.diningTime[0];
          params.endDate = params.diningTime[1];
          this.searchForm.startDate = params.diningTime[0];
          this.searchForm.endDate = params.diningTime[1];
        }
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getOrderMealList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      deptSummarySearchReset() {
        this.query = {};
        this.searchForm = {};
        this.searchOrderForm = {};
        this.searchOrderForm.businessOutletsId = this.businessOutletsId;
        this.searchOrderForm.dinnerDate = this.dinnerDate;
        this.searchOrderForm.mealType = this.mealType;
        this.onLoadDeptSummary(this.deptSummaryPage);
      },
      deptSummarySearchChange(params, done) {
        this.query = params;
        this.searchOrderForm = params;
        this.deptSummaryPage.currentPage = 1
        this.onLoadDeptSummary(this.deptSummaryPage, params);
        done();
      },
      deptSummarySelectionChange(list) {
        this.selectionList = list;
      },
      deptSummarySelectionClear() {
        this.selectionList = [];
        this.$refs.deptSummaryForm.toggleSelection();
      },
      deptSummaryCurrentChange(currentPage){
        this.deptSummaryPage.currentPage = currentPage;
      },
      deptSummarySizeChange(pageSize){
        this.deptSummaryPage.pageSize = pageSize;
      },
      onLoadDeptSummary(page, params = {}) {
        this.deptSummaryLoading = true;
       /* params.planId = this.planId;*/
        params.businessOutletsId = this.businessOutletsId;
        params.dinnerDate = this.dinnerDate;
        params.mealType = this.mealType;
        getOrderList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptSummaryPage.total = data.total;
          this.deptSummaryData = data.records;
          this.deptSummaryLoading = false;
          this.deptSummarySelectionClear();
        });
      },


      deptDetailsSearchReset() {
        this.query = {};
        this.searchDeptUserForm = {};
        this.onLoadDeptDetails(this.deptDetailsPage);
      },
      deptDetailsSearchChange(params, done) {
        this.query = params;
        this.searchDeptUserForm = params;
        this.deptDetailsPage.currentPage = 1
        this.onLoadDeptDetails(this.deptDetailsPage, params);
        done();
      },
      deptDetailsSelectionChange(list) {
        this.selectionList = list;
      },
      deptDetailsSelectionClear() {
        this.selectionList = [];
        this.$refs.deptDetailsForm.toggleSelection();
      },
      deptDetailsCurrentChange(currentPage){
        this.deptDetailsPage.currentPage = currentPage;
      },
      deptDetailsSizeChange(pageSize){
        this.deptDetailsPage.pageSize = pageSize;
      },
      onLoadDeptDetails(page, params = {}) {
        this.deptDetailsLoading = true;
        params.planId = this.planId;
        params.deptId = this.deptId;
        getDeptUserDetailsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptDetailsPage.total = data.total;
          this.deptDetailsData = data.records;
          this.deptDetailsLoading = false;
          this.deptDetailsSelectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      /*      exportBiddingData(){
              const loading = this.$loading({
                lock: true,
                text: '正在导出数据，请稍后',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              exportBiddingData2(this.searchFrom).then(res => {
                loading.close();
                const blob = new Blob([res.data]);
                const fileName = '中标情况统计报表.xlsx';
                const linkNode = document.createElement('a');

                linkNode.download = fileName; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击

                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
              })
            },*/
      openDeptSummaryDateils(row){
        this.title = row.title;
        this.businessOutletsName = row.businessOutletsName;
        this.payStartTime =  row.payStartTime;
        this.payEndTime =  row.payEndTime;
        this.serveFoodStartDate = row.serveFoodStartDate;
        this.serveFoodEndDate = row.serveFoodEndDate;
        this.isPublish = row.isPublish;
        this.planId = row.id;
        this.searchForm.planId = this.planId;
        this.deptSummaryPage.currentPage = 1;
        this.onLoadDeptSummary(this.deptSummaryPage)
        this.managementVisible = true;
      },
      openDeptUserDetails(row){
        this.deptId = row.deptId;
        this.planId = row.planId;
        this.searchDeptUserForm.deptId = this.deptId;
        this.searchDeptUserForm.planId = this.planId;
        this.deptDetailsVisible = true;
      },
      exportDeptSummaryData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出下方明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportOrderMealData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '部门用餐信息汇总报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportDeptUserDetailsData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出下方明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDeptUserDetailsData(this.searchDeptUserForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '班级人员订餐信息报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },

      getMorDiningNumber(row){
        this.businessOutletsId = row.businessOutletsId;
        this.dinnerDate = row.dinnerDate;
        this.mealType = 0;
        this.deptSummaryPage.currentPage = 1;
        this.searchOrderForm.businessOutletsId = row.businessOutletsId;
        this.searchOrderForm.dinnerDate = row.dinnerDate;
        this.searchOrderForm.mealType = this.mealType;
        this.businessOutletsId = row.businessOutletsId;
        this.dinnerDate = row.dinnerDate;
        this.onLoadDeptSummary(this.deptSummaryPage)
        this.managementVisible = true;
      },
      getAftDiningNumber(row){
        this.businessOutletsId = row.businessOutletsId;
        this.dinnerDate = row.dinnerDate;
        this.mealType = 1;
        this.deptSummaryPage.currentPage = 1;
        this.searchOrderForm.businessOutletsId = row.businessOutletsId;
        this.searchOrderForm.dinnerDate = row.dinnerDate;
        this.searchOrderForm.mealType = this.mealType;
        this.onLoadDeptSummary(this.deptSummaryPage)
        this.managementVisible = true;
      },
      getDinDiningNumber(row){
        this.businessOutletsId = row.businessOutletsId;
        this.dinnerDate = row.dinnerDate;
        this.mealType = 2;
        this.deptSummaryPage.currentPage = 1;
        this.searchOrderForm.businessOutletsId = row.businessOutletsId;
        this.searchOrderForm.dinnerDate = row.dinnerDate;
        this.searchOrderForm.mealType = this.mealType;
        this.onLoadDeptSummary(this.deptSummaryPage)
        this.managementVisible = true;
      },
      getNigDiningNumber(row){
        this.businessOutletsId = row.businessOutletsId;
        this.dinnerDate = row.dinnerDate;
        this.mealType = 3;
        this.deptSummaryPage.currentPage = 1;
        this.searchOrderForm.businessOutletsId = row.businessOutletsId;
        this.searchOrderForm.dinnerDate = row.dinnerDate;
        this.searchOrderForm.mealType = this.mealType;
        this.onLoadDeptSummary(this.deptSummaryPage)
        this.managementVisible = true;
      },
      exportOrderData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出自选餐数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportOrderData(this.searchOrderForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '预订餐次订单报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      synchronOrder(){
        const loading = this.$loading({
          lock: true,
          text: '正在同步订单数据......',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        synchronOrder2(this.searchOrderForm).then(() => {
          loading.close()
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "同步成功"
          });
        }, error => {
          console.log(error);
        });
      },
    }
  };
</script>

<style>
</style>
