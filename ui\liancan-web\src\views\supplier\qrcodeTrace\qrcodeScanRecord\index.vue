<template>
    <basicContainer>
        <avue-crud
            ref="crud"
            v-model="form"
            :option="option"
            :table-loading="loading"
            :data="data"
            :page="page"
            :before-open="beforeOpen"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @on-load="onLoad">
            <template slot="menuLeft">
                <el-button
                    class="filter-item"
                    size="small"
                    type="warning"
                    icon="el-icon-download"
                    @click="handleDownload">导出</el-button>
            </template>
        </avue-crud>
        <canvas ref="qrcodeCanvas" />
    </basicContainer>
</template>

<script>
import QRCode from "qrcode";
import {
    getListSummaryByGood as getList,
    getDetailSummaryByGood as getDetail,
    exportReportSummaryByGood as exportReport
} from "@/api/qrcodeTrace/qrcodeTraceScan";
import {
    mapGetters
} from "vuex";
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: false,
            data: [],
            selectionList: [],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            option: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                menu: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: true,
                column: [
                    {//4
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        hide:true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "商品子项编码",
                        prop: "code",
                        type: "input",
                        display:false,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        search: true,
                        display:false,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {//5
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "溯源码",
                        prop: "cardSn",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {
                        label: "生产日期",
                        prop: "prodDate",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {
                        label: "生产批次",
                        prop: "prodBatchNo",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {
                        label: "扫码人员",
                        prop: "optUserName",
                        type: "input",
                        search: true,
                    },
                    {
                        label: "扫码时间",
                        prop: "optTime",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {
                        label: "开始日期",
                        prop: "startDate",
                        type: "date",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                        search: true,searchSpan: 5,
                        hide: true,
                    },
                    {
                        label: "结束日期",
                        prop: "endDate",
                        type: "date",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                        search: true,searchSpan: 5,
                        hide: true
                    },
                ]
            },
        }
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        onLoad(page, params = {}) {
            // this.loading = true;
            // getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
            //     const data = res.data.data;
            //     this.page.total = data.total;
            //     this.data = data.records;
            //     this.loading = false;
            // });
            this.data = [
            {
                "biddingTypeId": 1,
                "code": "001",
                "name": "苹果",
                "foodSpec": "大",
                "unit": "kg",
                "cardSn": "1234567890",
                "prodDate": "2022-01-01",
                "prodBatchNo": "001",
                "optUserName": "王伟明",
                "optTime": "2022-01-01 12:00:00",
                "startDate": "2022-01-01",
                "endDate": "2022-01-31"
            },
            {
                "biddingTypeId": 2,
                "code": "002",
                "name": "苋菜",
                "foodSpec": "大",
                "unit": "g",
                "cardSn": "2345678901",
                "prodDate": "2022-02-01",
                "prodBatchNo": "002",
                "optUserName": "李晓玲",
                "optTime": "2022-02-01 13:00:00",
                "startDate": "2022-02-01",
                "endDate": "2022-02-28"
            },
            {
                "biddingTypeId": 3,
                "code": "003",
                "name": "豆芽",
                "foodSpec": "大",
                "unit": "ml",
                "cardSn": "3456789012",
                "prodDate": "2022-03-01",
                "prodBatchNo": "003",
                "optUserName": "张志强",
                "optTime": "2022-03-01 14:00:00",
                "startDate": "2022-03-01",
                "endDate": "2022-03-31"
            },
            {
                "biddingTypeId": 4,
                "code": "004",
                "name": "鸭腿",
                "foodSpec": "大",
                "unit": "t",
                "cardSn": "4567890123",
                "prodDate": "2022-04-01",
                "prodBatchNo": "004",
                "optUserName": "刘晨曦",
                "optTime": "2022-04-01 15:00:00",
                "startDate": "2022-04-01",
                "endDate": "2022-04-30"
            },
            {
                "biddingTypeId": 5,
                "code": "005",
                "name": "带鱼",
                "foodSpec": "大",
                "unit": "kg",
                "cardSn": "5678901234",
                "prodDate": "2022-05-01",
                "prodBatchNo": "005",
                "optUserName": "陈佳敏",
                "optTime": "2022-05-01 16:00:00",
                "startDate": "2022-05-01",
                "endDate": "2022-05-31"
            },
            {
                "biddingTypeId": 6,
                "code": "006",
                "name": "巴浪鱼",
                "foodSpec": "大",
                "unit": "g",
                "cardSn": "6789012345",
                "prodDate": "2022-06-01",
                "prodBatchNo": "006",
                "optUserName": "江涛",
                "optTime": "2022-06-01 17:00:00",
                "startDate": "2022-06-01",
                "endDate": "2022-06-30"
            },
            {
                "biddingTypeId": 7,
                "code": "007",
                "name": "豆豉",
                "foodSpec": "大",
                "unit": "ml",
                "cardSn": "7890123456",
                "prodDate": "2022-07-01",
                "prodBatchNo": "007",
                "optUserName": "黄文杰",
                "optTime": "2022-07-01 18:00:00",
                "startDate": "2022-07-01",
                "endDate": "2022-07-31"
            },
            {
                "biddingTypeId": 8,
                "code": "008",
                "name": "白菜",
                "foodSpec": "大",
                "unit": "t",
                "cardSn": "8901234567",
                "prodDate": "2022-08-01",
                "prodBatchNo": "008",
                "optUserName": "沈丽君",
                "optTime": "2022-08-01 19:00:00",
                "startDate": "2022-08-01",
                "endDate": "2022-08-31"
            }]
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        handleDownload: function() {
            let loading;
            this.$confirm("确定导出数据?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    loading = this.$loading({
                        lock: true,
                        text: '正在导出数据，请稍后',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    return exportReport(this.page.currentPage, this.page.pageSize, this.query);
                })
                .then((res) => {
                    loading.close();
                    const blob = new Blob([res.data]);
                    const fileName = '扫码记录.xlsx';
                    const linkNode = document.createElement('a');
                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();
                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
    }
}
</script>

<style scoped>

</style>
