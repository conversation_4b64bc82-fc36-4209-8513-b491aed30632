<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
               <template slot="menu" slot-scope="{row}">
                  <el-button  size="mini" icon="el-icon-view" type="text" @click="viewOpen(row)">查看</el-button>
                </template>

      <template slot-scope="scope" slot="leaveProofForm">
        <!-- <div v-for="(item, index) in scope.value" :key="index" style="width: 100%;margin-bottom:10px">
            <el-link type="primary" @click="openPreviewByView(item)">{{item.name}}</el-link>
         </div> -->
        <img width="200px" style="margin-right:20px" v-for="(item, index) of scope.value" :key="index" :src="item.link" @click="openPreviewByView(item)">
      </template>

    </avue-crud>


    <el-dialog  append-to-body :visible.sync="imageDialog" @close="closeImage">
      <el-image  :src="fileUrl">
      </el-image>
    </el-dialog>

    <el-dialog title="商品详情" :visible.sync="viewVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
     <el-row>
       <div>
        <div  class="row-table">
          <div class="row-table-first">所属食堂</div><div class="row-table-second">{{viewModel.deptName}}</div>
          <div class="row-table-first">商品名称</div><div class="row-table-second">{{viewModel.goodsName}}</div>
          <div class="row-table-first">品牌</div><div class="row-table-second">{{viewModel.brand}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">计量单位</div><div class="row-table-second">{{ viewModel.unit }}</div>
          <div class="row-table-first">出仓数量</div><div class="row-table-second">{{viewModel.thisNumber}}</div>
          <div class="row-table-first">食材大类</div><div class="row-table-second">{{viewModel.bigName}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">食材小类</div><div class="row-table-second">{{ viewModel.smallName }}</div>
          <div class="row-table-first">出仓类型</div><div class="row-table-second">{{viewModel.$type}}</div>
          <div class="row-table-first">出仓登记人</div><div class="row-table-second">{{viewModel.registerName}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">出仓登记时间</div><div class="row-table-second">{{ viewModel.registerTime }}</div>
          <div class="row-table-first">出仓确认人</div><div class="row-table-second">{{viewModel.confirmName}}</div>
          <div class="row-table-first">出仓确认时间</div><div class="row-table-second">{{viewModel.confirmTime}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">损失证明</div><div class="row-table-second" style="width: 89.5%;"></div>
        </div>
        <!-- <div  class="row-table" style="line-height: 130px;">
          <div class="row-table-first">图片</div>
          <div class="row-table-second" style="width: 89.5%; height: 130px;"><img class="image-view" :src="viewModel.imgUrl"></div>
        </div> -->
       </div>
     </el-row>
   </el-dialog>

  </basic-container>
</template>

<script>
import {getOutConfirmList} from "@/api/liancan/storageCount";
import {
  getDeptTree2
} from "@/api/system/dept";
import {mapGetters} from "vuex";
var DIC = {
  bidding: [{
    label: '政府采购',
    value: "2"
  },{
    label: '校内大宗采购',
    value: "0"
  },{
    label: '校内零散采购',
    value: "1"
  }]
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      viewModel: {},
      viewVisible: false,
      form: {},
      imageDialog:false,
      fileUrl:'',
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        /*      height:'auto',
              calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: true,
        searchSpan:100,
        menu: true,
        menuWidth: 100,
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "所属食堂",
            prop: "deptId",
            type: "tree",
            //  multiple: true,
            dicData: [],
            props: {
              label: "title"
            },
            search: false,
            hide: true,
            /*       rules: [{
                     required: true,
                     message: "请选择所属部门",
                     trigger: "click"
                   }]*/
          },
          {
            label: "所属食堂",
            prop: "deptName",
            type: "input",
            display: false,
            hide: true,
          },
          {
            label: "单位",
            prop: "deptIds",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search: true,
            fixed:true,
            align: 'center',
            width: 120,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }],
            display: false,
          },
          {
            label: "学校",
            prop: "schoolId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search: true,
            fixed:true,
            align: 'center',
            width: 120,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }],
            display: false,
          },
          {
            label: "商品名称",
            prop: "goodsName",
            type: "input",
            search:true,
          },
          {
            label: "商品名称",
            prop: "name",
            type: "input",
            hide:true,
            display: false,
          },
          {
            label: "品牌",
            prop: "brand",
            type: "input",
          },
          {
            label: "计量单位",
            prop: "unit",
            type: "input",
          },
          {
            label: "出仓数量",
            prop: "thisNumber",
            type: "input",
          },
          {
            label: "食材大类",
            prop: "purchaseTypeId",
            type: "select",
            search: false,
            //dicFlag: false,
            dicUrl: "/api/service/rabbit-liancan/biddingType/ingredientsType",
            props: {
              label: "name",
              value: "id"
            },
            rules: [{
              required: true,
              message: "请选择招标大类",
              trigger: "blur"
            }],
            hide: true
          },
          {
            label: "食材小类",
            prop: "commodityTypeId",
            type: "select",
            search: false,
            dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/allDict",
            props: {
              label: "name",
              value: "id"
            },
            rules: [{
              required: true,
              message: "请选择招标小类",
              trigger: "blur"
            }],
            hide: true
          },
          {
            label: "登记开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
            viewDisplay: false,
          },
          {
            label: "登记结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
            viewDisplay: false,
          },
          /*           {
                       label: '登记时间',
                       prop: 'registrationDate',
                       type:'datetime',
                       searchSpan:6,
                       searchRange:true,
                       search:true,
                       format: 'yyyy-MM-dd',
                       valueFormat: 'yyyy-MM-dd',
                       hide:true,
                         display: false,
                     },*/
          {
            label: "出仓类型",
            prop: "type",
            type: "select",
            dicData: [
              {
                label: "正常出库",
                value: "0"
              }, {
                label: "盘点报废",
                value: "1"
              }
            ],
            search: true,
          },
          {
            label: "出仓登记人",
            prop: "registerName",
            type: "input",
            search:true,
          },
          {
            label: '出仓登记时间',
            prop: 'registerTime',
            type: 'date',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            width:150,
          },
          {
            label: "出仓确认人",
            prop: "confirmName",
            type: "input",
          },
          {
            label: '出仓确认时间',
            prop: 'confirmTime',
            type: 'date',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            width:150,
          },
          {
            label: '备注',
            prop: 'remarks',
            type: 'input',
            overHidden: true,
            hide: true
          },
          {
            label: '确认状态',
            prop: 'confirmStats',
            type: 'select',
            search:true,
            hide:true,
            dicData: [
              {
                label: "未确认",
                value: "1"
              },
              {
                label: "确认通过",
                value: "2"
              }
            ],
            display: false,
          },
          {
            label: '确认状态',
            prop: 'confirmStatus',
            type: 'select',
            dicData: [
              {
                label: "未确认",
                value: "1"
              },
              {
                label: "确认通过",
                value: "2"
              }
            ],
          },
          {
            label: "损失原因",
            prop: "cause",
            type: "input",
            span:12,
            slot:true,
            rules: [{
              required: true,
              message: "请输入损失原因",
              trigger: "blur"
            }],
            display: false,
            hide:true,
          },
          {
            label: "损失处理方式",
            prop: "handleMethod",
            type: "input",
            span:12,
            slot:true,
            rules: [{
              required: true,
              message: "请输入损失处理方式",
              trigger: "blur"
            }],
            display: false,
            hide:true,
          },
          {
            label: '损失证明',
            prop: 'leaveProof',
            hide:true,
            formslot:true,
          },
          {
            label:"关联盘点单号",
            prop:"verificationId",
            display:false,
            hide:true,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    getDeptTree2(this.schoolId,null).then(res => {
      const index = this.$refs.crud.findColumnIndex("deptId");
      this.option.column[index].dicData = res.data.data;
    });
    // 单位列表是否显示
    this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei') ? true: false;
    if (this.userInfo.userType === 'school'){
      this.option.column[3].search = false;
      this.option.column[3].hide = true;

      this.option.column[4].search = false;
      this.option.column[4].hide = true;
    }else if (this.userInfo.userType === 'education'){
      this.option.column[3].search = false;
      this.option.column[3].hide = true;

      this.option.column[4].search = false;
      this.option.column[4].hide = true;
    }else if (this.userInfo.userType === 'jiWei'){
      this.option.column[3].search = false;
      this.option.column[3].hide = true;

      this.option.column[4].search = false;
      this.option.column[4].hide = true;
    }else if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[2].hide = true;
      this.option.column[3].search = false;
      this.option.column[3].hide = true;

      this.option.column[4].search = false;
      this.option.column[4].hide = true;
    }
  },
  methods: {
    beforeOpen(done, type) {
      if (this.form.type === '1') {
        this.option.column[21].display = true;
        this.option.column[20].display = true;
        this.option.column[19].display = true;
      }else {
        this.option.column[21].display = false;
        this.option.column[20].display = false;
        this.option.column[19].display = false;
      }
      //只有盘点报废才显示关联id
      this.option.column.forEach(ele=>{
        if(ele.prop == 'verificationId'){
          if(this.form.verificationId != null && this.form.verificationId != 0){
            ele.display = true;
          }else{
            ele.display = false;
          }
        }
      })
      done();
    },
    openPreviewByView:function(item){
      this.imageDialog = true;
      this.fileUrl = item.link;
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      params.startDateTime = params.startDate;
      params.endDateTime = params.endDate;
      this.query = params;

      /*       if (params.registrationDate != '' && params.registrationDate != null && params.registrationDate != undefined) {
               params.startDateTime = params.registrationDate[0];
               params.endDateTime = params.registrationDate[1];
             }*/
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      //默认查未确认的？
      params.confirmStatus = 1;
      if(this.schoolId != null && this.schoolId != ''){
        params.deptId = this.schoolId;
      }
      getOutConfirmList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        let list = data.records;
        for(let i=0; i<list.length; i++){
          if(!list[i].type){
            list[i].type = "0";
          }
          //设置图片数据
          if(list[i].leaveProof != null && list[i].leaveProof.length > 0){
            var images = list[i].leaveProof;
            var imageList = [];
            for(var y = 0 ; y < images.length ; y++){
              imageList.push({'name':'img_'+y+1,'link':images[y]})
            }
            list[i].leaveProof = imageList;
          }
        }
        this.data = list;
        this.loading = false;
        this.selectionClear();
      });
    },
    viewOpen(row){
        this.viewModel =  row
        console.log(this.viewModel)
        this.viewVisible = true;
    }
  }
};
</script>

<style scoped>
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}
</style>
