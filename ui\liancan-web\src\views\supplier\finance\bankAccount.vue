<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="form"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.bank_account_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-turn-off"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="(permission.bankAccountCheck || userInfo.role_name === 'administrator') && row.status == 1 && row.checkStatus == 0"
                   @click.stop="check(row, 0)">审核
        </el-button>
      </template>
      <template slot="frontImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.frontImg" fit="cover" @click="handleClickPreview(row.frontImg)"></el-image>
      </template>
      <template slot="backImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.backImg" fit="cover" @click="handleClickPreview(row.backImg)"></el-image>
      </template>
      <template slot="status"  slot-scope="{row}">
        <el-tag v-if="row.status == '0'" size="medium" type="blue">停用</el-tag>
        <el-tag v-if="row.status == '1'" size="medium" type="success">使用</el-tag>
        <el-tag v-if="row.checkStatus == '0'" size="medium" type="success">未审核</el-tag>
        <el-tag v-if="row.checkStatus == '1'" size="medium" type="success">审核通过</el-tag>
        <el-tag v-if="row.checkStatus == '2'" size="medium" type="success">审核不通过</el-tag>
      </template>
    </avue-crud>
    <el-dialog title="审核" :visible.sync="checkVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="form" :option="checkOption" v-model="form">
      </avue-form>
      <div style="margin-top: 20px;text-align: right;">
        <el-button type="primary" icon="el-icon-check" size="small" @click="passOrNo(1)">通过</el-button>
        <el-button type="danger" icon="el-icon-circle-close" size="small" @click="passOrNo(2)">不通过</el-button>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, passOrNo} from "@/api/liancan2/bankAccount";
  import {mapGetters} from "vuex";
  var DIC = {
    status: [{
      label: '停用',
      value: "0"
    },{
      label: '正常',
      value: "1"
    }],
    purpose: [{
      label: '收款',
      value: "1"
    },{
      label: '付款',
      value: "2"
    }],
  }
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        dialogImageUrl:'',
        dialogVisible:false,
        checkVisible: false,
        detail: {},
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          align: 'center',
          column: [
            {
              label: "id",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              labelWidth: 150,
            },
            {
              label: "学校（食堂）",
              prop: "deptName",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: true,
              labelWidth: 150,
            },
            {
              label: "开户银行",
              prop: "bank",
              type: "select",
              search: true,
              maxlength:30,
              showWordLimit:true,
              labelWidth: 150,
              dicUrl: "/api/service/rabbit-liancan2/bankAccount/getBankList",
              props: {
                label: "name",
                value:"bank"
              },
            },
            {
              label: "银行帐号",
              prop: "account",
              type: "input",
              search: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请输入银行帐号",
                trigger: "blur"
              }],
              maxlength:30,
              showWordLimit:true,
            },
            {
              label: "用途",
              prop: "purpose",
              type: "select",
              search: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请选择用途",
                trigger: "blur"
              }],
              maxlength:30,
              showWordLimit:true,
              dicData: DIC.purpose,
            },
            {
              label: "正面照",
              prop: "frontImg",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请上图片",
                trigger: "blur"
              }],
            },
            {
              label: "反面照",
              prop: "backImg",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请上图片",
                trigger: "blur"
              }],
            },
              {
                label: "创建时间",
                prop: "createTime",
                type: "datetime",
                format: "yyyy-MM-dd HH:mm:ss",
                valueFormat: "yyyy-MM-dd HH:mm:ss",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
                labelWidth: 150,
                rules: [{
                  required: true,
                  message: "请选择创建时间",
                  trigger: "blur"
                }],
              },
              {
                  label: "状态",
                  prop: "status",
                  type: "select",
                  search: true,
                  slot:true,
                labelWidth: 150,
                  rules: [{
                      required: true,
                      message: "请选择状态",
                      trigger: "blur"
                  }],
                  dicData: DIC.status,
              },
          ]
        },
        data: [],

        checkOption: {
          height: 'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          align: 'center',
          editBtn: false,
          delBtn: false,
          addBtn: false,
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: "id",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              disabled: true,
              labelWidth: 150,
            },
            {
              label: "开户银行",
              prop: "bank",
              type: "select",
              disabled: true,
              rules: [{
                required: true,
                message: "请选择开户银行",
                trigger: "blur"
              }],
              maxlength:30,
              showWordLimit:true,
              labelWidth: 150,
              dicUrl: "/api/service/rabbit-liancan2/bankAccount/getBankList",
              props: {
                label: "name",
                value:"bank"
              },
            },
            {
              label: "银行帐号",
              prop: "account",
              type: "input",
              disabled: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请输入银行帐号",
                trigger: "blur"
              }],
              maxlength:30,
              showWordLimit:true,
            },
            {
              label: "用途",
              prop: "purpose",
              type: "select",
              disabled: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请选择用途",
                trigger: "blur"
              }],
              maxlength:30,
              showWordLimit:true,
              dicData: DIC.purpose,
            },
            {
              label: "正面照",
              prop: "frontImg",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              disabled: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请上图片",
                trigger: "blur"
              }],
            },
            {
              label: "反面照",
              prop: "backImg",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              disabled: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请上图片",
                trigger: "blur"
              }],
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "datetime",
              format: "yyyy-MM-dd HH:mm:ss",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              disabled: true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请选择创建时间",
                trigger: "blur"
              }],
            },
            {
              label: "状态",
              prop: "status",
              type: "select",
              search: true,
              slot:true,
              labelWidth: 150,
              rules: [{
                required: true,
                message: "请选择状态",
                trigger: "blur"
              }],
              dicData: DIC.status,
              disabled: true,
            },
          ]
        },
      };
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.bank_account_add, false),
          viewBtn: this.vaildData(this.permission.bank_account_view, false),
          delBtn: this.vaildData(this.permission.bank_account_delete, false),
          editBtn: this.vaildData(this.permission.bank_account_edit, false),
          checkBtn: this.vaildData(this.permission.bank_account_check, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      if (this.userInfo.userType === 'canteen'){
        this.option.column[1].search = false;
        this.option.column[1].hide = true;
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      check(row){
        this.checkVisible = true;
        getDetail(row.id).then(res => {
          let data = res.data.data;
          this.form = data;
          this.loading = false;
          this.selectionClear();
        });
      },
      passOrNo(checkStatus){
        this.$confirm("确定此操作?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return passOrNo(this.form.id, checkStatus);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.checkVisible = false;
          });
      },

    }
  };
</script>
