<template>
    <basic-container>
        <avue-crud
            :defaults.sync="defaults"
            :option="option"
            :table-loading="loading"
            :data="data"
            :page="page"
            v-model="form"
            ref="crud"
            :before-open="beforeOpen"
            @search-change="searchChange"
            @search-reset="searchReset"
            @current-change="currentChange"
            @size-change="sizeChange"
            @on-load="onLoad">
            <template slot="menu" slot-scope="scope">
                <el-button type="text" size="small" icon="el-icon-house" @click="showInventory(scope.row)">实时库存</el-button>
            </template>
        </avue-crud>
        <el-dialog
            title="实时库存"
            :visible.sync="dialogVisible"
            width="65%"
            top="15px"
            @open="handleDialogOpen"
            @close="dialogVisible=false"
            :append-to-body="true">
            <by-product-summary ref="goods" :deptId="deptId" :deptName="deptName" :canteenId="canteenId" :canteenName="canteenName"></by-product-summary>
        </el-dialog>
    </basic-container>
</template>

<script>
import byProductSummary from '@/views/supplier/customer/inventory/byProductSummary'
import {getCanteenStockByCustomerList} from "@/api/supplier/supplierInventoryPublicSet";
import {mapGetters} from "vuex";
import {getDetail} from "@/api/logout/logoutLogging";
export default {
    components: {
        byProductSummary
    },
    data() {
        return {
            defaults: {},
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            data: [],
            selectionList: [],
            option: {
                height:'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                viewBtn: false,
                menu: true,
                addBtn: false,
                editBtn:false,
                delBtn:false,
                selection: false,
                labelWidth: 150,
                align: 'center',
                pagination: false,
                column: [
                    {
                        label: "单位名称",
                        prop: "supplierName",
                        type: "input",
                    },
                    {
                        label: "单位名称",
                        prop: "supplierId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-liancan/supplierInventoryPublicSet/dept/select-all",
                        props: {
                            label: "deptName",
                            value: "id"
                        },
                        span: 24,
                        filterable: true,
                        searchFilterable: true,
                        search: true,
                        hide: true,
                    },
                    {
                        label: "单位简称",
                        prop: "supplierSimpleName",
                        type: "input",
                    },
                    {
                        label: "下属食堂",
                        prop: "canteenName",
                        type: "input",
                    },
                    {
                        label: "下属食堂",
                        prop: "canteenId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-liancan/supplierInventoryPublicSet/canteen/select-all",
                        props: {
                            label: "deptName",
                            value: "id"
                        },
                        span: 24,
                        filterable: true,
                        searchFilterable: true,
                        hide: true,
                    },
                    {
                        label: "联系人",
                        prop: "contactsName",
                        type: "input",
                    },
                    {
                        label: "联系电话",
                        prop: "contactsPhone",
                        type: "input",
                    },
                ]
            },
            // 实时库存
            dialogVisible: false,
            deptId: '',
            deptName: '',
            canteenId: '',
            canteenName: '',
        }
    },
    computed: {
        ...mapGetters(["permission"]),
    },
    methods: {
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {

            }
            done();
        },
        currentChange(currentPage){
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize){
            this.page.pageSize = pageSize;
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getCanteenStockByCustomerList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            });
        },
        showInventory(row) {
            this.deptId = row.supplierId;
            this.deptName = row.supplierName;
            this.canteenId = row.canteenId;
            this.canteenName = row.canteenName;
            console.log("33=========>"+this.deptId+","+this.deptName+","+this.canteenId+","+this.canteenName);
            this.dialogVisible = true;
        },

        handleDialogOpen() {
            this.$refs.goods.reLoad();
        },
    }
}
</script>

<style lang="scss" scoped>
/deep/ .el-table__fixed-body-wrapper {
    top: 40px !important;
}
</style>
