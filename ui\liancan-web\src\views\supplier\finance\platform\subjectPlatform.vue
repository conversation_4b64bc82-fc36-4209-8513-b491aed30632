<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-turn-off"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="addRow(row, 0)">添加
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button class="filter-item" size="small" type="success" icon="el-icon-upload" @click="exportRecord">导出</el-button>
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.account_sets_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot="frontImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.frontImg" fit="cover" @click="handleClickPreview(row.frontImg)"></el-image>
      </template>
      <template slot="backImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.backImg" fit="cover" @click="handleClickPreview(row.backImg)"></el-image>
      </template>
      <template slot="status"  slot-scope="{row}">
        <el-tag v-if="row.isUse == '0'" size="medium" type="blue">未启用</el-tag>
        <el-tag v-if="row.isUse == '1'" size="medium" type="success">已启用</el-tag>
      </template>
<!--      <template slot="search" slot-scope="{row,size}">-->
<!--        <el-tag>标题</el-tag>-->
<!--        <el-input placeholder="自定义输入框" :size="size" style="width:200px" v-model="search.slot"></el-input>-->
<!--      </template>-->
      <template slot="subjectCode" slot-scope="{row}" >
        <div v-if="row.layer==undefined||row.layer==null||row.layer==0">{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==1">{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==2">&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==3">&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==4">&emsp;&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==5">&emsp;&emsp;&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==6">&emsp;&emsp;&emsp;&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
      </template>
    </avue-crud>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/supplier/finance/platform/subjectPlatform";
import {mapGetters} from "vuex";
import {exportSubjectPlateformRecord} from "@/api/supplier/finance/exportExcel";
var DIC = {
  // 余额方向
  balanceDirection: [{
    label: '借',
    value: 0
  },{
    label: '贷',
    value: 1
  }],
  // 类型
  systemDefault: [{
    label: '固定科目',
    value: 0
  },{
    label: '自定义科目',
    value: 1
  }],
  // 是否允许选择
  isSelect: [{
    label: '否',
    value: 0
  },{
    label: '是',
    value: 1
  }],
}
export default {
  data() {
    return {
      search:{},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        selection: true,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: '上级科目',
            prop: 'subjectId',
            type: "tree",
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: true,
            filters: true,
            hide: true,
            // dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/getSubjectList',
            dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/platform/tree/scope',
            props: {
              label: "subjectName",
              value: "id"
            },
            rules: [{
              required: true,
              message: "科目",
              trigger: "blur"
            }],
          },
          {
            label: "上级科目",
            prop: "parentSubjectName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            labelWidth: 150,
            maxlength:30,
            showWordLimit:true,
            align: "left",
            rules: [{
              required: true,
              message: "上级科目",
              trigger: "blur"
            }],
          },
          // {
          //   label: "科目编码",
          //   prop: "subjectCode",
          //   type: "input",
          //   addDisplay: true,
          //   editDisplay: true,
          //   viewDisplay: true,
          //   search: true,
          //   labelWidth: 150,
          //   maxlength:30,
          //   showWordLimit:true,
          //   rules: [{
          //     required: true,
          //     message: "科目编码",
          //     trigger: "blur"
          //   }],
          // },
          {
            label: "科目编码",
            prop: "subjectCode",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            labelWidth: 150,
            maxlength:30,
            showWordLimit:true,
            align: "left",
            slot: true,
            rules: [{
              required: true,
              message: "科目编码",
              trigger: "blur"
            }],
          },
          {
            label: "科目名称",
            prop: "subjectName",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            showWordLimit:true,
            rules: [{
              required: true,
              message: "科目名称",
              trigger: "blur"
            }],
            width: 250,
          },
          // {
          //   label: "科目名称",
          //   prop: "displaySubjectName",
          //   type: "input",
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          //   search: false,
          //   showWordLimit:true,
          //   width: 250,
          // },
          {
            label: "类别",
            prop: "subjectTypeId",
            type: "select",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            labelWidth: 150,
            dicUrl: "/api/service/rabbit-supplier/finance/financialSubjectType/subjectType",
            props: {
              label: "typeName",
              value:"id"
            },
            rules: [{
              required: true,
              message: "资产类别",
              trigger: "blur"
            }],
          },
          {
            label: "余额方向",
            prop: "balanceDirection",
            type: "select",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.balanceDirection,
            rules: [{
              required: true,
              message: "余额方向",
              trigger: "blur"
            }],
          },
          {
            label: "类型",
            prop: "systemDefault",
            type: "select",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.systemDefault,
            rules: [{
              required: true,
              message: "类型",
              trigger: "blur"
            }],
          },
          {
            label: "是否允许选择使用",
            prop: "isSelect",
            type: "select",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: true,
            searchLabelWidth: 130,
            maxlength:30,
            dicData: DIC.isSelect,
            rules: [{
              required: true,
              message: "是否允许选择使用",
              trigger: "blur"
            }],
          },
          //用途
          {
            label: "用途",
            prop: "effect",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            labelWidth: 150,
            search: false,
          },
          {
            label: "关联供应商",
            prop: "supplierId",
            type: "tree",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            labelWidth: 150,
            search: false,
            dicUrl: "/api/service/rabbit-signUp/supplierSignUp/schoolSupplier",
            props: {
              label: "name",
              value:"id"
            },
            // rules: [{
            //   required: true,
            //   message: "科目编码",
            //   trigger: "blur"
            // }],
          },
          {
            label: "最后编辑人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "最后编辑时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
        ]
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
    }
  },
  methods: {
    getDistplayStr(layer,subjectCode) {
      return subjectCode;
    },
    addRow(row, index) {
      this.$refs.crud.toggleSelection();
      this.selectionList.push(row);
      this.$refs.crud.rowAdd();
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }else if(["add"].includes(type)){
        if(this.selectionList.length>0) {//已经选定值
          setTimeout(() => {
            // 默认值绑定
            // 上级科目-当前选定行
            this.form.subjectId = this.selectionList[0].id;
            // 科目编码-当前选定行
            this.form.subjectCode = this.selectionList[0].subjectCode;
            // 资产类别-当前选定行
            this.form.subjectTypeId = this.selectionList[0].subjectTypeId;
            // 余额方向-当前选定行
            this.form.balanceDirection = this.selectionList[0].balanceDirection;
            // 类型-自定义科目
            this.form.systemDefault = 1;
            // 是否允许选择使用-是
            this.form.isSelect = 1;
          }, 100);
        }
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleClickPreview: function(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    openAccount(row){
      this.checkVisible = true;
      getDetail(row.id).then(res => {
        let data = res.data.data;
        this.$confirm("您确定要启用【"+data.accountName+"】么？", {
          confirmButtonText: "我确认启用帐套",
          cancelButtonText: "我再检查一下",
          type: "warning"
        })
          .then(() => {
            return openAccount(row);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "启用成功!"
            });
          });
      });
    },
    exportRecord(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportSubjectPlateformRecord(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '会计科目列表(平台).xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },

  }
};
</script>
