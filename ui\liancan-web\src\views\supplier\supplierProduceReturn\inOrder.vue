<template>
    <basic-container>
        <el-dialog title="选择产品领料单"
                   :visible.sync="isShowInOrder"
                   v-if="isShowInOrder"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeSelectOrderHandler" width="60%">
            <avue-crud :option="inOrderOption"
                       :table-loading="inOrderLoading"
                       :data="inOrderData"
                       :page="inOrderPage"
                       v-model="inOrderForm"
                       @search-change="searchhange"
                       @search-reset="searchReset"
                       @selection-change="selectionChange"
                       @current-change="currentChange"
                       @size-change="sizeChange"
                       @on-load="onLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="selectInOrder(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
    </basic-container>
</template>
<script>
import {getList} from "@/api/supplier/supplierProduceReturnOrder";
export default {
    emits: ['choiceClick'],
    data(){
        return{
            inOrderForm: {},
            inOrderData:[],
            isShowInOrder:false,
            inOrderLoading:false,
            orderList:[],
            inOrderPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            inOrderOption: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                searchMenuSpan: 4, //搜索按钮长度
                // searchSpan:24,      //搜索框长度  最大长度24
                // searchLabelWidth: 120, //搜索框标题宽度 默认80
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "orderDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() < Date.now();
                            // }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "orderDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "orderDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据号",
                        prop: "orderCode",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "物料名称",
                        prop: "goodsName",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "物料成本总额",
                        prop: "totalAmt",
                        type: "number",
                        labelWidth: 140,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: false,
                            message: "请输入成本总额",
                            trigger: "blur"
                        } ],
                        precision: 2,
                        display: false
                    },
                    {
                        label: '领料员',
                        prop: 'qualityInspectorId',
                        type:'select',
                        search: true,
                        dicUrl: `/api/rabbit-supplier/user-list`,
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        multiple:true
                    },
                    {
                        label: "领料仓库",
                        prop: "warehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,

                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "登记人",
                        prop: "createUserName",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                        display: false
                    },
                    {
                        label: "登记时间",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                        display: false
                    },
                ]
            },
        }
    },
    methods:{
        //关闭显示订单窗口
        closeSelectOrderHandler() {
            this.isShowInOrder = false;
        },
        searchhange(params, done) {
            this.orderList = params;
            this.page.currentPage = 1
            this.receiveOrderListOnLoad(this.receiveOrderPage, params);
            done();
        },
        searchReset() {
            this.orderList = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange(list) {
            this.orderList = list;
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.inOrderLoading = true;

            getList(this.currentPage, this.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.inOrderPage.total = data.total;
                this.inOrderData = data.records;
                this.inOrderLoading = false;
                //this.selectionClear();
            });
        },
        selectInOrder(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.$emit('choiceClick',row);
            this.isShowInOrder = false;
        },
    },
}
</script>
