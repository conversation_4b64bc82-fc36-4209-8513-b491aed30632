<template>
  <basic-container>
    <avue-crud :option="warningOption"
               :table-loading="warningLoading"
               :data="warningData"
               :page="warningPage"
               :permission="permissionList"
               v-model="warningForm"
               ref="warningForm"
               @search-change="searchChangeWarning"
               @search-reset="searchResetWarning"
               @selection-change="selectionChangeWarning"
               @current-change="currentChangeWarning"
               @size-change="sizeChangeWarning"
               @on-load="onLoadWarning">
      <template slot="menu" slot-scope="{row}">
        <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="openWarning(row,'1')">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="handOpenWarningPush(row)">去函询
        </el-button>
        <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="openWarning(row,'2')">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="checkReply(row)">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="management(row)">处置
        </el-button>
        <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="checkNanagement(row)">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '5'" size="mini" type="text" @click="checkFile(row)">查看
        </el-button>
        <!--       <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="handOpenInquiry(row)">查看函询
               </el-button>
               <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="handOpenInquiry(row)">查看函询
               </el-button>-->
      </template>
    </avue-crud>
    <el-dialog title="查看" :visible.sync="warningDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div>
        <el-col :span="24">
          <div sstyle="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">预警日期：</span>{{this.warningDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">当前状态：</span>{{this.inquiryStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">办理单位：</span>{{this.handlingUnit}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规单位：</span>{{this.deptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">单位类型：</span>{{this.unitType}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规分类：</span>{{this.category}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规内容：</span>{{this.content}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规取证内容：</span>{{this.evidence}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType == '2'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">已发函询</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询日期：</span>{{this.inquiryDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询单位：</span>{{this.inquiryDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询人姓名：</span>{{this.pushInquerUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询内容：</span><a href="javascript:void(0);" style="color: #1e9fff" @click="openMakeInquiry">点此查看</a></div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType3 == '3'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">食堂回复</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复日期：</span>{{this.replyTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复单位：</span>{{this.replyDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复人姓名：</span>{{this.replyName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复意见：</span>{{this.replyContent}}</div>
        </el-col>
        <el-col :span="24">
          <div>图片:</div>
          <span v-for="(item,index) in this.fileList">
        <img :src="item.url" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item.url)" class="avatar">
        </span>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType4 == '4'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">处置信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置日期：</span>{{this.handleDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置单位：</span>{{this.handleDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置人姓名：</span>{{this.handleUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置意见：</span>{{this.handleContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType5 == '5'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">归档信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档日期：</span>{{this.fileDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档单位：</span>{{this.fileDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档人姓名：</span>{{this.fileUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档意见：</span>{{this.fileContent}}</div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :title="`关于${this.warningName1}涉嫌${this.warningName2}的函询`" :visible.sync="warningRecordVisible1" :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <div style="font-size: 20px;">{{this.warningName1}}相关负责人:</div>
      <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">经我单位监管，贵单位可能存在以下违规行为，特此函询，请据实说明情况。</div>
      <avue-form ref="warningRecordForm1" :option="warningRecordOption1" v-model="warningRecordForm1">
        <template slot-scope="scope" slot="menuForm">
          <el-button @click="pushInquiry" type="primary">发送</el-button>
          <el-button @click=" warningRecordVisible1 = false">取消</el-button>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <span style="font-size: 20px;">{{this.jiweiDeptName}}</span>
              <br/>
        <span style="font-size: 20px;">{{this.jiweiDate}}</span>
                 <br/>
        <span style="font-size: 15px;">如有疑问可联系负责人：{{this.jiweiAdminName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </span>
      <!--      <avue-form ref="warningRecordForm2" :option="warningRecordOption2" v-model="warningRecordForm2">
              <template slot-scope="scope" slot="menuForm">
                <el-button @click="pushInquiry" type="primary">发送</el-button>
                <el-button @click="tip">取消</el-button>
              </template>
            </avue-form>-->
    </el-dialog>
    <el-dialog :visible.sync="otherWarningVisible2" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <div style=" text-align: center;font-size: 26px;margin-bottom: 10px;">关于{{this.warningName1}}涉嫌其他违规的函询</div>
      <div style=" text-align: center;">函询编号：{{this.inquiryNo}}</div>
      <el-row>
        <el-col :span="25">
          <basic-container>
            <div style="font-size: 20px;">{{this.warningName1}}相关负责人：</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">经我单位监管，贵单位可能存在以下违规行为，特此函询，请据实说明情况。</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规发现日期：{{this.warningDate}}</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规分类：{{this.category}}</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规内容：{{this.warningContent}}</div>
            <div>
              <div><span style="font-size: 20px;margin-left: 40px;">以下为违规取证材料：{{this.evidence}}</span></div>
            </div>
          </basic-container>
        </el-col>
      </el-row>
      <el-row>
        <span style="font-size: 20px;float: right">{{this.jiweiDeptName}}<br/>{{this.jiweiDate}}</span>
        <br/>
        <span style="font-size: 15px;float: left;margin-top: 20px;">如有疑问可联系负责人：{{this.jiweiAdminName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </el-row>
      <br/>
      <br/>
      <br/>
      <br/>
      <br/>
    </el-dialog>

    <el-dialog :title="`处置`" :visible.sync="managementVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="managementForm" :option="managementOption" v-model="managementForm" @submit="saveManage">
      </avue-form>
    </el-dialog>
    <el-dialog :title="`归档`" :visible.sync="fileVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="fileForm" :option="fileOption" v-model="fileForm" @submit="fileManage">
      </avue-form>
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="40%" height="30%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getSchoolList} from "@/api/liancan/schoolAuthInfo";
  import {getViolationWarningList,getWarningHandlingList,getWarningById,saveInquiry,getInquiryById,saveManage,saveFile} from "@/api/liancan/illegalWarnLog";
  /*  import {getWarningList,getWarningById,saveInquiry,getInquiryById,saveArchivist,getAllWarningList,saveMakeInquiry} from "@/api/liancan/illegalWarnLog";*/
  import {
    getDeptById
  } from "@/api/system/dept";
  import {mapGetters} from "vuex";
  import AvueUeditor from 'avue-plugin-ueditor';
  export default {
    data() {
      return {
        regulationForm: {},
        query: {},
        warningForm:{},
        managementForm:{},
        fileForm:{},
        makeInquiryForm:{deptId:'',urlList:[]},
        loading: true,
        warningLoading:true,
        page: {
          pageSize: 50,
          currentPage: 1,
          total: 0
        },
        warningPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        allWarningListPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        warningListData:[],
        allWarningListData:[],
        uploadFilesList:[],
        fileList:[],
        warningData:[],
        warningVisible:false,
        warningDetailsVisible:false,
        warningRecordVisible1:false,
        otherWarningVisible2:false,
        managementVisible:false,
        fileVisible:false,
        dialogVisible:false,
        schoolType:undefined,
        canteenName:undefined,
        adminName:undefined,
        phone:undefined,
        warningDate:undefined,
        deptName:undefined,
        unitType:undefined,
        inquiryStatus:undefined,
        category:undefined,
        handlingUnit:undefined,
        schoolName:undefined,
        content:undefined,
        evidence:undefined,
        warningName1:undefined,
        jiweiDeptName:undefined,
        jiweiDate:undefined,
        jiweiAdminName:undefined,
        jiweiAdminAccount:undefined,
        warningName2:undefined,
        warningType:undefined,
        warningType3:undefined,
        warningType4:undefined,
        warningType5:undefined,
        inquiryDate:undefined,
        inquiryDeptName:undefined,
        pushInquerUserName:undefined,
        inquiryName:undefined,
        warningId:undefined,
        handleDate:undefined,
        handleUserName:undefined,
        handleContent:undefined,
        handleDeptName:undefined,
        fileUserName:undefined,
        fileContent:undefined,
        fileDate:undefined,
        fileDeptName:undefined,
        warningRecordForm1:{},
        dialogImageUrl:'',
        option: {
          /*       height:'auto',
                 calcHeight: 30,
                 searchShow: true,
                 searchMenuSpan: 6,
                 tip: false,
                 border: true,
                 index: true,
                 viewBtn: true,
                 selection: true,
                 viewBtn:false,
                 delBtn:false,
                 addBtn:false,
                 menu:false,*/
          addBtn:false,
          menu:false,
          column: [
            /*        {
                      label: "主键",
                      prop: "id",
                      type: "input",
                      addDisplay: false,
                      editDisplay: false,
                      viewDisplay: false,
                      hide: true,
                    },
                    {
                      label: "学校",
                      prop: "schoolName",
                      type: "input",
                     /!* slot:true,*!/
                    },*/
          ]
        },
        warningOption: {
          /* height:'auto',*/
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '预警日期',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              width:100,
            },
            {
              label: "学校",
              prop: "companyId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide:true,
              search: true,
              fixed:true,
              align: 'center',
              width: 120,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "deptName",
              type: "input",
            },
            {
              label: "违规分类",
              prop: "category",
              type: "select",
              search:true,
              dicData: [
                {
                  label: "收入违规",
                  value: "0"
                },
                {
                  label: "支出违规",
                  value: "1"
                },
                {
                  label: "采购违规",
                  value: "2"
                },
                {
                  label: "财务违规",
                  value: "3"
                },
                {
                  label: "招标违规",
                  value: "4"
                },
                {
                  label: "仓管违规",
                  value: "5"
                }
              ],

            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              width:600,
            },
            {
              label: "当前状态",
              prop: "inquiryStatus",
              type: "select",
              width:90,
              search:true,
              dicData: [
                {
                  label: "待函询",
                  value: "1"
                },
                {
                  label: "待食堂回复",
                  value: "2"
                },
                {
                  label: "待处置",
                  value: "3"
                },
                {
                  label: "待归档",
                  value: "4"
                },
                {
                  label: "已归档",
                  value: "5"
                }
              ],
            },
            {
              label: "办理单位",
              prop: "handlingUnit",
              type: "input",
              width:150,
            },
          ]
        },
        warningRecordOption1:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              display:false,
            },
            {
              label: "违规预警记录编号",
              prop: "inquiryNo",
              type: "input",
              disabled:true,
            },
            {
              label: "违规单位",
              prop: "deptName",
              type: "input",
              disabled:true,
            },
            {
              label: "违规类型",
              prop: "type",
              type: "select",
              disabled:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入类型",
                trigger: "blur"
              }],
            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              disabled:true,
              span:24,
            },
            {
              label: '违规时间',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: '违规发现时间',
              prop: 'findDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: "单位管理员姓名",
              prop: "userName",
              type: "input",
              disabled:true,
            },
            {
              label: "管理员手机号",
              prop: "userPhone",
              type: "input",
              disabled:true,
            },
            {
              label: "违规取证",
              prop: "evidence",
              type: "textarea",
              span: 24,
              disabled:true,
            },
          ]
        },
        managementOption:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          labelWidth: 150,
          column: [
            {
              label: "请输入处置意见",
              prop: "content",
              type: "textarea",
              minRows:10,
              maxlength:1000,
              span:24,
              showWordLimit:true,
              placeholder:"请具体说明情况，限1000字。",
              rules: [{
                required: true,
                message: "请输入处置意见",
                trigger: "click"
              }]

            },
          ]
        },
        fileOption:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          labelWidth: 150,
          column: [
            {
              label: "请输入归档意见",
              prop: "content",
              type: "textarea",
              minRows:10,
              maxlength:1000,
              span:24,
              showWordLimit:true,
              placeholder:"请具体说明情况，限1000字。",
              rules: [{
                required: true,
                message: "请输入归档意见",
                trigger: "click"
              }]

            },
          ]
        },
        data: []
      };
    },
    /*    created(){
          this.warningStatus = "1";
        },*/
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          /*     addBtn: this.vaildData(this.permission.policies_regulations_add, false),
               viewBtn: this.vaildData(this.permission.policies_regulations_view, false),
               delBtn: this.vaildData(this.permission.policies_regulations_delete, false),
               editBtn: this.vaildData(this.permission.policies_regulations_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      universityCanteen(){
        console.log(">>>>>>>>>>>>>>>大學食堂")
        this.page.currentPage = 1;
        this.schoolType = "university";
        this.canteenName = "大学食堂";
        this.onLoad(this.page)
      },
      middleSchoolCanteen(){
        console.log(">>>>>>>>>>>>>>>中学食堂")
        this.page.currentPage = 1;
        this.schoolType = "middle";
        this.canteenName = "中学食堂";
        this.onLoad(this.page)
      },
      primarySchoolCanteen(){
        console.log(">>>>>>>>>>>>>>>小学食堂")
        this.page.currentPage = 1;
        this.schoolType = "primary";
        this.canteenName = "小学食堂";
        this.onLoad(this.page)
      },
      childCanteen(){
        console.log(">>>>>>>>>>>>>>>幼儿食堂")
        this.page.currentPage = 1;
        this.schoolType = "child";
        this.canteenName = "幼儿食堂";
        this.onLoad(this.page)
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.regulationForm.id).then(res => {
            this.regulationForm = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchResetWarning() {
        this.query = {};
        this.onLoadWarning(this.warningPage);
      },
      searchResetAllWarning() {
        this.query = {};
        this.onLoadAllWarning(this.allWarningListPage);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      searchChangeWarning(params, done) {
        this.query = params;
        this.warningPage.currentPage = 1
        this.onLoadWarning(this.warningPage, params);
        done();
      },
      searchChangeAllWarning(params, done) {
        this.query = params;
        this.allWarningListPage.currentPage = 1
        this.onLoadAllWarning(this.allWarningListPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChangeWarning(list) {
        this.selectionList = list;
      },
      selectionChangeAllWarning(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.regulationForm.toggleSelection();
      },
      selectionClearWarning() {
        this.selectionList = [];
        this.$refs.warningForm.toggleSelection();
      },
      selectionClearAllWarning() {
        this.selectionList = [];
        this.$refs.allWarningListForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeWarning(currentPage){
        this.warningPage.currentPage = currentPage;
      },
      currentChangeAllWarning(currentPage){
        this.allWarningListPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeWarning(pageSize){
        this.warningPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getSchoolList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.schoolType).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoadWarning(page, params = {}) {
        this.warningLoading = true;
        getWarningHandlingList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.warningPage.total = data.total;
          this.warningData = data.records;
          this.warningLoading = false;
          this.selectionClearWarning();
        });
      },
      WarningClose(){
        this.warningVisible = false;
      },
      hanbleOpenWarningList(row){
        this.deptId = row.id;
        this.adminName = row.userName;
        this.phone = row.userPhone;
        this.schoolName = row.schoolName;
        this.warningVisible = true;
      },
      openWarning(row,status){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = status;
        this.warningType = status;
        this.warningType3 = status;
        this.warningType4 = status;
        this.warningType5 = status;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.jiweiDeptName;
        this.inquiryName = row.jiweiUserName;
        this.pushInquerUserName = row.pushInquerUserName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.warningDetailsVisible = true;
      },
      pushInquiry(row){
        saveInquiry(this.warningRecordForm1).then(() => {
          this.warningPage.currentPage = 1;
          this.onLoadWarning(this.warningPage);
          this.warningRecordVisible1 = false;
          this.$message({
            type: "success",
            message: "发送成功!"
          });
        }, error => {
          this.warningRecordVisible1 = false;
          window.console.log(error);
        });
      },
      handOpenWarningPush(row){
        getWarningById(row.id).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.warningName2 = res.data.data.typeName;
          this.warningRecordForm1 = res.data.data;
        });
        this.warningRecordVisible1 = true;
      },
      openMakeInquiry(){
        getInquiryById(this.warningId).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.warningContent = res.data.data.content;
          this.inquiryNo = res.data.data.inquiryNo;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.evidence = res.data.data.evidence;
          if (res.data.data.category == "0") {
            this.category = "收入违规"
          }
          if (res.data.data.category == "1") {
            this.category = "支出违规"
          }
          if (res.data.data.category == "2") {
            this.category = "采购违规"
          }
          if (res.data.data.category == "3") {
            this.category = "财务违规"
          }
          if (res.data.data.category == "4") {
            this.category = "招标违规"
          }
          if (res.data.data.category == "5") {
            this.category = "仓管违规"
          }
        });
        this.otherWarningVisible2 = true;
      },
      checkReply(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 2
        this.warningType5 = 1
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.jiweiDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.pushInquerUserName = row.pushInquerUserName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.warningDetailsVisible = true;
      },
      checkNanagement(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 4;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.jiweiDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.handleUserName = row.handleUserName;
        this.handleContent = row.handleContent;
        this.handleDeptName = row.handleDeptName;
        this.handleDate = row.handleDate;
        this.pushInquerUserName = row.pushInquerUserName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.warningDetailsVisible = true;
      },
      checkFile(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 4;
        this.warningType5 = 5;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.jiweiDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.handleUserName = row.handleUserName;
        this.handleContent = row.handleContent;
        this.handleDeptName = row.handleDeptName;
        this.handleDate = row.handleDate;
        this.fileUserName = row.fileUserName;
        this.fileContent = row.fileContent;
        this.fileDate = row.fileDate;
        this.fileDeptName = row.fileDeptName;
        this.unitType = row.unitType;
        this.pushInquerUserName = row.pushInquerUserName;
        this.deptName = row.deptName;
        this.warningDetailsVisible = true;
      },
      management(row){
        this.warningId = row.id;
        this.managementVisible = true;
      },
      fileOpen(row){
        this.warningId = row.id;
        this.fileVisible = true;
      },
      saveManage(row,done,loading){
        this.managementForm.warningId = this.warningId;
        saveManage(this.managementForm).then(() => {
          this.warningPage.currentPage = 1;
          this.onLoadWarning(this.warningPage);
          this.managementVisible = false;
          this.managementForm.content = "";
          done();
          this.$message({
            type: "success",
            message: "处置成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      fileManage(row,done,loading){
        this.fileForm.warningId = this.warningId;
        saveFile(this.fileForm).then(() => {
          this.warningPage.currentPage = 1;
          this.onLoadWarning(this.warningPage);
          this.fileVisible = false;
          this.fileForm.content = "";
          done();
          this.$message({
            type: "success",
            message: "处置成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      queryImg(images){
        this.dialogImageUrl = images;
        this.dialogVisible = true;
      }
    }
  };
</script>

<style>
  .item {
    margin-top: 10px;
    margin-right: 40px;
  }
</style>
