<template>
  <basic-container>
    <div class="table-form">
      <div class="voucherListApproval">
        <div v-show="!showDiv" style="margin-left: 15px;">
          <el-form ref="form" :model="form" label-width="80px" >
            <el-form-item label="当前帐套:">
              <div style="display: flex;flex-direction: row;">
                <div>
                  <el-select v-model="accountSetsValue" placeholder="请选择帐套" style="width:400px;">
                    <el-option
                      v-for="item in accountSetsList"
                      :key="item.id"
                      :label="item.accountName"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </div>
                <div style="margin-left: 100px;">
                  <el-input v-model="this.peroidStr" placeholder="请选择会计期间" readonly="readonly" style="width: 250px;"></el-input>
                  <el-button type="primary" icon="el-icon-caret-bottom" @click="selectPeroidHandle">选择</el-button>
                  <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
                </div>
                <div style="margin-left: 100px;">
                  <!-- myApproveValue为0时查询全部人的凭证，1为查询待我审核的凭证 -->
                  <el-switch v-model="myApproveValue" active-text="待我审核的凭证" @change="changeApproveValue" active-color="#13ce66" ></el-switch>
                  <el-button v-if="(this.permission.financialVoucherApproval)" type="primary" @click="btnBatchApproveHandle" style="margin-left: 10px;">批量审核</el-button>
                </div>
              </div>
            </el-form-item>
          </el-form>
        </div>
        <!-- <el-button style="margin-left: 15px;" type="success" size="mini" @click="clickShow">筛选条件 {{ showDiv ? "展开" : "收起" }}</el-button>
        <el-divider></el-divider> -->

        <avue-crud :option="option"
                  :table-loading="loading"
                  :data="data"
                  :page="page"
                  :before-open="beforeOpen"
                  :span-method="spanMethod"
                  v-model="form"
                  ref="crud"
                  @row-update="rowUpdate"
                  @row-save="rowSave"
                  @row-del="rowDel"
                  @search-change="searchChange"
                  @search-reset="searchReset"
                  @selection-change="selectionChange"
                  @current-change="currentChange"
                  @size-change="sizeChange"
                  @on-load="onLoad">

          <!--      自定义列-->
          <!--      摘要-->
          <template slot-scope="scope" slot="summary">
            <div v-for="(itemc) in scope.row.detailsVOList" v-bind:key="itemc">
              <div class="subDivCell">
                &nbsp;&nbsp;&nbsp;{{itemc.summary}}
              </div>
            </div>
            <div class="subDivSummary">&nbsp;&nbsp;&nbsp;合计</div>
          </template>
          <!--      科目-->
          <template slot-scope="scope" slot="subjectName">
            <div v-for="(itemc) in scope.row.detailsVOList" v-bind:key="itemc">
              <div class="subDivCell">
                &nbsp;&nbsp;&nbsp;{{itemc.displaySubjectName}}
              </div>
            </div>
            <div class="subDivSummary">&nbsp;&nbsp;&nbsp;{{(dealBigMoney(scope.row.debitAmountTotal))}}</div>
          </template>
          <!--      借方金额-->
          <template slot-scope="scope" slot="debitAmount">
            <div v-for="(itemc) in scope.row.detailsVOList" v-bind:key="itemc">
              <div class="subDivCell" style="align-items: center;justify-content: center;">
                &nbsp;&nbsp;&nbsp;{{(itemc.debitAmount!=0?itemc.debitAmount:'') | amtFilters}}
              </div>
            </div>
            <div class="subDivSummary" style="align-items: center;justify-content: center;">{{(scope.row.debitAmountTotal)|amtFilters}}</div>
          </template>
          <!--      贷方金额-->
          <template slot-scope="scope" slot="creditAmount">
            <div v-for="(itemc) in scope.row.detailsVOList" v-bind:key="itemc">
              <div class="subDivCell" style="align-items: center;justify-content: center;">
                {{(itemc.creditAmount!=0?itemc.creditAmount:'')|amtFilters}}
              </div>
            </div>
            <div class="subDivSummary" style="align-items: center;justify-content: center;">{{(scope.row.creditAmountTotal)|amtFilters}}</div>
          </template>
          <!--      凭证号-->
          <template slot-scope="scope" slot="numberInfo">
            <div style="align-items: center;justify-content: center;">
              {{scope.row.word + '-' + scope.row.number}}
            </div>
          </template>
          <!--      附件单据-->
          <template slot-scope="scope" slot="receiptNum">
            <div style="align-items: center;justify-content: center;">
              {{scope.row.receiptNum>0?(scope.row.receiptNum + '张'):''}}
            </div>
          </template>
    <!--      自定义列-->


    <!--      自定义操作栏-->
    <!--      查看-->
          <template slot-scope="{row}" slot="menu">
            <el-button type="text"
                      icon="el-icon-view"
                      size="small"
                      plain
                      v-if="permission.financialVoucherDetail"
                      style="border: 0;background-color: transparent !important;"
                      @click.stop="viewHandle(row, 0)">查看
            </el-button>
    <!--        审核-->
            <el-button type="text"
                      icon="el-icon-turn-off"
                      size="small"
                      v-if="(row.auditStatus!=1)&&(row.isCheckout!=1)&&(row.redStatus!=1)&&(permission.financialVoucherApproval)"
                      plain
                      style="border: 0;background-color: transparent !important;"
                      @click.stop="approveHandle(row, 0)">审核
            </el-button>
            <el-button type="text"
                      icon="el-icon-turn-off"
                      size="small"
                      v-if="row.auditStatus==1&&(row.isCheckout!=1)&&(row.redStatus!=1)&&(row.generateStatus!=1)&&(permission.financialVoucherReApproval)"
                      plain
                      style="border: 0;background-color: transparent !important;"
                      @click.stop="backApproveHandle(row, 1)">反审核
            </el-button>
            <el-button type="text"
                      icon="el-icon-turn-off"
                      size="small"
                      v-if="(row.isCheckout==1||row.redStatus==1)&&(permission.financialVoucherApproval)"
                      disabled
                      plain
                      style="border: 0;background-color: transparent !important;">审核
            </el-button>
    <!--        编辑-->
            <el-button type="text"
                      icon="el-icon-edit"
                      size="small"
                      plain
                      v-if="(row.auditStatus!=1)&&(row.isCheckout!=1)&&(permission.financialVoucherEdit)"
                      style="border: 0;background-color: transparent !important;"
                      @click.stop="editHandle(row, 1)">编辑
            </el-button>
            <el-button type="text"
                      icon="el-icon-edit"
                      size="small"
                      plain
                      disabled
                      v-if="(row.auditStatus==1)||(row.isCheckout==1)&&(permission.financialVoucherEdit)"
                      style="border: 0;background-color: transparent !important;">编辑
            </el-button>
    <!--        删除-->
            <el-button type="text"
                      icon="el-icon-delete"
                      size="small"
                      plain
                      v-if="(row.auditStatus!=1)&&(row.isCheckout!=1)&&(permission.financialVoucherDelete)"
                      style="border: 0;background-color: transparent !important;"
                      @click.stop="delHandle(row, 0)">删除
            </el-button>
            <el-button type="text"
                      icon="el-icon-delete"
                      size="small"
                      plain
                      disabled
                      v-if="(row.auditStatus==1)||(row.isCheckout==1)&&(permission.financialVoucherDelete)"
                      style="border: 0;background-color: transparent !important;">删除
            </el-button>
    <!--        冲销-->
            <el-button type="text"
                      icon="el-icon-c-scale-to-original"
                      size="small"
                      plain
                      v-if="(row.redStatus!=1)&&(row.auditStatus==1)&&(row.isCheckout!=1)&&(permission.financialVoucherElimination)"
                      style="border: 0;background-color: transparent !important;"
                      @click.stop="reversalHandle(row, 0)">冲销
            </el-button>
            <el-button type="text"
                      icon="el-icon-c-scale-to-original"
                      size="small"
                      plain
                      disabled
                      v-if="(row.redStatus==1||row.auditStatus!=1)&&(permission.financialVoucherElimination)"
                      style="border: 0;background-color: transparent !important;">冲销
            </el-button>

          </template>
          <template slot="frontImg" slot-scope="{row}">
            <el-image  style="width: 30px; height: 50px" :src="row.frontImg" fit="cover" @click="handleClickPreview(row.frontImg)"></el-image>
          </template>
          <template slot="backImg" slot-scope="{row}">
            <el-image  style="width: 30px; height: 50px" :src="row.backImg" fit="cover" @click="handleClickPreview(row.backImg)"></el-image>
          </template>
          <template slot="status"  slot-scope="{row}">
            <el-tag v-if="row.isUse == '0'" size="medium" type="blue">未启用</el-tag>
            <el-tag v-if="row.isUse == '1'" size="medium" type="success">已启用</el-tag>
          </template>
        </avue-crud>
        <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
          <img width="100%" height="100%" :src="dialogImageUrl" alt="">
        </el-dialog>

        <el-dialog title="选择会计期间"
                  append-to-body
                  :visible.sync="selectPeroidShow"
                  width="600px">
          <el-form ref="form" label-width="80px">
            <el-form-item label="查询区间">
              <el-col :span="11">
                <el-select v-model="startPeroid" ref="startPeroidName" placeholder="请选择开始期间">
                  <el-option
                    v-for="item in peroidList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
              <el-col class="line" :span="2">-</el-col>
              <el-col :span="11">
                <el-select v-model="endPeroid" ref="endPeroidName" placeholder="请选择结束期间">
                  <el-option
                    v-for="item in peroidList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value">
                  </el-option>
                </el-select>
              </el-col>
            </el-form-item>
          </el-form>
          <span slot="footer"
                class="dialog-footer">
            <el-button @click="selectPeroidShow = false">取 消</el-button>
            <el-button type="primary"
                      @click="afterSelectPeroidHandle">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </basic-container>
</template>

<script>
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getList, getDetail, add, update, remove, processExport} from "@/api/supplier/finance/financialVoucherList";
import {mapGetters} from "vuex";
import {
  getAccountSetsList,
  getPeroidList,
  approveBatch,
  deleteBatch,
  sortAgain,
  reversalVoucher,
  reversalHandle
} from "@/api/supplier/finance/voucher";
var DIC = {
  // 余额方向
  auditStatus: [{
    label: '未审核',
    value: 0
  },{
    label: '已审核',
    value: 1
  }],
}
export default {
  data() {
    return {
      showContent: '收起',
      showDiv: false,
      selectVoucherPeroid: '',
      checkoutType: '',
      checkoutDesc: '',
      myApproveValue: true,
      autoApproveValue: 0,
      peroidStr: '',
      startPeroid: '',
      endPeroid: '',
      peroidList: [],
      selectPeroidShow: false,
      accountSetsValue: '',
      accountSetsList:[],//当前食堂所有的帐套
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      option: {
        height: 900,
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        addBtn:false,
        refreshBtn: false,
        searchBtn: true,
        selection: true,
        columnBtn: false,
        addRowBtn: false,
        align: 'center',
        menuWidth:100,
        menuAlign:'center',
        menuHeaderAlign:'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "日期",
            prop: "businessDate",
            type: "datetime",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: true,
            width: 100,
            maxlength:30,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
            searchSpan: 4,
          },
          {
            label: "凭证号",
            prop: "number",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: true,
            hide: true,
            width: 80,
            maxlength:30,
            searchSpan: 3,
          },
          {
            label: "凭证号",
            prop: "numberInfo",
            type: "input",
            width: 80,
            slot: true,
          },
          {
            label: "摘要",
            prop: "summary",
            width: 120,
            slot: true
          },
          {
            label: "科目",
            prop: "subjectId",
            type: "tree",
            search: true,
            hide: true,
            dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope',
            props: {
              label: "subjectName",
              value:"id"
            },
            searchSpan: 4,
          },
          {
            label: "科目",
            prop: "subjectName",
            type: "input",
            slot: true,
          },
          {
            label: "借方金额",
            prop: "debitAmount",
            type: "input",
            width: 100,
            slot: true,
          },
          {
            label: "贷方金额",
            prop: "creditAmount",
            type: "input",
            width: 100,
            slot: true,
          },
          {
            label: "附件单据",
            prop: "receiptNum",
            type: "input",
            width: 100,
            slot: true,
          },
          {
            label: "制单人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: true,
            width: 120,
            searchSpan: 3,
          },
          {
            label: "状态",
            prop: "auditStatus",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: true,
            width: 80,
            maxlength:30,
            dicData: DIC.auditStatus,
            searchSpan: 3,
          },
          {
            label: "审核人",
            prop: "auditUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            width: 120,
          },
        ]
      },
      data: [],
    };
  },
  filters: {
    amtFilters,
  },
  computed: {
    ...mapGetters(["userInfo", "permission"]),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    this.selectVoucherPeroid = this.$route.query.period;
    if(this.selectVoucherPeroid!=undefined && this.selectVoucherPeroid != '') {
      this.startPeroid = this.selectVoucherPeroid;
      this.endPeroid = this.selectVoucherPeroid;
      this.peroidStr = this.startPeroid + '至' + this.endPeroid;
    }
    this.checkoutType = this.$route.query.checkoutType;
    this.checkoutDesc = this.$route.query.checkoutDesc;
    this.accountSetsValue = this.$route.query.accountSetsId;
    console.log("checkoutType:"+this.checkoutType)
    console.log("checkoutDesc:"+this.checkoutDesc)
    this.loading = true;
    if(this.accountSetsValue != undefined && this.accountSetsValue == '')
      this.onLoad(this.page)

    getAccountSetsList()
      .then(res => {
        this.accountSetsList = res.data.data.records;
        if(this.accountSetsList!=null && this.accountSetsList.length == 1) {
          if(this.accountSetsValue == undefined || this.accountSetsValue == '')
            this.accountSetsValue = this.accountSetsList[0].id;
        }
      }).catch(err=>{}).finally(fin=>{this.loading = false;});
  },
  methods: {
    backApproveHandle(row, type) {
      this.approveHandle(row, type);
    },
    addVoucher() {
      this.$router.push({
        path: "/financialVoucherAdd",
        query: {id:'', accountSetsId: this.accountSetsValue},
        meta : { $keepAlive : true },
      });
    },
    viewHandle(row, type) {
      this.$router.push({
        path: "/financialVoucherView",
        query: {id:row.id, accountSetsId: this.accountSetsValue},
        meta : { $keepAlive : true },
      });
    },
    approveHandle(row, type) {
      if(row.isCheckout==1) {
        this.$message.warning("该凭证已结帐,不能再"+(type==0?'审核':'反审核'));
        return;
      }
      if (row.auditStatus==1 && type==0) {
        this.$message.warning("该凭证已审核,不能再审核");
        return;
      }
      if (row.auditStatus!=1 && type==1) {
        this.$message.warning("该凭证未审核,不能反审核");
        return;
      }
      //检测是否选择了只能自己批量审核的数据
      this.$confirm("确定将选择数据"+(type==0?'审核':'反审核')+"?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return approveBatch(row.id,type);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "审核成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    editHandle(row, type) {
      this.$router.push({
        path: "/financialVoucherEdit",
        query: {id:row.id, accountSetsId: this.accountSetsValue}
      });
    },
    delHandle(row, type) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "删除成功!"
          });
        });
    },
    reversalHandle(row, type) {
      if(row.isCheckout==1) {
        this.$message.warning("该凭证已结帐,不能再冲销");
        return;
      }
      if (row.auditStatus!=1) {
        this.$message.warning("该凭证未审核,不能冲销");
        return;
      }
      this.$confirm("确定将选择凭证反冲销?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return reversalHandle(row.id,type);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "冲销成功!"
          });
        });
    },
    // 金额大写
    /** 数字金额大写转换(可以处理整数,小数,负数) */
    dealBigMoney(n)
    {
      var fraction = ['角', '分'];
      var digit = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
      var unit = [ ['元', '万', '亿'], ['', '拾', '佰', '仟']  ];
      var head = n < 0? '欠': '';
      n = Math.abs(n);

      var s = '';

      for (var i = 0; i < fraction.length; i++)
      {
        s += (digit[Math.floor(n * 10 * Math.pow(10, i)) % 10] + fraction[i]).replace(/零./, '');
      }
      s = s || '整';
      n = Math.floor(n);

      for (i = 0; i < unit[0].length && n > 0; i++)
      {
        var p = '';
        for (var j = 0; j < unit[1].length && n > 0; j++)
        {
          p = digit[n % 10] + unit[1][j] + p;
          n = Math.floor(n / 10);
        }
        s = p.replace(/(零.)*零$/, '').replace(/^$/, '零')  + unit[0][i] + s;
      }
      return head + s.replace(/(零.)*零元/, '元').replace(/(零.)+/g, '零').replace(/^整$/, '零元整');
    } ,
    // 行合并
    spanMethod({ row, column, rowIndex, columnIndex }) {
      if(column.property==[this.key]){
        const _row=this.spanArr[rowIndex];
        const _col=_row>0?1:0;
        return {
          rowspan:_row,
          colspan:_col
        }
      }

    },
    // 批量审核
    btnBatchApproveHandle() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要审核的凭证");
        return;
      }
      for(var i=0;i<this.selectionList.length;i++){
        if(this.selectionList[i].auditStatus != 0){
          this.$message.warning("选中的第"+(i+1)+"条记录,不是待审核状态!");
            return;
        }
      }

      //检测是否选择了只能自己批量审核的数据
      this.$confirm("确定将选择数据审核?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return approveBatch(this.ids, 0);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "批量审核成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 批量删除
    btnBatchDeleteHandle() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择要删除的凭证");
        return;
      }

      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return deleteBatch(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "批量删除成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 整理断号
    btnSortAgainHandle() {
      if(this.startPeroid=='' || this.endPeroid == '') {
        this.$message.warning("请选择会计期间");
        return;
      }else if(this.startPeroid != this.endPeroid) {
        this.$message.warning("请选择会计期间");
        return;
      }
      this.$confirm("确定整理断号?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return sortAgain(this.startPeroid,this.endPeroid);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "整理断号成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    // 刷新
    refreshData() {
      this.page.currentPage = 1
      var param = this.$refs.crud.searchForm;
      var params = param;
      this.onLoad(this.page, params);
    },
    // 选择会计期间范围
    selectPeroidHandle() {
      getPeroidList(1, 10000, Object.assign({accountSetsId: this.accountSetsValue}, this.query))
        .then(res => {
          var list = res.data.data.records;
          this.peroidList = [];
          if(list != null && list.length >0) {
            for (var i=0;i<list.length;i++) {
              const item = list[i];
              const p = item.voucherPeriod + '';
              const str = p.substring(0,4) + '年第' + p.substring(4,6) + '期';
              const it = {label:str,value:p};
              this.peroidList.push(it);
            }
          }
        });

      this.selectPeroidShow = true;
    },
    // 确认会计期间
    afterSelectPeroidHandle() {
      this.peroidStr = "";
      const s = this.$refs.startPeroidName.selectedLabel;
      const e = this.$refs.endPeroidName.selectedLabel;

      if(s != '' && e != '') {
        if(this.startPeroid > this.endPeroid) {
          this.$message.warning("开始期间不能大于结束期间");
          return;
        }
        this.peroidStr = s + '至' + e;
        this.selectPeroidShow = false;
      }else {
        if(s == '') {
          this.$message.warning("请选择查询的开始期间");
        }
        if(e == '') {
          this.$message.warning("请选择查询的结束期间");
        }
      }
    },

    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.startPeroid = '';
      this.endPeroid = '';
      this.peroidStr = '';
      this.myApproveValue = true;
      this.autoApproveValue = 0;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      // this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      params.startPeroid = this.startPeroid;
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.checkoutType = this.checkoutType;
      params.checkoutDesc = this.checkoutDesc;
      params.autoApproveValue = this.autoApproveValue;
      if(this.myApproveValue ==true){
        params.myApproveValue = 1;
      } else {
        params.myApproveValue = 0;
      }
      // params.myApproveValue = this.myApproveValue;
      params.autoApproveValue = this.autoApproveValue;

      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleClickPreview: function(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    handleExport( params = {}){
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }

      params.startPeroid = this.startPeroid;
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.checkoutType = this.checkoutType;
      params.checkoutDesc = this.checkoutDesc;
      params.autoApproveValue = this.autoApproveValue;
      if(this.myApproveValue ==true){
        params.myApproveValue = 1;
      } else {
        params.myApproveValue = 0;
      }
      // params.myApproveValue = this.myApproveValue;
      params.autoApproveValue = this.autoApproveValue;
      // params.current = this.page.currentPage;
      // params.size = this.page.pageSize;
      console.log(params.current+"     "+params.size)
      processExport(Object.assign(params, this.query)).then(res => {
        if (!res.data) {
          return;
        }
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel",
        }); // 构造一个blob对象来处理数据，并设置文件类型
        const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
        const a = document.createElement("a"); //创建a标签
        a.style.display = "none";
        a.href = href; // 指定下载链接
        let fileName = res.headers["content-disposition"];
        fileName = fileName.split("=")[1];
        a.download = decodeURIComponent(fileName); //指定下载文件名
        a.click(); //触发下载
        URL.revokeObjectURL(a.href); //释放URL对象
      });
    },
    exportTable(){
      let that = this;
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      this.loading = true;

      var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
      xhr.open(
        "get",
        "/api/service/rabbit-supplier/finance/financialVoucher/downloadForVoucher?startPeroid="+this.startPeroid+"&endPeroid="+this.endPeroid+"&accountSetsId="+this.accountSetsValue+"&account="+this.userInfo.account,
        true
      );
      xhr.responseType = "blob";
      xhr.onload = function() {
        that.loading = false;
        const url = window.URL.createObjectURL(this.response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "记账凭证.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      xhr.send();
    },
    clickShow(){
      this.showDiv = !this.showDiv
      this.option.searchShow = false
    },
    billMonthly(to={}){
      let tmpMyApproveValue = 0;
      if(this.myApproveValue ==true){
        tmpMyApproveValue = 1;
      } else {
        tmpMyApproveValue = 0;
      }
      var param = {
          checkoutType : to.query.checkoutType,
          checkoutDesc : to.query.checkoutDesc,
          startPeroid : to.query.period,
          endPeroid : to.query.period,
          myApproveValue : tmpMyApproveValue,
          autoApproveValue : this.autoApproveValue
        };
        this.peroidStr = this.startPeroid + '至' + this.endPeroid;
        // this.$slot.subjectName = to.query.checkoutType + ' ' + to.query.checkoutDesc;
        this.loading = true;
        getList(this.page.currentPage, this.page.pageSize, param).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
    },
    changeApproveValue(){
      this.onLoad(this.page);
    }
  },
  watch: {
    // 从按月结账跳转过来才执行
    '$route' (to, from) {
      if (from.path == '/finance/billedMonthly') {
        this.billMonthly(to)
      }
    }
  },
  activated(){
    if(Object.keys(this.$route.query).length > 0){
      this.data = []
      if(this.$route.query.checkoutType==1001 && this.$route.query.checkoutDesc=='计提折旧'
        || this.$route.query.checkoutType==2001 && this.$route.query.checkoutDesc=='计提职工薪酬'
        || this.$route.query.checkoutType==3001 && this.$route.query.checkoutDesc=='结转损益' ){
        this.billMonthly(this.$route)
      }
    }
  },
};
</script>

<style>
  .subDivCell {
    height: 45px;
    width: 120%;
    margin-left: -10px;
    display: flex;
    flex-direction: row;
    /*text-align: center;*/
    align-items: center;
    /*justify-content: center;*/
    border-bottom: 1px solid lightgray;
    /*background: #0b89f5;*/
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
 .subDivSummary {
   height: 35px;
   width: 120%;
   margin-left: -10px;
   display: flex;
   flex-direction: row;
   /*text-align: center;*/
   align-items: center;
   /*justify-content: center;*/
   font-weight: bolder;
   font-size: 14px;
   /*background: #00deff;*/
 }
 .voucherListApproval .avue-crud__menu{
   display: none;
 }
</style>

<style  lang="less" scoped>
  /deep/ .table-form .el-form-item__label{
      display: none;
  }
  /deep/ .table-form .el-form-item__content{
    margin-left: 0 !important;
  }
</style>
