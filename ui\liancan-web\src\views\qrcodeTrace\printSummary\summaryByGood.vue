<template>
    <basicContainer>
        <avue-crud
            ref="crud"
            v-model="form"
            :option="option"
            :table-loading="loading"
            :data="data"
            :page="page"
            :before-open="beforeOpen"
            @search-change="searchChange"
            @search-reset="searchReset"
            @selection-change="selectionChange"
            @current-change="currentChange"
            @size-change="sizeChange"
            @on-load="onLoad">
            <template slot="menuLeft">
                <el-button
                    class="filter-item"
                    size="small"
                    type="warning"
                    icon="el-icon-download"
                    @click="handleDownload">导出</el-button>
            </template>
        </avue-crud>
        <canvas ref="qrcodeCanvas" />
    </basicContainer>
</template>

<script>
import QRCode from "qrcode";
import {
    getListSummaryByGood as getList,
    getDetailSummaryByGood as getDetail,
    exportReportSummaryByGood as exportReport
} from "@/api/qrcodeTrace/qrcodeTracePrint";
import {
    mapGetters
} from "vuex";
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: false,
            data: [],
            selectionList: [],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            option: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                menu: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: true,
                column: [
                    {
                        label: "统计时段",
                        prop: "queryDateRange",
                        type: "input",
                    },
                    {//4
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        hide:true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "打印人员",
                        prop: "optUserName",
                        type: "input",
                        search: true,
                    },
                    {
                        label: "开始日期",
                        prop: "startDate",
                        type: "date",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                        search: true,searchSpan: 5,
                        hide: true,
                    },
                    {
                        label: "结束日期",
                        prop: "endDate",
                        type: "date",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                        search: true,searchSpan: 5,
                        hide: true
                    },

                    {
                        label: "商品子项编码",
                        prop: "code",
                        type: "input",
                        search: true,
                        display:false,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        search: true,
                        display:false,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                    {//5
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "打印标签数量",
                        prop: "point",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                    },
                ]
            },
        }
    },
    computed: {
        ...mapGetters(["permission"]),
        permissionList() {
            return {
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    methods: {
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            });
        },
        beforeOpen(done, type) {
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        handleDownload: function() {
            let loading;
            this.$confirm("确定导出数据?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    loading = this.$loading({
                        lock: true,
                        text: '正在导出数据，请稍后',
                        spinner: 'el-icon-loading',
                        background: 'rgba(0, 0, 0, 0.7)'
                    });
                    return exportReport(this.page.currentPage, this.page.pageSize, this.query);
                })
                .then((res) => {
                    loading.close();
                    const blob = new Blob([res.data]);
                    const fileName = '添加剂台账.xlsx';
                    const linkNode = document.createElement('a');
                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();
                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
    }
}
</script>

<style scoped>

</style>
