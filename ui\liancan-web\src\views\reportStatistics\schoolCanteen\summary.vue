<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="openDeptSummaryDateils(row)">详情
        </el-button>
      </template>
    </avue-crud>
    <el-dialog :title="`部门汇总`" :visible.sync="managementVisible" :append-to-body="true" @close="managementVisible = false" width="70%">
      <el-row>
        <div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              营业网点：{{this.businessOutletsName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              计划名称：{{this.title}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              计划状态：{{this.isPublish}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              缴费开始时间：{{this.payStartTime}}
            </div>
            <div style="width:21%;margin-bottom: 20px;margin-top:20px;float: left;">
              缴费结束时间：{{this.payEndTime}}
            </div>
            <div style="width:19%;margin-bottom: 20px;margin-top:20px;float: left;">
              供餐开始时间：{{this.serveFoodStartDate}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              供餐结束时间：{{this.serveFoodEndDate}}
            </div>
          </div>
        </div>
      </el-row>
      <avue-crud :option="deptSummaryOption"
                 :table-loading="deptSummaryLoading"
                 :data="deptSummaryData"
                 :page="deptSummaryPage"
                 v-model="deptSummaryForm"
                 ref="deptSummaryForm"
                 @search-change="deptSummarySearchChange"
                 @search-reset="deptSummarySearchReset"
                 @selection-change="deptSummarySelectionChange"
                 @current-change="deptSummaryCurrentChange"
                 @size-change="deptSummarySizeChange"
                 @on-load="onLoadDeptSummary">
        <template slot-scope="{row}" slot="menu">
          <el-button type="text"
                     icon="el-icon-view"
                     size="small"
                     plain
                     style="border: 0;background-color: transparent !important;"
                     @click.stop="openDeptUserDetails(row)">详情
          </el-button>
        </template>
        <template slot="menuLeft">
          <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportDeptSummaryData">导出</el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <el-dialog :title="`班级详情`" :visible.sync="deptDetailsVisible" :append-to-body="true" @close="deptDetailsVisible = false" width="70%">
      <avue-crud :option="deptDetailsOption"
                 :table-loading="deptDetailsLoading"
                 :data="deptDetailsData"
                 :page="deptDetailsPage"
                 v-model="deptDetailsForm"
                 ref="deptDetailsForm"
                 @search-change="deptDetailsSearchChange"
                 @search-reset="deptDetailsSearchReset"
                 @selection-change="deptDetailsSelectionChange"
                 @current-change="deptDetailsCurrentChange"
                 @size-change="deptDetailsSizeChange"
                 @on-load="onLoadDeptDetails">
        <template slot="menuLeft">
          <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportDeptUserDetailsData">导出</el-button>
        </template>
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getPayMealsList,getDeptSummaryList,getDeptUserDetailsList,exportDeptSummaryData,exportDeptUserDetailsData} from "@/api/businessManage/studentUniteOrder";
  import {mapGetters} from "vuex";

  const DIC = {
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
    meal: [
      {
        label: '否',
        value: "0"
      },
      {
        label: '是',
        value: "1"
      }
    ],
    way: [
      {
        label: '未订餐',
        value: "0"
      },
      {
        label: '系统续订',
        value: "1"
      },
      {
        label: '主动订餐',
        value: "2"
      }
    ],
  }
  export default {
    data() {
      return {
        form: {},
        query: {},
        deptDetailsForm:{},
        loading: true,
        deptSummaryLoading:true,
        deptDetailsLoading:true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptSummaryPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptDetailsPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        deptSummaryData:[],
        deptDetailsData:[],
        searchFrom:{},
        deptSummaryForm:{},
        searchForm:{},
        searchDeptUserForm:{},
        dialogVisible:false,
        managementVisible:false,
        deptDetailsVisible:false,
        dialogImageUrl:undefined,
        title:undefined,
        businessOutletsName:undefined,
        payStartTime:undefined,
        payEndTime:undefined,
        serveFoodStartDate:undefined,
        serveFoodEndDate:undefined,
        isPublish:undefined,
        planId:undefined,
        deptId:undefined,
        option: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          searchSpan:100,
          column: [
            {
              label: "统缴餐计划",
              prop: "title",
              type: "input",
              search:true,
            },
            {
              label: "营业网点",
              prop: "businessOutletsId",
              type: "select",
              span: 24,
              rules: [{
                required: true,
                message: "请输入营业网点",
                trigger: "blur"
              }],
              hide:true,
              search:true,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              props: {
                label: "name",
                value: "id"
              },
            },
            {
              label: "营业网点名称",
              prop: "businessOutletsName",
              type: "input",
            },
            {
              label: '缴费开始时间',
              prop: 'payStartTime',
              type: 'input',
            },
            {
              label: '缴费结束时间',
              prop: 'payEndTime',
              type: 'input',
            },
            {
              label: '供餐开始时间',
              prop: 'serveFoodStartDate',
              type: 'input',
            },
            {
              label: '供餐结束时间',
              prop: 'serveFoodEndDate',
              type: 'input',
            },
            {
              label: '发布状态',
              prop: 'isPublish',
              type: 'input',
            },
            {
              label: '发布状态',
              prop: 'publish',
              type: 'select',
              hide:true,
              search:true,
              dicData: [
                {
                  label: "已发布",
                  value: "1"
                },
                {
                  label: "已结束",
                  value: "2"
                }
              ],
            },
            {
              label: '计划订餐人数',
              prop: 'planOrderNumber',
              type: 'input',
            },
            {
              label: '实际订餐人数',
              prop: 'actualOrderNumber',
              type: 'input',
            },
   /*         {
              label: '未订餐',
              prop: 'noOrderCount',
              type: 'input',
            },*/
          ]
        },
        deptSummaryOption:{
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          printBtn:true,
          searchSpan:100,
          column: [
/*            {
              label: "未订餐人数",
              prop: "noDiningNumber",
              type: "select",
              search:true,
              hide:true,
              dicData: [
                {
                  label: "等于0",
                  value: "1"
                },
                {
                  label: "大于0",
                  value: "2"
                }
              ],
            },*/
            {
              label: "计划名称",
              prop: "planName",
              type: "input",
            },
            {
              label: "所属部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                label: "title",
                value: "id"
              },
              search:true,
              /*  hide:true,*/
              rules: [{
                required: true,
                message: "请输入所属租户",
                trigger: "click"
              }]
            },
       /*     {
              label: "部门",
              prop: "deptName",
              type: "input",
            },*/
            {
              label: '部门人数',
              prop: 'deptNumber',
              type: 'input',
            },
            {
              label: '计划订餐人数',
              prop: 'planOrderNumber',
              type: 'input',
            },
            {
              label: '实际订餐人数',
              prop: 'actualOrderNumber',
              type: 'input',
            },
   /*         {
              label: '未订餐人数',
              prop: 'noOrderCount',
              type: 'input',
            },*/
          ]
        },
        deptDetailsOption:{
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          menu:false,
          printBtn:true,
          column: [
        /*    {
              label: "部门",
              prop: "deptName",
              type: "input",
            },*/
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                label: "title",
                value: "id"
              },
              editDisplay: false,
              viewDisplay: false,
              /*slot:true,*/
              rules: [{
                required: true,
                message: "请输入部门",
                trigger: "click"
              }]
            },
            {
              label: '姓名',
              prop: 'userName',
              type: 'input',
              search:true,
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
            },
            {
              label: '学号/工号',
              prop: 'studentJobNo',
              type: 'input',
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              search:true,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: '已订餐',
              prop: 'orderStatus',
              type: 'select',
              dicData: DIC.meal,
              search:true,
            },
            {
              label: '订餐途径',
              prop: 'orderWay',
              type: 'input',
              dicData: DIC.way,
            },
            {
              label: '订餐时间',
              prop: 'createTime',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:s',
            },
            {
              label: '订餐操作人',
              prop: 'orderOperatorName',
              type: 'input',
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          /*        addBtn: this.vaildData(this.permission.work_personnel_add, false),
                  viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                  delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                  editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchFrom = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getPayMealsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      deptSummarySearchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoadDeptSummary(this.deptSummaryPage);
      },
      deptSummarySearchChange(params, done) {
        this.query = params;
        this.searchForm = params;
        this.deptSummaryPage.currentPage = 1
        this.onLoadDeptSummary(this.deptSummaryPage, params);
        done();
      },
      deptSummarySelectionChange(list) {
        this.selectionList = list;
      },
      deptSummarySelectionClear() {
        this.selectionList = [];
        this.$refs.deptSummaryForm.toggleSelection();
      },
      deptSummaryCurrentChange(currentPage){
        this.deptSummaryPage.currentPage = currentPage;
      },
      deptSummarySizeChange(pageSize){
        this.deptSummaryPage.pageSize = pageSize;
      },
      onLoadDeptSummary(page, params = {}) {
        this.deptSummaryLoading = true;
        params.planId = this.planId;
        getDeptSummaryList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptSummaryPage.total = data.total;
          this.deptSummaryData = data.records;
          this.deptSummaryLoading = false;
          this.deptSummarySelectionClear();
        });
      },


      deptDetailsSearchReset() {
        this.query = {};
        this.searchDeptUserForm = {};
        this.searchDeptUserForm.planId = this.planId;
        this.searchDeptUserForm.deptId = this.deptId;
        this.onLoadDeptDetails(this.deptDetailsPage);
      },
      deptDetailsSearchChange(params, done) {
        this.query = params;
        this.searchDeptUserForm = params;
        this.deptDetailsPage.currentPage = 1
        this.onLoadDeptDetails(this.deptDetailsPage, params);
        done();
      },
      deptDetailsSelectionChange(list) {
        this.selectionList = list;
      },
      deptDetailsSelectionClear() {
        this.selectionList = [];
        this.$refs.deptDetailsForm.toggleSelection();
      },
      deptDetailsCurrentChange(currentPage){
        this.deptDetailsPage.currentPage = currentPage;
      },
      deptDetailsSizeChange(pageSize){
        this.deptDetailsPage.pageSize = pageSize;
      },
      onLoadDeptDetails(page, params = {}) {
        this.deptDetailsLoading = true;
        params.planId = this.planId;
        params.deptId = this.deptId;
        getDeptUserDetailsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptDetailsPage.total = data.total;
          this.deptDetailsData = data.records;
          this.deptDetailsLoading = false;
          this.deptDetailsSelectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
/*      exportBiddingData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportBiddingData2(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '中标情况统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },*/
      openDeptSummaryDateils(row){
          this.title = row.title;
          this.businessOutletsName = row.businessOutletsName;
          this.payStartTime =  row.payStartTime;
          this.payEndTime =  row.payEndTime;
          this.serveFoodStartDate = row.serveFoodStartDate;
          this.serveFoodEndDate = row.serveFoodEndDate;
          this.isPublish = row.isPublish;
          this.planId = row.id;
          this.searchForm.planId = this.planId;
          this.deptSummaryPage.currentPage = 1;
          this.onLoadDeptSummary(this.deptSummaryPage)
          this.managementVisible = true;
      },
      openDeptUserDetails(row){
        this.deptId = row.deptId;
        this.planId = row.planId;
        this.searchDeptUserForm.deptId = this.deptId;
        this.searchDeptUserForm.planId = this.planId;
        this.deptDetailsPage.currentPage = 1;
        this.onLoadDeptDetails(this.deptDetailsPage);
        this.deptDetailsVisible = true;
      },
      exportDeptSummaryData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出下方明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDeptSummaryData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '部门用餐信息汇总报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportDeptUserDetailsData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出下方明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDeptUserDetailsData(this.searchDeptUserForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '班级人员订餐信息报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
