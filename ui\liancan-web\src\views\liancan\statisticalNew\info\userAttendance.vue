<template>
  <basic-container>
    <avue-crud :option="attedancenTableOption"
               :table-loading="tableLoading"
               :data="schedulingData"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="schedulingForm"
               ref="schedulingForm"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.monthly_summary_export" class="filter-item" size="small" type="primary" icon="el-icon-upload" @click="handleExcel()">导出报表</el-button>
      </template>
    </avue-crud>


  </basic-container>
</template>
<script>
import {
  loadAttendancesMonthlySummary2,
  queryStatisticeSummaryData
} from "@/api/attendance/index";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      messageForm: {
        recipient: []
      },
      searchForm:{},
      page: {
        total: 0, // 总页数
        currentPage: 1, // 当前页数
        pageSize: 10, // 每页显示多少条,
        isAsc: false //是否倒序
      },
      schedulingForm: {

      },
      workDay: [],
      lockStatus: [],
      schedulingData: [],
      dictsData: [], // 字典目录数据集合
      dictItemsData: [], // 字典项数据集合
      dictName: undefined,
      dictType: undefined,
      dictId: undefined,
      schedulingDay1: [],
      deptIds:undefined,
      begindate:undefined,
      enddate:undefined,
      nickName:undefined,
      companyId:undefined,
      dictItemForm: {
        dictId: undefined,
        type: undefined,
        label: undefined,
        value: undefined,
        description: undefined,
        sort: 0
      },
      attedancenTableOption: {
        searchMenuSpan:6,
        menu:false,
        index: true,
        indexLabel:"序号",
        border: true,
        align: 'left',
        stripe: true,
        viewBtn: false,
        menuAlign: 'center',
        menuWidth: 250,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        column: [
          {
            label: "所属食堂",
            prop: "companyId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            fixed:true,
            search: false,
          },
          {
            label: '姓名',
            prop: 'name',
            width: 125,
            align: 'center',
            search:true,
            fixed:true
          },
          {
            label: "开始时间",
            prop: "begintime",
            type: "datetime",
            span: 8,
            display: false,
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          {
            label: "结束时间",
            prop: "endtime",
            type: "datetime",
            span: 8,
            display: false,
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          /*       {
                   label: "部门",
                   prop: "deptId",
                   type: "tree",
                   dicUrl: "/api/service/rabbit-canteen/DeptSetting/tree",
                   props: {
                     label: "title",
                     value: "id"
                   },
                   /!* multiple:true,*!/
                   search:true,
                   rules: [{
                     required: true,
                     message: "请输入所属部门",
                     trigger: "click"
                   }]
                 },*/
          {
            label: '职务',
            prop: 'positioname',
            width: 140,
            align: 'center',
          },
          {
            label: '工作天数',
            align: 'center',
            children: [
              {
                label: '出勤次数',
                prop: 'attendanceDays',
                align: 'center',
              },
              {
                label: '旷工次数',
                prop: 'absenteeismNumber',
                align: 'center',

              }
            ]
          },
          {
            label: '迟到',
            align: 'center',
            children: [{
              label: '次数',
              prop: 'beLateNumber',
              align: 'center',

            },  {
              label: '时长',
              prop: 'beLateDuration',
              align: 'center',

            },
            ]
          },
          {
            label: '早退',
            align: 'center',
            children: [
              {
                label: '次数',
                prop: 'beLeaveEarlyNumber',
                align: 'center',
              },
              {
                label: '时长',
                prop: 'beLeaveEarlyDuration',
                align: 'center',

              },
            ]
          },
          {
            label: '请假次数',
            prop: 'leaveCount',
            width: 100,
            align: 'center',
          },
          {
            label: '请假（小时）',
            align: 'center',
            children: [{
              label: '病假',
              prop: 'sickLeave',
              align: 'center',

            }, {
              label: '事假',
              prop: 'holiday',
              align: 'center',

            },
              {
                label: '婚假',
                prop: 'marriage',
                align: 'center',

              },
              {
                label: '产假',
                prop: 'maternityLeave',
                align: 'center',
              },
              {
                label: '休假',
                prop: 'paidLeave',
                align: 'center',

              }
            ]
          },
          {
            label: '缺卡',
            align: 'center',
            children: [
              {
                label: '上班次数',
                prop: 'startLackCardNumber',
                align: 'center',

              },
              {
                label: '下班次数',
                prop: 'stopLackCardNumber',
                align: 'center',

              }
            ]
          },
        ],
      },
      tableLoading: false,
      dictItemTableLoading: false,
      dictItemDialogVisible: false
    }
  },
  created() {
    if (this.userInfo.userType === 'canteen'){
      this.attedancenTableOption.column[0].search = false;
      this.attedancenTableOption.column[0].hide = true;
    }
  },
  computed: {
    ...mapGetters(["permission", "access_token", "userInfo"]),
    permissionList() {
      return {
        /*  addBtn: this.hasPermission(this.permissions.sys_dict_add),
          delBtn: this.hasPermission(this.permissions.sys_dict_del),
          editBtn: this.hasPermission(this.permissions.sys_dict_edit)*/
      };
    }
  },
  methods: {
    searchReset() {
      this.query = {};
      this.deptIds = undefined;
      this.begindate = undefined;
      this.enddate = undefined;
      this.nickName = undefined;
      this.companyId = undefined;
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      // this.deptIds = params.deptId;
      this.begindate = params.begintime;
      this.enddate = params.endtime;
      this.nickName = params.name;
      // this.companyId = params.companyId;
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.schedulingForm.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.tableLoading = true;
      if(this.schoolId != null && this.schoolId != '') {
        loadAttendancesMonthlySummary2(page.currentPage, page.pageSize, Object.assign(params, this.query),this.schoolId,this.begindate,this.enddate,this.nickName).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.schedulingData = data.records;
          this.tableLoading = false;
          this.selectionClear();
        });
      }
    },
    /**
     * 导出报表
     */
    handleExcel: function(params) {
      const loading = this.$loading({
        lock: true,
        text: '正在导出考勤数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      queryStatisticeSummaryData(Object.assign({
        begindate :this.begindate,
        enddate :this.enddate,
        deptIds: this.deptIds,
        nickName:this.nickName,
        companyId:this.companyId
      })).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '考勤报表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click();  //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
  }
}
</script>

<style>
</style>
