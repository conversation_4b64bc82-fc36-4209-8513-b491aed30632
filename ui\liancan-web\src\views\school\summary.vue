<template>
  <basic-container>
    <div>
      <div class="agncy-list">
        <div class="mess-header">
          <div
            :class="{acitve:activeIdx==index}"
            v-for="(item,index) in messList"
            :key="index"
            @click="menuClick(index, activeIdxs)"
          >
            {{item}}
          </div>
        </div>
        <div class="mess-headers">
          <div
            :class="{acitve:activeIdxs==index}"
            v-for="(item,index) in messLists"
            :key="index"
            @click="menuClicks(activeIdx, index)"
          >
            {{item}}
          </div>
        </div>
        <div class="mess-content" v-if="this.activeIdxs == 0">
          <avue-crud :option="allWarningOption"
                     :table-loading="allWarningListloading"
                     :data="allWarningListData"
                     :page="allWarningListPage"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="allWarningListForm"
                     ref="allWarningListForm"
                     @search-change="searchChangeAllWarning"
                     @search-reset="searchResetAllWarning"
                     @selection-change="selectionChangeAllWarning"
                     @current-change="currentChangeAllWarning"
                     @size-change="sizeChangeAllWarning"
                     @on-load="onLoadAllWarning">
          </avue-crud>
        </div>
        <div class="mess-content" v-if="this.activeIdxs == 1">
          <avue-crud :option="option"
                     :table-loading="loading"
                     :data="data"
                     :page="page"
                     v-model="form"
                     ref="crud"
                     @search-change="searchChange"
                     @search-reset="searchReset"
                     @selection-change="selectionChange"
                     @current-change="currentChange"
                     @size-change="sizeChange"
                     @on-load="onLoad">
            <!--<template slot="menu" slot-scope="scope">
              <el-button v-if="scope.row.auditStatus == '0'" type="text" size="small" icon="el-icon-edit" @click="check(scope.row)">去审核
              </el-button>
            </template>-->
            <template slot="menu" slot-scope="{row}">
              <a style="color: #409EFF;" @click="check(row)">查看详情</a>
            </template>
          </avue-crud>
        </div>
        <!--  <todoList title="学校实名认证" :todoList="schoolIndexList" @allClick="lookAll($event,'项目开标')" @detail="lookDetail" subTitle="查看详情"/>-->
      </div>
    </div>
    <el-dialog title="审核" :visible.sync="isShow" :append-to-body="true" @close="closeForm">
      <avue-form ref="registerForm" :option="registerFormOption" v-model="registerForm">
        <template slot="menuForm">
          <el-button type="success" v-if="this.registerForm.auditStatus == '0'" @click="allow()">通过</el-button>
          <el-button type="danger" v-if="this.registerForm.auditStatus == '0'" @click="reject()">驳回</el-button>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog :title="`关于${this.warningName1}涉嫌${this.warningName2}的函询`" :visible.sync="warningRecordVisible2" :append-to-body="true" @close="warningRecordFormClose" width="70%">
      <el-row>
        <el-col :span="25">
          <basic-container>
            <div style="font-size: 20px;">{{this.warningName1}}相关负责人:</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">经我单位监管，贵单位可能存在以下违规行为，特此函询，请据实说明情况。</div>
            <avue-form ref="warningRecordForm2" :option="warningRecordOption2" v-model="warningRecordForm2">
            </avue-form>
          </basic-container>
        </el-col>
      </el-row>
      <el-row>
        <span style="font-size: 20px;float: right">{{this.jiweiDeptName}}<br/>{{this.jiweiDate}}<br/>函询发送时间：{{this.inquiryTime}}</span>
        <br/>
        <span style="font-size: 15px;float: left;margin-top: 20px;">如有疑问可联系负责人：{{this.jiweiAdminName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </el-row>
      <br/>
      <br/>
      <br/>
      <br/>
      <br/>
      <el-row style="border-top:1px solid #000;">
        <el-col :span="19">
          <span style="font-size: 20px;float: left">学校回复：{{this.replyContent}}</span>
          <br/>
          <span style="font-size: 20px;margin-top: 10px;"><br/>回复时间：{{this.replyDate}} &nbsp;&nbsp;&nbsp;回复人：{{this.replyUserName}}</span>
        </el-col>
        <el-col :span="5" v-if="this.archives == '3'">
          <span style="font-size: 30px;float: right">已结案</span>
          <br/>
          <span style="font-size: 20px;float: right;margin-top: 10px;">归档时间:{{this.archivistDate}}</span>
          <br/>
          <span style="font-size: 20px;float: right;margin-top: 10px;">归档人：{{this.archivistName}}</span>
        </el-col>
        <el-button v-if="this.archives == '2'" style="float: right;" type="primary" @click="closingFiling">结案归档</el-button>
      </el-row>
    </el-dialog>
    <el-dialog :title="`关于${this.warningName1}涉嫌${this.warningName2}的函询`" :visible.sync="warningRecordVisible1" :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <div style="font-size: 20px;">{{this.warningName1}}相关负责人:</div>
      <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">经我单位监管，贵单位可能存在以下违规行为，特此函询，请据实说明情况。</div>
      <avue-form ref="warningRecordForm1" :option="warningRecordOption1" v-model="warningRecordForm1">
        <template slot-scope="scope" slot="menuForm">
          <el-button @click="pushInquiry" type="primary">发送</el-button>
          <el-button @click=" warningRecordVisible1 = false">取消</el-button>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <span style="font-size: 20px;">{{this.jiweiDeptName}}</span>
              <br/>
        <span style="font-size: 20px;">{{this.jiweiDate}}</span>
                 <br/>
        <span style="font-size: 15px;">如有疑问可联系负责人：{{this.jiweiAdminName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </span>
    </el-dialog>
    <el-dialog :title="`关于${this.warningName1}涉嫌其他违规的函询-编号${this.inquiryNo}`" :visible.sync="otherWarningVisible3" :append-to-body="true" @close="otherWarningInquiryFormClose" width="70%">
      <el-row>
        <el-col :span="25">
          <basic-container>
            <div style="font-size: 20px;">{{this.warningName1}}相关负责人:</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">{{this.warningContent}}</div>
            <div>
              <div style="font-size: 20px;">以下为违规取证材料:</div>
              <span v-for="(item,index) in fileList">
             <span>
        <img v-if="item.suffixType == 3" :src="item.value" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item.value)" class="avatar">
        </span>
           <span>
           <ul style="list-style: none" v-if="item.suffixType == 4">
          <li>
            <a :href="`${item.value}`" style="color: #409EFF;" target="_blank">{{item.value}}</a>
          </li>
        </ul>
     </span>
           </span>
            </div>
          </basic-container>
        </el-col>
      </el-row>
      <el-row>
        <span style="font-size: 20px;float: right">{{this.jiweiDeptName}}<br/>{{this.jiweiDate}}<br/>函询发送时间：{{this.inquiryTime}}</span>
        <br/>
        <span style="font-size: 15px;float: left;margin-top: 20px;">如有疑问可联系负责人：{{this.jiweiAdminName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </el-row>
      <br/>
      <br/>
      <br/>
      <br/>
      <br/>
      <el-row style="border-top:1px solid #000;">
        <el-col :span="19">
          <span style="font-size: 20px;float: left">学校回复：{{this.replyContent}}</span>
          <br/>
          <span style="font-size: 20px;margin-top: 10px;"><br/>回复时间：{{this.replyDate}} &nbsp;&nbsp;&nbsp;回复人：{{this.replyUserName}}</span>
        </el-col>
        <el-col :span="5" v-if="this.archives == '3'">
          <span style="font-size: 30px;float: right">已结案</span>
          <br/>
          <span style="font-size: 20px;float: right;margin-top: 10px;">归档时间:{{this.archivistDate}}</span>
          <br/>
          <span style="font-size: 20px;float: right;margin-top: 10px;">归档人：{{this.archivistName}}</span>
        </el-col>
        <el-button v-if="this.archives == '2'" style="float: right;" type="primary" @click="closingFiling">结案归档</el-button>
      </el-row>
    </el-dialog>
    <el-dialog title="查看" :visible.sync="warningDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div>
        <el-col :span="24">
          <div sstyle="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">预警日期：</span>{{this.warningDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">当前状态：</span>{{this.inquiryStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">办理单位：</span>{{this.handlingUnit}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规单位：</span>{{this.deptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">单位类型：</span>{{this.unitType}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规分类：</span>{{this.category}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规内容：</span>{{this.content}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规取证内容：</span>{{this.evidence}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType == '2'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">已发函询</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询日期：</span>{{this.inquiryDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询单位：</span>{{this.inquiryDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询人姓名：</span>{{this.pushInquerUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询内容：</span><a href="javascript:void(0);" style="color: #1e9fff" @click="openMakeInquiry">点此查看</a></div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType3 == '3'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">食堂回复</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复日期：</span>{{this.replyTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复单位：</span>{{this.replyDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复人姓名：</span>{{this.replyName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复意见：</span>{{this.replyContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType4 == '4'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">处置信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置日期：</span>{{this.handleDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置单位：</span>{{this.handleDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置人姓名：</span>{{this.handleUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置意见：</span>{{this.handleContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType5 == '5'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">归档信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档日期：</span>{{this.fileDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档单位：</span>{{this.fileDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档人姓名：</span>{{this.fileUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档意见：</span>{{this.fileContent}}</div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :visible.sync="otherWarningVisible2" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <div style=" text-align: center;font-size: 26px;margin-bottom: 10px;">关于{{this.warningName1}}涉嫌其他违规的函询</div>
      <div style=" text-align: center;">函询编号：{{this.inquiryNo}}</div>
      <el-row>
        <el-col :span="25">
          <basic-container>
            <div style="font-size: 20px;">{{this.warningName1}}相关负责人：</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">经我单位监管，贵单位可能存在以下违规行为，特此函询，请据实说明情况。</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规发现日期：{{this.warningDate}}</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规分类：{{this.category}}</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规内容：{{this.warningContent}}</div>
            <div>
              <div><span style="font-size: 20px;margin-left: 40px;">以下为违规取证材料：{{this.evidence}}</span></div>
            </div>
          </basic-container>
        </el-col>
      </el-row>
      <el-row>
        <span style="font-size: 20px;float: right">{{this.jiweiDeptName}}<br/>{{this.jiweiDate}}</span>
        <br/>
        <span style="font-size: 15px;float: left;margin-top: 20px;">如有疑问可联系负责人：{{this.jiweiAdminName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </el-row>
      <br/>
      <br/>
      <br/>
      <br/>
      <br/>
    </el-dialog>
    <el-dialog :title="`处置`" :visible.sync="managementVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="managementForm" :option="managementOption" v-model="managementForm" @submit="saveManage">
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import todoList from '@/components/todoList';
  import {getschoolAuthInfoIndex} from "@/api/liancan/schoolAuthInfo";
  import {allow, reject, getList} from "@/api/liancan/schoolAuthInfo";
  import { registerFormOption2} from '@/const/school/index';
  /*    import {getWarningList,saveInquiry,getInquiryById,getViolationWarningList,getWarningHandlingList,getWarningById,saveManage,saveFile,saveArchivist,getAllWarningList,saveMakeInquiry,warningData,replyData} from "@/api/liancan/illegalWarnLog";*/
  import {getWarningList,getWarningById,saveInquiry,getInquiryById,saveArchivist,getAllWarningList,saveMakeInquiry,pageWarningList,getReplyList,saveManage,saveFile,getProcessedWraningList,warningData} from "@/api/liancan/illegalWarnLog";
  export default {
    data() {
      return {
        activeIdx: 0,
        activeIdxs: 0,
        page: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 10, // 每页显示多少条,
          isAsc: false //是否倒序
        },
        allWarningListPage:{
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 10, // 每页显示多少条,
          isAsc: false //是否倒序
        },
        messList: [ '新消息','已处理' ],
        //messLists:[ '违规预警','实名认证','投诉建议' ],
        messLists:[ '违规预警','实名认证' ],
        schoolIndexList: [],
        agencyData:[],
        sortData:[],
        allWarningListData:[],
        isShow: false,
        form:{},
        warningRecordForm1:{},
        allWarningListForm:{},
        registerForm: {
          id: null,
          deptId: null,
          name: null,
          type: null,
          province: null,
          city: null,
          area: null,
          site: null,
          userName: null,
          identityNo: null,
          studentNumber: null,
          lodge: null,
          agencyCode: null,
          landCertificateNo: null,
          responsibleNo: null,
          contactPerson: null,
          contactPhone: null,
          businessLicense: null,
          businessLicenseNo: null,
          validPeriodStart: null,
          validPeriodEnd: null,
          validPeriod: [],
          schoolLogo: null,
          auditStatus: ''
        },
        allWarningListloading:true,
        warningVisible:false,
        warningDetailsVisible:false,
        warningRecordVisible1:false,
        warningDetailsVisible2:false,
        otherWarningVisible2:false,
        managementVisible:false,
        fileVisible:false,
        warningRecordForm2:{},
        managementForm:{},
        replyListForm:{},
        fileForm:{},
        processedForm:{},
        auditStatus:undefined,
        warningName:undefined,
        deptId:undefined,
        deptName:undefined,
        unitType:undefined,
        warningName1:undefined,
        warningName2:undefined,
        jiweiDeptName:undefined,
        jiweiDate:undefined,
        jiweiAdminName:undefined,
        jiweiAdminAccount:undefined,
        inquiryTime:undefined,
        archives:undefined,
        archivistName:undefined,
        archivistDate:undefined,
        replyContent:undefined,
        replyDate:undefined,
        replyUserName:undefined,
        warningStatus:undefined,
        toDay:undefined,
        suffixType:undefined,
        fileName:undefined,
        warningContent:undefined,
        inquiryNo:undefined,
        schoolType:undefined,
        canteenName:undefined,
        adminName:undefined,
        phone:undefined,
        warningDate:undefined,
        inquiryStatus:undefined,
        category:undefined,
        handlingUnit:undefined,
        schoolName:undefined,
        content:undefined,
        evidence:undefined,
        warningType:undefined,
        warningType3:undefined,
        warningType4:undefined,
        warningType5:undefined,
        inquiryDate:undefined,
        inquiryDeptName:undefined,
        pushInquerUserName:undefined,
        inquiryName:undefined,
        warningId:undefined,
        handleDate:undefined,
        handleUserName:undefined,
        handleContent:undefined,
        handleDeptName:undefined,
        fileUserName:undefined,
        fileContent:undefined,
        fileDate:undefined,
        fileDeptName:undefined,
        registerFormOption: registerFormOption2,
        warningRecordOption1:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              display:false,
            },
            {
              label: "违规预警记录编号",
              prop: "inquiryNo",
              type: "input",
              disabled:true,
            },
            {
              label: "违规单位",
              prop: "deptName",
              type: "input",
              disabled:true,
            },
            {
              label: "违规类型",
              prop: "type",
              type: "select",
              disabled:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入类型",
                trigger: "blur"
              }],
            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              disabled:true,
              span:24,
            },
            {
              label: '违规时间',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: '违规发现时间',
              prop: 'findDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: "单位管理员姓名",
              prop: "userName",
              type: "input",
              disabled:true,
            },
            {
              label: "管理员手机号",
              prop: "userPhone",
              type: "input",
              disabled:true,
            },
            {
              label: "违规取证",
              prop: "evidence",
              type: "textarea",
              span: 24,
              disabled:true,
            },
          ]
        },
        option: {
          //height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          delBtn: false,
          addBtn: false,
          editBtn: false,
          selection: true,
          column: [
            {
              label: "id",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "学校",
              prop: "title",
              type: "input",
              editDisabled: true,
            },
            {
              label: "时间",
              prop: "createTime",
              type: "input",
              //slot: true,
              overHidden: true,
              editDisabled: true,
            },
          ]
        },
        allWarningOption: {
          /*   height:'auto',
            calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "违规类型",
              prop: "title",
              type: "input",
            },
            {
              label: '违规时间',
              prop: 'warningTime',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
          ]
        },
        data: [],
        loading: true,
      }
    },
    components: {
      todoList
    },
    created(){
      getschoolAuthInfoIndex().then(res => {
        res.data.data.records.forEach(item =>{
          this.agencyData.push(item);
        })
      });
      warningData().then(res => {
        res.data.data.forEach(item =>{
          this.agencyData.push(item);
        })
      });
    },
    methods:{
      check: function(row) {
        this.isShow = true;
        this.registerForm = row;
      },
      menuClick(idx, idxs) {
        console.log(">>>>>>>>>>>>>>",JSON.stringify(idx))
        console.log(">>>>>>>>>>>>>>",JSON.stringify(idxs))
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          if (idxs == 0){
            this.allWarningListPage.currentPage = 1;
            this.onLoadAllWarning(this.allWarningListPage);
          }
          if (idxs == 1){
            this.auditStatus = 0;
            this.page.currentPage = 1;
            this.onLoad(this.page)
          }
        }
        if (idx == 1){
          if (idxs == 0){
            this.allWarningListPage.currentPage = 1;
            this.onLoadProcessed(this.allWarningListPage);
          }
          if (idxs == 1){
            this.auditStatus = 1;
            this.page.currentPage = 1;
            this.onLoad(this.page)
          }
        }
      },
      menuClicks(idx, idxs) {
        console.log(">>>>>>>>>>>>>>",JSON.stringify(idx))
        console.log(">>>>>>>>>>>>>>",JSON.stringify(idxs))
        if(this.activeIdxs == idxs) return
        this.activeIdxs = idxs
        if (idx == 0){
          if (idxs == 0){
            this.allWarningListPage.currentPage = 1;
            this.onLoadAllWarning(this.allWarningListPage);
          }
          if (idxs == 1){
            this.auditStatus = 0;
            this.page.currentPage = 1;
            this.onLoad(this.page)
          }
        }
        if (idx == 1) {
          if (idxs == 0) {
            this.allWarningListPage.currentPage = 1;
            this.onLoadProcessed(this.allWarningListPage);
          }
          if (idxs == 1) {
            this.auditStatus = 1;
            this.page.currentPage = 1;
            this.onLoad(this.page)
          }
        }
      },
      // 全部
      lookAll(e, name) {
        this.$router.push({
          path: '/home/<USER>/message/summary',
          query: {
            messName: name
          }
        })
      },
      //关闭下单弹窗
      closeForm: function() {
        this.isShow = false
        //this.$refs.orderForm.resetForm()
      },
      //去审核
      lookDetail: function(row) {
        this.isShow = true;
        this.registerForm = row;
      },
      //通过
      allow: function() {
        allow(this.registerForm.id).then(res => {
          if (res.data.success) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: 'success'
            })
          }else {
            this.$message({
              showClose: true,
              message: "操作失败",
              type: 'error'
            })
          }
        });
        this.isShow = false;
        getschoolAuthInfoIndex().then(res => {
          this.schoolIndexList = res.data.data.records;
        });
      },
      //驳回
      reject: function() {
        reject(this.registerForm.id).then(res => {
          if (res.data.success) {
            this.$message({
              showClose: true,
              message: "操作成功",
              type: 'success'
            })
          }else {
            this.$message({
              showClose: true,
              message: "操作失败",
              type: 'error'
            })
          }
        });
        this.isShow = false;
        getschoolAuthInfoIndex().then(res => {
          this.schoolIndexList = res.data.data.records;
        });
      },
      // 详情
      lookWarningDetail(row) {
        if (row.inquiryStatus === '1'){
          this.warningId = row.id;
          this.warningDate = row.warningDate;
          if (row.inquiryStatus == "1"){
            this.inquiryStatus = "待函询"
          }
          if (row.inquiryStatus == "2") {
            this.inquiryStatus = "待食堂回复"
          }
          if (row.inquiryStatus == "3") {
            this.inquiryStatus = "待处置"
          }
          if (row.inquiryStatus == "4") {
            this.inquiryStatus = "待归档"
          }
          if (row.inquiryStatus == "5") {
            this.inquiryStatus = "已归档"
          }
          if (row.category == "0") {
            this.category = "收入违规"
          }
          if (row.category == "1") {
            this.category = "支出违规"
          }
          if (row.category == "2") {
            this.category = "采购违规"
          }
          if (row.category == "3") {
            this.category = "财务违规"
          }
          if (row.category == "4") {
            this.category = "招标违规"
          }
          if (row.category == "5") {
            this.category = "仓管违规"
          }
          this.inquiryStatus1 = 1;
          this.managementStatus3 = 1
          this.fileStatus = 1;
          this.warningType = status;
          this.warningType = status;
          this.warningType3 = status;
          this.warningType4 = status;
          this.warningType5 = status;
          this.handlingUnit = row.handlingUnit;
          this.content = row.content;
          this.evidence = row.evidence;
          this.inquiryDate = row.inquiryDate;
          this.inquiryDeptName = row.pushInquiryDeptName;
          this.inquiryName = row.jiweiUserName;
          this.deptName = row.deptName;
          this.unitType = row.unitType;
          this.pushInquerUserName = row.pushInquerUserName;
          this.warningDetailsVisible = true;
        }
        if (row.inquiryStatus === '2'){
          this.warningId = row.id;
          this.warningDate = row.warningDate;
          if (row.inquiryStatus == "1"){
            this.inquiryStatus = "待函询"
          }
          if (row.inquiryStatus == "2") {
            this.inquiryStatus = "待食堂回复"
          }
          if (row.inquiryStatus == "3") {
            this.inquiryStatus = "待处置"
          }
          if (row.inquiryStatus == "4") {
            this.inquiryStatus = "待归档"
          }
          if (row.inquiryStatus == "5") {
            this.inquiryStatus = "已归档"
          }
          if (row.category == "0") {
            this.category = "收入违规"
          }
          if (row.category == "1") {
            this.category = "支出违规"
          }
          if (row.category == "2") {
            this.category = "采购违规"
          }
          if (row.category == "3") {
            this.category = "财务违规"
          }
          if (row.category == "4") {
            this.category = "招标违规"
          }
          if (row.category == "5") {
            this.category = "仓管违规"
          }
          this.managementStatus3 = 1
          this.inquiryStatus1 = 2;
          this.fileStatus = 1;
          this.warningType = 2;
          this.warningType = 2;
          this.warningType3 = 2;
          this.warningType4 = 2;
          this.warningType5 = 2;
          this.handlingUnit = row.handlingUnit;
          this.content = row.content;
          this.evidence = row.evidence;
          this.inquiryDate = row.inquiryDate;
          this.inquiryDeptName = row.pushInquiryDeptName;
          this.inquiryName = row.jiweiUserName;
          this.deptName = row.deptName;
          this.unitType = row.unitType;
          this.pushInquerUserName = row.pushInquerUserName;
          this.warningDetailsVisible = true;
        }
        if (row.inquiryStatus === '3'){
          this.warningId = row.id;
          this.warningDate = row.warningDate;
          if (row.inquiryStatus == "1"){
            this.inquiryStatus = "待函询"
          }
          if (row.inquiryStatus == "2") {
            this.inquiryStatus = "待食堂回复"
          }
          if (row.inquiryStatus == "3") {
            this.inquiryStatus = "待处置"
          }
          if (row.inquiryStatus == "4") {
            this.inquiryStatus = "待归档"
          }
          if (row.inquiryStatus == "5") {
            this.inquiryStatus = "已归档"
          }
          if (row.category == "0") {
            this.category = "收入违规"
          }
          if (row.category == "1") {
            this.category = "支出违规"
          }
          if (row.category == "2") {
            this.category = "采购违规"
          }
          if (row.category == "3") {
            this.category = "财务违规"
          }
          if (row.category == "4") {
            this.category = "招标违规"
          }
          if (row.category == "5") {
            this.category = "仓管违规"
          }
          this.inquiryStatus1 = 3;
          this.warningType = 2;
          this.warningType3 = 3;
          this.managementStatus3 = 3;
          this.warningType4 = 1;
          this.warningType5 = 1;
          this.handlingUnit = row.handlingUnit;
          this.content = row.content;
          this.evidence = row.evidence;
          this.inquiryDate = row.inquiryDate;
          this.inquiryDeptName = row.pushInquiryDeptName;
          this.inquiryName = row.jiweiUserName;
          this.replyContent = row.replyContent;
          this.replyName = row.replyName;
          this.replyTime = row.replyTime;
          this.replyDeptName = row.replyDeptName;
          this.deptName = row.deptName;
          this.unitType = row.unitType;
          this.pushInquerUserName = row.pushInquerUserName;
          this.warningDetailsVisible = true;
        }
        if (row.inquiryStatus === '4'){
          this.warningId = row.id;
          this.warningDate = row.warningDate;
          if (row.inquiryStatus == "1"){
            this.inquiryStatus = "待函询"
          }
          if (row.inquiryStatus == "2") {
            this.inquiryStatus = "待食堂回复"
          }
          if (row.inquiryStatus == "3") {
            this.inquiryStatus = "待处置"
          }
          if (row.inquiryStatus == "4") {
            this.inquiryStatus = "待归档"
          }
          if (row.inquiryStatus == "5") {
            this.inquiryStatus = "已归档"
          }
          if (row.category == "0") {
            this.category = "收入违规"
          }
          if (row.category == "1") {
            this.category = "支出违规"
          }
          if (row.category == "2") {
            this.category = "采购违规"
          }
          if (row.category == "3") {
            this.category = "财务违规"
          }
          if (row.category == "4") {
            this.category = "招标违规"
          }
          if (row.category == "5") {
            this.category = "仓管违规"
          }
          this.inquiryStatus1 = 4;
          this.managementStatus3 = 1
          this.warningType = 2;
          this.warningType3 = 3;
          this.warningType4 = 4;
          this.managementStatus3 = 4;
          this.fileStatus = 4;
          this.handlingUnit = row.handlingUnit;
          this.content = row.content;
          this.evidence = row.evidence;
          this.inquiryDate = row.inquiryDate;
          this.inquiryDeptName = row.pushInquiryDeptName;
          this.inquiryName = row.jiweiUserName;
          this.replyContent = row.replyContent;
          this.replyName = row.replyName;
          this.replyTime = row.replyTime;
          this.replyDeptName = row.replyDeptName;
          this.handleUserName = row.handleUserName;
          this.handleContent = row.handleContent;
          this.handleDeptName = row.handleDeptName;
          this.handleDate = row.handleDate;
          this.deptName = row.deptName;
          this.unitType = row.unitType;
          this.pushInquerUserName = row.pushInquerUserName;
          this.warningDetailsVisible = true;
        }
        if (row.inquiryStatus === '5'){
          this.warningId = row.id;
          this.warningDate = row.warningDate;
          if (row.inquiryStatus == "1"){
            this.inquiryStatus = "待函询"
          }
          if (row.inquiryStatus == "2") {
            this.inquiryStatus = "待食堂回复"
          }
          if (row.inquiryStatus == "3") {
            this.inquiryStatus = "待处置"
          }
          if (row.inquiryStatus == "4") {
            this.inquiryStatus = "待归档"
          }
          if (row.inquiryStatus == "5") {
            this.inquiryStatus = "已归档"
          }
          if (row.category == "0") {
            this.category = "收入违规"
          }
          if (row.category == "1") {
            this.category = "支出违规"
          }
          if (row.category == "2") {
            this.category = "采购违规"
          }
          if (row.category == "3") {
            this.category = "财务违规"
          }
          if (row.category == "4") {
            this.category = "招标违规"
          }
          if (row.category == "5") {
            this.category = "仓管违规"
          }
          this.inquiryStatus1 = 5;
          this.managementStatus3 = 1
          this.fileStatus = 1;
          this.warningType = 2;
          this.warningType3 = 3;
          this.warningType4 = 4;
          this.warningType5 = 5;
          this.handlingUnit = row.handlingUnit;
          this.content = row.content;
          this.evidence = row.evidence;
          this.inquiryDate = row.inquiryDate;
          this.inquiryDeptName = row.pushInquiryDeptName;
          this.inquiryName = row.jiweiUserName;
          this.replyContent = row.replyContent;
          this.replyName = row.replyName;
          this.replyTime = row.replyTime;
          this.replyDeptName = row.replyDeptName;
          this.handleUserName = row.handleUserName;
          this.handleContent = row.handleContent;
          this.handleDeptName = row.handleDeptName;
          this.handleDate = row.handleDate;
          this.fileUserName = row.fileUserName;
          this.fileContent = row.fileContent;
          this.fileDate = row.fileDate;
          this.fileDeptName = row.fileDeptName;
          this.deptName = row.deptName;
          this.unitType = row.unitType;
          this.pushInquerUserName = row.pushInquerUserName;
          this.warningDetailsVisible = true;
        }
      },
      handOpenWarningPush(){
        getWarningById(this.warningId).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.warningName2 = res.data.data.typeName;
          this.warningRecordForm1 = res.data.data;
        });
        this.warningRecordVisible1 = true;
      },
      pushInquiry(row){
        saveInquiry(this.warningRecordForm1).then(() => {
          this.allWarningListPage.currentPage = 1;
          this.onLoadAllWarning(this.allWarningListPage);
          this.warningRecordVisible1 = false;
          this.$message({
            type: "success",
            message: "发送成功!"
          });
        }, error => {
          this.warningRecordVisible1 = false;
          this.warningDetailsVisible = false;
          window.console.log(error);
        });
      },
      openMakeInquiry(){
        getInquiryById(this.warningId).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.warningContent = res.data.data.content;
          this.inquiryNo = res.data.data.inquiryNo;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.evidence = res.data.data.evidence;
          if (res.data.data.category == "0") {
            this.category = "收入违规"
          }
          if (res.data.data.category == "1") {
            this.category = "支出违规"
          }
          if (res.data.data.category == "2") {
            this.category = "采购违规"
          }
          if (res.data.data.category == "3") {
            this.category = "财务违规"
          }
          if (res.data.data.category == "4") {
            this.category = "招标违规"
          }
          if (res.data.data.category == "5") {
            this.category = "仓管违规"
          }
        });
        this.otherWarningVisible2 = true;
      },
      saveManage(row,loading){
        this.managementForm.warningId = this.warningId;
        saveManage(this.managementForm).then(() => {
          this.allWarningListPage.currentPage = 1;
          this.onLoadAllWarning(this.allWarningListPage);
          loading();
          this.managementForm.content = "";
          this.warningDetailsVisible = false;
          this.$message({
            type: "success",
            message: "处置成功!"
          });
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      fileManage(row,loading){
        this.fileForm.warningId = this.warningId;
        saveFile(this.fileForm).then(() => {
          this.warningData = [];
          warningData().then(res => {
            res.data.data.forEach(item =>{
              this.agencyData.push(item);
            })
          });
          loading();
          this.fileForm.content = "";
          this.warningDetailsVisible = false;
          this.$message({
            type: "success",
            message: "归档成功!"
          });
        }, error => {
          loading();
          window.console.log(error);
        });
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      searchChangeAllWarning(params, done) {
        this.query = params;
        this.allWarningListPage.currentPage = 1
        this.onLoadAllWarning(this.allWarningListPage, params);
        done();
      },
      searchChangeProcessed(params, done) {
        this.query = params;
        this.processedPage.currentPage = 1
        this.onLoadProcessed(this.processedPage, params);
        done();
      },
      searchResetAllWarning() {
        this.query = {};
        this.onLoadAllWarning(this.allWarningListPage);
      },
      searchResetProcessed() {
        this.query = {};
        this.onLoadProcessed(this.processedPage);
      },
      selectionChangeAllWarning(list) {
        this.selectionList = list;
      },
      selectionChangeProcessed(list) {
        this.selectionList = list;
      },
      currentChangeAllWarning(currentPage){
        this.allWarningListPage.currentPage = currentPage;
      },
      currentChangeProcessed(currentPage){
        this.processedPage.currentPage = currentPage;
      },
      sizeChangeAllWarning(pageSize){
        this.allWarningListPage.pageSize = pageSize;
      },
      sizeChangeProcessed(pageSize){
        this.processedPage.pageSize = pageSize;
      },
      selectionClearAllWarning() {
        this.selectionList = [];
        this.$refs.allWarningListForm.toggleSelection();
      },
      selectionClearProcessed() {
        this.selectionList = [];
        this.$refs.processedForm.toggleSelection();
      },
      onLoadProcessed(page, params = {}) {
        this.allWarningListloading = true;
        getProcessedWraningList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.allWarningListPage.total = data.total;
          this.allWarningListData = data.records;
          this.allWarningListloading = false;
          this.selectionClearAllWarning();
        });
      },
      onLoadAllWarning(page, params = {}) {
        this.allWarningListloading = true;
        pageWarningList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.deptId).then(res => {
          const data = res.data.data;
          this.allWarningListPage.total = data.total;
          this.allWarningListData = data.records;
          this.allWarningListloading = false;
          this.selectionClearAllWarning();
        });
      },
      /**
       * 加载数据
       */
      onLoad(page, params = {}) {
        this.loading = true;
        params.auditStatus = this.auditStatus;
        getschoolAuthInfoIndex(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      openMakeInquiryReply(row){
        getInquiryById(row.id).then(res => {
          this.inquiryNo = res.data.data.inquiryNo;
          this.warningName1 = res.data.data.deptName;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.inquiryTime = res.data.data.pushTime;
          this.archives = res.data.data.inquiryStatus;
          this.archivistName = res.data.data.archivistName;
          this.archivistDate = res.data.data.archivistDate;
          this.replyContent =  res.data.data.replyContent;
          this.replyDate = res.data.data.replyDate;
          this.replyUserName = res.data.data.replyName;
          this.warningContent = res.data.data.content;
        });
        this.warningRecordForm2.id = row.id;
        this.fileList = row.fileList;
        this.otherWarningVisible3 = true;
      },
      openWarning(row,status){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = status;
        this.warningType = status;
        this.warningType3 = status;
        this.warningType4 = status;
        this.warningType5 = status;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      handOpenWarningPush(row){
        getWarningById(row.id).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          if (res.data.data.type == "0"){
            this.warningName2 = "侵吞利息";
          }
          if (res.data.data.type == "1"){
            this.warningName2 = "招标作假";
          }
          if (res.data.data.type == "2"){
            this.warningName2 = "采购缺人";
          }
          if (res.data.data.type == "3"){
            this.warningName2 = "验收缺人";
          }
          if (res.data.data.type == "4"){
            this.warningName2 = "无票收入";
          }
          if (res.data.data.type == "5"){
            this.warningName2 = "采购无票支付";
          }
          if (res.data.data.type == "6"){
            this.warningName2 = "燃料无票支付";
          }
          if (res.data.data.type == "7"){
            this.warningName2 = "水电无票支付";
          }
          if (res.data.data.type == "8"){
            this.warningName2 = "结余超标";
          }
          if (res.data.data.type == "9"){
            this.warningName2 = "会计无证";
          }
          if (res.data.data.type == "10"){
            this.warningName2 = "教师搭食";
          }
          if (res.data.data.type == "11"){
            this.warningName2 = "其他";
          }
          this.warningRecordForm1 = res.data.data;
        });
        this.warningRecordVisible1 = true;
      },
      openMakeInquiry(){
        getInquiryById(this.warningId).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.warningContent = res.data.data.content;
          this.inquiryNo = res.data.data.inquiryNo;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiAdminName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.evidence = res.data.data.evidence;
          if (res.data.data.category == "0") {
            this.category = "收入违规"
          }
          if (res.data.data.category == "1") {
            this.category = "支出违规"
          }
          if (res.data.data.category == "2") {
            this.category = "采购违规"
          }
          if (res.data.data.category == "3") {
            this.category = "财务违规"
          }
          if (res.data.data.category == "4") {
            this.category = "招标违规"
          }
          if (res.data.data.category == "5") {
            this.category = "仓管违规"
          }
        });
        this.otherWarningVisible2 = true;
      },
      checkReply(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      checkNanagement(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 4;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.handleUserName = row.handleUserName;
        this.handleContent = row.handleContent;
        this.handleDeptName = row.handleDeptName;
        this.handleDate = row.handleDate;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      checkFile(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 4;
        this.warningType5 = 5;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.handleUserName = row.handleUserName;
        this.handleContent = row.handleContent;
        this.handleDeptName = row.handleDeptName;
        this.handleDate = row.handleDate;
        this.fileUserName = row.fileUserName;
        this.fileContent = row.fileContent;
        this.fileDate = row.fileDate;
        this.fileDeptName = row.fileDeptName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      management(row){
        this.warningId = row.id;
        this.managementVisible = true;
      },
      fileOpen(row){
        this.warningId = row.id;
        this.fileVisible = true;
      },
      saveManage(row,loading){
        this.managementForm.warningId = this.warningId;
        saveManage(this.managementForm).then(() => {
          this.allWarningListPage.currentPage = 1;
          this.onLoadAllWarning(this.allWarningListPage);
          this.managementVisible = false;
          this.managementForm.content = "";
          loading();
          this.$message({
            type: "success",
            message: "处置成功!"
          });
        }, error => {
          loading
          window.console.log(error);
        });
      },
      fileManage(row,loading){
        this.fileForm.warningId = this.warningId;
        saveFile(this.fileForm).then(() => {
          this.allWarningListPage.currentPage = 1;
          this.onLoadAllWarning(this.allWarningListPage);
          this.fileVisible = false;
          this.fileForm.content = "";
          loading();
          this.$message({
            type: "success",
            message: "归档成功!"
          });
        }, error => {
          loading();
          window.console.log(error);
        });
      },
    },

  }
</script>

<style lang="scss" scoped>
  .agncy-list{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      ::v-deep div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-headers{
      display: flex;
      height: 50px;
      background: #fff;
      ::v-deep div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      height: 640px;
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20px;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
  }
</style>
