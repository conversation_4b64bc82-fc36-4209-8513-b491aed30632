<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="success"  icon="el-icon-download" size="small" @click="exportExcel" >导出</el-button>
<!--                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>-->
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {orderTrackList, getDetail, add, update, exportDataDetail} from "@/api/supplier/supplierProcureStockIn";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";
var DIC = {
    approveStatus: [{
        label: '未审核',
        value: '0'
    }, {
        label: '通过',
        value: '1'
    }, {
        label: '不通过',
        value: '-1'
    },],
    billStatus: [{
        label: '未接单',
        value: '0'
    }, {
        label: '取消/拒单',
        value: '-1'
    }, {
        label: '已接单',
        value: '1'
    },{
        label: '配送中',
        value: '2'
    },{
        label: '已送达',
        value: '3'
    },{
        label: '收货结束',
        value: '4'
    },],
    processStatus: [{
        label: '未入库',
        value: '0'
    }, {
        label: '部分入库',
        value: '1'
    }, {
        label: '全部入库',
        value: '2'
    },],
    closeStatus: [{
        label: '未关闭',
        value: '0'
    }, {
        label: '手动关闭',
        value: '-1'
    }, {
        label: '已关闭',
        value: '1'
    },],

}
export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                printBtn:true,
                // 其他配置...
                exportBtn: true, // 启用导出按钮
                excelExport: true, // 启用Excel导出
                csvExport: true, // 启用CSV导出
                selection: false,
                menu: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            disabledDate(time) {
                                return time.getTime() < Date.now();
                            }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "订单号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: '交付类型',
                        prop: 'deliveryType',
                        type: 'select',
                        search: true,
                        dicData: [
                            {
                                label: '传统入库',
                                value: 0
                            },
                            {
                                label: '直发客户',
                                value: 1
                            }
                        ],
                        rules: [{
                            required: true,
                            message: '请选择交付类型',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    {
                        label: '供应商',
                        prop: 'supplierCustomerId',
                        type: 'select',
                        search: true,
                        props: {
                            label: 'customerName',
                            value: 'id'
                        },
                        dicUrl: '/api/service/rabbit-supplier/customer-list',
                        rules: [
                            {
                                required: true,
                                message: '请选择供应商',
                                trigger: 'blur'
                            }
                        ]
                    },
                    {
                        label: '订单审核',
                        prop: 'approveStatus',
                        type: 'select',
                        search: true,
                        rules: [
                            {
                                required: false,
                                message: '请选择状态',
                                trigger: 'blur'
                            }
                        ],
                        dicData: DIC.approveStatus,
                    },
                    //订单流程
                    {
                        label: '订单流程',
                        prop: 'orderFlow',
                        type: 'select',
                        search: true,
                        dicData: [
                            {
                                label: '不用',
                                value: 0
                            },
                            {
                                label: '使用',
                                value: 1
                            }
                        ],
                        rules: [{
                            required: false,
                            message: '请选择订单流程',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                     //订单进展
                     {
                        label: '订单进展',
                        prop: 'billStatus',
                        type: 'select',
                        search: true,
                        dicData: [
                            {
                                label: '未接单',
                                value: '0'
                            },
                            {
                                label: '食堂已收',
                                value: '1'
                            },
                            {
                                label: '取消/拒单',
                                value: '2'
                            },
                            {
                                label: '已送达',
                                value: '3'
                            },
                            {
                                label: '配送中',
                                value: '4'
                            },
                            {
                                label: '已出库',
                                value: '5'
                            },
                            {
                                label: '接单备货',
                                value: '6'
                            },
                            {
                                label: '已退货',
                                value: '7'
                            },{
                                label: '收货结束',
                                value: "8"
                            },{
                                label: '其他',
                                value: "9"
                            }
                        ],
                        rules: [{
                            required: false,
                            message: '请选择订单进展',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    //订单执行
                    {
                        label: '订单执行',
                        prop: 'processStatus',
                        type: 'select',
                        search: true,
                        dicData: DIC.processStatus,
                        rules: [{
                            required: true,
                            message: '请选择订单执行状态',
                            trigger: 'blur'
                        }],
                        width: 80,
                    },
                    //订单关闭
                    {
                        label: '订单关闭',
                        prop: 'closeStatus',
                        type: 'select',
                        search: true,
                        dicData: DIC.closeStatus,
                        rules: [{
                            required: true,
                            message: '请选择订单关闭状态',
                            trigger: 'blur'
                        }],
                        width: 80,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                        search: true,
                        width: 100,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                        search: true,
                        width: 100,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "下单数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '0';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '0';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "下单金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '0';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                    {
                        label: "实购签收/入库数量",
                        prop: "qtyStockIn",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qtyStockIn===0) {
                                return '0';
                            }
                            return amtFilters(val.qtyStockIn);
                        },
                    },
                    {
                        label: "实购签收/入库金额",
                        prop: "amtStockIn",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.amtStockIn===0) {
                                return '0';
                            }
                            return amtFilters(val.amtStockIn);
                        },
                    },
                ]
            },
            data: [],
            isShowStockIn: false,
            isShowSelectGoods: false,
            isShowSelectOrder: false,

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "stockInData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.stockInData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },
        addStockInHandler () {
            this.form = {};
            this.businessDate = '';//new Date();
            this.supplierCustomerId = '';
            this.salesman = '';
            this.editType = 'add';
            this.isShowStockIn = true;
        },
        closeAddForm() {
            if (this.stockInData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowStockIn = false;
                    });
            }else{
                this.isShowStockIn = false;
            }
        },
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        closeShowForm(){
            this.isShowStockInDetail = false;
        },

        viewRow(row,index) {
            this.editType = 'view';
            this.form = row;
            this.stockInOnLoad(this.stockInPage)
            this.isShowStockInDetail = true;
        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            orderTrackList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //主界面基本方法 end

        //导出
        exportExcel(){
            exportDataDetail(Object.assign(this.query)).then(res => {
                if (!res.data) {
                    return;
                }
                const blob = new Blob([res.data], {
                    type: "application/vnd.ms-excel",
                }); // 构造一个blob对象来处理数据，并设置文件类型
                const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
                const a = document.createElement("a"); //创建a标签
                a.style.display = "none";
                a.href = href; // 指定下载链接
                let fileName = res.headers["content-disposition"];
                fileName = fileName.split("=")[1];
                a.download = decodeURIComponent(fileName); //指定下载文件名
                a.click(); //触发下载
                URL.revokeObjectURL(a.href); //释放URL对象
            });
        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
