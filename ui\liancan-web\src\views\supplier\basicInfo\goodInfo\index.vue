<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <!--            选择商品信息-->
            <template slot="menuLeft">
                <el-button type="danger"
                           size="small"
                           icon="el-icon-delete"
                           plain
                           @click="handleDelete">删除
                </el-button>
                <el-button type="warning"
                           size="small"
                           icon="el-icon-star-off"
                           @click="moveWarehouseBatchHandler">批量修改默认仓库
                </el-button>
            </template>
            <template slot="status" slot-scope="{row}">
                <el-tag v-if="row.status == '1'" type="success">正常</el-tag>
                <el-tag v-if="row.status == '0'" type="danger">停用</el-tag>
            </template>
        </avue-crud>
        <!-- 公共商品选择 开始 -->
        <el-dialog title="商品" :visible.sync="isShow2" :append-to-body="true" @close="closeForm2" width="90%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crud"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
                <template slot="status" slot-scope="{row}">
                    <el-tag v-if="row.status == '1'" type="success">正常</el-tag>
                    <el-tag v-if="row.status == '0'" type="danger">停用</el-tag>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 商品选择 结束 -->

        <!--修改默认仓库 开始-->
        <el-dialog title="修改默认仓库"
                   :append-to-body="true"
                   :visible.sync="isShow3"
                   v-if="isShow3"
                   width="30%">
            <div class="el-dialog-div" style="height: 120px;">
                <avue-form :option="optionSelectWarehouse"
                           ref="warehouseForm"
                           v-model="warehouseForm"></avue-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeSelectLandle">取消</el-button>
                <el-button type="primary" @click="saveSelectWarehoseHandle">确认</el-button>
            </span>
        </el-dialog>
        <!--修改默认仓库 结束-->
    </basic-container>
</template>

<script>
import {getList as getGoodsList} from "@/api/liancan/shopGoodsPublic";
import {getList, getDetail, add, update, remove, switchWarehouse} from "@/api/supplier/supplierBaseGood";
import {mapGetters} from "vuex";

export default {
    data() {
        return {
            form: {},
            warehouseForm: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            defaults: {},
            currentOpenType: '',
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                tip: false,
                border: true,
                index: true,
                addBtn: true,
                delBtn: false,
                editBtn: true,
                viewBtn: true,
                selection: true,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "typeQuery",
                        type: "select",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请选择商品类型",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingQuery'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "biddingQuery",
                        type: "select",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请选择二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeIdQuery'],
                        search: true,
                        dicFlag: false,
                        overHidden: true,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeIdQuery",
                        type: "select",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请填写商品子项名称",
                            trigger: "blur"
                        }],
                        span: 24,
                        search: true,
                        searchLabelWidth: 100,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请输入生产厂家",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                        disabled: true,
                    },
                    {
                        label: "默认存放仓库",
                        prop: "warehouseId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                        searchLabelWidth: 100
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "radio",
                        slot:true,
                        dicData: [
                            {
                                label: '正常',
                                value: "1"
                            },
                            {
                                label: '停用',
                                value: "0"
                            },
                        ],
                        value: "1",
                        rules: [{
                            required: true,
                            message: "请选择状态",
                            trigger: "blur"
                        }],
                    },
                    //以下在列表不显示////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        disabled: true,
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },

                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                        disabled: true,
                        hide: true,
                    },

                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                        hide: true,
                        span: 24,
                    },

                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        disabled: true,
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                        hide: true,
                    },
                    {
                        label: "销售单价",
                        prop: "salePrice",
                        type: "input",
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: true,
                        rules: [{
                            required: true,
                                message: "请输入销售单价",
                                trigger: "blur",
                                pattern: /^[0-9]+(\.[0-9]+)?$/,
                                validator: (rule, value, callback) => {
                                if (value <= 0) {
                                    callback(new Error("销售单价必须大于0"));
                                } else {
                                    callback();
                                }
                                }
                        }]
                    },
                    {
                        label: "零售单价",
                        prop: "retailPrice",
                        type: "input",
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: true,
                        rules: [{
                            required: true,
                                message: "请输入零售单价",
                                trigger: "blur",
                                pattern: /^[0-9]+(\.[0-9]+)?$/,
                                validator: (rule, value, callback) => {
                                if (value <= 0) {
                                    callback(new Error("零售单价必须大于0"));
                                } else {
                                    callback();
                                }
                                }
                        }]
                    },
                    {
                        label: "采购单价",
                        prop: "purchasePrice",
                        type: "input",
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: true,
                        rules: [{
                            required: true,
                            message: "请输入采购单价",
                            trigger: "blur",
                            pattern: /^[0-9]+(\.[0-9]+)?$/,
                            validator: (rule, value, callback) => {
                            if (value <= 0) {
                                callback(new Error("采购单价必须大于0"));
                            } else {
                                callback();
                            }
                            }
                        }]
                    },
                    {
                        label: "商品来源",
                        prop: "sourceType",
                        type: "input",
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: true,
                        rules: [{
                            required: false,
                            message: "请输入商品来源",
                            trigger: "change"
                        }]
                    },
                ]
            },
            viewFlag: false,
            dataNum: 1,
            arrayData: [{
                viewId: 1,
                viewName: 'view1'
            }],
            allSupplierData: [],
            uploadDataList: [],
            imageId: '',
            fileVOList: [],
            // 上传文件列表
            fileList: [],
            supplierTableLoading: false,
            supplierForm: {},
            dialogSupplier: false,
            data: [],
            goodsId: '',
            isShow2: false,
            isShow3: false,
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品类型",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品大类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品小类",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品子项编码",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },

                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },

                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产商类型",
                        prop: "manufacturerType",
                        type: "input",
                    },
                    {
                        label: "营业执照/身份证号",
                        prop: "idCard",
                        type: "input",
                    },
                    {
                        label: "生产许可证号",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },

                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },
                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
            optionSelectWarehouse: {
                emptyBtn: false,
                submitBtn: false,
                column: [{
                    label: '默认存放仓库',
                    prop: 'warehouseId',
                    type: 'select',
                    labelWidth: 180,
                    span: 24,
                    row: true,
                    props: {
                        label: 'warehouseName',
                        value: 'id'
                    },
                    rules: [{
                        required: true,
                        message: "请选择默认存放仓库",
                        trigger: "blur"
                    }],
                    dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                }]
            }
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {

    },
    watch: {
        'form.type'() {
            let type = this.form.type;
            //食材
            if (type == "1314471996665397249" || type == "1366636175794667521") {
                this.option.column.forEach(then => {
                    if (then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
                        then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard') {
                        then.addDisplay = true
                    }
                })
            }
            //燃料
            if (type == "1316561056074649602" || type == "1366636571036516354") {
                this.option.column.forEach(then => {
                    if (then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
                        then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard') {
                        then.addDisplay = false;
                    }
                })
            }
        },
    },
    methods: {
        closeSelectLandle() {
            this.isShow3 = false;
        },
        saveSelectWarehoseHandle() {
            if (this.ids=='' || this.warehouseForm.warehouseId == '') {
                this.$message.warning("请选择默认存放仓库");
                return;
            }
            this.$confirm("确定将选择数据移仓?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return switchWarehouse(this.ids,this.warehouseForm.warehouseId);
                })
                .then(() => {
                    this.isShow3 = false;
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        moveWarehouseBatchHandler() {
            //判断是否已经选择了要修改的行
            if (this.selectionList.length == 0) {
                this.$message({
                    showClose: true,
                    message: "最少要选择一个商品",
                    type: 'error'
                })
                return false
            }
            //弹框显示修改窗口
            this.isShow3 = true;
        },
        rowSave(row, loading, done) {
            console.log("rowSave",row)
            //判断销售单价不能为空且是否为大于0的数字
            if (!this.isNumeric(row.salePrice) || row.salePrice == '' || row.salePrice <= 0) {
                this.$message({
                    showClose: true,
                    message: "销售单价必须为大于0的数字",
                    type: 'error'
                })
                done();
                return false
            }
            //判断零售单价不能为空且是否为大于0的数字
            if (!this.isNumeric(row.retailPrice) || row.retailPrice == '' || row.retailPrice <= 0) {
                this.$message({
                    showClose: true,
                    message: "零售单价必须为大于0的数字",
                    type: 'error'
                })
                done();
                return false
            }
            //判断采购单价不能为空且是否为大于0的数字
            if (!this.isNumeric(row.purchasePrice) || row.purchasePrice == '' || row.purchasePrice <= 0) {
                this.$message({
                    showClose: true,
                    message: "采购单价必须为大于0的数字",
                    type: 'error'
                })
                done();
                return false
            }
            add(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {
            console.log("rowUpdate",row)
            //判断销售单价不能为空且是否为大于0的数字
            if (!this.isNumeric(row.salePrice) || row.salePrice == '' || row.salePrice <= 0) {
                this.$message({
                    showClose: true,
                    message: "销售单价必须为大于0的数字",
                    type: 'error'
                })
                done();
                return false
            }
            //判断零售单价不能为空且是否为大于0的数字
            if (!this.isNumeric(row.retailPrice) || row.retailPrice == '' || row.retailPrice <= 0) {
                this.$message({
                    showClose: true,
                    message: "零售单价必须为大于0的数字",
                    type: 'error'
                })
                done();
                return false
            }
            //判断采购单价不能为空且是否为大于0的数字
            if (!this.isNumeric(row.purchasePrice) || row.purchasePrice == '' || row.purchasePrice <= 0) {
                this.$message({
                    showClose: true,
                    message: "采购单价必须为大于0的数字",
                    type: 'error'
                })
                done();
                return false
            }
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },

        viewDel(index) {
            //处理图片数据
            this.fileList.forEach(element => {
                if (element.view == index) {
                    this.fileList.splice(element, 1);
                }
            })
            //处理页面
            if (this.arrayData.length <= 1) {
                this.$message({
                    showClose: true,
                    message: "最少要填写一个供应商",
                    type: 'error'
                })
                return false
            }
            this.arrayData.splice(index, 1) //删除了数组中对应的数据也就将这个位置的输入框删除
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            this.currentOpenType = type;
            // if (this.form.type == "1314471996665397249" || type == "1366636175794667521") {
            //
            // }
            //燃料
            // if (this.form.type == "1316561056074649602" || type == "1366636571036516354") {
            //
            // }
            // 新增初始化
            // if (type == 'add') {
            //     // this.publicRow = {}
            //     // this.publicForm = {}
            //     // this.publicForm.publicName = ''
            //     // this.$set(this.publicOption.column[0], 'display', true)
            // }
            // if (type == 'edit') {
            //     // this.$set(this.publicOption.column[0], 'display', false)
            // }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //图片相关
        //上传前回调
        beforeUpload: function (file) {
            var str = file.name;
            str = str.substring(str.lastIndexOf("\\.") + 1, str.length);
            var reStr = this.selectType(str);
            if (reStr == "NO") {
                this.$message.error('文件格式错误');
                return false;
            } else {
                this.fileType = reStr;
                return true;
            }
        },
        //判断文件类型
        selectType(type) {
            var imageList = ["jpg", "jpeg", "png", "JPG", "PNG"];
            for (var item in imageList) {
                if (imageList[item] == type) {
                    return "IMAGE";
                }
            }
            return "NO";
        },
        choice() {
            this.isShow2 = true
        },
        searchChange2(params, done) {
            this.query = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        searchReset2() {
            this.query = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange2(list) {
            this.selectionList = list;
        },
        currentChange2(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        goodsListOnLoad(page, params = {}) {
            this.goodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.total;
                this.goodsListData = data.records;
                this.goodsListLoading = false;
                this.selectionClear();
            });
        },
        addGoods(row) {
            console.log(row)
            row.addOrEdit = 'add'//新增
            this.form = Object.assign({}, row); // 复制一份，避免直接引用
            this.form.publicId = row.id; // 赋值publicId
            this.isShow2 = false
        },
        updateOpen(row, index) {
            console.log(row)
            // row.addOrEdit = 'edit'//更新
            // this.publicRow = row
            // //食材
            // if (row.type == "1314471996665397249" || row.type == "1366636175794667521") {
            //     this.publicOption.column.forEach(then => {
            //         if (then.prop == 'biddingTypeIdNamePublic' || then.prop == 'netWeightPublic' || then.prop == 'shelfLifePublic' || then.prop == 'productionPublic' ||
            //             then.prop == 'originPublic' || then.prop == 'qualityLevelPublic' || then.prop == 'qualityStandardPublic') {
            //             then.display = true
            //         }
            //     })
            // }
            // //燃料
            // if (row.type == "1316561056074649602" || row.type == "1366636571036516354") {
            //     this.publicOption.column.forEach(then => {
            //         if (then.prop == 'biddingTypeIdNamePublic' || then.prop == 'netWeightPublic' || then.prop == 'shelfLifePublic' || then.prop == 'productionPublic' ||
            //             then.prop == 'originPublic' || then.prop == 'qualityLevelPublic' || then.prop == 'qualityStandardPublic') {
            //             then.display = false
            //         }
            //     })
            // }
            //
            // // this.publicForm.publicId = row.publicId
            // this.publicForm.publicName = row.name
            // //调用编辑表单
            // this.$refs.crud.rowEdit(row, row.$index);
        },
        isNumeric(str) {
            // 移除字符串首尾空白
            if(str==null||str==''||str=='null'||str=='undefined'){
                return false;
            }
            // 空字符串直接返回 false
            if (!str) return false;
            // 使用 parseFloat 转换 + isNaN 检测
            return !isNaN(parseFloat(str)) &&
                    isFinite(str); // 排除 Infinity
        }
    }
};
</script>

<style>
</style>
