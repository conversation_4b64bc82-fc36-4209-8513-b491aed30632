<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="scope">
        <el-button size="mini" type="text" icon="el-icon-upload" @click="download(scope.row)">下载
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/operate/versionManage";
import {mapGetters} from "vuex";
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: true,
        addBtn: true,
        delBtn: false,
        selection: false,
        labelWidth: 150,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "名称",
            prop: "title",
            type: "input",
            rules: [{
              required: true,
              message: "请输入版本名称",
              trigger: "blur"
            }]
          },
          {
            label: "版本名称",
            prop: "versionNoStr",
            type: "input",
            rules: [{
              required: true,
              message: "请输入版本名称，如1.0.0",
              trigger: "blur"
            }],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "版本名称",
            prop: "versionNo",
            type: "input",
            rules: [{
              required: true,
              message: "请输入版本名称，如1.0.0",
              trigger: "blur"
            }],
            hide: true,
          },
          {
            label: "版本号",
            prop: "versionNumber",
            type: "number",
            rules: [{
              required: true,
              message: "请输入版本号",
              trigger: "blur"
            }]
          },
          {
            label: "APK包",
            prop: "apk",
            type: "input",
            // editDisplay: false,
            overHidden: true,
          },
          {
            label: 'APK包',
            prop: 'apkList',
            type: 'upload',
            loadText: 'APK包上传中，请稍等',
            dataType: 'array',
            hide: true,
            limit: 1,
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            // rules: [{
            //   required: true,
            //   message: "请上传APK包再提交",
            //   trigger: "blur"
            // }],
            action: '/api/service/rabbit-resource/oss/endpoint/put-file',
          },
          {
            label: "限定食堂",
            prop: "canteenIdList",
            type: "tree",
            //dicData: [],
            multiple: true,
            dicUrl: "/api/service/rabbit-system/dept/canteen/selectTree",
            props: {
              //res: "data",
              label: "deptName",
              value:"id"
            },
            checkStrictly: true,
            overHidden: true,
            search: true,
          },
          {
            label: "备注",
            prop: "remarks",
            type: "textarea",
            maxlength: 255,
            showWordLimit: true,
            span: 24,
            labelWidth: 150,
          },
          // {
          //   label: "备注",
          //   prop: "remarks",
          //   type: "input",
          //   overHidden: true,
          // },
          {
            label: '创建时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
        ]
      },
      data: []
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    // permissionList() {
    //   return {
    //     addBtn: this.vaildData(this.permission.canteen_manage_add, false),
    //     viewBtn: this.vaildData(this.permission.canteen_manage_view, false),
    //     delBtn: this.vaildData(this.permission.canteen_manage_delete, false),
    //     editBtn: this.vaildData(this.permission.canteen_manage_edit, false)
    //   };
    // }
  },
  methods: {
    // beforeOpen(done, type) {
    //   if (["edit", "view"].includes(type)) {
    //     getDetail(this.form.id).then(res => {
    //       this.form = res.data.data;
    //     });
    //   }
    //   done();
    // },
    rowUpdate(row, index, loading, done) {

      if (row.apkList.length > 0) {
        row.apk = row.apkList[0]
      }

      if (row.apk==undefined || row.apk.length == 0) {
        done();
        return this.$message.error('APK包不能为空');
      }

      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowSave(row, loading, done) {
      if (row.apkList.length > 0) {
        row.apk = row.apkList[0]
      }

      if (row.apk==undefined || row.apk.length == 0) {
        done();
        return this.$message.error('APK包不能为空');
      }

      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log("getList ======== ", res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    download(row) {
      let link = document.createElement("a");
      fetch(row.apk).then((res) => res.blob())
        .then((blob) => {
          link.href = URL.createObjectURL(blob);
          link.download = '';
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        });
    },
  }
}
</script>

<style lang="scss">

</style>
