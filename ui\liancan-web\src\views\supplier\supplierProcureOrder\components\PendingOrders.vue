<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">去处理
                </el-button>
            </template>
        </avue-crud>
        <!-- 去处理待下单 -->
        <!-- 去处理待下单 开始 -->
        <el-dialog title="待下单处理"
                   :visible.sync="handleOrderVisible"
                   :append-to-body="true"
                   width="80%"
                   :close-on-click-modal="false">
            <avue-crud :option="handleOrderOption"
                      :table-loading="orderLoading"
                      :data="handleOrderData"
                      v-model="handleOrderForm"
                      :page="handleOrderPage"
                      ref="handleOrderCrud"
                      @row-update="rowUpdateOrder"
                      @row-save="rowSaveOrder"
                      @search-change="handleOrderSearchChange"
                      @search-reset="handleOrderSearchReset"
                      @selection-change="handleOrderSelectionChange"
                      @current-change="handleOrderCurrentChange"
                      @size-change="handleOrderSizeChange"
                      @on-load="handleOrderOnLoad">
            <template slot="leftMenu">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-user"
                    @click="batchSelectSupplier()">批量选择供应商
                </el-button>
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="small"
                    icon="el-icon-edit"
                    v-if="!row.$cellEdit"
                    @click="rowCellOrder(row,index)"
                >修改</el-button>
                <el-button
                    type="text"
                    size="small"
                    icon="el-icon-check"
                    v-if="row.$cellEdit"
                    @click="rowSaveOrder(row,index)"
                >保存</el-button>
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-check"
                    @click="selectRow(row,index)">选择
                </el-button>
            </template>
            </avue-crud>
            <div slot="footer" class="dialog-footer">
                <el-button @click="handleOrderVisible = false">取消</el-button>
                <el-button type="primary" @click="handleOrderSubmit">保存</el-button>
            </div>
        </el-dialog>
        <!-- 去处理待下单 结束 -->
        <!-- 批量选择供应商 开始 -->
        <el-dialog title="批量选择供应商"
                   :visible.sync="batchSelectSupplierVisible"
                   :append-to-body="true"
                   width="60%"
                   :close-on-click-modal="false">
                <avue-crud :option="batchSelectSupplierOption"
                        :data="batchSelectSupplierData"
                        v-model="batchSelectSupplierForm"
                        :page="batchSelectSupplierPage"
                        ref="batchSelectSupplierCrud"
                        @on-load="batchSelectSupplierOnLoad">
                <template slot="menu" slot-scope="{row,index}">
                    <el-button
                        type="text"
                        size="mini"
                        icon="el-icon-check"
                        @click="supplierSelectRow(row,index)">选择
                    </el-button>
                </template>
                </avue-crud>
            <div slot="footer" class="dialog-footer">
                <el-button @click="batchSelectSupplierVisible = false">取消</el-button>
                <!-- <el-button type="primary" @click="batchSelectSupplierSubmit">确定</el-button> -->
            </div>
        </el-dialog>
        <!-- 批量选择供应商 结束 -->
    </basic-container>
</template>

<script>
import {getList,getDetail,saveOrder} from "@/api/supplier/supplierPendingOrder";
import {mapGetters} from "vuex";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";

export default {
    emits: ['on-load'],
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            orderLoading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                searchMenuSpan: 4, //搜索按钮长度
                // searchSpan:24,      //搜索框长度  最大长度24
                // searchLabelWidth: 120, //搜索框标题宽度 默认80
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                printBtn:false,
                // 其他配置...
                exportBtn: true, // 启用导出按钮
                excelExport: true, // 启用Excel导出
                csvExport: true, // 启用CSV导出
                selection: true,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    //单据类型
                    {
                        label: '单据类型',
                        prop: 'billType',
                        type: 'select',
                        search: true,
                        dicData: [
                            {
                                label: '销售订单',
                                value: 1
                            }
                        ],
                        rules: [{
                            required: true,
                            message: '请选择单据类型',
                            trigger: 'blur'
                        }],
                    },
                    //收单时间
                    {
                        label: "收单时间",
                        prop: "createTime",
                        type: "date",
                        format: 'yyyy-MM-dd HH:mm:ss',
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },

                    //订单号
                    {
                        label: "订单号",
                        prop: "code",
                        type: "input",
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    //指定收货方
                    {
                        label: "指定收货方",
                        prop: "receiveDeptName",
                        type: "input",
                        search: true,
                        rules: [{
                            required: false,
                            message: "请输入收货方",
                            trigger: "blur"
                        }],
                    },
                    //收货方食堂
                    {
                        label: "收货方食堂",
                        prop: "receiveCanteenId",
                        type: "select",
                        search: true,
                        dicUrl: "/api/service/rabbit-supplier/canteen-list",
                        props: {
                            label: "canteenName",
                            value: "id"
                        },
                        rules: [{
                            required: false,
                            message: "请选择收货方食堂",
                            trigger: "blur"
                        }],
                    },
                    //收货方联系人
                    {
                        label: '收货方联系人',
                        prop: 'receiveContactId',
                        type:'select',
                        search: false,
                        hide: true,
                        dicUrl: `/api/rabbit-supplier/user-list`,
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        multiple:true
                    },
                    //收货方联系电话
                    {
                        label: '收货方联系电话',
                        prop: 'receiveContactPhone',
                        type: 'input',
                    },
                    //要求送货时间
                    {
                        label: '要求送货时间',
                        prop: 'sendTime',
                        type: 'date',
                    },
                    //要求送货地址
                    {
                        label: '要求送货地址',
                        prop: 'address',
                        type: 'input',
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                ]
            },
            data: [],
            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },

            approveVisible: false,
            handleOrderVisible: false,
            rowOrder: {},
            handleOrderPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            handleOrderOption: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                labelWidth: 120,
                menuWidth: 180,
                tip: false,
                border: true,
                index: true,
                selection: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                printBtn: false,
                align: 'center',
                column: [
                    {
                        label: '商品子项编码',
                        prop: 'goodCode',
                        type: 'input',
                        disabled: true
                    },
                    {
                        label: '商品子项名称',
                        prop: 'name',
                        type: 'input',
                        disabled: true
                    },
                    {
                        label: '计量单位',
                        prop: 'unit',
                        type: 'input',
                        disabled: true
                    },
                    {
                        label: '下单数量',
                        prop: 'qty',
                        type: 'input',
                        disabled: true
                    },
                    {
                        label: '销售单价',
                        prop: 'price',
                        type: 'input',
                        disabled: true
                    },
                    {
                        label: '采购单价',
                        prop: 'salePrice',
                        type: 'input',
                        cell: true,
                    },
                    {
                        label: '下单原因',
                        prop: 'reason',
                        type: 'textarea'
                    },
                    {
                        label: '本次指定供应商',
                        prop: 'supplierCustomerName',
                        type: 'input',
                        width: 180,
                        dicData: []
                    }
                ]
            },
            handleOrderData: [],
            handleOrderForm: {},

            batchSelectSupplierVisible: false,
            batchSelectSupplierOption: {
                height: '350px',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: false,
                searchMenuSpan: 4,
                labelWidth: 120,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                selection: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                printBtn: false,
                menu: true,
                align: "center",
                column: [
                    {
                        label: "单位名称",
                        prop: "customerName",
                        type: "input"
                    },
                    {
                        label: "单位简称",
                        prop: "customerName",
                        type: "input"
                    },
                    {
                        label: "社会信用代码",
                        prop: "regNum",
                        type: "input"
                    },
                    {
                        label: "联系人",
                        prop: "contactsName",
                        type: "input"
                    },
                    {
                        label: "联系电话",
                        prop: "mobile",
                        type: "input"
                    },
                    {
                        label: "备注",
                        prop: "remarks",
                        type: "textarea"
                    }
                ]
            },
            batchSelectSupplierData: [],
            batchSelectSupplierForm: {},
            batchSelectSupplierPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "orderData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt','haveOrderqty','notOrderqty'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.orderData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },
        batchSelectSupplier() {
            this.batchSelectSupplierVisible = true;
        },
        //打开查看详情窗口
        viewRow(row,index) {
            this.editType = 'view';
            this.form = row;
            this.handleOrderOnLoad(this.orderPage);
            this.handleOrderVisible = true;
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
                // 回调父类的方法
                this.$emit('load-complete', params);
                console.log("load-complete=============>1");
            });
        },
        selectRow(row,index) {
            this.batchSelectSupplierVisible = true;
            this.handleOrderForm = row;
        },
        batchSelectSupplierSubmit() {
            this.batchSelectSupplierVisible = false;
        },
        handleOrderSubmit() {
            //判断handleOrderData是否选择了供应商
            if(!this.handleOrderData || this.handleOrderData.length === 0) {
                this.$message.error('订单数据不能为空!');
                return;
            }
            //判断handleOrderData是否选择了供应商
            let emptySupplierItems = this.handleOrderData.filter(item => !item.supplierCustomerId || item.supplierCustomerId == '' || item.supplierCustomerId == null || item.supplierCustomerId == undefined || item.supplierCustomerId == 0);
            if(emptySupplierItems.length > 0) {
                let emptySupplierNames = emptySupplierItems.map(item => item.name).join('、');
                this.$message.error(`以下商品未选择供应商: ${emptySupplierNames}`);
                return;
            }
            //判断是否填写了采购单价
            let emptyPriceItems = this.handleOrderData.filter(item => !item.salePrice || item.salePrice == '' || item.salePrice == null || item.salePrice == undefined || item.salePrice == 0);
            if(emptyPriceItems.length > 0) {
                let emptyPriceNames = emptyPriceItems.map(item => item.name).join('、');
                this.$message.error(`以下商品未填写采购单价: ${emptyPriceNames}`);
                return;
            }
            //判断采购单价是否高于销售单价
            let priceHigherThanSalePriceItems = this.handleOrderData.filter(item => item.price < item.salePrice);
            if(priceHigherThanSalePriceItems.length > 0) {
                let priceHigherThanSalePriceNames = priceHigherThanSalePriceItems.map(item => item.name).join('、');
                this.$message.error(`以下商品采购单价高于销售单价: ${priceHigherThanSalePriceNames}`);
                return;
            }

            this.$confirm('再次提醒：供应商接单后将会直接把商品送到客户指定的收货地址，本单位不需要进行商品入库和出库操作。确定保存并按供应商分别创建采购订单？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 确认提交的逻辑
                this.form.detailList = this.handleOrderData;
                saveOrder(this.form).then(res=>{
                    if(res.data.code == 200) {
                        this.$message.error('保存成功！如果订单需要审核，请及时通知审核人员，以便影响工作推进！');
                        this.onLoad(this.page);
                    }else{
                        this.$message({
                            type: 'error',
                            message: '提交失败!'
                        });
                    }
                    this.handleOrderVisible = false;
                });
            }).catch(() => {
                // 取消提交的逻辑
                this.$message({
                    type: 'info',
                    message: '已取消提交'
                });
                this.handleOrderVisible = false;
            });
        },
        handleOrderOnLoad(params = {}) {
            this.orderLoading = true;
            params.isSupplyFlag = 1
            getDetail(this.form.id).then(res => {
                const data = res.data.data;
                this.handleOrderData = data;
                this.handleOrderPage.total = this.handleOrderData.length;
                this.orderLoading = false;
                this.handleOrderSelectionClear();
                // 回调父类的方法
                this.$emit('on-load', params);
                console.log("on-load=============>2");
            });

        },
        handleOrderSelectionClear() {
            this.handleOrderSelectionList = [];
            this.$refs.handleOrderCrud.toggleSelection();
        },
        handleOrderCurrentChange(currentPage) {
            this.handleOrderPage.currentPage = currentPage;
        },
        handleOrderSizeChange(pageSize) {
            this.handleOrderPage.pageSize = pageSize;
        },
        handleOrderSearchChange(params, done) {
            this.handleOrderQuery = params;
            this.handleOrderOnLoad(this.handleOrderPage);
            done();
        },
        handleOrderSearchReset() {
            this.handleOrderQuery = {};
            this.handleOrderOnLoad(this.handleOrderPage);
        },
        handleOrderSelectionChange(list) {
            this.handleOrderSelectionList = list;
        },
        //供应商选择行
        batchSelectSupplierOnLoad() {
            getSupplierCustomerList()
                .then(res => {
                    console.log("res=============>",res);
                    if(res.data.code == 200) {
                        const filteredData = res.data.data.filter(item => item.customerNature != 6);
                        this.batchSelectSupplierData = filteredData;
                        this.batchSelectSupplierPage.total = this.batchSelectSupplierData.length;
                    }
                }).catch(err=>{}).finally(fin=>{this.loading = false;});
        },
        supplierSelectRow(row,index) {
            console.log("row=============>",row);
            this.batchSelectSupplierForm = row;
            this.batchSelectSupplierVisible = false;
            //更新商品明细
            this.handleOrderData.forEach(item=>{
                if(item.id == this.handleOrderForm.id) {
                    item.supplierCustomerName = row.customerName;
                    item.supplierCustomerId = row.id;
                }
            });
        },
        rowUpdateOrder(row, index, loading, done) {
            done()
        },
        rowCellOrder(row, index) {
            this.rowOrder= row;
            this.$refs.handleOrderCrud.rowCell(row, index)
        },
        rowSaveOrder(row, index) {
            //修改orderData对应的row
            this.handleOrderData[index] = row;
            row.$cellEdit = false;
            this.$refs.handleOrderCrud.rowCellUpdate();
        },
        //主界面基本方法 end
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
