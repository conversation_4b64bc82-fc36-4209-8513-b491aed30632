<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <!--自定义新增按钮-->
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="success"  icon="el-icon-coordinate" size="small" @click="auditOrderHandler" >审核</el-button>
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">审核
                </el-button>
            </template>
        </avue-crud>
        <!-- 打开新增页面 开始 -->
        <el-dialog title="新增"
                   :visible.sync="isShowOrder"
                   v-if="isShowOrder"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm" width="60%"  style="height: 90%;">
                <el-form ref="formAdd" label-width="80px">
                    <el-form-item label="单据日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-date-picker type="date"
                                            placeholder="选择日期"
                                            :picker-options="pickerOptions"
                                            v-model="businessDate"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="供应商" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-select v-model="supplierCustomerId" placeholder="请选择"  @change="selectCustomerHandler" style="width: 100%;">
                                <el-option
                                    v-for="item in supplierList"
                                    :key="item.companyCompanyId"
                                    :label="item.customerName"
                                    :value="item.companyCompanyId">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="联系人" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
<!--                            <el-select v-model="salesman" placeholder="请选择" style="width: 100%;">-->
<!--                                <el-option-->
<!--                                    v-for="item in salesmanList"-->
<!--                                    :key="item.id"-->
<!--                                    :label="item.contactName"-->
<!--                                    :value="item.id">-->
<!--                                </el-option>-->
<!--                            </el-select>-->
                            <el-input v-model="salesmanName" placeholder="请填写联系人" style="width: 100%;">
                            </el-input>
                        </el-col>
                        <el-col :span="4"></el-col>
                        <el-col :span="3">联系电话</el-col>
                        <el-col :span="6">
                            <el-input v-model="phone" placeholder="请填写联系电话" style="width: 100%;">
                            </el-input>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="配送日期" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-date-picker type="date"
                                            placeholder="选择日期"
                                            :picker-options="pickerOptions"
                                            v-model="sendTime"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="配送地址" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="19">
                            <el-input v-model="site" placeholder="请填写配送地址" style="width: 100%;">
                            </el-input>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="订单固定流程" prop="region">
                        <el-col :span="6">
                            <el-select v-model="orderProcessType" placeholder="请选择" style="width: 100%;">
                                <el-option
                                    v-for="item in DIC.orderProcessType"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="订单商品" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="3">
                            <el-button type="primary" size="small" @click="selectGoodHandler">从商品库中选择</el-button>
                        </el-col>
                    </el-form-item>
                </el-form>
                <div style="height: 200px;overflow-y: scroll;">
                    <avue-crud :option="orderOption"
                               :table-loading="orderLoading"
                               :data="orderData"
                               :page="orderPage"
                               :before-open="beforeOpenOrder"
                               v-model="orderForm"
                               ref="crudOrder"
                               @row-update="rowUpdateOrder"
                               @row-save="rowSaveOrder"
                               @row-del="rowDelOrder"
                               @search-change="searchChangeOrder"
                               @search-reset="searchResetOrder"
                               @selection-change="selectionChangeOrder"
                               @current-change="currentChangeOrder"
                               @size-change="sizeChangeOrder"
                               @cell-click="handleRowClick"
                               @on-load="orderOnLoad">
                        <template slot="menu" slot-scope="{row,index}">
                            <el-button
                                type="text"
                                size="small"
                                icon="el-icon-edit"
                                v-if="!row.$cellEdit"
                                @click="rowCellOrder(row,index)"
                            >修改</el-button>
                            <el-button
                                type="text"
                                size="small"
                                icon="el-icon-check"
                                v-if="row.$cellEdit"
                                @click="rowSaveOrder(row,index)"
                            >保存</el-button>
                            <el-button
                                type="text"
                                size="mini"
                                icon="el-icon-delete"
                                @click="deleteRowOrder(row)">删除
                            </el-button>
                        </template>
                    </avue-crud>
                </div>
                <div>
                    订单总额:{{totalAmt}}元
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddForm">取消</el-button>
                    <el-button type="primary" @click="saveSelectHandle">保存</el-button>
                </span>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 公共商品选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowSelectGoods"
                   v-if="isShowSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeSelectForm" width="60%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crudSelectGoods"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 商品选择 结束 -->
        <!-- 打开详情页面 开始 -->
        <el-dialog title="详情"
                   :visible.sync="isShowOrderDetail"
                   v-if="isShowOrderDetail"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeShowForm" width="60%"  style="height: 90%;">
            <el-row type="flex" class="row-bg" justify="right">
                <el-col :span="24" style="height: 100%;" align="right">
                    <el-button type="primary" icon="el-icon-printer" size="small" v-print="print" >打印</el-button>
                </el-col>
            </el-row>
            <div id="printArea">
                <el-row type="flex" class="row-bg" justify="left" style="height: 60px;">
                    <el-col :span="24">
                        <div style="width: 100%;height:100%;text-align:center;"><h1>采购订单</h1></div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left" style="margin-top: 45px;">
                    <el-col :span="2">
                        <div class="head-label">单据日期</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.businessDate}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">供应商</div>
                    </el-col>
                    <el-col :span="4">
                        <div>{{this.form.supplierCustomerName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">业务员</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.salesmanName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">单据号</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.code}}</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">登记人</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.createUserName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">登记时间</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.createTime}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label"></div>
                    </el-col>
                    <el-col :span="3">
                        <div></div>
                    </el-col>

                    <el-col :span="2">
                        <div></div>
                    </el-col>
                    <el-col :span="3">
                        <div></div>
                    </el-col>
                </el-row>
                <div style="overflow-y: scroll;margin-top: 20px;">
                    <el-table
                        :data="orderData"
                        stripe
                        border
                        fit
                        show-summary
                        :header-cell-style="tableHeaderStyle"
                        :row-style="tableDetailStyle"
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column property="rowId" label="序号" align="center"></el-table-column>
                        <el-table-column property="name" label="商品名称" align="center"></el-table-column>
                        <el-table-column property="unitName" label="计量单位" align="center"></el-table-column>
                        <el-table-column property="warehouseName" label="入库仓库" align="center"></el-table-column>
                        <el-table-column property="qty" label="数量" align="center"></el-table-column>
                        <el-table-column property="price" label="单价" align="center"></el-table-column>
                        <el-table-column property="amt" label="金额" align="center"></el-table-column>
                        <el-table-column property="haveOrderqty" label="已入库数量" align="center"></el-table-column>
                        <el-table-column property="notOrderqty" label="未入库数量" align="center"></el-table-column>
                    </el-table>
                </div>
<!--                订单审核信息-->
                <div style="margin-top: 45px;"></div>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">审核订单</div>
                    </el-col>
                </el-row>
                <el-row v-for="item in this.form.orderAuditStatusList" :key="item" type="flex" class="row-bg" justify="left">
<!--                    状态-->
                    <el-col :span="4">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
<!--                    审核人-->
                    <el-col :span="4">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
<!--                    越梦雨-->
                    <el-col :span="4">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
<!--                    操作时间-->
                    <el-col :span="12">
                        <div>操作时间:&nbsp&nbsp{{this.form.createTime}}</div>
                    </el-col>
                </el-row>
<!--                供应商接单-->
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">供应商接单</div>
                    </el-col>
                </el-row>
                <el-row v-for="item in this.form.orderReceiveStatusList" :key="item" type="flex" class="row-bg" justify="left">
                    <!--                    状态-->
                    <el-col :span="4">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
                    <!--                    审核人-->
                    <el-col :span="4">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
                    <!--                    越梦雨-->
                    <el-col :span="4">
                        <div>{{this.form.createTime}}</div>
                    </el-col>
                    <!--                    操作时间-->
                    <el-col :span="12">
                        <div>操作时间:&nbsp&nbsp{{this.form.createTime}}</div>
                    </el-col>
                </el-row>
<!--                商品入库:根据入库状态显示不同内容，0未入库，不显示列表；部分入库、全部入库，显示列表-->
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">商品入库</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <!--                    入库状态-->
                    <el-col :span="4">
                        <div>未入库</div>
                    </el-col>
                    <!--                    审核人-->
                    <el-col :span="4">
                        <div>关联采购入库单</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-table
                        :data="haveOrderData"
                        stripe
                        border
                        fit
                        show-summary
                        :header-cell-style="tableHeaderStyle"
                        :row-style="tableDetailStyle"
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column property="name" label="单据日期" align="center"></el-table-column>
                        <el-table-column property="name" label="单据号" align="center"></el-table-column>
                        <el-table-column property="unit" label="供应商" align="center"></el-table-column>
                        <el-table-column property="name" label="采购名称" align="center"></el-table-column>
                        <el-table-column property="amt" label="采购总额" align="center"></el-table-column>
                        <el-table-column property="warehouseName" label="业务员" align="center"></el-table-column>
                        <el-table-column property="qty" label="入库仓库" align="center"></el-table-column>
                        <el-table-column property="price" label="登记人" align="center"></el-table-column>
                        <el-table-column property="price" label="登记时间" align="center"></el-table-column>
                    </el-table>
                </el-row>
            </div>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 审核 开始 -->
        <el-dialog title="审核" :visible.sync="approveVisible" :append-to-body="true" width="60%">
            <div style="margin-bottom: 20px;">
                <el-alert
                    title="重要提示"
                    type="warning"
                    description="如果采购订单选择使用订单固定处理流程，且供应商是系统内的使用单位，订单审核通过后将会直接发给供应商，供应商必须在本系统内进行接单、出库、配送等操作。"
                    show-icon
                    :closable="false">
                </el-alert>
            </div>
            <div style="margin-top: 20px;text-align: right;">
                <el-button type="primary" icon="el-icon-check" size="small" @click="reviewCheck(1)">通过</el-button>
                <el-button type="danger" icon="el-icon-circle-close" size="small" @click="reviewCheck(-1)">不通过</el-button>
                <el-button type="primary" icon="el-icon-circle-close" size="small" plain @click="chanel()">取消</el-button>
            </div>
        </el-dialog>
        <!-- 审核 结束 -->
    </basic-container>
</template>

<script>
import {
    getList,
    getDetail,
    add,
    update,
    remove,
    exportData,
    approve,
    close,
    open,
    stockin,
    rebackOrder,
} from "@/api/supplier/supplierProcureOrder";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";
import {reviewCheck} from "@/api/liancan2/expenditureCheck";

const DIC = {
    //供应商来源
    supplierSourceType: [
        {
            label: '自行新增',
            value: '0'
        },
        {
            label: '系统',
            value: '1'
        },
    ],
    //订单固定流程
    orderProcessType: [
        {
            label: '不用',
            value: 0
        },
        {
            label: '使用',
            value: 1
        },
    ],
    //固定处理流程进展
    orderProcess: [
        {
            label: '-',
            value: 0
        },
        {
            label: '供应商未接单',
            value: 1
        },
        {
            label: '取消/拒单',
            value: 2
        },
        {
            label: '供应商接单备货',
            value: 3
        },
        {
            label: '供应商登记出库',
            value: 4
        },
        {
            label: '供应商配送中',
            value: 5
        },
        {
            label: '商品已送达',
            value: 6
        },
        {
            label: '收货结束',
            value: 7
        },
    ],

    //订单状态（全部、未关闭、手动关闭、已关闭）
    orderCloseStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未关闭',
            value: 0
        },
        {
            label: '手动关闭',
            value: -2
        },
        {
            label: '已关闭',
            value: -1
        },
    ],
    //订单审核（全部、未审核、通过、不通过）
    orderAuditStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未审核',
            value: 0
        },
        {
            label: '通过',
            value: 1
        },
        {
            label: '不通过',
            value: -1
        },
    ],
    //订单状态（全部、未接单、已接单、取消/拒单、配送中、已送达、已收货）
    // 0-未接单 1-食堂已收 2-取消/拒单 3-已送达 4-配送中 5已出库 6接单备货 7已出库 9收货结束
    orderStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未接单',
            value: 0
        },
        {
            label: '已接单',
            value: 1
        },
        {
            label: '取消/拒单',
            value: -1
        },
        {
            label: '配送中',
            value: 2
        },
        {
            label: '已送达',
            value: 3
        },
        {
            label: '已收货',
            value: 4
        },
    ],
    //订单执行（全部、未入库、部分入库、全部入库）
    orderDealStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未入库',
            value: 0
        },
        {
            label: '部分入库',
            value: 2
        },
        {
            label: '全部入库',
            value: 1
        },
    ],
}
export default {
    emits: ['on-load'],
    data() {
        return {
            DIC: DIC, // 将 DIC 对象暴露给组件实例，使其在模板中可通过 this.DIC 访问
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                searchMenuSpan: 4, //搜索按钮长度
                labelWidth: 180,
                menuWidth: 100,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                printBtn:false,
                // 其他配置...
                exportBtn: true, // 启用导出按钮
                excelExport: true, // 启用Excel导出
                csvExport: true, // 启用CSV导出
                selection: true,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                    {
                        label: "订单号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: '交付类型',
                        prop: 'deliveryType',
                        type: 'select',
                        search: true,
                        dicData: [
                            {
                                label: '传统入库',
                                value: 0
                            },
                            {
                                label: '直发客户',
                                value: 1
                            }
                        ],
                        rules: [{
                            required: true,
                            message: '请选择交付类型',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    {
                        label: '供应商',
                        prop: 'supplierCustomerId',
                        type: 'select',
                        search: true,
                        props: {
                            label: 'customerName',
                            value: 'companyCompanyId'
                        },
                        dicUrl: '/api/service/rabbit-supplier/customer-list',
                        rules: [
                            {
                                required: true,
                                message: '请选择供应商',
                                trigger: 'blur'
                            }
                        ]
                    },
                    //订单审核
                    {
                        label: '订单审核',
                        prop: 'orderApproveStatus',
                        type: 'select',
                        dicData: [
                            {
                                label: '待审核',
                                value: 0
                            },
                            {
                                label: '已通过',
                                value: 1
                            },
                            {
                                label: '未通过',
                                value: 2
                            }
                        ],
                        rules: [{
                            required: false,
                            message: '请选择订单审核状态',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    //订单流程
                    {
                        label: '订单流程',
                        prop: 'orderFlow',
                        type: 'select',
                        dicData: [
                            {
                                label: '不用',
                                value: 0
                            },
                            {
                                label: '使用',
                                value: 1
                            }
                        ],
                        rules: [{
                            required: false,
                            message: '请选择订单流程',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    //订单进展
                    {
                        label: '订单进展',
                        prop: 'orderStatus',
                        type: 'select',
                        dicData: [
                            {
                                label: '未接单',
                                value: '0'
                            },
                            {
                                label: '食堂已收',
                                value: '1'
                            },
                            {
                                label: '取消/拒单',
                                value: '2'
                            },
                            {
                                label: '已送达',
                                value: '3'
                            },
                            {
                                label: '配送中',
                                value: '4'
                            },
                            {
                                label: '已出库',
                                value: '5'
                            },
                            {
                                label: '接单备货',
                                value: '6'
                            },
                            {
                                label: '收货结束',
                                value: '7'
                            }
                        ],
                        rules: [{
                            required: false,
                            message: '请选择订单进展',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    //订单执行
                    {
                        label: '订单执行',
                        prop: 'orderDealStatus',
                        type: 'select',
                        dicData: DIC.orderDealStatus,
                        rules: [{
                            required: true,
                            message: '请选择订单执行状态',
                            trigger: 'blur'
                        }],
                        width: 80,
                    },
                    //订单关闭
                    {
                        label: '订单关闭',
                        prop: 'orderCloseStatus',
                        type: 'select',
                        dicData: DIC.orderCloseStatus,
                        rules: [{
                            required: true,
                            message: '请选择订单关闭状态',
                            trigger: 'blur'
                        }],
                        width: 80,
                    },

                    {
                        label: "订单总额",
                        prop: "amt",
                        type: "number",
                        labelWidth: 140,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: false,
                            message: "请输入订单总额",
                            trigger: "blur"
                        } ],
                        precision: 2,
                    },
                    {
                        label: "已执行金额",
                        prop: "amtHaveExecute",
                        type: "number",
                        labelWidth: 140,
                        width: 120,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: false,
                            message: "请输入已执行金额",
                            trigger: "blur"
                        } ],
                        precision: 2,
                    },
                    {
                        label: "未执行金额",
                        prop: "amtNotExecute",
                        type: "number",
                        labelWidth: 140,
                        width: 120,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: false,
                            message: "请输入未执行金额",
                            trigger: "blur"
                        } ],
                        precision: 2,
                    },
                    {
                        label: "登记人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "登记时间",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                    },
                ]
            },
            data: [],
            isShowOrder: false,
            isShowSelectGoods: false,
            isShowSelectOrder: false,

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },
            approveVisible: false,
            supplierList: [],
            salesmanList: [],

        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "orderData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt','haveOrderqty','notOrderqty'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.orderData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },

        //审核
        auditOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.approveVisible = true;
        },

        //打印
        printOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.form = this.selectionList[this.selectionList.length - 1];
            this.viewRow(this.form,0);
        },
        //关闭新增窗口
        closeAddForm() {
            if (this.orderData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowOrder = false;
                    });
            }else{
                this.isShowOrder = false;
            }
        },
        //关闭选择商品窗口
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        //关闭显示订单窗口
        closeShowForm(){
            this.isShowOrderDetail = false;
        },
        //打开查看详情窗口
        viewRow(row,index) {
            console.log("row",row);
            this.editType = 'approve';
            this.form = row;
            this.selectionList = [];
            this.selectionList.push(row);
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.approveVisible = true;
        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        rowSave(row, loading, done) {
            console.log(row)
            add(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                this.$router.go(0)
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {

            console.log(row)
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {

                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1;
            // params.orderStatus = '0';
            params.orderApproveStatus = '0';
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
                // 回调父类的方法
                this.$emit('load-complete', params);
                console.log("load-complete=============>1");
            });
        },
        //主界面基本方法 end

        //导出
        exportExcel(){
            exportData(Object.assign(this.query)).then(res => {
                if (!res.data) {
                    return;
                }
                const blob = new Blob([res.data], {
                    type: "application/vnd.ms-excel",
                }); // 构造一个blob对象来处理数据，并设置文件类型
                const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
                const a = document.createElement("a"); //创建a标签
                a.style.display = "none";
                a.href = href; // 指定下载链接
                let fileName = res.headers["content-disposition"];
                fileName = fileName.split("=")[1];
                a.download = decodeURIComponent(fileName); //指定下载文件名
                a.click(); //触发下载
                URL.revokeObjectURL(a.href); //释放URL对象
            });
        },

        //审核
        reviewCheck(status) {
            var message = '';
            if (status == -1) {
                message = '审核【不通过】的采购订单，将被终止后续操作。本操作不可撤销恢复，确定审核【不通过】？';
            } else {
                message = '确定审核【通过】该采购订单？';
            }
            this.$confirm(message, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                return approve(this.ids, status);
            }).then(data => {
                if (data.data.success) {
                    this.$message({
                        type: "success",
                        message: "操作成功"
                    });
                    this.chanel();
                    this.onLoad(this.page);
                } else {
                    this.$message({
                        showClose: true,
                        message: "操作失败",
                        type: 'error'
                    })
                }
            })
        },
        //取消
        chanel(){
            this.approveVisible = false;
        },
        selectCustomerHandler(val){
            console.log("======>",val);
            this.supplierList.forEach(e=>{
                if(e.id == val){
                    this.salesmanName = e.contactsName;
                    this.phone = e.mobile;
                }
            })
        }
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
