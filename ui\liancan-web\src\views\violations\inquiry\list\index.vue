<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="regulationForm"
               ref="regulationForm"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="{row}">
        <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="openWarning(row,'2')">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="replyOpen(row)">回复
        </el-button>
        <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="checkReply(row)">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="checkNanagement(row)">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '5'" size="mini" type="text" @click="checkFile(row)">查看
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="查看" :visible.sync="warningDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">预警日期：</span>{{this.warningDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">当前状态：</span>{{this.inquiryStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;">办理单位：{{this.handlingUnit}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规单位：</span>{{this.deptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">单位类型：</span>{{this.unitType}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规分类：</span>{{this.category}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规内容：</span>{{this.content}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规取证内容：</span>{{this.evidence}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType == '2'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">已发函询</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询日期：</span>{{this.inquiryDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询单位：</span>{{this.inquiryDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询人姓名：</span>{{this.pushInquerUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询内容：</span><a href="javascript:void(0);" style="color: #1e9fff" @click="openMakeInquiry">点此查看</a></div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType3 == '3'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">食堂回复</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复日期：</span>{{this.replyTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复单位：</span>{{this.replyDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复人姓名：</span>{{this.replyName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复内容：</span>{{this.replyContent}}</div>
        </el-col>
        <el-col :span="24">
          <div>图片:</div>
          <span v-for="(item,index) in this.fileList">
        <img :src="item.url" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item.url)" class="avatar">
        </span>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType4 == '4'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">处置信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置日期：</span>{{this.handleDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置单位：</span>{{this.handleDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置人姓名：</span>{{this.handleUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置意见：</span>{{this.handleContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType5 == '5'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">归档信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档日期：</span>{{this.fileDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档单位：</span>{{this.fileDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档人姓名：</span>{{this.fileUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档意见：</span>{{this.fileContent}}</div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :visible.sync="otherWarningVisible2" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <div style=" text-align: center;font-size: 26px;margin-bottom: 10px;">关于{{this.warningName1}}涉嫌其他违规的函询</div>
      <div style=" text-align: center;">函询编号：{{this.inquiryNo}}</div>
      <el-row>
        <el-col :span="25">
          <basic-container>
            <div style="font-size: 20px;">{{this.warningName1}}相关负责人：</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">经我单位监管，贵单位可能存在以下违规行为，特此函询，请据实说明情况。</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规发现日期：{{this.warningDate}}</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规分类：{{this.category}}</div>
            <div style="font-size: 20px;margin-left: 40px;margin-bottom: 20px;margin-top: 20px;">违规内容：{{this.warningContent}}</div>
            <div>
              <div><span style="font-size: 20px;margin-left: 40px;">以下为违规取证材料：{{this.evidence}}</span></div>
            </div>
          </basic-container>
        </el-col>
      </el-row>
      <el-row>
        <span style="font-size: 20px;float: right">{{this.jiweiDeptName}}<br/>{{this.jiweiDate}}</span>
        <br/>
        <span style="font-size: 15px;float: left;margin-top: 20px;">如有疑问可联系负责人：{{this.jiweiUserName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;联系电话：{{this.jiweiAdminAccount}}</span>
      </el-row>
      <br/>
      <br/>
      <br/>
      <br/>
      <br/>
    </el-dialog>
    <el-dialog :title="`回复`" :visible.sync="replyVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="replyForm" :option="replyOption" v-model="replyForm" @submit="saveReply" :upload-preview="uploadPreview"
                 :upload-error="uploadError" :upload-delete="uploadDelete" :upload-before="uploadBefore" :upload-after="uploadAfter">
      </avue-form>
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="40%" height="30%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getWarningList,getWarningById,saveInquiry,getInquiryById,saveArchivist,getSchoolInquiryList,saveReply} from "@/api/liancan/illegalWarnLog";
  import {setOption} from "@/api/liancan2/common";
  import {mapGetters} from "vuex";
  import AvueUeditor from 'avue-plugin-ueditor';
  export default {
    data() {
      return {
        regulationForm: {},
        query: {},
        warningListForm:{},
        warningRecordForm:{},
        warningRecordForm1:{},
        warningRecordForm2:{},
        replyForm:{},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        warningListPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        warningListData:[],
        uploadFilesList:[],
        warningListVisible:false,
        warningRecordVisible:false,
        warningRecordVisible1:false,
        warningRecordVisible2:false,
        otherWarningVisible:false,
        replyVisible:false,
        otherWarningSuccessVisible:false,
        warningDetailsVisible:false,
        otherWarningVisible2:false,
        dialogVisible:false,
        warningListloading:true,
        warningName:undefined,
        deptId:undefined,
        warningName1:undefined,
        warningName2:undefined,
        jiweiDeptName:undefined,
        jiweiDate:undefined,
        jiweiUserName:undefined,
        jiweiAdminAccount:undefined,
        inquiryTime:undefined,
        archives:undefined,
        archivistName:undefined,
        archivistDate:undefined,
        warningId:undefined,
        replyContent:undefined,
        replyDate:undefined,
        replyUserName:undefined,
        schoolType:undefined,
        canteenName:undefined,
        adminName:undefined,
        phone:undefined,
        warningDate:undefined,
        inquiryStatus:undefined,
        category:undefined,
        handlingUnit:undefined,
        schoolName:undefined,
        content:undefined,
        evidence:undefined,
        warningType:undefined,
        warningType3:undefined,
        warningType4:undefined,
        inquiryDate:undefined,
        inquiryDeptName:undefined,
        pushInquerUserName:undefined,
        inquiryName:undefined,
        replyName:undefined,
        replyTime:undefined,
        replyDeptName:undefined,
        fileUserName:undefined,
        fileContent:undefined,
        fileDate:undefined,
        fileDeptName:undefined,
        deptName:undefined,
        unitType:undefined,
        dialogImageUrl:undefined,
        fileList:[],
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          addBtn:false,
          editBtn:false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "所属食堂",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
              props: {
                label: "deptName",
                value:"id"
              },
              hide: true,
              addDisplay: false,
              search:true,
            },
            {
              label: "所属食堂",
              prop: "deptName",
              type: "input",
            },
            {
              label: '预警日期',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              span: 24,
              align: 'center',

            },
            {
              label: "违规分类",
              prop: "category",
              type: "select",
              search:true,
              align: 'center',
              dicData: [
                {
                  label: "收入违规",
                  value: "0"
                },
                {
                  label: "支出违规",
                  value: "1"
                },
                {
                  label: "采购违规",
                  value: "2"
                },
                {
                  label: "财务违规",
                  value: "3"
                },
                {
                  label: "招标违规",
                  value: "4"
                },
                {
                  label: "仓管违规",
                  value: "5"
                }
              ],

            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              width:600,
            },
            {
              label: "当前状态",
              prop: "inquiryStatus",
              type: "select",
              width:90,
              search:true,
              align: 'center',
              dicData: [
                {
                  label: "待函询",
                  value: "1"
                },
                {
                  label: "待食堂回复",
                  value: "2"
                },
                {
                  label: "待处置",
                  value: "3"
                },
                {
                  label: "待归档",
                  value: "4"
                },
                {
                  label: "已归档",
                  value: "5"
                }
              ],
            },
    /*        {
              label: "办理单位",
              prop: "handlingUnit",
              type: "input",
              width:150,
            },*/
          ]
        },
        WarningOption: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "违规预警类型",
              prop: "type",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入类型",
                trigger: "blur"
              }],
            },
            {
              label: '违规时间',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              span: 24
            },
            {
              label: "违规单位",
              prop: "deptName",
              type: "input",
            },
            {
              label: "单位管理员姓名",
              prop: "userName",
              type: "input",
            },
            {
              label: "手机号(账号)",
              prop: "userPhone",
              type: "input",
            },
          ]
        },
        warningRecordOption:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              display:false,
            },
            {
              label: "违规预警记录编号",
              prop: "inquiryNo",
              type: "input",
              disabled:true,
            },
            {
              label: "违规单位",
              prop: "deptName",
              type: "input",
              disabled:true,
            },
            {
              label: "违规类型",
              prop: "type",
              type: "select",
              disabled:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入类型",
                trigger: "blur"
              }],
            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              disabled:true,
            },
            {
              label: '违规时间',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: '违规发现时间',
              prop: 'findDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: "单位管理员姓名",
              prop: "userName",
              type: "input",
              disabled:true,
            },
            {
              label: "管理员手机号",
              prop: "userPhone",
              type: "input",
              disabled:true,
            },
            {
              label: "违规取证",
              prop: "evidence",
              type: "textarea",
              span: 24,
              disabled:true,
            },
          ]
        },
        warningRecordOption1:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              display:false,
            },
            {
              label: "函询编号",
              prop: "inquiryNo",
              type: "input",
              disabled:true,
            },
            {
              label: "违规单位",
              prop: "deptName",
              type: "input",
              disabled:true,
            },
            {
              label: "违规类型",
              prop: "type",
              type: "select",
              disabled:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入类型",
                trigger: "blur"
              }],
            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              disabled:true,
              span:24,
            },
            {
              label: '违规时间',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: '违规发现时间',
              prop: 'findDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: "单位管理员姓名",
              prop: "userName",
              type: "input",
              disabled:true,
            },
            {
              label: "管理员手机号",
              prop: "userPhone",
              type: "input",
              disabled:true,
            },
            {
              label: "违规取证",
              prop: "evidence",
              type: "textarea",
              span: 24,
              disabled:true,
            },
          ]
        },
        warningRecordOption2:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              display:false,
            },
            {
              label: "函询编号",
              prop: "inquiryNo",
              type: "input",
              disabled:true,
            },
            {
              label: "违规单位",
              prop: "deptName",
              type: "input",
              disabled:true,
            },
            {
              label: "违规类型",
              prop: "type",
              type: "select",
              disabled:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入类型",
                trigger: "blur"
              }],
            },
            {
              label: "违规内容",
              prop: "content",
              type: "input",
              disabled:true,
              span:24,
            },
            {
              label: '违规时间',
              prop: 'illegalDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: '违规发现时间',
              prop: 'findDate',
              type: 'date',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              disabled:true,
            },
            {
              label: "单位管理员姓名",
              prop: "userName",
              type: "input",
              disabled:true,
            },
            {
              label: "管理员手机号",
              prop: "userPhone",
              type: "input",
              disabled:true,
            },
            {
              label: "违规取证",
              prop: "evidence",
              type: "textarea",
              span: 24,
              disabled:true,
            },
          ]
        },
        replyOption:{
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: true,
          delBtn:false,
          editBtn:false,
          addBtn:false,
          labelWidth: 150,
          column: [
            {
              label: "请输入回复意见",
              prop: "content",
              type: "textarea",
              minRows:10,
              maxlength:1000,
              span:24,
              showWordLimit:true,
              placeholder:"请具体说明情况，限1000字。",
              rules: [{
                required: true,
                message: "请输入回复意见",
                trigger: "click"
              }]
            },
            {
              label: '上传',
              prop: 'imgList',
              type: 'upload',
              span: 24,
              dataType: 'array',
              listType: 'picture-card',
              hide:true,
              limit:8,
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              rules: [{
                required: true,
                message: '请上传图片',
                trigger: 'click'
              }],
            },
          ]
        },
        data: []
      };
    },
    created() {
      setOption(this.option.column, this.userInfo.userType);
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.policies_regulations_add, false),
          viewBtn: this.vaildData(this.permission.policies_regulations_view, false),
          delBtn: this.vaildData(this.permission.policies_regulations_delete, false),
          editBtn: this.vaildData(this.permission.policies_regulations_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.regulationForm.id).then(res => {
            this.regulationForm = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchResetWarning() {
        this.query = {};
        this.onLoadWarning(this.warningListPage);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      searchChangeWarning(params, done) {
        this.query = params;
        this.warningListPage.currentPage = 1
        this.onLoadWarning(this.warningListPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChangeWarning(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.regulationForm.toggleSelection();
      },
      selectionClearWarning() {
        this.selectionList = [];
        this.$refs.warningListForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeWarning(currentPage){
        this.warningListPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeWarning(pageSize){
        this.warningListPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getSchoolInquiryList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoadWarning(page, params = {}) {
        this.warningListloading = true;
        getWarningList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.deptId).then(res => {
          const data = res.data.data;
          this.warningListPage.total = data.total;
          this.warningListData = data.records;
          this.warningListloading = false;
          this.selectionClearWarning();
        });
      },
      hanbleOpenWarningList(row){
        this.warningName = row.schoolName;
        this.deptId = row.id;
        this.warningListPage.currentPage = 1;
        this.onLoadWarning(this.warningListPage);
        this.warningListVisible = true;
      },
      handOpenWarningRecord(row){
        getWarningById(row.id).then(res => {
          this.warningRecordForm = res.data.data;
        });
        this.warningRecordVisible1 = true;
      },
      pushInquiry(row){
        saveInquiry(this.warningRecordForm1).then(() => {
          this.onLoadWarning(this.warningListPage);
          this.warningRecordVisible1 = false;
          this.$message({
            type: "success",
            message: "发送成功!"
          });
        }, error => {
          this.warningRecordVisible1 = false;
          window.console.log(error);
        });
      },
      closingFiling(){
        let year = new Date(new Date).getFullYear();
        let month =new Date(new Date).getMonth() + 1 < 10? "0" + (new Date(new Date).getMonth() + 1): new Date(new Date).getMonth() + 1;
        let date =new Date(new Date).getDate() < 10? "0" + new Date(new Date).getDate(): new Date(new Date).getDate();
        let hh =new Date(new Date).getHours() < 10? "0" + new Date(new Date).getHours(): new Date(new Date).getHours();
        let mm =new Date(new Date).getMinutes() < 10? "0" + new Date(new Date).getMinutes(): new Date(new Date).getMinutes();
        let ss =new Date(new Date).getSeconds() < 10? "0" + new Date(new Date).getSeconds(): new Date(new Date).getSeconds();
        this.nowTime = year + "-" + month + "-" + date +" "+hh+":"+mm+':'+ss  ;
        this.archives = "3";
        this.archivistDate = this.nowTime;
        this.warningRecordForm2.archivist = this.archivistName;
        this.warningRecordForm2.archivistDate = this.nowTime;
        console.log(">>>>>>>>>>>>>>>>>>>>>>>",JSON.stringify(this.warningRecordForm2))
        saveArchivist(this.warningRecordForm2).then(() => {
          this.onLoadWarning(this.warningListPage);
          this.$message({
            type: "success",
            message: "归档成功!"
          });
        }, error => {
          window.console.log(error);
        });
      },
      warningRecordFormClose(){
        this.onLoadWarning(this.warningListPage);
        this.warningRecordVisible2 = false;
      },
      saveReply(row,loading){
        this.replyForm.warningId = this.warningId;
        this.replyForm.imgList =this.uploadFilesList;
        saveReply(this.replyForm).then(() => {
          this.onLoad(this.page);
          this.replyVisible = false;
          this.$refs.replyForm.resetForm()
          this.replyForm.content = "";
          this.uploadFilesList = [];
          loading();
          this.$message({
            type: "success",
            message: "回复成功!"
          });
        }, error => {
          loading();
          this.uploadFilesList = [];
          window.console.log(error);
        });
      },
      openWarning(row,status){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = status;
        this.warningType3 = status;
        this.warningType4 = status;
        this.handlingUnit = row.handlingUnit;
        this.unitType = row.unitType;
        this.deptName = row.deptName;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      openMakeInquiry(){
        getInquiryById(this.warningId).then(res => {
          this.warningName1 = res.data.data.deptName;
          this.warningContent = res.data.data.content;
          this.inquiryNo = res.data.data.inquiryNo;
          this.jiweiDeptName = res.data.data.jiweiDeptName;
          this.jiweiDate = res.data.data.jiweiTime;
          this.jiweiUserName = res.data.data.jiweiUserName;
          this.jiweiAdminAccount  = res.data.data.jiweiAccount;
          this.evidence = res.data.data.evidence;
          if (res.data.data.category == "0") {
            this.category = "收入违规"
          }
          if (res.data.data.category == "1") {
            this.category = "支出违规"
          }
          if (res.data.data.category == "2") {
            this.category = "采购违规"
          }
          if (res.data.data.category == "3") {
            this.category = "财务违规"
          }
          if (res.data.data.category == "4") {
            this.category = "招标违规"
          }
          if (res.data.data.category == "5") {
            this.category = "仓管违规"
          }
        });
        this.otherWarningVisible2 = true;
      },
      replyOpen(row){
        this.warningId = row.id;
        this.replyVisible = true;
      },
      checkReply(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 2
        this.warningType5 = 1
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.replyDeptName = row.replyDeptName;
        this.fileList = row.replyFileList;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      checkNanagement(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 4;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.handleUserName = row.handleUserName;
        this.handleContent = row.handleContent;
        this.handleDeptName = row.handleDeptName;
        this.handleDate = row.handleDate;
        this.unitType = row.unitType;
        this.deptName = row.deptName;
        this.fileList = row.replyFileList;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      checkFile(row){
        this.warningId = row.id;
        this.warningDate = row.warningDate;
        if (row.inquiryStatus == "1"){
          this.inquiryStatus = "待函询"
        }
        if (row.inquiryStatus == "2") {
          this.inquiryStatus = "待食堂回复"
        }
        if (row.inquiryStatus == "3") {
          this.inquiryStatus = "待处置"
        }
        if (row.inquiryStatus == "4") {
          this.inquiryStatus = "待归档"
        }
        if (row.inquiryStatus == "5") {
          this.inquiryStatus = "已归档"
        }
        if (row.category == "0") {
          this.category = "收入违规"
        }
        if (row.category == "1") {
          this.category = "支出违规"
        }
        if (row.category == "2") {
          this.category = "采购违规"
        }
        if (row.category == "3") {
          this.category = "财务违规"
        }
        if (row.category == "4") {
          this.category = "招标违规"
        }
        if (row.category == "5") {
          this.category = "仓管违规"
        }
        this.warningType = 2;
        this.warningType3 = 3;
        this.warningType4 = 4;
        this.warningType5 = 5;
        this.handlingUnit = row.handlingUnit;
        this.content = row.content;
        this.evidence = row.evidence;
        this.inquiryDate = row.inquiryDate;
        this.inquiryDeptName = row.pushInquiryDeptName;
        this.inquiryName = row.jiweiUserName;
        this.replyContent = row.replyContent;
        this.replyName = row.replyName;
        this.replyTime = row.replyTime;
        this.replyDeptName = row.replyDeptName;
        this.handleUserName = row.handleUserName;
        this.handleContent = row.handleContent;
        this.handleDeptName = row.handleDeptName;
        this.handleDate = row.handleDate;
        this.fileUserName = row.fileUserName;
        this.fileContent = row.fileContent;
        this.fileDate = row.fileDate;
        this.fileDeptName = row.fileDeptName;
        this.deptName = row.deptName;
        this.unitType = row.unitType;
        this.fileList = row.replyFileList;
        this.pushInquerUserName = row.pushInquerUserName;
        this.warningDetailsVisible = true;
      },
      uploadBefore(file, done, loading,column) {
        /*        console.log(">>>>>>>>>上传前的方法",file)*/
        var str = file.name;
        str = str.substring(str.lastIndexOf("\.") + 1, str.length);

        var reStr = this.selectType(str);
        if (reStr == "NO") {
          loading();
          this.$message.error('文件格式错误,只能上传"jpg", "jpeg", "png", "JPG", "PNG"');
          return false;
        } else {
          /*    var newFile = new File([file], '1234', { type: file.type });*/
          this.fileName = file.name
          done()
          return true;
        }
        /*        console.log(file,column)*/
        //如果你想修改file文件,由于上传的file是只读文件，必须复制新的file才可以修改名字，完后赋值到done函数里,如果不修改的话直接写done()即可
        /*     var newFile = new File([file], '1234', { type: file.type });
             done(newFile)*/
        /*   this.$message.success('上传前的方法')*/
      },
      uploadError(error, column) {
        /*        this.$message.success('上传失败')*/
        /*        console.log(">>>>>>>>>上传失败",error, column)*/
      },
      uploadAfter(res, done, loading,column) {
        let proof = {
          label: this.fileName,
          value: res.link
        }
        this.uploadFilesList.push(proof);
        /*        this.selectionForm.chooseUserl =this.uploadFilesList;*/
        /*        console.log(">>>>>>>>上传后的方法",res)*/
        done()
        /*        this.$message.success('上传后的方法')*/
      },
      uploadPreview(file,column,done){
        /* console.log(">>>>>>>>>>>>>>>>>>>>>自定义查看方法",file,column)*/
        done()//默认执行打开方法
        /*       this.$message.success('自定义查看方法,查看控制台')*/
      },
      uploadDelete(column,file) {
        /*      console.log(">>>>>>>>>>>>>>>>>>>>>dd",JSON.stringify(this.uploadFilesList))*/
        this.uploadFilesList.splice(file.uid,1);
        /*        console.log(">>>>>>>>>>>>>>>>>>>>>dd",file.uid)*/
        /*    return this.$confirm(`这里是自定义的，是否确定移除该选项？`);*/
      },
      //判断文件类型
      selectType(type) {
        var imageList = ["jpg", "jpeg", "png", "JPG", "PNG"];
        for (var item in imageList) {
          if (imageList[item] == type) {
            return "FILE";
          }
        }
        return "NO";
      },
      queryImg(images){
        this.dialogImageUrl = images;
        this.dialogVisible = true;
      }
    }
  };
</script>

<style>
</style>
