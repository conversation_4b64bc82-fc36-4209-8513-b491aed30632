<template>
  <basic-container>
    <div class="table-form">
      <div v-show="!showDiv" style="margin-left: 15px;">
        <el-form ref="form" :model="form" label-width="80px" >
          <el-form-item label="当前帐套:">
            <div style="display: flex;flex-direction: row;">
              <div>
                <el-select v-model="accountSetsValue" placeholder="请选择帐套" style="width:400px;">
                  <el-option
                    v-for="item in accountSetsList"
                    :key="item.id"
                    :label="item.accountName"
                    :value="item.id">
                  </el-option>
                </el-select>
              </div>
              <div style="margin-left: 100px;">
                <el-input v-model="this.peroidStr" placeholder="请选择日期" readonly="readonly" style="width: 250px;"></el-input>
                <el-button type="primary" icon="el-icon-caret-bottom" @click="selectPeroidHandle">选择</el-button>
                <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
              </div>
            </div>
          </el-form-item>
        </el-form>

        <!-- <div style="width:80px;text-align: right;vertical-align: middle;float: left;font-size: 14px;color: #606266;line-height: 40px;padding: 0 12px 0 0;box-sizing: border-box;"><span>科目:</span></div>
        <el-select style="width: 250px;margin-bottom: 30px;" v-model="subjectName" filterable placeholder="Select" @change="changeSubject($event)">
          <el-option
            v-for="item in subjectOptions"
            :key="item.id"
            :label="item.subjectCode+' '+item.subjectName"
            :value="item.id"
          />
        </el-select> -->
      </div>

      <!-- <el-button style="margin-left: 15px;" type="success" size="mini" @click="clickShow">筛选条件 {{ showDiv ? "展开" : "收起" }}</el-button>
      <el-divider></el-divider> -->
      <avue-crud :option="option"
                :table-loading="loading"
                :data="data"
                :permission="permissionList"
                :before-open="beforeOpen"
                :cell-style="cellStyle"
                v-model="form"
                ref="crud"
                @search-change="searchChange"
                @search-reset="searchReset"
                @selection-change="selectionChange"
                @current-change="currentChange"
                @size-change="sizeChange"
                @on-load="onLoad"
                @cell-click="cellClick">
        <template slot="menuLeft">
          <div v-if="!showDiv">
            <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportTable">打印</el-button>
            <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportTableForMonth">打印所有科目</el-button>
            <el-button type="success" size="small" @click="handleExport">导出</el-button>
          </div>
        </template>
        <template slot="balanceDirection"  slot-scope="{row}">
                <el-tag v-if="row.balance==null || row.balance==0 || row.balance == undefined" size="medium" type="danger">平</el-tag>
                <el-tag v-else-if="row.balanceDirection == 0" size="medium" type="blue">借</el-tag>
                <el-tag v-else-if="row.balanceDirection == 1" size="medium" type="success">贷</el-tag>
                <el-tag v-else size="medium" type="danger">平</el-tag>
        </template>
      </avue-crud>
    </div>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog title="选择会计期间"
               append-to-body
               :visible.sync="selectPeroidShow"
               width="600px">
      <el-form ref="form" label-width="80px">
        <el-form-item label="查询区间">
          <el-col :span="11">
            <el-select v-model="startPeroid" ref="startPeroidName" placeholder="请选择开始期间">
              <el-option
                v-for="item in peroidList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-select v-model="endPeroid" ref="endPeroidName" placeholder="请选择结束期间">
              <el-option
                v-for="item in peroidList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="selectPeroidShow = false">取 消</el-button>
        <el-button type="primary"
                   @click="afterSelectPeroidHandle">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getVoucherListForLedger, processExportForLedger} from "@/api/finance/financialVoucherList";
import {mapGetters} from "vuex";
import {getAccountSetsList,getPeroidList} from "@/api/finance/voucher";
import {amtFilters,amtFiltersEx} from "@/api/finance/financialUtils";
// import {getList as getSubjectList, getSubjectTreeScope} from "@/api/finance/subject";
var DIC = {
  // 余额方向
  balanceDirection: [{
    label: '借',
    value: 0
  },{
    label: '贷',
    value: 1
  },{
    label: '平',
    value: 2
  }]
}
export default {
  data() {
    return {
      showContent: '收起',
      showDiv: false,
      selectVoucherPeroid: '',
      // subjectOptions: [],
      subjectId: '',
      // subjectName: '',
      peroidStr: '',
      startPeroid: '',
      endPeroid: '',
      peroidList: [],
      selectPeroidShow: false,
      accountSetsValue: '',
      accountSetsList:[],//当前食堂所有的帐套
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 100000,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      option: {
        menu: false,
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        startPeroid: '',
        endPeroid: '',
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn:false,
        refreshBtn: false,
        searchBtn: true,
        selection: false,
        columnBtn: false,
        addRowBtn: false,
        // printBtn:true,
        // excelBtn:true,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "科目",
            prop: 'subjectId',
            type: "tree",
            search: true,
            viewDisplay: false,
            addDisplay: false,
            editDisplay: false,
            filters: true,
            labelWidth: 200,
            width: 200,
            dicUrl: '/api/service/rabbit-finance/financialVoucher/tree/scopeForAllName',
            props: {
              label: "displaySubjectNameAll",
              value: "id"
            },
            rules: [{
              required: true,
              message: "科目",
              trigger: "blur"
            }],
            searchSpan: 4,
          },
          {
            label: "日期",
            prop: "businessDate",
            type: "datetime",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            // search: false,
            maxlength:30,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          {
              label: "凭证日期",
              prop: "businessDateRange",
              type: "daterange",
              format:'yyyy-MM-dd',
              valueFormat:'yyyy-MM-dd',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              search: true,
              searchRange: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              searchSpan: 5,
              hide: true,
          },
          {
            label: "凭证字号",
            prop: "word",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // formatter:(row,value,label)=>{
            //   if(row.word != '' && row.number !=0){
            //     return row.word+'-'+row.number
            //   } else {
            //     return ''
            //   }
            // }
          },
          {
            label: "摘要",
            prop: "summary",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            maxlength:30,
            showWordLimit:true,
          },
          {
            label: "借方金额",
            prop: "accountBalanceSheetDebitAmountStr",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            maxlength:30,
            // formatter:(row,value,label)=>{
            //   if(value==0) {
            //     return '';
            //   }
            //   return amtFilters(value);
            // },
            // formatter:(row,value,label)=>{
            //   if(value == 0){
            //     return ''
            //   } else{
            //     if(value == 0){
            //       return ''
            //     } else{
            //       return value
            //     }
            //   }
            // }
          },
          {
            label: "贷方金额",
            prop: "accountBalanceSheetCreditAmountStr",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            // formatter:(row,value,label)=>{
            //   if(value==0) {
            //     return '';
            //   }
            //   return amtFilters(value);
            // },
            // formatter:(row,value,label)=>{
            //   if(value == 0){
            //     return ''
            //   } else{
            //     return value
            //   }
            // }
          },
          {
            label: "方向",
            prop: "balanceDirection",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            maxlength:30,
            dicData: DIC.balanceDirection,
            slot: true,
          },
          {
            label: "余额",
            prop: "balanceStr",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            maxlength:30,
            // formatter:(row,value,label)=>{
            //   if(value==0) {
            //     return '';
            //   }
            //   return amtFilters(value);
            // },
            // formatter:(row,value,label)=>{
            //   if(value == 0){
            //     return ''
            //   } else{
            //     return value
            //   }
            // }
          }
        ]
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  mounted(){
  },
  created(){
    this.selectVoucherPeroid = this.$route.query.period;
    if(this.selectVoucherPeroid!=undefined && this.selectVoucherPeroid != '') {
      this.startPeroid = this.selectVoucherPeroid;
      this.endPeroid = this.selectVoucherPeroid;
      this.peroidStr = this.startPeroid + '至' + this.endPeroid;
    }
    this.init()
  },
  methods: {
    refreshData() {
      // this.page.currentPage = 1
      // this.onLoad(this.page, this.query);
      this.changeSubject(this.subjectId)
    },
    selectPeroidHandle() {
      this.peroidList = []
      getPeroidList()
        .then(res => {
          var list = res.data.data.records;
          if(list != null && list.length >0) {
            for (var i=0;i<list.length;i++) {
              const item = list[i];
              const p = item.voucherPeriod + '';
              const str = p.substring(0,4) + '年第' + p.substring(4,6) + '期';
              const it = {label:str,value:p};
              this.peroidList.push(it);
            }
          }
      });
      this.selectPeroidShow = true;
    },
    afterSelectPeroidHandle() {
      this.peroidStr = "";
      const s = this.$refs.startPeroidName.selectedLabel;
      const e = this.$refs.endPeroidName.selectedLabel;

      if(s != '' && e != '') {
        if(this.startPeroid > this.endPeroid) {
          this.$message.warning("开始期间不能大于结束期间");
          return;
        }
        this.peroidStr = s + '至' + e;
        this.selectPeroidShow = false;
      }else {
        if(s == '') {
          this.$message.warning("请选择查询的开始期间");
        }
        if(e == '') {
          this.$message.warning("请选择查询的结束期间");
        }
      }
    },
    beforeOpen(done, type) {
      // if (["edit", "view"].includes(type)) {
      //   getDetail(this.form.id).then(res => {
      //     this.form = res.data.data;
      //   });
      // }
      // done();
    },
    searchReset() {
      this.query = {};
      this.startPeroid = '';
      this.endPeroid = '';
      this.peroidStr = '';
      // this.onLoad(this.page);
    },
    searchChange(params, done) {
      if(params.subjectId == undefined){
        this.$message.warning('请先选择科目');
        done();
        return;
      }
      this.subjectId = params.subjectId;
      done();
      this.changeSubject(this.subjectId);
      // this.query = params;
      // this.page.currentPage = 1
      // this.onLoad(this.page, params);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      // this.loading = true;
      this.getgetAccountSets()

      this.changeSubject(this.subjectId)

      // getSubjectList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
      //   console.log(res)
      //   const records = res.data.data.records
      //   this.subjectOptions = records
      //   this.subjectName = records[0].subjectCode + " " + records[0].subjectName
      //   this.subjectId = records[0].id
      //   //初始页面先选择日期
      //   // console.log("this.subjectId" + this.subjectId)
      //   // if(this.subjectId != '' && this.subjectId != undefined){
      //   //   this.changeSubject(records[0].id)
      //   // }
      // });
    },
    changeSubject(subjectId){
      //更改账套值时查询数据
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      if(subjectId == '' || subjectId == undefined){
        this.$message.warning('请先选择科目');
        this.loading = false;
        return;
      }
      this.loading = true;
      let params = {}
      params.startPeroid = this.startPeroid;
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.subjectId = subjectId;
      params.accountSetsId = this.accountSetsValue;
      getVoucherListForLedger(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.length;
        this.data = data;
        this.loading = false;
        this.selectionClear();
      });
    },
    getgetAccountSets(){
      getAccountSetsList()
        .then(res => {
          this.accountSetsList = res.data.data.records;
          if(this.accountSetsList!=null && this.accountSetsList.length == 1) {
            this.accountSetsValue = this.accountSetsList[0].id;
          }
      });
    },
    cellClick(row, column, cell, event) {
      if(column.label === "凭证字号"){
        this.$router.push({
          path: "/financialVoucherView",
          query: {id:row.id}
        });
      }
    },
    cellStyle({row,column,rowIndex,columnIndex}){
      if(columnIndex==2){
        return {
          color:'blue',
          fontWeight:'bold',
          fontSize:'20',
          textDecoration: 'underline',
          cursor: 'pointer'
        }
      }
    },
    init() {
      // 通过科目汇总表跳转过来执行
      this.subjectId = this.$route.query.subjectId;
      this.startPeroid = this.$route.query.startPeroid;
      this.endPeroid = this.$route.query.endPeroid;
      this.peroidStr = this.$route.query.peroidStr;
      this.accountSetsValue = this.$route.query.accountSetsId
      this.onLoad()
    },
    exportTable(){
      let that = this;
      //更改账套值时查询数据
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      if(this.subjectId == '' || this.subjectId == undefined){
        this.$message.warning('请先选择科目');
        this.loading = false;
        return;
      }
      this.loading = true;

      var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
      xhr.open(
        "get",
        "/api/service/rabbit-finance/financialVoucher/rpt/downloadForLedger?startPeroid="+this.startPeroid+"&endPeroid="+this.endPeroid+"&accountSetsId="+this.accountSetsValue+"&subjectId="+this.subjectId,
        true
      );
      xhr.responseType = "blob";
      xhr.onload = function() {
        that.loading = false;
        const url = window.URL.createObjectURL(this.response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "明细账.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      xhr.send();
    },
    exportTableForMonth(){
      let that = this;
      //更改账套值时查询数据
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }

      this.loading = true;
      let mess = this.$message({
        message: '导出中,请耐心等待！时长可能需要1-5分钟...',
        duration: 0,
        type: 'warning'
      });

      var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
      xhr.open(
        "get",
        "/api/service/rabbit-finance/financialVoucher/rpt/downloadForLedgerMonth?startPeroid="+this.startPeroid+"&endPeroid="+this.endPeroid+"&accountSetsId="+this.accountSetsValue,
        true
      );
      xhr.responseType = "blob";
      xhr.onload = function() {
        that.loading = false;
        const url = window.URL.createObjectURL(this.response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "明细账.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        mess.close()
      };
      xhr.send();
    },
    handleExport( params = {}){
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      if(this.subjectId == '' || this.subjectId == undefined){
        this.$message.warning('请先选择科目');
        this.loading = false;
        return;
      }
      params.startPeroid = this.startPeroid;
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.subjectId = this.subjectId;
      params.accountSetsId = this.accountSetsValue;
      processExportForLedger(Object.assign(params, this.query)).then(res => {
        if (!res.data) {
          return;
        }
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel",
        }); // 构造一个blob对象来处理数据，并设置文件类型
        const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
        const a = document.createElement("a"); //创建a标签
        a.style.display = "none";
        a.href = href; // 指定下载链接
        let fileName = res.headers["content-disposition"];
        fileName = fileName.split("=")[1];
        a.download = decodeURIComponent(fileName); //指定下载文件名
        a.click(); //触发下载
        URL.revokeObjectURL(a.href); //释放URL对象
      });
    },
    clickShow(){
      this.showDiv = !this.showDiv
      this.option.searchShow = false
    }
  }
};
</script>

<style  lang="less" scoped>
  /deep/ .table-form .el-form-item__label{
      display: none;
  }
  /deep/ .table-form .el-form-item__content{
    margin-left: 0 !important;
  }
</style>
