<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportOrderData">导出</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {supplierOrderStatistical,supplierOrderStatisticalExport} from "@/api/liancan/order";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        searchFrom:{},
        dialogVisible:false,
        dialogImageUrl:undefined,
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          menu:false,
          align: 'center',
          column: [
            {
              label: "客户名称",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan/order/supplier/order/canteenStatistical",
              props: {
                label: "name",
                value:"deptId"
              },
              search: true,
            },
            {
              label: "取消/拒单",
              prop: "cancelQuantity",
              type: "number",
              minRows: 0,
            },
            {
              label: "未验收",
              prop: "unshippedQuantity",
              type: "number",
              minRows: 0,
            },
            {
              label: "未验收总金额",
              prop: "unshippedTotalMoney",
              type: "number",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "已按期配送",
              prop: "shippedQuantity",
              type: "number",
              minRows: 0,
            },
            {
              label: "已按期配送总金额",
              prop: "shippedTotalMoney",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "开始时间",
              prop: "startTime",
              type: "date",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              hide: true,
              search: true,
              display: false,
              showColumn:false,
            },
            {
              label: "结束时间",
              prop: "endTime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              type: "date",
              hide: true,
              search: true,
              display: false,
              showColumn:false,
            },
          ]
        },
        showSummary: true,
        sumColumnList: [
          {
            name: 'cancelQuantity',
            type: 'sum'
          },
          {
            name: 'unshippedQuantity',
            type: 'sum'
          },
          {
            name: 'unshippedTotalMoney',
            type: 'sum'
          },
          {
            name: 'shippedQuantity',
            type: 'sum'
          },
          {
            name: 'shippedTotalMoney',
            type: 'sum'
          },
        ],
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          /*        addBtn: this.vaildData(this.permission.work_personnel_add, false),
                  viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                  delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                  editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchFrom = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        supplierOrderStatistical(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      exportOrderData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          BACKGROUND: 'rgba(0, 0, 0, 0.7)'
        });
        supplierOrderStatisticalExport(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '订单统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
