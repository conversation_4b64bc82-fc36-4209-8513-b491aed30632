<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="avatar" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
      </template>
    </avue-crud>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
import {getListByCanteen,
  getDetail,
  add,
  update
} from "@/api/personnel/systemPersonnel";
import {
  getDeptTree
} from "@/api/setting/dept/systemDeptSetting";
import {
  getDeptAgencyType,
} from "@/api/system/dept";
import {mapGetters} from "vuex";
const DIC = {
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
  VAILD: [{
    label: '学生',
    value: '1'
  },{
    label: '教职工',
    value: '2'
  },
  ],
  VAILD2: [{
    label: '教职工',
    value: '2'
  },
  ],
  PERSONNEL:[{
    label: '停用',
    value: '0'
  },{
    label: '启用',
    value: '1'
  }, {
    label: '注销',
    value: '2'
  },{
    label: '锁定',
    value: '3'
  }],
  OPEN:[{
    label: '是',
    value: '1'
  }]
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      importForm:{},
      searchForm: {},
      editForm:{},
      editPersonnelForm:{},
      editPersonnelForm2:{},
      resetForm:{},
      downForm:{},
      loading: true,
      importDialogVisible:false,
      batchEditVisible:false,
      dialogVisible:false,
      schoolVisibleb:false,
      resetVisible:false,
      id:undefined,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      agencyType:undefined,
      modeType:undefined,
      modeType2:undefined,
      selectionList: [],
      uploadFilesList:[],
      dialogImageUrl:undefined,
      fileName:undefined,
      userId:undefined,
      isShowTea:undefined,
      isShowStu:undefined,
      numberTea:undefined,
      numberStu:undefined,
      codeTimeTea:undefined,
      codeTimeStu:undefined,
      editditVisible:false,
      editditVisible2:false,
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        selection: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        labelWidth: 190,
        dialogWidth: "70%",
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "登录账号",
            prop: "account",
            type: "input",
            width: 100,
            addDisplay: false,
            rules: [{
              required: true,
              message: "请输入登录账号",
              trigger: "blur"
            }],
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search:true,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 100,
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            search:true,
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }],
            width: 50,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            rules: [{
              required: true,
              message: "请输入学号/工号",
              trigger: "blur"
            }],
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            minWidth: 120,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
            props: {
              label: "title",
              value: "id"
            },
            editDisplay: false,
            viewDisplay: false,
            multiple:true,
            /*slot:true,*/
            /*search:true,*/
            hide:true,
            rules: [{
              required: true,
              message: "请输入部门",
              trigger: "click"
            }]
          },
          {
            label: "用餐类别",
            prop: "mealsName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            search:true,
            hide:true,
            editDisplay: false,
            viewDisplay: false,
            dicUrl: "/api/service/rabbit-liancan/diningType/select",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD,
            value:'1',
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelType2',
            type: "radio",
            //slot: true,
            dicData: DIC.VAILD2,
            value:'2',
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: '人员类别',
            prop: 'personnelTypeName',
            type: "input",
            addDisplay:false,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }],
            width: 90,
          },
          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span:24,
            slot: true,
            rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],
            width: 90,
          },
          {
            label: "人员状态",
            prop: "status",
            type: "radio",
            dicData: [{
              label: "启用",
              value: '1'
            },
            ],
            value:'1',
            hide:true,
            rules: [{
              required: true,
              message: "请选择人员状态",
              trigger: "blur"
            }],
            width: 90,
          },
          /*{
            label: "人员状态",
            prop: "status1",
            type: "radio",
            dicData: DIC.PERSONNEL,
            addDisplay:false,
            editDisplay:false,
            viewDisplay:false,
            hide:true,
            rules: [{
              required: true,
              message: "请输入人员状态",
              trigger: "blur"
            }],
            width: 90,
          },*/
          {
            label: "一卡通卡号",
            prop: "cardId",
            type: "input",
          },
          {
            label: "有效期",
            prop: "effectiveTime",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide:true,
            viewDisplay:false,
            addDisplay:false,
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < Date.now();
              },
            },
            width: 90,
          },
          {
            label: "民族",
            prop: "nation",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            hide:true,
          },
          /*       {
                   label: "开通伙食费钱包",
                   prop: "boardOpenStatuss",
                   type: "radio",
                   dicData: DIC.OPEN,
                   hide:true,
                   value:'1',
                   mock:{
                     type:'dic'
                   },
                 },*/
          /*     {
                 label: "伙食费钱包余额(元)",
                 prop: "balance",
                 type: "input",*/
          /*      disabled:true,*/
          /*              rules: [{
                          required: true,
                          message: "请输入伙食费钱包初始余额(元)",
                          trigger: "blur"
                        }],*/
          /*   },*/
          /*{
            label: "自选餐每餐次消费限额(元)",
            prop: "consumQuota",
            type: "input",
          },*/
          /*{
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },*/
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission", 'userInfo']),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.system_personnel_add, false),
        viewBtn: this.vaildData(this.permission.system_personnel_view, false),
        delBtn: this.vaildData(this.permission.system_personnel_delete, false),
        editBtn: this.vaildData(this.permission.system_personnel_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    getDeptAgencyType().then(res =>{
      this.agencyType = res.data.data.agencyType;
      if (res.data.data.applicationMode === '1'){
        this.modeType = '1';
        this.modeType2 = '2';
      }else if (res.data.data.applicationMode === '2'){
        this.modeType = '2';
        this.modeType2 = '2';
      }else if (res.data.data.applicationMode === '3'){
        this.modeType = '1';
        this.modeType2 = '1';
        this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        /* this.option.menu = false;*/
      }else if (res.data.data.applicationMode === '0'){
        this.modeType = '1';
        this.modeType2 = '1';
        this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        /*          this.option.menu = false;*/
      }
      console.log(">>>>>>>>>",JSON.stringify(res.data.data.agencyType))
      if (res.data.data.agencyType == '0'){
        this.option.column[10].search = false;
      }else {
        this.option.column[9].search = false;
      }
    })
    getDeptTree().then(res => {
      const index = this.$refs.crud.findColumnIndex("deptId");
      this.option.column[index].dicData = res.data.data;
    });
  },
  methods: {
    rowSave(row, loading, done) {
      if (this.agencyType === '0'){
        row.personnelType = row.personnelType;
      }else {
        row.personnelType = row.personnelType2;
      }
      add(row).then(() => {
        loading();
        this.page.currentPage = 1;
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      if (row.boardOpenStatus instanceof Array){
        row.boardOpenStatus = row.boardOpenStatus
      }else {
        row.boardOpenStatus1 = row.boardOpenStatus
        row.boardOpenStatus = []
      }
      /*        row.deptId = row.deptId.join(",");*/
      update(row).then(() => {
        loading();
        this.page.currentPage = 1;
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    beforeOpen(done, type) {
      done();
      // 转成数组
      if (this.form.attribute != null) {
        let attribute = (this.form.attribute).split(',')
        this.form.attribute = attribute
      }
      // 转成数组
      if (this.form.boardOpenStatus != null) {
        let boardOpenStatus = (this.form.boardOpenStatus).split(',')
        this.form.boardOpenStatus = boardOpenStatus
      }
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          const balanceColumn = this.option.column[13];
          if (this.form.boardOpenStatus.map(parseInt) == 1){
            balanceColumn.disabled = false;
          }
          if (this.form.boardOpenStatus.map(parseInt) == 0){
            balanceColumn.disabled = true;
          }
          this.form = res.data.data;
        });
      }
      getDeptAgencyType().then(res =>{
        this.agencyType = res.data.data.agencyType;
      })
      //8:显示教职工，9：显示学生或者教职工
      if (this.agencyType === '0'){
        this.option.column[10].display = false;
      }else {
        this.option.column[9].display = false;
      }
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.searchForm = params;
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
      this.$refs.crud.refreshTable();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.status = "1";
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getListByCanteen(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleClickPreview: function(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
  }
};
</script>

<style>
</style>
