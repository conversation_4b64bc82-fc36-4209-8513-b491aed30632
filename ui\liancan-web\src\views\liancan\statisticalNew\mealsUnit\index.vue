<template>
  <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
          <template slot="menu" slot-scope="scope">
            <el-button type="text" size="small" icon="el-icon-view" @click="showDetail(scope.row)">查看详情
            </el-button>
          </template>
        </avue-crud>

        <el-dialog title="详情" :visible.sync="detailVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
          <detailVue :query="pageParams"></detailVue>
        </el-dialog>
  </basic-container>
</template>

<script>
import {getServeFoodUnitList, getCanteenUnitList, getSchoolInfo} from "@/api/liancan/statistical";
import detailVue from "@/views/liancan/statisticalNew/detail";

import {mapGetters} from "vuex";
var DIC = {
  agencyType: [
    {
      label: '学校',
      value: "0"
    },
    {
      label: '政府机关',
      value: "1"
    },
    {
      label: '事业单位',
      value: "2"
    },
    {
      label: '国企',
      value: "3"
    },
    {
      label: '其他',
      value: "4"
    },
  ],
}
export default {
  data() {
    return {
      activeIdx: 0,
      messList: [ '供餐单位', '供餐单位设立的食堂' ],
      form: {},
      goodsForm:{},
      query: {},
      goodsQuery:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      goodsPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      isShow: false,
      resOrderId: null,
      confirmBat: false,
      pageParams: {},
      detailVisible: false,
      option: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
              label: '省份',
              prop: 'province',
              type: 'select',
              props: {
              label: 'regionName',
              value: 'id'
              },
              cascaderItem: ['city', 'area'],
              search:true,
              hide: true,
              viewDisplay: false,
              dicUrl: `/api/rabbit-system/region/getProvince`,
              rules: [
              {
                required: true,
                message: '请选择省份',
                trigger: 'blur'
              }
              ]
            },
            {
              label: '城市',
              prop: 'city',
              type: 'select',
              props: {
              label: 'regionName',
              value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
              rules: [
              {
                required: true,
                message: '请选择城市',
                trigger: 'blur'
              }
              ]
            },
            {
              label: '地区',
              prop: 'area',
              type: 'select',
              props: {
              label: 'regionName',
              value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
              rules: [
              {
                required: true,
                message: '请选择地区',
                trigger: 'blur'
              }
              ]
            },
          {
            label: "所属单位",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            viewDisplay: false,
            editDisplay: false,
            search: false,
            hide: true,
          },
          {
            label: "单位名称",
            prop: "deptName",
            type: "input",
            search: true,
          },
          {
            label: "单位类型",
            prop: "agencyType",
            type: "select",
            dicData: DIC.agencyType,
          },
          {
            label: "单位地址",
            prop: "address",
            type: "input",
          },
          {
            label: "管理员姓名",
            prop: "userName",
            type: "input",
          },
          {
            label: "管理员手机号",
            prop: "userPhone",
            type: "input",
          },
        ]
      },
      //index 2
      goodsOption: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: "食堂id",
            prop: "canteenId",
            type: "input",
            hide: true,
          },
          {
            label: "所属单位",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            viewDisplay: false,
            editDisplay: false,
            search: true,
          },
          {
            label: "食堂名称",
            prop: "canteenName",
            search: true,
            type: "input",
          },
          {
            label: "食堂类型",
            prop: "canteenType",
            addDisplay: false,
            editDisplay: false,
            type: "select",
            dicData: [
              {
                label: "学生食堂",
                value: "1"
              },
              {
                label: "教师食堂(单位食堂)",
                value: "2"
              }
            ],
          },
          {
            label: "经营方式",
            prop: "operationMode",
            addDisplay: false,
            editDisplay: false,
            type: "select",
            dicData: [
              {
                label: "自主经营",
                value: "1"
              },
              {
                label: "承包经营",
                value: "2"
              }
            ],
          },
          {
            label: "食堂负责人",
            prop: "userName",
            type: "input",
          },
          {
            label: "负责人电话",
            prop: "userPhone",
            type: "input",
          },
          {
            label: "食堂状态",
            prop: "status",
            addDisplay: false,
            editDisplay: false,
            type: "select",
            dicData: [
              {
                label: "启用",
                value: "0"
              },
              {
                label: "停用",
                value: "1"
              }
            ],
          },
          {
            label: '建立日期',
            prop: 'createTime',
            type: 'datetime',
            overHidden: true,
            format: 'yyyy-MM-dd',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          // {
          //   label: "建立人",
          //   prop: "createUser",
          //   type: "select",
          //   dicUrl: '/api/service/rabbit-user/user-list',
          //   props: {
          //     label: "realName",
          //     value: "id"
          //   },
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          // },
          {
            label: "建立人",
            prop: "createUser",
            type: "input",
          },
          {
            label: "启用账套",
            prop: "isUse",
            addDisplay: false,
            editDisplay: false,
            type:"select",
            dicData: [
              {
                label: "未启用",
                value: 0
              },
              {
                label: "启用",
                value: 1
              }
            ],
          },
        ]
      },
      data: [],
      goodsData:[],
      isShowDialog: false,
      canteenName: null,
      canteenId: null,
      activeIdx2: 0,
      idx: 0,
    }
  },
  components: {
    'detailVue': detailVue,  //将别名demo 变成 组件 Demo
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
    },
    ids() {
    }
  },
  created(){
  },
  methods: {
    menuClick(idx) {
      this.query=[];
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.query = {};
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
      if (idx == 1){
        this.query = {};
        this.goodsPage.currentPage = 1;
        this.onGoodsLoad(this.goodsPage);
      }
    },
    showDetail (row) {
      getSchoolInfo(row.deptId).then(res => {
        console.log('showDetail ===== ', res)
        this.detailVisible = true;
        this.pageParams = res.data.data;
      })
    },
    beforeOpen(done, type) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchResetGoods(){
      this.goodsQuery = {};
      this.onGoodsLoad(this.goodsPage);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchChangeGoods(params, done) {
      this.goodsQuery = params;
      this.goodsPage.currentPage = 1
      this.onGoodsLoad(this.goodsPage, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    currentChangeGoods(currentPage){
      this.goodsPage.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    sizeChangeGoods(pageSize){
      this.goodsPage.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getServeFoodUnitList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getServeFoodUnitList ===== ', res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    onGoodsLoad(page, params = {}) {
      this.loading = true;
      getCanteenUnitList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getCanteenUnitList ===== ', res)
        const data = res.data.data;
        this.goodsPage.total = data.total;
        this.goodsData = data.records;
        this.loading = false;
      })
    },
    typeClick(num) {
      this.activeIdx2  = num;
      this.idx = num + 1;
    },
    opendialog(name,id){
      console.log('opendialog ==== ', name, id)
      this.canteenName = name;
      this.canteenId = id;
      this.isShowDialog = true;
      this.activeIdx2  = 0;
      this.idx = 1;
    },
    closeDialog(){
      this.isShowDialog = false;
      this.idx = -1;
    },
  }
}
</script>

<style lang="scss" scoped>
.fontsize {
  font-weight: 700;
}
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    margin-bottom: 20px;
    /deep/ div {
      width: 160px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
.mess_list_dialog {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  .mess_item_first {
    margin: -14px 20px 0 0;
    font-weight: bold;
    font-size: 20px;
  }
  .mess_item_dialog {
    width: 120px;
    height: 37px;
    line-height: 35px;
    text-align: center;
    border: 1px solid #C0C0C0;
    margin: 0 10px 10px 0;
    border-radius: 12px
    /*&:hover {*/
    /*  color: #333333;*/
    /*  background: #1b16f1;*/
    /*}*/
  }
  .acitve_dialog {
    background: #409EFF;
  }
}
</style>
