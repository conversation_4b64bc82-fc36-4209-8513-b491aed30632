<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="menuLeft">
                <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportOrderData">导出</el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {orderStatistical,orderStatisticalExport} from "@/api/liancan/order";
import {mapGetters} from "vuex";
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            searchFrom:{},
            dialogVisible:false,
            dialogImageUrl:undefined,
            option: {
                height:'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: true,
                viewBtn: true,
                selection: false,
                labelWidth: 150,
                dialogWidth: 900,
                addBtn:false,
                menu:false,
                align: 'center',

                column: [
                    {
                        label: "学校",
                        prop: "deptId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dept/select",
                        props: {
                            label: "deptName",
                            value:"id"
                        },
                        hide: true,
                        search: true,
                        fixed:true,
                        align: 'center',
                        width: 120,
                        rules: [{
                            required: true,
                            message: "请选择部门",
                            trigger: "click"
                        }]
                    },
                    {
                        label: "单位名称",
                        prop: "deptName",
                        type: "input",
                    },
                    {
                        label: "学校类型",
                        prop: "agencyType",
                        type: "select",
                        dicUrl: '/api/service/rabbit-system/dict/dictionary?code=agency_type',
                        props: {
                            label: 'dictValue',
                            value: 'dictKey'
                        },
                        //disabled: true,
                        search: true
                    },
                    {
                        label: "食堂名称",
                        prop: "name",
                        type: "input",
                    },
                    {
                        label: "供应商数量",
                        prop: "supplierQuantity",
                        type: "number",
                        minRows: 0,
                    },
                    {
                        label: "未配送",
                        prop: "unshippedQuantity",
                        type: "number",
                    },
                    {
                        label: "已配送",
                        prop: "orderQuantity",
                        type: "number",
                    },
                    {
                        label: "采购单数合计",
                        prop: "orderQuantitySum",
                        type: "number",
                        minRows: 0,
                    },
                    {
                        label: "已配送金额总计",
                        prop: "moneySum",
                        type: "input",
                        precision:2,
                        mock:{
                            type:'number',
                            min:0,
                            precision:2
                        },
                        minRows: 0,
                    },
                    {
                        label: "开始时间",
                        prop: "startTime",
                        type: "date",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        hide: true,
                        search: true,
                        display: false,
                        showColumn:false,
                    },
                    {
                        label: "结束时间",
                        prop: "endTime",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        type: "date",
                        hide: true,
                        search: true,
                        display: false,
                        showColumn:false,
                    },
                ]
            },
            showSummary: true,
            sumColumnList: [
                {
                    name: 'supplierQuantity',
                    type: 'sum'
                },
                {
                    name: 'orderQuantity',
                    type: 'sum'
                },
                {
                    name: 'moneySum',
                    type: 'sum'
                }
            ],
            data: []
        };
    },
    computed: {
        ...mapGetters(["permission","userInfo"]),
        permissionList() {
            return {
                /*        addBtn: this.vaildData(this.permission.work_personnel_add, false),
                        viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                        delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                        editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created(){
        // 单位列表是否显示
        this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    },
    methods: {
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.searchFrom = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage){
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize){
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            orderStatistical(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        handleClickPreview: function(url) {
            this.dialogImageUrl = url;
            this.dialogVisible = true;
        },
        exportOrderData(){
            const loading = this.$loading({
                lock: true,
                text: '正在导出数据，请稍后',
                spinner: 'el-icon-loading',
                BACKGROUND: 'rgba(0, 0, 0, 0.7)'
            });
            this.searchFrom.status = "2";
            orderStatisticalExport(this.searchFrom).then(res => {
                loading.close();
                const blob = new Blob([res.data]);
                const fileName = '采购订单统计报表.xlsx';
                const linkNode = document.createElement('a');

                linkNode.download = fileName; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击

                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
            })
        },
    }
};
</script>

<style>
</style>
