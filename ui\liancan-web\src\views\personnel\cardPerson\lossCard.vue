<template>
  <basicContainer>
    <avue-crud ref="form" v-model="form" :option="tableOption" :data="data" @on-load="onLoad" :page.sync="page"
               @row-update="handleUpdate" :table-loading="tableLoading" @search-change="handleSearch"
               @refresh-change="handleRefresh" @search-reset="handleRefresh" :before-open="beforeOpen"
               @size-change="sizeChange" @selection-change="selectionChange">
      <template slot="avatar" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="mini" icon="el-icon-setting" @click="lossCard(scope.row)">挂失</el-button>
      </template>
    </avue-crud>
  </basicContainer>
</template>

<script>

import {getList,
  getDetail,
  add,
  update,
  getCardNum,
} from "@/api/personnel/systemPersonnel";
import {
  updateCard,
} from "@/api/waterCard/cardSerialPort";

import {
  mapGetters
} from 'vuex';
import func from "@/util/func";

const DIC = {
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
  VAILD: [{
    label: '人员(1)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '1'
  },{
    label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '2'
  },
  ],
  VAILD2: [{
    label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '2'
  },
  ]
}
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      categoryStatus:undefined,
      searchForm: {},
      query: {},
      form: {},
      data: [],
      detailInfo: {},
      detailBox: false,
      size: 'small',
      tableLoading: false,
      selectionList: [],
      batchIndex: -1,
      tableOption: {
        height: 'auto',
        calcHeight: 80,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        viewBtn: false,
        selection: true,
        align: 'left',
        column: [
            {
                label: "主键",
                prop: "id",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
                hide: true,
            },
            {
                label: "姓名",
                prop: "userName",
                type: "input",
                sortable:true,
                search:true,
                rules: [{
                  required: true,
                  message: "请输入姓名",
                  trigger: "blur"
                }],

              },
              {
                label: '性别',
                prop: 'sex',
                type: "radio",
                //slot: true,
                dicData: DIC.sex,
                search:true,
                sortable:true,
                rules: [{
                  required: true,
                  message: '请选择性别',
                  trigger: 'blur'
                }],
              },
              {
                label: "编号",
                prop: "studentJobNo",
                type: "input",
                search:true,
                sortable:true,
                rules: [{
                  required: true,
                  message: "请输入编号",
                  trigger: "blur"
                }],
              },
              {
                label: "部门",
                prop: "deptName",
                type: "input",
                overHidden: true,
                sortable:true,
                minWidth: 120,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
              },
              {
                label: "部门",
                prop: "deptId",
                type: "tree",
                dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
                props: {
                  label: "title",
                  value: "id"
                },
                editDisplay: false,
                viewDisplay: false,
                multiple:true,
                /*slot:true,*/
                search:true,
                hide:true,
                rules: [{
                  required: true,
                  message: "请输入部门",
                  trigger: "click"
                }]
              },
              {
                label: "用餐类别",
                sortable:true,
                prop: "mealsName",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
              },
              {
                label: "用餐类别",
                prop: "mealsType",
                type: "select",
                search:true,
                hide:true,
                editDisplay: false,
                viewDisplay: false,
                dicUrl: "/api/service/rabbit-liancan/diningType/dict",
                props: {
                  res: "data",
                  label: "name",
                  value: "id"
                },
              },
              {
                label: '人员类别',
                prop: 'personnelType',
                type: "radio",
                //slot: true,
                dicData: DIC.VAILD,
                search:true,
                rules: [{
                  required: true,
                  message: '请选择人员类别',
                  trigger: 'blur'
                }],
                width: 90,
              },
              {
                label: '人员类别',
                prop: 'personnelType2',
                type: "radio",
                //slot: true,
                dicData: DIC.VAILD2,
                value:'2',
                search:true,
                hide:true,
                rules: [{
                  required: true,
                  message: '请选择人员类别',
                  trigger: 'blur'
                }],
                width: 90,
              },
              {
                label: '人员类别',
                prop: 'personnelTypeName',
                type: "input",
                addDisplay:false,
                hide:true,
                rules: [{
                  required: true,
                  message: '请选择人员类别',
                  trigger: 'blur'
                }],
                width: 90,
              },
              {
                label: "人员照片",
                prop: "avatar",
                type: 'upload',
                listType: 'picture-img',
                action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                propsHttp: {
                  res: 'data',
                  url: 'link',
                },
                span:24,
                slot: true,
                width: 90,
              },
              {
                label: "人员状态",
                prop: "status",
                type: "radio",
                dicData: [{
                  label: "启用",
                  value: '1'
                },
                ],
                value:'1',
                search:true,
                hide:true,
                rules: [{
                  required: true,
                  message: "请选择人员状态",
                  trigger: "blur"
                }],
              },
              // {
              //   label: "卡号",
              //   prop: "cardId",
              //   type: "input",
              //   editDisplay: false
              // },
              // {
              //   label: "卡序号",
              //   prop: "cardNum",
              //   type: "input",
              //   editDisplay: false
              // },
              // {
              //   label: "卡状态",
              //   prop: "cardStatus",
              //   type: "select",
              //   dicData: [
              //     {
              //       value: '0',
              //       label: '未开卡'
              //     },
              //     {
              //       value: '1',
              //       label: '正常'
              //     },
              //     {
              //       value: '2',
              //       label: '已挂失'
              //     },
              //     {
              //       value: '3',
              //       label: '异常'
              //     },
              //     {
              //       value: '4',
              //       label: '过期'
              //     }
              //   ],
              //   // search: true,
              //   editDisplay: false
              // },
              // {
              //   label: "生效日期",
              //   prop: "startDate",
              //   type: "date",
              //   format: "yyyy-MM-dd",
              //   valueFormat: "yyyy-MM-dd",
              //   rules: [{
              //     required: true,
              //     message: "请选择生效日期",
              //     trigger: "blur"
              //   }],
              // },
              // {
              //   label: "终止日期",
              //   prop: "endDate",
              //   type: "date",
              //   format: "yyyy-MM-dd",
              //   valueFormat: "yyyy-MM-dd",
              //   rules: [{
              //     required: true,
              //     message: "请选择终止日期",
              //     trigger: "blur"
              //   }],
              // },
        ]
      },
      syncBox: false,
      schoolList: [],
      // 提交表单
      // deviceId: 0,
      //记录当前卡务机
      deviceInfo: {},
      cardForm: {
        // fee: 0,
        // waterTypeList: []
      },
      rules: {
        deviceId: [
          { required: true, message: '请选择IC卡设备', trigger: 'blur' },
        ],
        // groupId: [
        //   { required: true, message: '请选择用水分组', trigger: 'blur' },
        // ],
        waterTypeList: [
          { required: true, message: '请选择开通水务', trigger: 'blur' },
        ],
        cardNum: [
          { required: true, message: '请输入卡序号', trigger: 'blur' },
        ],
        // startDate: [
        //   { required: true, message: '请选择生效日期', trigger: 'blur' },
        // ],
        // endDate: [
        //   { required: true, message: '请选择终止日期', trigger: 'blur' },
        // ],
        identifyCode: [
          { required: true, message: '请输入学生帐号', trigger: 'blur' },
        ],
        cardId: [
          { required: true, message: '请输入卡序列号', trigger: 'blur' },
        ],
      },

      //串口相关参数=======================
      // 串口配置
      isNotReady: true,
      isSendCard: false,
      unitCode: 0,
      port: {},
      reader: {},
      messageRecive: {},
      messageReciveStr: '',
      reciveStr: '',
      tipStr: '卡务机未准备就绪,请稍后,若长时间未反应请手动开启',
      errorMsg: '',
      connected: {},
      serialOptions: {
        baudRate: 115200, // 一个正的、非零的值，表示串口通信应该建立的波特率
        dataBits: 8, // 7或8的整数值，表示每帧的数据位数。默认值为8
        stopBits: 1, // 1或2的整数值，表示帧结束时的停止位数。默认值为1。
        parity: 'none', // 奇偶校验模式为“none”、“偶”或“奇”。默认值为none。
        bufferSize: 2048, // 一个无符号长整数，指示要建立的读和写缓冲区的大小。如果未通过，默认值为255。
        flowControl: 'none', // 流控制类型，“none”或“hardware”。默认值为none。
      },
      //串口相关参数=======================
      // 过滤设备与Arduino Uno USB供应商/产品id。
      portFilters: [
        { usbVendorId: 0x2341, usbProductId: 0x0043 },
        { usbVendorId: 0x2341, usbProductId: 0x0001 }
      ],
    }
  },
  async mounted() {
    //加载页面完毕开启串口
    // this.prepareOpen();
  },
  created() {
    this.connected.value = false;
    this.categoryStatus = "0";
  },
  computed: {
    ...mapGetters(['tenant', 'userInfo']),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  watch: {
    'form.saleType'() {
      let saleType = this.form.saleType;
      let groupIds = this.findObject(this.tableOption.column, 'groupIds');
      if (saleType == '2') {
        groupIds.display = true;
      } else {
        groupIds.display = false;
      }
    }
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit"].includes(type)) {
        // 所属学校
        const schoolId = this.findObject(this.tableOption.column, 'schoolId');
        schoolId.disabled = true;
      }
      done();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      console.log(this.query)
      this.query.status = "1";//
      params.categoryStatus = this.categoryStatus;
      params.cardStatus = '1';//查询状态为1正常的人员, 卡状态(0:未开卡,1:正常,2:已挂失,3:异常,4过期)
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    selectionClear() {
      this.batchIndex = -1;
      this.selectionList = [];
      try{
        this.$refs.crud.toggleSelection();
        // eslint-disable-next-line no-empty
      }catch (error){}
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page)
    },
    handleSearch: function(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    handleRefresh: function() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page)
    },
    handleUpdate(row, index, done, loading) {
      // update(row).then(() => {
      //   this.onLoad(this.page);
      //   this.$message({
      //     type: "success",
      //     message: "操作成功!"
      //   });
      //   done();
      // }, error => {
      //   window.console.log(error);
      //   loading();
      // });
    },
    // 挂失提交
    lossCard(row) {
      this.$confirm("确定要挂失？", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
      }).then(() => {
        row.applyType = '3'//挂失
        updateCard(row).then(res => {
          if (res.data.code === 200) {
            this.$message.success('操作成功！');
            this.detailBox = false
            this.onLoad(this.page);
          }
        }, error => {
          window.console.log(error);
        })
      });
    }
  }
}
</script>

<style scoped>
.margin-top {
  margin-top: 15px;
}
.my-label {
  width: 200px;
}
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}
</style>
