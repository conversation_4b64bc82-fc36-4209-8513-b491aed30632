<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="addOrderHandler" >新增</el-button>
                <el-button type="primary"  icon="el-icon-download" size="small" @click="exports" >导出</el-button>
                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>
        <!-- 打开新增页面 开始 -->
        <el-dialog title="新增"
                   :visible.sync="isShowOrder"
                   v-if="isShowOrder"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm" width="60%"  style="height: 90%;">
            <el-form ref="formAdd" label-width="80px">
                <el-form-item label="单据日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-date-picker type="date"
                                        placeholder="选择日期"
                                        :picker-options="pickerOptions"
                                        v-model="businessDate"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;"></el-date-picker>
                    </el-col>
                </el-form-item>
                <el-form-item label="订单商品" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="3">
                        <el-button type="primary" size="small" @click="selectGoodHandler">从商品库中选择</el-button>
                    </el-col>
                </el-form-item>
            </el-form>
            <div style="height: 350px;overflow-y: scroll;">
                <avue-crud :option="orderOption"
                           :table-loading="orderLoading"
                           :data="orderData"
                           :page="orderPage"
                           :before-open="beforeOpenOrder"
                           v-model="orderForm"
                           ref="crudOrder"
                           @row-update="rowUpdateOrder"
                           @row-save="rowSaveOrder"
                           @row-del="rowDelOrder"
                           @search-change="searchChangeOrder"
                           @search-reset="searchResetOrder"
                           @selection-change="selectionChangeOrder"
                           @current-change="currentChangeOrder"
                           @size-change="sizeChangeOrder"
                           @cell-click="handleRowClick"
                           @on-load="orderOnLoad">
                    <template slot="menu" slot-scope="{row,index}">
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-edit"
                            v-if="!row.$cellEdit"
                            @click="rowCellOrder(row,index)"
                        >修改</el-button>
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-check"
                            v-if="row.$cellEdit"
                            @click="rowSaveOrder(row,index)"
                        >保存</el-button>
                        <el-button
                            type="text"
                            size="mini"
                            icon="el-icon-delete"
                            @click="deleteRowOrder(row)">删除
                        </el-button>
                    </template>
                </avue-crud>
            </div>
            <div>
                订单总额:{{totalAmt}}元
            </div>
            <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddForm">取消</el-button>
                    <el-button type="primary" @click="saveSelectHandle">保存</el-button>
                </span>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 公共商品选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowSelectGoods"
                   v-if="isShowSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeSelectForm" width="60%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crudSelectGoods"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 商品选择 结束 -->
        <!-- 打开详情页面 开始 -->
        <el-dialog title="详情"
                   :visible.sync="isShowOrderDetail"
                   v-if="isShowOrderDetail"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeShowForm" width="60%"  style="height: 90%;">
            <el-row type="flex" class="row-bg" justify="right">
                <el-col :span="24" style="height: 100%;" align="right">
                    <el-button type="primary" icon="el-icon-printer" size="small" v-print="print" >打印</el-button>
                </el-col>
            </el-row>
            <div id="printArea">
                <el-row type="flex" class="row-bg" justify="left" style="height: 60px;">
                    <el-col :span="24">
                        <div style="width: 100%;height:100%;text-align:center;"><h1>移仓单</h1></div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left" style="margin-top: 45px;">
                    <el-col :span="2">
                        <div class="head-label">单据日期</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.businessDate}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">业务员</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.salesmanName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">单据号</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.code}}</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">登记人</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.createUserName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">登记时间</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.createTime}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label"></div>
                    </el-col>
                    <el-col :span="3">
                        <div></div>
                    </el-col>

                    <el-col :span="2">
                        <div></div>
                    </el-col>
                    <el-col :span="3">
                        <div></div>
                    </el-col>
                </el-row>
                <div style="overflow-y: scroll;margin-top: 20px;">
                    <el-table
                        :data="orderData"
                        stripe
                        border
                        fit
                        show-summary
                        :header-cell-style="tableHeaderStyle"
                        :row-style="tableDetailStyle"
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column property="rowId" label="序号" align="center"></el-table-column>
                        <el-table-column property="name" label="商品名称" align="center"></el-table-column>
                        <el-table-column property="unitName" label="计量单位" align="center"></el-table-column>
                        <el-table-column property="outWarehouseName" label="调出仓库" align="center"></el-table-column>
                        <el-table-column property="inWarehouseName" label="调入仓库" align="center"></el-table-column>
                        <el-table-column property="qty" label="移仓数量" align="center"></el-table-column>
                        <el-table-column property="price" label="成本单价" align="center"></el-table-column>
                        <el-table-column property="amt" label="移仓金额" align="center"></el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 审核 开始 -->
        <el-dialog title="审核" :visible.sync="approveVisible" :append-to-body="true" width="60%">
            <div style="margin-top: 20px;text-align: right;">
                <el-button type="primary" icon="el-icon-check" size="small" @click="reviewCheck(1)">通过</el-button>
                <el-button type="danger" icon="el-icon-circle-close" size="small" @click="reviewCheck(-1)">不通过</el-button>
                <el-button type="primary" icon="el-icon-circle-close" size="small" plain @click="chanel()">取消</el-button>
            </div>
        </el-dialog>
        <!-- 审核 结束 -->
    </basic-container>
</template>

<script>
import {
    getList,
    getDetail,
    add,
    update,
    remove,
    exportWebData,
} from "@/api/supplier/supplierStockMove";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getGoodsList, stockLoad} from "@/api/supplier/supplier";
import {reviewCheck} from "@/api/liancan2/expenditureCheck";

const DIC = {
    //订单状态（全部、未关闭、手动关闭、已关闭）
    orderCloseStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未关闭',
            value: '0'
        },
        {
            label: '手动关闭',
            value: '-2'
        },
        {
            label: '已关闭',
            value: '-1'
        },
    ],
    //订单审核（全部、未审核、通过、不通过）
    orderAuditStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未审核',
            value: '0'
        },
        {
            label: '通过',
            value: '1'
        },
        {
            label: '不通过',
            value: '-1'
        },
    ],
    //订单状态（全部、未接单、已接单、取消/拒单、配送中、已送达、已收货）
    orderStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未接单',
            value: '0'
        },
        {
            label: '已接单',
            value: '1'
        },
        {
            label: '取消/拒单',
            value: '-1'
        },
        {
            label: '配送中',
            value: '2'
        },
        {
            label: '已送达',
            value: '3'
        },
        {
            label: '已收货',
            value: '4'
        },
    ],
    //订单执行（全部、未入库、部分入库、全部入库）
    orderDealStatus: [
        {
            label: '全部',
            value: ''
        },
        {
            label: '未入库',
            value: '0'
        },
        {
            label: '部分入库',
            value: '2'
        },
        {
            label: '全部入库',
            value: '1'
        },
    ],
}
export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            createUserName:'',
            salesmanName:'',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                searchMenuSpan: 4, //搜索按钮长度
                // searchSpan:24,      //搜索框长度  最大长度24
                // searchLabelWidth: 120, //搜索框标题宽度 默认80
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: true,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            disabledDate(time) {
                                return time.getTime() < Date.now();
                            }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "商品名称",
                        prop: "goodsName",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: false,
                        props: {
                            label: 'name',
                            value: 'goodGoodId'
                        },
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "商品金额",
                        prop: "totalAmt",
                        type: "number",
                        labelWidth: 140,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: true,
                            message: "请输入采购总额",
                            trigger: "blur"
                        } ],
                        precision: 2,
                        display: false
                    },
                    {
                        label: "调出仓库",
                        prop: "outWarehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "调入仓库",
                        prop: "inWarehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "登记人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                        display: false
                    },
                    {
                        label: "登记时间",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                        display: false
                    },
                ]
            },
            data: [],
            isShowOrder: false,
            isShowSelectGoods: false,
            isShowSelectOrder: false,

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },

            //新增订单单弹窗参数 start
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            isShowOrderDetail: false,
            supplierList: [],
            salesmanList: [],
            supplierCustomerId: '',
            businessDate: '',
            salesman: '',
            supplierValue: {},
            queryOrder: {},
            rowOrder: {},
            orderLoading: true,
            orderData: [],
            orderPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            orderForm: {},
            orderOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "调出仓库",
                        prop: "outWarehouseId",
                        type: "select",
                        cell: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateOrder(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        // rules: [{
                        //     required: true,
                        //     message: "请选择订单仓库",
                        //     trigger: "blur"
                        // }],
                        search: true,
                    },
                    {
                        label: "调入仓库",
                        prop: "inWarehouseId",
                        type: "select",
                        cell: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateOrder(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        // rules: [{
                        //     required: true,
                        //     message: "请选择订单仓库",
                        //     trigger: "blur"
                        // }],
                        search: true,
                    },
                    {
                        label: "调出仓现有库存",
                        prop: "stockOutWarehouse",
                        type: "input",
                        dataType:'number',
                        disabled: true,
                        cell: false,
                    },
                    {
                        label: "调入仓现有库存",
                        prop: "stockInWarehouse",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        disabled: true,
                    },
                    {
                        label: "移仓数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        // rules: [{
                        //     required: true,
                        //     message: "请输入订单数量",
                        //     trigger: "blur"
                        // }],
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateOrder(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "订单单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: false,
                        hide: true,
                        // rules: [{
                        //     required: true,
                        //     message: "请输入订单单价",
                        //     trigger: "blur"
                        // }],
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateOrder(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "订单金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        hide: true,
                        rules: [{
                            required: false,
                            message: "请输入订单金额",
                            trigger: "blur"
                        }],
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            //新增订单单弹窗参数 end

            //选择商品弹窗参数 start
            queryGoodsList: {},
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择一类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
            //选择商品弹框参数 end

            approveVisible: false,

        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
    },
    watch: {
        //计算合计金额
        "orderData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.orderData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },
        //增加
        addOrderHandler () {
            this.form = {};
            this.businessDate = '';//new Date();
            this.supplierCustomerId = '';
            this.salesman = '';
            this.editType = 'add';
            this.isShowOrder = true;
        },
        //审核
        auditOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.approveVisible = true;
        },
        //删除
        deleteOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //撤单
        rebackOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.$confirm("确定将选择单据撤销?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return rebackOrder(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //关闭
        closeOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.$confirm("确定将选择单据关闭?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return close(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //启用
        openOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.$confirm("确定将选择单据启用?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return open(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //入库
        orderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
            this.$confirm("确定将选择单据入库?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return stockin(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //打印
        printOrderHandler () {
            if (this.ids=='') {
                this.$message.warning("请选择操作的行");
                return;
            }
        },
        //关闭新增窗口
        closeAddForm() {
            if (this.orderData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowOrder = false;
                    });
            }else{
                this.isShowOrder = false;
            }
        },
        //关闭选择商品窗口
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        //关闭显示订单窗口
        closeShowForm(){
            this.isShowOrderDetail = false;
        },
        //打开查看详情窗口
        viewRow(row,index) {
            this.editType = 'view';
            this.form = row;
            this.orderOnLoad(this.orderPage)
            this.isShowOrderDetail = true;
        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        rowSave(row, loading, done) {
            console.log(row)
            add(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
                this.$router.go(0)
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {

            console.log(row)
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {

                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //主界面基本方法 end

        //选择商品基本方法 start
        selectionClearGoodsList() {
            this.selectionList = [];
            this.$refs.crudSelectGoods.toggleSelection();
        },
        searchChange2(params, done) {
            this.queryGoodsList = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        searchReset2() {
            this.queryGoodsList = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange2(list) {
            this.selectionList = list;
        },
        currentChange2(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        goodsListOnLoad(page, params = {}) {
            this.goodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.queryGoodsList)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.length;
                this.goodsListData = data;
                this.goodsListLoading = false;
                this.selectionClearGoodsList();
            });
        },
        addGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.orderRefreshData(row);
            this.isShowSelectGoods = false
        },
        //选择商品基本方法 end

        //新增采购订单单基本方法 start
        selectionClearOrder() {
            this.selectionList = [];
            try{
                this.$refs.crudOrder.toggleSelection();
            }catch(err){console.error(err);}
        },
        saveSelectHandle() {
            //保存前先检查数据
            //检测单据日期
            if (this.businessDate === undefined || this.businessDate === '') {
                this.$message.warning("请选择单据日期");
                return;
            }
            //如果当前保存为空，则不提示
            if (this.orderData.length==0) {
                this.$message.warning("请选择商品")
                return;
            }

            var isSave = true;
            this.orderData.forEach((item,index)=>{
                var outWarehouseId = item.outWarehouseId;
                var inWarehouseId = item.inWarehouseId;
                var qty = item.qty;
                var price = item.price;
                var stockOutWarehouse = item.stockOutWarehouse;
                var stockInWarehouse = item.stockInWarehouse;

                if (outWarehouseId==='') {
                    isSave = false;
                    this.$message.warning("第"+index+"行,未选择调出仓库");
                    return;
                }
                if (inWarehouseId==='') {
                    isSave = false;
                    this.$message.warning("第"+index+"行,未选择调入仓库");
                    return;
                }

                if (outWarehouseId===inWarehouseId) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,调出仓库与调入仓库不能相同");
                    return;
                }

                if (qty===0) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,移仓数量不正确");
                    return;
                }

                if (qty>stockOutWarehouse) {
                    isSave = false;
                    this.$message.warning("移仓数量不能超过库存数量,当前"+stockOutWarehouse+",请求"+qty);
                    return;
                }

                item.amt = qty*price;
            });

            if (!isSave) {
                return;
            }

            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    this.form.supplierCustomerId = this.supplierCustomerId;
                    this.form.businessDate = this.businessDate;
                    this.form.salesman = this.salesman;
                    if (this.editType === 'edit') {
                        this.orderLoading = true;
                        update(this.form,this.orderData).then((res) => {
                            this.orderLoading = false;
                            if (res.data.code == 200) {
                                this.onLoad(this.page);
                                this.$message({
                                    type: "success",
                                    message: "操作成功!"
                                });
                                this.isShowOrder = false;
                            }else{
                                this.$message({
                                    type: "error",
                                    message: "保存失败:"+res.data.message,
                                });
                            }
                        }, error => {
                            window.console.log(error);
                        });
                    }else {
                        this.orderLoading = true;
                        add(this.form,this.orderData).then((res) => {
                            this.orderLoading = false;
                            if (res.data.code == 200) {
                                this.onLoad(this.page);
                                this.$message({
                                    type: "success",
                                    message: "操作成功!"
                                });
                                this.isShowOrder = false;
                            }else{
                                this.$message({
                                    type: "error",
                                    message: "保存失败:"+res.data.message,
                                });
                            }
                        }, error => {
                            window.console.log(error);
                        });
                    }
                })
                .then(() => {
                    this.isShowOrder = false;
                });
        },
        selectGoodHandler() {
            this.isShowSelectGoods = true;
        },
        selectOrderHandler() {
            this.isShowSelectOrder = true;
        },
        beforeOpenOrder(done, type) {
            done();
        },
        searchChangeOrder(params, done) {
            this.queryOrder = params;
            this.orderPage.currentPage = 1
            this.orderOnLoad(this.orderPage, params);
            done();
        },
        searchResetOrder() {
            this.queryOrder = {};
            this.orderOnLoad(this.orderPage);
        },
        selectionChangeOrder(list) {
            this.selectionList = list;
        },
        currentChangeOrder(currentPage) {
            this.orderPage.currentPage = currentPage;
        },
        sizeChangeOrder(pageSize) {
            this.orderPage.pageSize = pageSize;
        },
        orderOnLoad(page, params = {}) {
            this.orderLoading = true;
            if (this.form.id!==undefined && this.form.id!=='') {
                getDetail(this.form.id).then(res => {
                    const data = res.data.data;
                    this.businessDate = data.businessDate;
                    this.supplierCustomerId = data.supplierCustomerId;
                    this.salesman = data.salesman;
                    this.createUserName = data.createUserName;
                    this.salesmanName = data.salesmanName;
                    this.orderData = data.detailList;
                    this.orderPage.total = this.orderData.length;
                    var row = 0;
                    this.orderData.forEach((item) => {
                        row++;
                        item.rowId = row;
                        item.$cellEdit = false;
                    });
                    this.orderLoading = false;
                    this.selectionClearOrder();
                });
            }else{
                this.orderPage.total = 0;
                this.orderData = [];
                this.orderData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.orderLoading = false;
                this.selectionClearOrder();
            }
        },
        orderRefreshData(row) {
            row.warehouseId = "";
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            this.orderData.push(row);
        },
        deleteRowOrder(row) {
            // console.log(row)
        },
        //新增采购订单单基本方法 end

        //新增采购订单行编辑方法 start

        rowCellOrder(row, index) {
            this.rowOrder= row;
            this.$refs.crudOrder.rowCell(row, index)
            // this.$refs.crudOrder.rowCancel(row, row.$index);
        },
        async rowSaveOrder(row, index) {
            //修改orderData对应的row
            //计算对应行的采购金额
            var qty = row.qty;
            var price = row.price;
            var goodsId = row.id;
            await stockLoad(row.outWarehouseId,goodsId).then(res => {
                var stockAmount = res.data.data.stockAmount;
                row.stockOutWarehouse = stockAmount;
                if(stockAmount - qty < 0){
                    this.$message.warning("移仓数量不能超过库存数量,当前"+stockAmount+",请求"+qty);
                }
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
            await stockLoad(row.inWarehouseId,goodsId).then(res => {
                var stockAmount = res.data.data.stockAmount;
                row.stockInWarehouse = stockAmount;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});

            var amt = qty*price;
            row.amt = amt;

            console.log("=====>",row);

            this.orderData[index] = row;
            row.$cellEdit = false;
            this.$refs.crudOrder.rowCellUpdate();
            //合计所有的行
            this.summaryAmt();
        },
        rowCancelEditOrder(row, index) {
            // this.$refs.crudEx.rowCancel(row, index);
            this.onLoad(this.page,this.query);
        },
        rowUpdateOrder(row, index, loading, done) {
            done()
        },
        rowDelOrder(row) {
        },
        handleRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["warehouseId", "qty", "price", "amt"].includes(column.property)) {
                    this.$refs.crudOrder.rowCell(row, row.$index);
                }
            }
        },
        //新增采购订单行编辑方法 end
        exports(params = {}){
            const loading = this.$loading({
                lock: true,
                text: '正在导出数据，请稍后',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
            });
            params.isSupplyFlag = 1
            exportWebData(Object.assign(params, this.query)).then(res => {
                loading.close();
                const blob = new Blob([res.data]);
                const fileName = '移仓单数据.xlsx';
                const linkNode = document.createElement('a');

                linkNode.download = fileName; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击

                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
            })
        },

        //审核
        reviewCheck(status) {
            this.$confirm('确定此操作？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                return approve(this.ids, status);
            }).then(data => {
                if (data.data.success) {
                    this.$message({
                        type: "success",
                        message: "操作成功"
                    });
                    this.chanel();
                    this.onLoad(this.page);
                } else {
                    this.$message({
                        showClose: true,
                        message: "操作失败",
                        type: 'error'
                    })
                }
            })
        },
        //取消
        chanel(){
            this.approveVisible = false;
        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
