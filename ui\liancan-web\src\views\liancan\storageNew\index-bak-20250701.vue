<template>
  <basicContainer>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="totalQuantity" slot-scope="scope">
        <span size="medium" type="blue">共{{scope.row.totalQuantity}}种商品</span>
      </template>
      <template slot="sendTime" slot-scope="scope">
        <span>{{scope.row.sendTime}}之前</span>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="small" icon="el-icon-view" @click="opentForm2(scope.row)">验收进仓
        </el-button>
<!--        <el-button v-if="permission.orderAccepter" type="text" size="mini" icon="el-icon-bottom-right" @click.native="putAllGoodsStorage(scope.row)">验收-->
<!--        </el-button>-->
      </template>
      <template slot="menuLeft">
        <el-button v-if="permission.orderAccepterBatch" type="primary" size="small" icon="el-icon-circle-plus-outline" @click="confirmBatch2">一键验收</el-button>
      </template>
    </avue-crud>

    <el-dialog title="订单详情" :visible.sync="isShow" :append-to-body="true" @close="closeForm" width="80%">
      <avue-form ref="orderForm" :option="orderOption" v-model="orderForm">
      </avue-form>
      <div>
        <span class="fontsize" style="margin-right: 16px;">进仓确认历史：</span>
        <el-button class="fontsize" type="text" size="small" @click="opentForm3">查看</el-button>
      </div>
      <avue-crud
          ref="crud2"
          v-model="orderDetailForm"
          :option="orderDetailOption"
          :data="orderDetailData"
          :page="orderDetailPage"
          :table-loading="orderDetailLoading"
          @on-load="orderDetailOnLoad"
          @row-update="addUpdate">
        <template slot="menu" slot-scope="scope">
<!--          <el-button type="text" size="small" icon="el-icon-view" @click="checkCertificate(scope.row)">质量合格证明-->
<!--          </el-button>-->
        </template>
      </avue-crud>
      <div class="dialog_button">
        <el-button size="small" type="primary" @click="submitWarehousing(0)">直接进仓</el-button>
        <el-button size="small" type="primary" @click="submitWarehousing(1)">提交确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="验收信息" :visible.sync="isShowSub" :append-to-body="true" @close="closeFormSub" width="50%">
      <avue-form ref="storageForm" :option="storageOption" v-model="storageForm"  @submit="saveStorage">
      </avue-form>
    </el-dialog>

<!--    <el-dialog title="一键验收信息" :visible.sync="isShowSub2" :append-to-body="true" @close="closeFormSub2" width="50%">-->
<!--      <avue-form ref="storageForm2" :option="storageOption2" v-model="storageForm2"  @submit="saveStorage2">-->
<!--      </avue-form>-->
<!--    </el-dialog>-->

    <el-dialog title="一键进仓" :visible.sync="isShowSub3" :append-to-body="true" @close="closeFormSub3" width="50%">
      <div class="dialog_button" style="font-size: 20px;margin-bottom: 50px;">
        <span>当前已选</span><span style="color: red">{{selectionList.length}}</span><span>张采购单</span>
      </div>
      <div class="dialog_button">
        <el-button size="small" type="primary" @click="saveStorage3(0)">直接进仓</el-button>
        <el-button size="small" type="primary" @click="saveStorage3(1)">提交确认</el-button>
      </div>
    </el-dialog>

    <el-dialog title="进仓确认历史" :visible.sync="isShowSub4" :append-to-body="true" @close="closeFormSub4" width="50%">
      <avue-crud
        ref="crud"
        v-model="orderDetailForm2"
        :option="orderDetailOption2"
        :data="orderDetailData2"
        @on-load="orderDetailOnLoad2"
        :page="orderDetailPage2"
        :table-loading="orderDetailLoading2">
      </avue-crud>
    </el-dialog>
  </basicContainer>
</template>
<script>
    import {add, update, remove, orderDetailList, orderDetail,getAccepteOrderList,subAccepteOrder,getAccepterLog,subBatchAccepteOrder} from "@/api/liancan/order";
    import {optionOne as orderFormOption} from "@/const/order/index";
    import {mapGetters} from "vuex";
    var DIC = {
        isPutaway: [{
            label: '纸质发票',
            value: "0"
        },{
            label: '电子发票',
            value: "1"
        }],
        orderStatus: [{
            label: '未接单',
            value: "0"
        },{
            label: '食堂已收',
            value: "1"
        },{
            label: '取消/拒单',
            value: "2"
        },{
            label: '已送达',
            value: "3"
        },{
            label: '配送中',
            value: "4"
        }],
        isBuy: [{
            label: '未确认',
            value: "0"
        },{
            label: '确认通过',
            value: "1"
        },{
            label: '确认拒绝',
            value: "2"
        }],
        receiveStatus: [{
            label: '未收货',
            value: "0"
        },{
            label: '已收货',
            value: "1"
        }],
        accepterStatus: [{
            label: '未验收',
            value: "0"
        },{
            label: 'A角验收',
            value: "1"
        },{
            label: '通过',
            value: "2"
        },{
            label: '不通过',
            value: "3"
        }],
    }
    export default {
        data() {
            return {
                form: {},
                query: {},
                loading: true,
                orderDetailLoading: true,
                orderDetailLoading2: true,
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                orderDetailPage: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                orderDetailPage2: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                selectionList: [],
                orderDetailForm: {},
                orderDetailForm2: {},
                orderForm: {},
                btnShow: true,
                orderDetailOption2: {
                    align: "center",
                    height: 200,
                    calcHeight: 30,
                    searchShow: false,
                    searchMenuSpan: 6,
                    tip: false,
                    border: true,
                    index: false,
                    selection: false,
                    menu: false,
                    viewBtn: false,
                    addBtn: false,
                    editBtn: false,
                    delBtn: false,
                    column: [{
                            label: 'id',
                            prop: 'id',
                            type: 'input',
                            hide: true,
                            display: false,
                            showColumn: false,
                        },
                        {
                            label: "验收进仓确认人",
                            prop: "name",
                            type: "input",
                            width: 150,
                        },
                        {
                            label: "进仓确认时间",
                            prop: "createTime",
                            type: "datetime",
                            format: "yyyy-MM-dd HH:mm:ss",
                            valueFormat: "yyyy-MM-dd HH:mm:ss",
                            width: 170,
                        },
                        {
                            label: "进仓确认结果",
                            prop: "accepterStatus",
                            type: "select",
                            dicData: [
                                {
                                    label: "进仓通过",
                                    value: '0'
                                },
                                {
                                    label: "进仓不通过",
                                    value: '1'
                                },
                            ],
                            width: 120,
                        },
                        {
                            label: "备注",
                            prop: "remark",
                            type: "input",
                        },
                    ],
                },
                orderDetailOption: {
                    align: "center",
                    height: 250,
                    calcHeight: 30,
                    searchShow: false,
                    searchMenuSpan: 6,
                    tip: false,
                    border: true,
                    index: false,
                    selection: false,
                    viewBtn: false,
                    addBtn: false,
                    editBtn: false,
                    delBtn: false,
                    cellBtn:true,
                    column: [
                      {
                        label: 'id',
                        prop: 'id',
                        type: 'input',
                        hide: true,
                        display: false,
                      },
                      {
                        label: "商品",
                        prop: "goodsId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
                        props: {
                          label: "name",
                          value: "id"
                        },
                      },
                      {
                        label: "单价",
                        prop: "price",
                        type: "number",
                        precision:2,
                        // mock:{
                        //   type:'number',
                        //   max:1,
                        //   min:2,
                        //   precision:2
                        // },
                        //minRows: 0,
                      },
                      {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                          label: "dictValue",
                          value: "dictKey"
                        },
                      },
                      {
                        label: "采购下单数量",
                        prop: "quantity",
                        type: "number",
                      },
                      {
                        label: "实际验收数量(可更改)",
                        prop: "realQuantity",
                        // type: "number",
                        // rules: [{
                        //   required: true,
                        //   message: '请输入实际验收数量',
                        //   trigger: 'blur'
                        // }],
                        cell: true,
                      },
                      {
                        label: "小计",
                        prop: "subtotal",
                        type: "number",
                        precision: 2,
                        // mock:{
                        //   type:'number',
                        //   max:1,
                        //   min:2,
                        //   precision:2
                        // },
                        //minRows: 0,
                      },
                    ],
                },
                orderOption: {
                    emptyBtn: false,
                    submitBtn: false,
                    column: [
                        {
                            label: "采购单号",
                            prop: "id",
                            type: "input",
                            disabled: true,
                            span: 6,
                        },
                        {
                            label: "供应商",
                            prop: "supplierId",
                            type: "select",
                            dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
                            props: {
                                label: "name",
                                value: "id"
                            },
                            disabled: true,
                            span: 6,
                            row:true,
                            offset:3,
                        },
                        {
                            label: "采购方",
                            prop: "deptId",
                            type: "select",
                            dicUrl: '/api/service/rabbit-system/dept/dict',
                            props: {
                                label: "deptName",
                                value: "id"
                            },
                            disabled: true,
                            span: 6,
                        },
                        {
                            label: "采购人",
                            prop: "createUser",
                            type: "select",
                            dicUrl: '/api/service/rabbit-user/user-list',
                            props: {
                                label: "realName",
                                value: "id"
                            },
                            disabled: true,
                            span: 6,
                            row:true,
                            offset:3,
                        },
                        {
                            label: "发票",
                            prop: "invoiceId",
                            hide: true,
                            type: "select",
                            dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
                            props: {
                                label: "num",
                                value: "id"
                            },
                            disabled: true,
                            span: 6,
                        },
                        {
                            label: "采购确认人",
                            prop: "verifyUserId",
                            type: "select",
                            dicUrl: '/api/service/rabbit-user/user-list',
                            props: {
                                label: "realName",
                                value: "id"
                            },
                            disabled: true,
                            span: 6,
                            row:true,
                            offset:3,
                        },
                        {
                            label: "确认状态",
                            prop: "isBuy",
                            type: "select",
                            dicData: DIC.isBuy,
                            disabled: true,
                            span: 6,
                        },
                        {
                            label: "商品种类数",
                            prop: "totalQuantity",
                            type: "input",
                            slot: true,
                            disabled: true,
                            span: 6,
                            row:true,
                            offset:3,
                        },
                        {
                            label: "总价",
                            prop: "totalPrices",
                            type: "number",
                            precision:2,
                            mock:{
                                type:'number',
                                max:1,
                                min:2,
                                precision:2
                            },
                            display: false,
                        },
                        {
                            label: "配送时间",
                            prop: "sendTime",
                            type: "datetime",
                            format: "yyyy-MM-dd HH:mm",
                            valueFormat: "yyyy-MM-dd HH:mm:ss",
                            slot: true,
                            disabled: true,
                            span: 6,
                        },
                        {
                            label: "订单状态",
                            prop: "orderStatus",
                            type: "select",
                            dicData: DIC.orderStatus,
                            disabled: true,
                            span: 6,
                            row:true,
                            offset:3,
                        },
                        {
                            label: "送货地址",
                            prop: "site",
                            type: "input",
                            disabled: true,
                            span: 6,
                        },
                        {
                            label: "电话",
                            prop: "phone",
                            type: "input",
                            hide: true,
                            disabled: true,
                            span: 6,
                            row:true,
                            offset:3,
                        },
                        {
                            label: "联系人",
                            prop: "userName",
                            type: "input",
                            hide: true,
                            disabled: true,
                            span: 6,
                            row:true,
                        },
                    ],
                },
                storageOption: {
                    emptyBtn: false,
                    submitBtn: true,
                    //submitText: '保存',
                    column: [
                        {
                            label: 'id',
                            prop: 'id',
                            type: 'input',
                            hide: true,
                            display: false,
                            showColumn: false,
                        },
                        {
                            label: "进仓确认人",
                            prop: "verifyUserId",
                            type: "select",
                            multiple:true,
                            dicUrl: '/api/service/rabbit-user/user-list/1287918603143741442',
                            display:true,
                            props: {
                                label: "realName",
                                value: "id"
                            },
                            rules: [{
                                required: false,
                                message: '请选择进仓确认人',
                                trigger: 'blur'
                            }],
                        },
                        {
                            label: "验收进仓信息",
                            prop: 'goodsList',
                            type: 'dynamic',
                            hide: true,
                            span: 24,
                            children: {
                                align: 'center',
                                headerAlign: 'center',
                                width: '100%',
                                addBtn: false,
                                delBtn: false,
                                column: [{
                                      label: "商品",
                                      prop: "goodsId",
                                      type: "input",
                                      hide: true,
                                    },
                                    {
                                        label: "商品",
                                        prop: "goodsName",
                                        type: "input",
                                        readonly: true,
                                    },
                                    {
                                        label: "采购数量",
                                        prop: "quantity",
                                        type: "input",
                                        readonly: true,
                                    },
                                    {
                                        label: "验收进仓数量",
                                        prop: "realQuantity",
                                        type: "number",
                                        rules: [{
                                            required: true,
                                            message: '请输入数量',
                                            trigger: 'blur'
                                        }],
                                    },
                                ]
                            },
                        },
                    ],
                    disabledFlag:true,
                },
                storageOption2: {
                    emptyBtn: false,
                    submitBtn: true,
                    column: [
                        {
                            label: 'id',
                            prop: 'id',
                            type: 'input',
                            hide: true,
                            display: false,
                            showColumn: false,
                        },
                        {
                            label: "验收进仓确认人",
                            prop: "verifyUserId",
                            type: "select",
                            display:true,
                            multiple:true,
                            dicUrl: '/api/service/rabbit-user/user-list/1287918603143741442',
                            props: {
                                label: "realName",
                                value: "id"
                            },
                            rules: [{
                                required: false,
                                message: '请选择进仓确认人',
                                trigger: 'blur'
                            }],
                        }
                    ]
                },
                option: {
                /*    height:'auto',
                    calcHeight: 30,*/
                    searchShow: true,
                    searchMenuSpan: 6,
                    tip: false,
                    border: true,
                    index: false,
                    viewBtn: false,
                    selection: true,
                    addBtn: false,
                    editBtn: false,
                    delBtn: false,
                    menuWidth: 150,
                    align: "center",
                    column: [
                        {
                          label: "食堂",
                          prop: "deptId",
                          type: "tree",
                          dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
                          props: {
                            label: "deptName",
                            value:"id"
                          },
                          hide: true,
                          addDisplay: false,
                          search:true,
                        },
                        {
                          label: "食堂",
                          prop: "deptName",
                          type: "input",
                        },
                        {
                            label: "采购单号",
                            prop: "id",
                            type: "input",
                            width: 160,
                        },
                        {
                            label: "供应商",
                            prop: "supplierName",
                            type: "input",
                            overHidden: true,
                            width: 170,
                        },
                        {
                            label: "采购人",
                            prop: "userName",
                            type: "input",
                        },
                        {
                            label: "下单时间",
                            prop: "createTime",
                            type: "datetime",
                            format: "yyyy-MM-dd HH:mm:ss",
                            valueFormat: "yyyy-MM-dd HH:mm:ss",
                            display: false,
                            width: 150,
                        },
                        {
                            label: "发票",
                            prop: "invoiceId",
                            hide: true,
                            type: "select",
                            dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
                            props: {
                                label: "num",
                                value: "id"
                            },
                        },
                        {
                            label: "采购确认人",
                            prop: "verifyUserName",
                            type: "input",
                        },
                        {
                            label: "采购确认状态",
                            prop: "isBuy",
                            type: "select",
                            dicData: DIC.isBuy,
                            width: 120,
                        },
                        {
                            label: "商品种类数",
                            prop: "totalQuantity",
                            type: "input",
                            slot: true,
                            display: false,
                            hide: true,
                        },
                        {
                            label: "配送时间",
                            prop: "sendTime",
                            type: "datetime",
                            format: "yyyy-MM-dd HH:mm",
                            valueFormat: "yyyy-MM-dd HH:mm:ss",
                            slot: true,
                            width: 170,
                            hide: true,
                        },
                        {
                            label: "订单状态",
                            prop: "orderStatus",
                            type: "select",
                            dicData: DIC.orderStatus,
                            width: 90,
                            hide: true,
                        },
                        {
                            label: "送货地址",
                            prop: "site",
                            type: "input",
                            hide: true,
                        },
                        {
                            label: "电话",
                            prop: "phone",
                            type: "input",
                            hide: true,
                        },
                       {
                            label: "收货状态",
                            prop: "receiveStatus",
                            type: "select",
                            dicData: DIC.receiveStatus,
                            display: false,
                            hide: true,
                        },
                        {
                            label: "收货时间",
                            prop: "receiveTime",
                            type: "input",
                            display: false,
                            hide: true,
                        },
                    ],
                },
                orderDetailData: [],
                orderDetailData2: [],
                data: [],
                orderFormOption: orderFormOption,
                isShow: false,
                //resOrder: [],
                resOrderId: null,
                storageForm: {
                    goodsList: [],
                },
                storageForm2: {},
                isShowSub: false,
                isShowSub2: false,
                isShowSub3: false,
                isShowSub4: false,
                putType: null,
                //resGoodsList: [],
                showRow: {},
            };
        },
        computed: {
            ...mapGetters(["permission","userInfo"]),
            /* permissionList() {
               return {
                 addBtn: this.vaildData(this.permission.order_add, false),
                 viewBtn: this.vaildData(this.permission.order_view, false),
                 delBtn: this.vaildData(this.permission.order_delete, false),
                 editBtn: this.vaildData(this.permission.order_edit, false)
               };
             },*/
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            }
        },
      created(){
        if (this.userInfo.userType === 'canteen'){
          this.option.column[0].search = false;
          // this.option.column[1].hide = true;
        }
      },
        methods: {
            rowSave(row, loading, done) {
                add(row).then(() => {
                    loading();
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                }, error => {
                    done();
                    window.console.log(error);
                });
            },
            rowUpdate(row, index, loading, done) {
                update(row).then(() => {
                    loading();
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                }, error => {
                    done();
                    window.console.log(error);
                });
            },
            rowDel(row) {
                this.$confirm("确定将选择数据删除?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        return remove(row.id);
                    })
                    .then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    });
            },
            handleDelete() {
                if (this.selectionList.length === 0) {
                    this.$message.warning("请选择至少一条数据");
                    return;
                }
                this.$confirm("确定将选择数据删除?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                        return remove(this.ids);
                    })
                    .then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                        this.$refs.crud.toggleSelection();
                    });
            },
            beforeOpen(done, type) {
                /*if (["edit", "view"].includes(type)) {
                  getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                  });
                }*/
                done();
            },
            searchReset() {
                this.query = {};
                this.onLoad(this.page);
            },
            searchChange(params, done) {
                this.query = params;
                this.page.currentPage = 1
                this.onLoad(this.page, params);
                done();
            },
            selectionChange(list) {
                this.selectionList = list;
            },
            selectionClear() {
                this.selectionList = [];
                this.$refs.crud.toggleSelection();
            },
            currentChange(currentPage){
                this.page.currentPage = currentPage;
            },
            sizeChange(pageSize){
                this.page.pageSize = pageSize;
            },
            onLoad(page, params = {}) {
                this.loading = true;
                getAccepteOrderList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.data = data.records;
                    this.loading = false;
                    this.selectionClear();
                });
            },
            orderDetailOnLoad(orderDetailPage) {
               this.orderDetailLoading = true;
                orderDetailList(orderDetailPage.currentPage, orderDetailPage.pageSize, Object.assign({
                    orderId : this.resOrderId
                })).then(res => {
                    const data = res.data.data;
                    this.orderDetailPage.total = data.total;
                    this.orderDetailData = data.records;
                    // data.records.forEach(row=>{
                    //   if (row.price % 1 === 0){
                    //     row.price += '.00'
                    //   }else if (row.price.toString().split(".")[1].length === 1){
                    //     row.price += '0'
                    //   }
                    //   if (row.subtotal % 1 === 0){
                    //     row.subtotal += '.00'
                    //   }else if (row.subtotal.toString().split(".")[1].length === 1){
                    //     row.subtotal += '0'
                    //   }
                    // })
                    this.orderDetailLoading = false;
                });

            },
            addUpdate(form,index,done,loading){
              loading();
              if (form.realQuantity == null || form.realQuantity == '') {
                this.$message({
                  type: 'warning',
                  message: '实际回收数量不能为空',
                  duration: 1000,
                })
                return false
              }
              if (isNaN(form.realQuantity)) {
                this.$message({
                  type: 'warning',
                  message: '请输入数字',
                  duration: 1000,
                })
                return false
              } else {
                let real = parseInt(form.realQuantity);
                form.subtotal = real * form.price
              }
              done();
            },
            submitWarehousing(type) {
              let message = type == 1 ? '请确保各商品的收货数量正确无误。确定提交？' :
                '请确保各商品的收货数量正确无误。本操作将会跳过进仓确认环节，直接增加库存数量及金额。确定提交？'
              this.$confirm(message, '提示', {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
              })
                .then(() => {
                  let form = {
                    id: this.orderForm.id,
                    storageType: type,
                    goodsList: this.orderDetailData,
                  }
                  return subAccepteOrder(form);
                })
                .then(() => {
                  let message2 = type == 1 ? '操作成功！请通知进仓确认人及时审核。通过进仓确认后，订单中的各种商品的库存数量及金额才会增加！'
                    : '完成验收进仓操作，进货商品的库存数量及金额已增加。'
                  this.$message({
                    type: "success",
                    message: message2,
                    duration: 1500
                  });
                  setTimeout(() => {
                    this.onLoad(this.page);
                    this.isShowSub = false
                    this.isShowSub3 = false
                    this.isShow = false
                  }, 1500);
                });
            },
            orderDetailOnLoad2(orderDetailPage) {
                this.orderDetailLoading2 = true;
                getAccepterLog(orderDetailPage.currentPage, orderDetailPage.pageSize, Object.assign({
                    orderId : this.resOrderId
                })).then(res => {
                    const data = res.data.data;
                    this.orderDetailPage2.total = data.total;
                    this.orderDetailData2 = data.records;
                    this.orderDetailLoading2 = false;
                });

            },
            //打开入库弹窗
            opentForm: function(row) {
                this.btnShow = true;
                this.orderDetailOption.menu = true;
                this.isShow = true;
                this.resOrderId = row.id;
                if (row.verifyUserId == 0){
                    row.verifyUserId = ""
                }
                this.orderForm = row;
                this.orderDetailOnLoad(this.orderDetailPage);
            },
            //打开入库弹窗 查看使用
            opentForm2: function(row) {
                this.btnShow = true;
                this.orderDetailOption.menu = true;
                this.isShow = true;
                this.resOrderId = row.id;
                 if (row.verifyUserId == 0){
                    row.verifyUserId = ""
                }
                this.orderForm = row;
                this.orderDetailOnLoad(this.orderDetailPage);
                // this.orderDetailOnLoad2(this.orderDetailPage2);
            },
            //打开进仓确认历史弹窗
            opentForm3: function () {
              this.isShowSub4 = true;
              this.orderDetailOnLoad2(this.orderDetailPage2);
            },
            //关闭入库弹窗
            closeForm: function() {
                this.isShow = false
            },
            //关闭入库填写信息弹窗
            closeFormSub: function() {
                this.isShowSub = false;
                this.$refs.storageForm.resetForm()
            },
            closeFormSub2: function() {
                this.isShowSub2 = false;
                this.$refs.storageForm2.resetForm()
            },
            closeFormSub3: function () {
                this.isShowSub3 = false;
            },
            closeFormSub4: function () {
                this.isShowSub4 = false;
            },
            confirmBatch(){
                if(this.selectionList != null && this.selectionList.length > 0){
                    this.isShowSub2 = true;
                }else{
                    this.$message.warning('请选择至少一条数据');
                }
            },
            confirmBatch2() {
                if(this.selectionList != null && this.selectionList.length > 0){
                  this.isShowSub3 = true;
                }else{
                  this.$message.warning('请选择至少一条数据');
                }
            },
            putAllGoodsStorage(row) {
                this.putType = 0;
                this.isShowSub = true;
                //非食材确认入库无需选择确认人
                if(row.goodsType == 0){
                    this.storageOption.column[1].display = true;
                }else{
                    this.storageOption.column[1].display = false;
                }
                orderDetail(row.id).then(res => {
                    let obj2 = {
                        id: row.id,
                        verifyUserId:null,
                        goodsList: res.data.data
                    };
                    this.storageForm = obj2
                });
            },
            saveStorage(form, done) {
                var ids = form.verifyUserId;
                form.verifyUserIdList = ids;
                form.verifyUserId = '';
                subAccepteOrder(form).then(res => {
                        if (res.data.success) {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'success'
                            })
                            this.onLoad(this.page);
                            this.isShowSub = false
                            this.isShow = false
                            this.$refs.storageForm.resetForm()
                        }else {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'error'
                            })
                        }
                    });
                done()
            },
            saveStorage2(form, done) {
                if(this.selectionList != null && this.selectionList.length > 0){
                    let list = [];
                    this.selectionList.forEach(s => {
                        list.push(s.id);
                    });
                    subBatchAccepteOrder(Object.assign({
                        orderDetailIdList: list,
                        verifyUserIdList: form.verifyUserId,
                    })).then(res => {
                        if (res.data.success) {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'success'
                            })
                        }else {
                            this.$message({
                                showClose: true,
                                message: res.data.message,
                                type: 'error'
                            })
                        }
                        this.onLoad(this.page);
                    });
                }else{
                    this.$message.warning("请选择至少一条数据");
                }
                this.isShowSub2 = false;
                done()
            },
            saveStorage3(type) {
              if(this.selectionList != null && this.selectionList.length > 0){
                let list = [];
                this.selectionList.forEach(s => {
                  list.push(s.id);
                });
                let message = type == 1 ? '请确保各商品的收货数量正确无误。确定提交？' :
                  '请确保各商品的收货数量正确无误。本操作将会跳过进仓确认环节，直接增加库存数量及金额。确定提交？'
                this.$confirm(message, '提示', {
                  confirmButtonText: "确定",
                  cancelButtonText: "取消",
                  type: "warning"
                })
                  .then(() => {
                    let form = {
                      orderDetailIdList: list,
                      storageType: type
                    }
                    return subBatchAccepteOrder(form);
                  })
                  .then(() => {
                    let message2 = type == 1 ? '操作成功！请通知进仓确认人及时审核。通过进仓确认后，订单中的各种商品的库存数量及金额才会增加！'
                      : '完成验收进仓操作，进货商品的库存数量及金额已增加。'
                    this.$message({
                      type: "success",
                      message: message2,
                      duration: 1500
                    });
                    setTimeout(() => {
                      this.onLoad(this.page);
                      this.isShowSub = false
                      this.isShowSub3 = false
                      this.isShow = false
                    }, 1500);
                  });
              } else{
                this.$message.warning("请选择至少一条数据");
              }
            }

        }
    };
</script>

<style>
  .fontsize {
    font-weight: 700;
  }
  .dialog_button {
    width: 100%;
    display: flex;
    justify-content: center;
  }
</style>
