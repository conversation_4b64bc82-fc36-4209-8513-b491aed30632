<template>
  <basicContainer>
    <avue-crud v-model="form"
               ref="crud"
               :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :before-open="beforeOpen"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button class="filter-item" size="small" type="danger"  icon="el-icon-delete" plain @click="handleDelete">删除</el-button>
      </template>
<!--      <template slot-scope="{row}" slot="menu">-->
<!--        <el-button type="text" icon="el-icon-setting" size="small" plain style="border: 0;background-color: transparent !important;"-->
<!--                   @click.stop="disableHandler(row)">使用记录-->
<!--        </el-button>-->
<!--      </template>-->
    </avue-crud>
  </basicContainer>
</template>

<script>
import {
  getList,add, getDetail, remove, update,exportReport
} from "@/api/qrcodeTrace/basePrinterCtrl";
import {
  mapGetters
} from "vuex";
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: false,
      data: [],
      selectionList: [],
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: {
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        menu: true,
        addBtn: true,
        delBtn: true,
        viewBtn: false,
        selection: true,
        column: [
          {
            label: "食堂名称",
            prop: "deptId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value: "id"
            },
            search: true,
          },
          {
            label: "启用打印控制",
            prop: "useStatus",
            type: "select",
            dicData: [{
              label: "否",
              value: 0
            }, {
              label: "是",
              value: 1
            }],
            search: true,
          },
          {
            label: "当前张数",
            prop: "point",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "启用/停用操作人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "操作时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
        ]
      },
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.tableware_disinfection_add, false),
        viewBtn: this.vaildData(this.permission.tableware_disinfection_view, false),
        delBtn: this.vaildData(this.permission.tableware_disinfection_delete, false),
        editBtn: this.vaildData(this.permission.tableware_disinfection_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
      });
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    disableHandler(row){

    },
    handleDownload: function () {
      let loading;
      this.$confirm("确定导出数据?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          loading = this.$loading({
            lock: true,
            text: '正在导出数据，请稍后',
            spinner: 'el-icon-loading',
            background: 'rgba(0, 0, 0, 0.7)'
          });
          return exportReport(this.page.currentPage, this.page.pageSize, this.query);
        })
        .then((res) => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '添加剂台账.xlsx';
          const linkNode = document.createElement('a');
          linkNode.download = fileName;
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob);
          document.body.appendChild(linkNode);
          linkNode.click();
          URL.revokeObjectURL(linkNode.href);
          document.body.removeChild(linkNode);
        });
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
  }
}
</script>

<style scoped>

</style>
