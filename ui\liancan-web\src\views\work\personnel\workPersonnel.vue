<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.work_personnel_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot="avatar" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
      </template>
      <template slot="healthyCardPhoto" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.healthyCardPhoto" fit="cover" @click="handleClickPreview(row.healthyCardPhoto)"></el-image>
      </template>
    </avue-crud>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/work/personnel/workPersonnel";
  import {mapGetters} from "vuex";
  const DIC = {
    STATUS: [
      {
        label: '在职',
        value: "1"
      },{
        label: '已离职',
        value: "2"
      }
    ],
    PERSONSTATUS: [
      {
        label: '否',
        value: "0"
      },
      {
        label: '是',
        value: "1"
      }
    ],
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],}
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        dialogVisible:false,
        dialogImageUrl:undefined,
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          labelWidth: 150,
          dialogWidth: 900,
          searchSpan: 100,
          menuWidth: 180,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "人员编号",
              prop: "personnelNo",
              type: 'number',
              precision:0,
              mock:{
                type:'number',
                max:1,
                min:10,
                precision:0
              },
              width:100,
              row:true,
            },
    /*        {
              label: "人员编号",
              prop: "personnelNo",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入网点名称",
                trigger: "blur"
              }],
            },*/
            {
              label: "名字",
              prop: "realName",
              type: "input",
              search:true,
              rules: [{
                required: true,
                message: "请输入名字",
                trigger: "blur"
              }],
              width:120,
            },
            {
              label: "身份证号",
              prop: "idNumber",
              type: "input",
              search:true,
              maxlength: 18,
              minlength: 2,
              mock:{
                type:'name',
                en:true,
              },
              rules: [{
                required: true,
                message: "请输入身份证号",
                trigger: "blur"
              }],
              width:160,
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
              search:true,
              rules: [{
                required: true,
                message: '请选择性别',
                trigger: 'blur'
              }],
              width: 50,
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
              width:150,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
            },
            {
              label: "所属部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-canteen/DeptSetting/tree",
              props: {
                label: "title",
                value: "id"
              },
              /* multiple:true,*/
              slot:true,
              search:true,
              hide:true,
              rules: [{
                required: true,
                message: "请输入所属租户",
                trigger: "click"
              }]
            },
            {
              label: "人员照片",
              prop: "avatar",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              rules: [{
                required: true,
                message: "请上传人员照片",
                trigger: "blur"
              }],
              width: 70,
            },
            {
              label: "健康证照片",
              prop: "healthyCardPhoto",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              width: 90,
            },
            {
              label: "职位",
              prop: "position",
              type: "textarea",
              span: 24,
            },
            {
              label: '是否负责人',
              prop: 'outletsPerson',
              type: "radio",
              //slot: true,
              dicData: DIC.PERSONSTATUS,
              search:true,
            },
         /*   {
              label: "职责",
              prop: "duty",
              type: "textarea",
              span: 24,
            },*/
            {
              label: "联系电话",
              prop: "mobile",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入联系电话",
                trigger: "blur"
              }],
            },
            {
              label: "网点名称",
              prop: "outletsId",
              type: "select",
              span: 24,
              rules: [{
                required: true,
                message: "请输入营业网点",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              props: {
                label: "name",
                value: "id"
              },
            },
            {
              label: '在职状态',
              prop: 'status',
              type: "select",
              //slot: true,
              dicData: DIC.STATUS,
              rules: [{
                required: true,
                message: '请选择在职状态',
                trigger: 'blur'
              }],
            },
            {
              label: "创建时间",
              prop: "createTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.work_personnel_add, false),
          viewBtn: this.vaildData(this.permission.work_personnel_view, false),
          delBtn: this.vaildData(this.permission.work_personnel_delete, false),
          editBtn: this.vaildData(this.permission.work_personnel_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
    }
  };
</script>

<style>
</style>
