<template>
  <basicContainer>
    <avue-crud
      ref="crud"
      v-model="form"
      :option="option"
      :table-loading="loading"
      :data="data"
      :page="page"
      :before-open="beforeOpen"
      @row-update="rowUpdate"
      @row-save="rowSave"
      @row-del="rowDel"
      @search-change="searchChange"
      @search-reset="searchReset"
      @selection-change="selectionChange"
      @current-change="currentChange"
      @size-change="sizeChange"
      @on-load="onLoad">
      <template slot="menuLeft">
        <el-button
          class="filter-item"
          size="small"
          type="warning"
          icon="el-icon-download"
          @click="handleDownload">导出</el-button>
      </template>
    </avue-crud>
    <canvas ref="qrcodeCanvas" />
  </basicContainer>
</template>

<script>
    import QRCode from "qrcode";
    import {
        getList,
        add,
        getDetail,
        remove,
        update,
        exportReport
    } from "@/api/qrcodeTrace/basePrinterPointMgr";
    import {
        mapGetters
    } from "vuex";
    export default {
        data() {
            return {
                form: {},
                query: {},
                loading: false,
                data: [],
                selectionList: [],
                page: {
                    pageSize: 10,
                    currentPage: 1,
                    total: 0
                },
                option: {
                    height: 'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,
                    tip: false,
                    border: true,
                    index: false,
                    menu: false,
                    addBtn: false,
                    delBtn: false,
                    viewBtn: false,
                    selection: false,
                    column: [
                      {
                        label: "标纸码号",
                        prop: "cardSn",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                          search: true,
                      },
                      {
                        label: "张数",
                        prop: "point",
                        type: "number",
                        rules: [{
                          required: true,
                          message: '请输入数字',
                          trigger: 'blur'
                        }]
                      },
                      {
                        label: "登记人",
                        prop: "createUserName",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                      },
                      {
                        label: "登记时间",
                        prop: "createTime",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                      },
                      {
                        label: "状态",
                        prop: "useStatus",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                        dicData: [{
                          label: "未发货",
                          value: 0
                        }, {
                          label: "已发货",
                          value: 9
                        }, {
                          label: "已使用",
                          value: 1
                        }],
                      },
                      {
                            label: "使用单位",
                            prop: "activeDeptId",
                            type: "select",
                            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                            props: {
                                label: "deptName",
                                value: "id"
                            },
                            search: true,
                          hide: true,
                        },
                        {
                            label: "使用单位",
                            prop: "activeDeptName",
                            type: "input",
                            addDisplay: false,
                            editDisplay: false,
                            viewDisplay: true,
                        },
                        {
                            label: "使用人员",
                            prop: "activeUserName",
                            type: "input",
                            addDisplay: false,
                            editDisplay: false,
                            viewDisplay: true,
                        },
                        {
                            label: "使用时间",
                            prop: "activeTime",
                            type: "input",
                            addDisplay: false,
                            editDisplay: false,
                            viewDisplay: true,
                        },

                        {
                            label: "开始日期",
                            prop: "startDate",
                            type: "date",
                            format: "yyyy-MM-dd",
                            valueFormat: "yyyy-MM-dd",
                            search: true,searchSpan: 5,
                            hide: true,
                        },
                        {
                            label: "结束日期",
                            prop: "endDate",
                            type: "date",
                            format: "yyyy-MM-dd",
                            valueFormat: "yyyy-MM-dd",
                            search: true,searchSpan: 5,
                            hide: true
                        },
                    ]
                },
            }
        },
        computed: {
            ...mapGetters(["permission"]),
            permissionList() {
                return {
                    addBtn: this.vaildData(this.permission.tableware_disinfection_add, false),
                    viewBtn: this.vaildData(this.permission.tableware_disinfection_view, false),
                    delBtn: this.vaildData(this.permission.tableware_disinfection_delete, false),
                    editBtn: this.vaildData(this.permission.tableware_disinfection_edit, false)
                };
            },
            ids() {
                let ids = [];
                this.selectionList.forEach(ele => {
                    ids.push(ele.id);
                });
                return ids.join(",");
            }
        },
        methods: {
            onLoad(page, params = {}) {
                this.loading = true;
                getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                    const data = res.data.data;
                    this.page.total = data.total;
                    this.data = data.records;
                    this.loading = false;
                });
            },
            rowSave(row, loading, done) {
                add(row).then(() => {
                    loading();
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                }, error => {
                    done();
                    window.console.log(error);
                });
            },
            rowUpdate(row, index, loading, done) {
                update(row).then(() => {
                    loading();
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                }, error => {
                    done();
                    window.console.log(error);
                });
            },
            rowDel(row) {
                this.$confirm("确定将选择数据删除?", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    })
                    .then(() => {
                        return remove(row.id);
                    })
                    .then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                    });
            },
            handleDelete() {
                if (this.selectionList.length === 0) {
                    this.$message.warning("请选择至少一条数据");
                    return;
                }
                this.$confirm("确定将选择数据删除?", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    })
                    .then(() => {
                        return remove(this.ids);
                    })
                    .then(() => {
                        this.onLoad(this.page);
                        this.$message({
                            type: "success",
                            message: "操作成功!"
                        });
                        this.$refs.crud.toggleSelection();
                    });
            },
            beforeOpen(done, type) {
                if (["edit", "view"].includes(type)) {
                    getDetail(this.form.id).then(res => {
                        this.form = res.data.data;
                    });
                }
                done();
            },
            // 生成二维码
            generateQRCode(text) {
                const canvas = this.$refs.qrcodeCanvas;
                QRCode.toCanvas(canvas, text, {
                    width: 200, // 设置二维码的宽度
                    errorCorrectionLevel: "H", // 容错级别
                }, (error) => {
                    if (error) {
                        console.error("二维码生成失败:", error);
                    } else {
                        console.log("二维码生成成功");
                        this.printQRCode();
                    }
                });
            },
            // 打印二维码
            printQRCode() {
                const canvas = this.$refs.qrcodeCanvas;
                const imageUrl = canvas.toDataURL("image/png");
                console.log("imageUrl", imageUrl)

                // 打开打印页面
                const printWindow = window.open("", "_blank");
                printWindow.document.write(`<img src="${imageUrl}" style="width: 100%;"/>`);
                printWindow.document.close();
                setTimeout(() => {
                    printWindow.print();
                    printWindow.close();
                }, 300);
            },
            printHandler(row) {
                this.generateQRCode(row.cardSn)
            },
            handleDownload: function() {
                let loading;
                this.$confirm("确定导出数据?", {
                        confirmButtonText: "确定",
                        cancelButtonText: "取消",
                        type: "warning"
                    })
                    .then(() => {
                        loading = this.$loading({
                            lock: true,
                            text: '正在导出数据，请稍后',
                            spinner: 'el-icon-loading',
                            background: 'rgba(0, 0, 0, 0.7)'
                        });
                        return exportReport(this.page.currentPage, this.page.pageSize, this.query);
                    })
                    .then((res) => {
                        loading.close();
                        const blob = new Blob([res.data]);
                        const fileName = '码号使用记录.xlsx';
                        const linkNode = document.createElement('a');
                        linkNode.download = fileName;
                        linkNode.style.display = 'none';
                        linkNode.href = URL.createObjectURL(blob);
                        document.body.appendChild(linkNode);
                        linkNode.click();
                        URL.revokeObjectURL(linkNode.href);
                        document.body.removeChild(linkNode);
                    });
            },
            selectionChange(list) {
                this.selectionList = list;
            },
            selectionClear() {
                this.selectionList = [];
                this.$refs.crud.toggleSelection();
            },
            searchChange(params, done) {
                this.query = params;
                this.page.currentPage = 1
                this.onLoad(this.page, params);
                done();
            },
            searchReset() {
                this.query = {};
                this.onLoad(this.page);
            },
            currentChange(currentPage) {
                this.page.currentPage = currentPage;
            },
            sizeChange(pageSize) {
                this.page.pageSize = pageSize;
            },
        }
    }
</script>

<style scoped>

</style>
