<template>
  <basic-container>
    <div>
      <div class="all-mess">
        <div class="mess-header">
          <div
            :class="{acitve:activeIdx==index}"
            v-for="(item,index) in messList"
            :key="index"
            @click="menuClick(index)"
          >
            {{item}}
          </div>
        </div>
        <div class="mess-content" v-if="activeIdx == 0">
          <avue-crud :option="option"
                     :table-loading="loading"
                     :data="data"
                     :page="page"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="form"
                     ref="crud"
                     @row-update="rowUpdate"
                     @row-save="rowSave"
                     @row-del="rowDel"
                     @search-change="searchChange"
                     @search-reset="searchReset"
                     @selection-change="selectionChange"
                     @current-change="currentChange"
                     @size-change="sizeChange"
                     @on-load="onLoad">
            <template slot="menuLeft">
              <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData">导出</el-button>
              <a style="color: red" type="danger"
                 size="small"
                 icon="el-icon-delete"
                 plain
              >备注：当天的消费统计数据需到凌晨后统计。
              </a>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button type="text"
                         icon="el-icon-view"
                         size="small"
                         plain
                         @click.stop="openDetails1(row)">查看
              </el-button>
            </template>
          </avue-crud>
        </div>
        <div class="mess-content" v-if="activeIdx == 1">
          <avue-crud :option="monthOption"
                     :data="monthData"
                     :page="monthPage"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="monthForm"
                     ref="monthForm"
                     @search-change="searchChangeMonth"
                     @search-reset="searchResetMonth"
                     @selection-change="selectionChangeMonth"
                     @current-change="currentChangeMonth"
                     @size-change="sizeChangeMonth"
                     @on-load="onLoadMonth">
            <template slot="menuLeft">
              <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>
              <a style="color: red" type="danger"
                 size="small"
                 icon="el-icon-delete"
                 plain
              >备注：当天的消费统计数据需到凌晨后统计。
              </a>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button type="text"
                         icon="el-icon-view"
                         size="small"
                         plain
                         @click.stop="openDetails2(row)">查看
              </el-button>
            </template>
            <el-date-picker
              v-model="month"
              type="month"
              placeholder="选择月">
            </el-date-picker>
          </avue-crud>
        </div>

        <div class="mess-content" v-if="activeIdx == 2">
          <avue-crud :option="yearOption"
                     :data="yearData"
                     :page="yearPage"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="yearForm"
                     ref="yearForm"
                     @search-change="searchChangeYear"
                     @search-reset="searchResetYear"
                     @selection-change="selectionChangeYear"
                     @current-change="currentChangeYear"
                     @size-change="sizeChangeYear"
                     @on-load="onLoadYear">
            <template slot="menuLeft">
              <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData3">导出</el-button>
              <a style="color: red" type="danger"
                 size="small"
                 icon="el-icon-delete"
                 plain
              >备注：当天的消费统计数据需到凌晨后统计。
              </a>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button type="text"
                         icon="el-icon-view"
                         size="small"
                         plain
                         @click.stop="openDetails3(row)">查看
              </el-button>
            </template>
          </avue-crud>
        </div>
        <div class="mess-content" v-if="activeIdx == 3">
          <avue-crud :option="windowOption"
                     :table-loading="windowLoading"
                     :data="windowData"
                     :page="windowPage"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="windowForm"
                     ref="windowForm"
                     @search-change="windowSearchChange"
                     @search-reset="windowSearchReset"
                     @selection-change="windowSelectionChange"
                     @current-change="windowCurrentChange"
                     @size-change="windowSizeChange"
                     @on-load="windowOnLoad">
            <template slot="menuLeft">
              <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportWindowData">导出</el-button>
              <a style="color: red" type="danger"
                 size="small"
                 icon="el-icon-delete"
                 plain
              >备注：当天的消费统计数据需到凌晨后统计。
              </a>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button type="text"
                         icon="el-icon-view"
                         size="small"
                         plain
                         @click.stop="openDetails4(row)">查看
              </el-button>
            </template>
          </avue-crud>
        </div>
        <div class="mess-content" v-if="activeIdx == 4">
          <avue-crud :option="mealsOption"
                     :table-loading="mealsLoading"
                     :data="mealsData"
                     :page="mealsPage"
                     v-model="mealsForm"
                     ref="mealsForm"
                     @search-change="mealsSearchChange"
                     @search-reset="mealsSearchReset"
                     @selection-change="mealsSelectionChange"
                     @current-change="mealsCurrentChange"
                     @size-change="mealsSizeChange"
                     @on-load="mealsOnLoad">
            <template slot="menuLeft">
              <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportMealsData">导出</el-button>
              <a style="color: red" type="danger"
                 size="small"
                 icon="el-icon-delete"
                 plain
              >备注：当天的消费统计数据需到凌晨后统计。
              </a>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button type="text"
                         icon="el-icon-view"
                         size="small"
                         plain
                         @click.stop="openDetails5(row)">查看
              </el-button>
            </template>
          </avue-crud>
        </div>
        <div class="mess-content" v-if="activeIdx == 5">
          <avue-crud :option="mealsUserOption"
                    :table-loading="mealsUserLoading"
                    :data="mealsUserData"
                    :page="mealsUserPage"
                    :permission="permissionList"
                    :before-open="beforeOpen"
                    v-model="mealsUserForm"
                    ref="mealsUserForm"
                    @search-change="searchChangeMealsUser"
                    @search-reset="searchResetMealsUser"
                    @selection-change="selectionChangeMealsUser"
                    @current-change="currentChangeMealsUser"
                    @size-change="sizeChangeMealsUser"
                    @on-load="onLoadMealsUser">
            <template slot="menuLeft">
              <el-button v-if="permission.business_income_statistics_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportPersonCountData">导出</el-button>

            </template>
          </avue-crud>
        </div>
      </div>
    </div>
    <el-dialog :title="this.title" :visible.sync="dateDetailsVisible" width="70%" left :append-to-body="true" @close="tongjiaocan">
      <el-row style="padding-top: 30px;">
        <div v-if="isShow1" style="margin-bottom: 30px;">{{context}}：{{this.diningDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 营业网点：{{this.businessOutletsName}}</div>
        <div v-if="isShow2" style="margin-bottom: 30px;">起止日期：{{this.diningDate}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 营业网点：{{this.businessOutletsName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;窗口组：{{this.windowGroupName}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;窗口：{{this.windowName}}</div>
        <el-table
          :data="dateConsumData"
          border
          style="width: 100%;">
          <el-table-column
            prop="status"
            label="名称"
            align="center"
            width="180" style="background: #fafafa;">
          </el-table-column>
          <el-table-column
            prop="mealIncomeBalance1"
            label="统缴餐"
            align="center"
            width="180">
          </el-table-column>
          <el-table-column
            prop="freeConsumBalance1"
            align="center"
            label="自选餐(自由消费)">
          </el-table-column>
          <el-table-column
            prop="orderDishesBalance1"
            align="center"
            label="自选餐(预订菜品)">
          </el-table-column>
          <el-table-column
            prop="orderMealsBalance1"
            align="center"
            label="自选餐(预订餐次)">
          </el-table-column>
          <el-table-column
            prop="totalBalance"
            align="center"
            label="合计">
          </el-table-column>
        </el-table>
      </el-row>
    </el-dialog>

    <el-dialog :title="this.title" :visible.sync="mealsDetailsVisible" width="70%" left :append-to-body="true" @close="tongjiaocan">
      <el-row style="padding-top: 30px;">
        <el-table
          :data="businessMealsData"
          show-summary
          style="width: 100%;color: #2d2d2d" >
          <el-table-column
            prop="businessOutletName"
            label="营业网点"
            align="center" style="color: #2d2d2d">
          </el-table-column>
            <el-table-column label="未取餐(含自动扣费/退款)"    align="center">
              <el-table-column
                prop="noBrekkerBalance1"
                label="早餐"
                align="center" style="color: #2d2d2d">
              </el-table-column>
              <el-table-column
                prop="noLunchBalance1"
                label="中餐"
                align="center" style="color: #2d2d2d">
              </el-table-column>
              <el-table-column
                prop="noSupperBalance1"
                label="晚餐"
                align="center" style="color: #2d2d2d">
              </el-table-column>
              <el-table-column
                prop="noNightBalance1"
                label="夜餐"
                align="center" style="color: #2d2d2d">
              </el-table-column>
            </el-table-column>
          <el-table-column
            prop="totalBalance1"
            label="小计"
            align="center" style="color: #2d2d2d">
          </el-table-column>
        </el-table>
      </el-row>
      <avue-crud :option="dinnerOption"
                 :table-loading="dinnerLoading"
                 :data="dinnerData"
                 :page="dinnerPage"
                 v-model="dinnerForm"
                 ref="dinnerForm"
                 @search-change="dinnerSearchChange"
                 @search-reset="dinnerSearchReset"
                 @selection-change="dinnerSelectionChange"
                 @current-change="dinnerCurrentChange"
                 @size-change="dinnerSizeChange"
                 @on-load="dinnerOnLoad">
      </avue-crud>
    </el-dialog>

  </basic-container>
</template>
<script>
import {getList, getDetail, add, update, remove,getMonthList,getYearList,getWindowList,exportDateFoodData,exportMonthFoodData,exportYearFoodData,exportWindowData,getConsumDetails, getListForStatisticsPersonnel, exportForStatisticsPersonnel} from "@/api/queryStatistics/businessIncomeStatistics";
import {getMealsList,getDinnerList,getBusinessDetails,exportMealsData} from "@/api/queryStatistics/mealsIncomeStatistics";
// import { getMealsUserList } from "@/api/queryStatistics/foodWalletRecharge";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      activeIdx: 0,
      messList: [ '按日期统计', '按月份统计', '按年份统计', '按窗口统计','按餐次统计', '按人员统计' ],
      form: {},
      query: {},
      monthForm:{},
      yearForm:{},
      windowForm:{},
      mealsForm:{},
      dinnerForm:{},
      mealsUserForm:{},
      loading: true,
      monthLoading:true,
      windowLoading:true,
      mealsLoading:true,
      dinnerLoading:true,
      mealsUserLoading:true,
      dateDetailsVisible:false,
      mealsDetailsVisible:false,
      isShow1:true,
      isShow2:true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      monthPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      yearPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      windowPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      mealsPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      dinnerPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      mealsUserPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      searchForm:{},
      searchForm1:{},
      searchForm2:{},
      searchForm3:{},
      searchForm4:{},
      searchForm6:{},
      selectionList: [],
      dateConsumData:[],
      businessMealsData:[],
      mealsUserData:[],
      windowData:[],
      mealsData:[],
      dinnerData:[],
      title:undefined,
      context:undefined,
      diningDate:undefined,
      businessOutletsName:undefined,
      windowGroupName:undefined,
      windowName:undefined,
      windowId:undefined,
      businessOutletId:undefined,
      companyId:undefined,
      beginDate:undefined,
      endDate:undefined,
      option: {
        /*          height:'auto',
                  calcHeight: 30,
                  searchShow: true,*/
        /*  searchShow: true,*/
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        addBtn:false,
        delBtn:false,
        editBtn: false,
        /* menu:false,*/
        showSummary: true,
        sumColumnList: [
          {
            name: 'mealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'freeConsumBalance1',
            type: 'sum'
          },
          {
            name: 'orderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'orderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'noMealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          /*           {
                       label: "单位",
                       prop: "unitId",
                       type: "select",
                       dicUrl: "/api/service/rabbit-system/dept/select",
                       props: {
                         label: "deptName",
                         value:"id"
                       },
                       hide: false,
                       search:true,
                       rules: [{
                         required: true,
                         message: "请选择部门",
                         trigger: "click"
                       }]
                     },
                     {
                       label: "学校",
                       prop: "schoolId",
                       type: "select",
                       dicUrl: "/api/service/rabbit-system/dept/select",
                       props: {
                         label: "deptName",
                         value:"id"
                       },
                       hide: false,
                       search:true,
                       rules: [{
                         required: true,
                         message: "请选择部门",
                         trigger: "click"
                       }]
                     },*/
          {
            label: "日期",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "部门",
            prop: "classId",
            type: "tree",
            search: false,
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            // slot: true,
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: true,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            search:true,
            hide:true,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowGroupId'],
            cascaderChange: true
          },
          {
            label: "窗口组",
            prop: "windowGroupId",
            type: "select",
            span: 24,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口组",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowId'],
            cascaderChange: true
          },
          {
            label: "窗口号",
            prop: "windowId",
            type: "select",
            span: 24,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口号",
              trigger: "blur"
            }],
            dicFlag: false,
            multiple:true,
            dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
            props: {
              label: "number",
              value: "id"
            },
          },
          {
            label: "开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
    /*      {
            label: '统计日期',
            prop: 'queryDate',
            type:'datetime',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide:true,
          },*/
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "mealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(自由消费)",
                prop: "freeConsumBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订菜品)",
                prop: "orderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "orderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: '未取餐(含自动扣费/退款)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "noMealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(预订菜品)",
                prop: "noOrderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "noOrderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      monthOption:{
        /*          height:'auto',*/
        /*    calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'mealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'freeConsumBalance1',
            type: 'sum'
          },
          {
            name: 'orderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'orderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'noMealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          /*       {
                   label: "单位",
                   prop: "unitId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search:true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },
                 {
                   label: "学校",
                   prop: "schoolId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search:true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },*/
          {
            label: '月份',
            prop: 'monthDate',
            type:'month',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM',
            valueFormat: 'yyyy-MM',
            hide:true,
          },
          {
            label: "月份",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: true,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            search:true,
            hide:true,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowGroupId'],
            cascaderChange: true
          },
          {
            label: "窗口组",
            prop: "windowGroupId",
            type: "select",
            span: 24,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口组",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowId'],
            cascaderChange: true
          },
          {
            label: "窗口号",
            prop: "windowId",
            type: "select",
            span: 24,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口号",
              trigger: "blur"
            }],
            dicFlag: false,
            multiple:true,
            dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
            props: {
              label: "number",
              value: "id"
            },
          },
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "mealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(自由消费)",
                prop: "freeConsumBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订菜品)",
                prop: "orderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "orderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: '未取餐(含自动扣费/退款)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "noMealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(预订菜品)",
                prop: "noOrderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "noOrderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      yearOption:{
        /*          height:'auto',*/
        /*    calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'mealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'freeConsumBalance1',
            type: 'sum'
          },
          {
            name: 'orderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'orderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'noMealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          /*   {
               label: "单位",
               prop: "unitId",
               type: "select",
               dicUrl: "/api/service/rabbit-system/dept/select",
               props: {
                 label: "deptName",
                 value:"id"
               },
               hide: false,
               search:true,
               rules: [{
                 required: true,
                 message: "请选择部门",
                 trigger: "click"
               }]
             },
             {
               label: "学校",
               prop: "schoolId",
               type: "select",
               dicUrl: "/api/service/rabbit-system/dept/select",
               props: {
                 label: "deptName",
                 value:"id"
               },
               hide: false,
               search:true,
               rules: [{
                 required: true,
                 message: "请选择部门",
                 trigger: "click"
               }]
             },*/
          {
            label: '年份',
            prop: 'yearDate',
            type:'year',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM',
            valueFormat: 'yyyy-MM',
            hide:true,
          },
          {
            label: "年份",
            prop: "createTime",
            type: "datetime",
            format: "yyyy",
            valueFormat: "yyyy",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: true,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            search:true,
            hide:true,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowGroupId'],
            cascaderChange: true
          },
          {
            label: "窗口组",
            prop: "windowGroupId",
            type: "select",
            span: 24,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口组",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowId'],
            cascaderChange: true
          },
          {
            label: "窗口号",
            prop: "windowId",
            type: "select",
            span: 24,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口号",
              trigger: "blur"
            }],
            dicFlag: false,
            multiple:true,
            dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
            props: {
              label: "number",
              value: "id"
            },
          },
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "mealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(自由消费)",
                prop: "freeConsumBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订菜品)",
                prop: "orderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "orderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: '未取餐(含自动扣费/退款)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "noMealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(预订菜品)",
                prop: "noOrderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "noOrderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalancel",
            sortable:true,
          },
        ]
      },
      windowOption: {
        /*          height:'auto',
                  calcHeight: 30,
                  searchShow: true,*/
        /*  searchShow: true,*/
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        addBtn:false,
        delBtn:false,
        editBtn: false,
        /* menu:false,*/
        showSummary: true,
        sumColumnList: [
          {
            name: 'mealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'freeConsumBalance1',
            type: 'sum'
          },
          {
            name: 'orderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'orderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'noMealIncomeBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderDishesBalance1',
            type: 'sum'
          },
          {
            name: 'noOrderMealsBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "日期",
            prop: "startAndEndDate",
            type: "input",
            width:300,
            sortable:true,
          },
          /*            {
                        label: "营业网点",
                        prop: "businessOutletId",
                        type: "select",
                        //dicFlag: false,
                        multiple: false,
                        search:true,
                        dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
                        props: {
                          label: "name",
                          value: "id"
                        },
                      },*/
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: true,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            search:true,
            hide:true,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowGroupId'],
            cascaderChange: true
          },
          {
            label: "窗口组",
            prop: "windowGroupId",
            type: "select",
            span: 24,
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口组",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowId'],
            cascaderChange: true
          },
          {
            label: "窗口号",
            prop: "windowId",
            type: "select",
            span: 24,
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请输入窗口号",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
            props: {
              label: "number",
              value: "id"
            },
          },
          {
            label: "营业网点",
            prop: "businessOutletName",
            type: "input",
            sortable:true,
          },
          {
            label: "窗口组",
            prop: "windowGroupName",
            type: "input",
            sortable:true,
          },
          {
            label: "窗口号",
            prop: "windowName",
            type: "input",
            sortable:true,
          },
          {
            label: "开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
/*          {
            label: '统计日期',
            prop: 'queryDate',
            type:'datetime',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide:true,
          },*/
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "统缴餐",
              prop: "mealIncomeBalance1",
              sortable:true,
            },
              {
                label: "自选餐(自由消费)",
                prop: "freeConsumBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订菜品)",
                prop: "orderDishesBalance1",
                sortable:true,
              },
              {
                label: "自选餐(预订餐次)",
                prop: "orderMealsBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      mealsOption: {
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        addBtn:false,
        delBtn:false,
        editBtn: false,
        /* menu:false,*/
        showSummary: true,
        sumColumnList: [
          {
            name: 'brekkerBalance1',
            type: 'sum'
          },
          {
            name: 'lunchBalance1',
            type: 'sum'
          },
          {
            name: 'supperBalance1',
            type: 'sum'
          },
          {
            name: 'nightBalance1',
            type: 'sum'
          },
          {
            name: 'otherBalance1',
            type: 'sum'
          },
          {
            name: 'noBrekkerBalance1',
            type: 'sum'
          },
          {
            name: 'noLunchBalance1',
            type: 'sum'
          },
          {
            name: 'noSupperBalance1',
            type: 'sum'
          },
          {
            name: 'noNightBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "日期",
            prop: "startAndEndDate",
            type: "input",
            width:300,
            sortable:true,
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: true,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletId",
            type: "select",
            span: 24,
            search:true,
            sortable:true,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
            props: {
              label: "name",
              value: "id"
            },
          },
          {
            label: "开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
/*          {
            label: '统计日期',
            prop: 'queryDate',
            type:'datetime',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide:true,
          },*/
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "早餐",
              prop: "brekkerBalance1",
              sortable:true,
            },
              {
                label: "中餐",
                prop: "lunchBalance1",
                sortable:true,
              },
              {
                label: "晚餐",
                prop: "supperBalance1",
                sortable:true,
              },
              {
                label: "夜餐",
                prop: "nightBalance1",
                sortable:true,
              },  {
                label: "其他",
                prop: "otherBalance1",
                sortable:true,
              },]
          },
          {
            label: '未取餐(含自动扣费/退款)',
            align: 'center',
            children: [{
              label: "早餐",
              prop: "noBrekkerBalance1",
              sortable:true,
            },
              {
                label: "中餐",
                prop: "noLunchBalance1",
                sortable:true,
              },
              {
                label: "晚餐",
                prop: "noSupperBalance1",
                sortable:true,
              },   {
                label: "夜餐",
                prop: "noNightBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      dinnerOption: {
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        addBtn:false,
        delBtn:false,
        editBtn: false,
        menu:false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'brekkerBalance1',
            type: 'sum'
          },
          {
            name: 'lunchBalance1',
            type: 'sum'
          },
          {
            name: 'supperBalance1',
            type: 'sum'
          },
          {
            name: 'nightBalance1',
            type: 'sum'
          },
          {
            name: 'otherBalance1',
            type: 'sum'
          },
          {
            name: 'totalBalance1',
            type: 'sum'
          },
        ],
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            search: true,
            sortable:true,
          },
          {
            label: "营业网点",
            prop: "businessOutletName",
            type: "input",
            sortable:true,
          },
          {
            label: "窗口组",
            prop: "windowGroupName",
            type: "input",
            sortable:true,
          },
          {
            label: "窗口号",
            prop: "windowName",
            type: "input",
            sortable:true,
          },
          {
            label: '已取餐(含正常扣费/补扣/纠错)',
            align: 'center',
            children: [{
              label: "早餐",
              prop: "brekkerBalance1",
              sortable:true,
            },
              {
                label: "中餐",
                prop: "lunchBalance1",
                sortable:true,
              },
              {
                label: "晚餐",
                prop: "supperBalance1",
                sortable:true,
              },
              {
                label: "夜餐",
                prop: "nightBalance1",
                sortable:true,
              },  {
                label: "其他",
                prop: "otherBalance1",
                sortable:true,
              },]
          },
          {
            label: "合计",
            prop: "totalBalance1",
            sortable:true,
          },
        ]
      },
      mealsUserOption: {
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        excelBtn: false,
        headerAlign: 'center',
        align: 'center',
        menu:false,
        column: [
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
              label: "开始时间",
              prop: "beginDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
            {
            label: "营业网点",
            prop: "businessOutletId",
            type: "select",
            span: 24,
            search:true,
            sortable:true,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
            props: {
              label: "name",
              value: "id"
            },
          },
          {
            label: "统计起止时间",
            prop: "dateTime",
            width: 200,
          },
          {
            label: "姓名",
            prop: "userName",
          },
          {
            label: "编号",
            prop: "studentJobNo",
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            sortable:true,
            dicData: [
                {
                    label: '男',
                    value: "1"
                },
                {
                    label: '女',
                    value: "2"
                }
            ],
            width: 100,
          },
          {
            label: "部门",
            prop: "classId",
            type: "tree",
            search: true,
            // hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            // slot: true,
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          // {
          //   label: "部门",
          //   prop: "canteenName",
          // },
          {
            label: '自选餐预定餐次',
            display:false,
            children: [{
              label: '早餐预订次数',
              prop: 'orderMealBreakfastTotal',
              display:false
            },{
              label: '早餐取餐次数',
              prop: 'orderMealBreakfast',
              display:false
            }, {
              label: '午餐预订次数',
              prop: 'orderMealLunchTotal',
              display:false
            },{
              label: '午餐取餐次数',
              prop: 'orderMealLunch',
              display:false
            }, {
              label: '晚餐预订次数',
              prop: 'orderMealDinnerTotal',
              display:false
            }, {
              label: '晚餐取餐次数',
              prop: 'orderMealDinner',
              display:false
            }, {
              label: '夜餐预订次数',
              prop: 'orderMealNightTotal',
              display:false
            },{
              label: '夜餐取餐次数',
              prop: 'orderMealNight',
              display:false
            }, {
              label: '预订次数小计',
              prop: 'orderMealTotal',
              display:false
            }, {
              label: '取餐次数小计',
              prop: 'orderMeal',
              display:false
            }]
          },
          {
            label: '自选餐预定餐次金额',
            prop: 'orderMealPriceTotal',
            display:false,
            width: 150,
          },
          {
            label: '自选餐预定菜品金额',
            prop: 'noOrderDishesBalance',
            display:false,
            width: 150,
          },
          {
            label: '自选餐自由消费金额',
            prop: 'freeConsumeBalance',
            display:false,
            width: 150,
          },
          {
            label: '统缴餐',
            display:false,
            children: [{
              label: '早餐次数',
              prop: 'breakfastNum',
              display:false
            },{
              label: '中餐次数',
              prop: 'lunchNum',
              display:false
            }, {
              label: '晚餐次数',
              prop: 'dinnerNum',
              display:false
            },{
              label: '夜餐次数',
              prop: 'nightNum',
              display:false
            }, {
              label: '小计',
              prop: 'TotalNum',
              display:false
            }
          ]
          },
          {
            label: '统缴餐金额',
            prop: 'consumeBalance',
            display:false,
            width: 150,
          },
          // {
          //   label: '金额合计',
          //   prop: '',
          //   display:false,
          //   width: 150,
          // }
        ]
      },
      data: [],
      monthData:[],
      yearData:[],
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.food_wallet_recharge_add, false),
        viewBtn: this.vaildData(this.permission.food_wallet_recharge_view, false),
        delBtn: this.vaildData(this.permission.food_wallet_recharge_delete, false),
        editBtn: this.vaildData(this.permission.food_wallet_recharge_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created() {
    const { messName } = this.$route.query
    if(messName){
      this.messList.forEach((item, idx) => {
        item == messName ? this.activeIdx = idx : ''
      })
    }
    if (this.userInfo.userType === 'canteen') {
      this.option.column[2].search = false;
      this.monthOption.column[3].search = false;
      this.yearOption.column[3].search = false;
      this.windowOption.column[2].search = false;
      this.mealsOption.column[2].search = false;
      this.option.column[2].hide = true;
      this.monthOption.column[3].hide = true;
      this.yearOption.column[3].hide = true;
      this.windowOption.column[2].hide = true;
      this.mealsOption.column[2].hide = true;
      this.dinnerOption.column[1].search = false;
      this.dinnerOption.column[1].hide = true;
    }else {
      this.dinnerOption.column[1].search = false;
      this.dinnerOption.column[1].hide = true;
    }
    // 单位列表是否显示
    this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
  },
  methods: {
    menuClick(idx) {
      this.query=[];
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.query = {};
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
      if (idx == 1){
        this.query = {};
        this.monthPage.currentPage = 1;
        this.onLoadMonth(this.monthPage);
      }
      if (idx == 2){
        this.query = {};
        this.yearPage.currentPage = 1;
        this.onLoadYear(this.yearPage);
      }
      if (idx == 3){
        this.query = {};
        this.windowPage.currentPage = 1;
        this.windowOnLoad(this.windowPage);
      }
      if (idx == 4){
        this.query = {};
        this.mealsPage.currentPage = 1;
        this.mealsOnLoad(this.mealsPage);
      }
      if (idx == 5){
        this.mealsUserPage.currentPage = 1;
        this.onLoadMealsUser(this.mealsUserPage);
      }
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.searchForm = {};
      this.onLoad(this.page);
    },
    windowSearchReset() {
      this.query = {};
      this.searchForm3 = {};
      this.windowOnLoad(this.windowPage);
    },
    mealsSearchReset() {
      this.query = {};
      this.searchForm4 = {};
      this.mealsOnLoad(this.mealsPage);
    },
    searchResetMealsUser() {
      this.query = {};
      this.searchForm6 = {};
      this.onLoadMealsUser(this.mealsUserPage);
    },
    dinnerSearchReset() {
      this.query = {};
      this.dinnerOnLoad(this.dinnerPage);
    },
    searchResetMonth() {
      this.query = {};
      this.searchForm1 = {};
      this.onLoadMonth(this.monthPage);
    },
    searchResetYear() {
      this.query = {};
      this.searchForm2 = {};
      this.onLoadYear(this.yearPage);
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      params.beginDate = params.startDate;
      params.endDate = params.endDate;
      this.searchForm = params;
      this.query = params;
      this.page.currentPage = 1
  /*    if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
        params.beginDate = params.queryDate[0];
        params.endDate = params.queryDate[1];
      }*/
      this.onLoad(this.page, params);
      done();
    },
    windowSearchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      params.beginDate = params.startDate;
      params.endDate = params.endDate;
      this.searchForm3.beginDate = params.startDate;
      this.searchForm3.endDate = params.endDate;
      this.searchForm3 = params;
      this.query = params;
      this.windowPage.currentPage = 1
/*      if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
        params.beginDate = params.queryDate[0];
        params.endDate = params.queryDate[1];
        this.searchForm3.beginDate = params.queryDate[0];
        this.searchForm3.endDate = params.queryDate[1];
      }*/
      this.windowOnLoad(this.windowPage, params);
      done();
    },
    mealsSearchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      params.beginDate = params.startDate;
      params.endDate = params.endDate;
      this.searchForm4.beginDate = params.startDate;
      this.searchForm4.endDate = params.endDate;
      this.searchForm4 = params;
      this.query = params;
      this.mealsPage.currentPage = 1
/*      if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
        params.beginDate = params.queryDate[0];
        params.endDate = params.queryDate[1];
        this.searchForm4.beginDate = params.queryDate[0];
        this.searchForm4.endDate = params.queryDate[1];
        params.queryDate = [];
      }*/
      this.mealsOnLoad(this.mealsPage, params);
      done();
    },
    searchChangeMealsUser(params, done) {
      if (params.beginDate != '' && params.beginDate != null && params.beginDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.beginDate == '' || params.beginDate == null || params.beginDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.beginDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      this.searchForm6.startDate = params.beginDate;
      this.searchForm6.endDate = params.endDate;
      this.searchForm6 = params;
      this.query = params;
      /*   if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
        params.startDate = params.queryDate[0];
        params.endDate = params.queryDate[1];
        this.searchForm6.startDate = params.queryDate[0];
        this.searchForm6.endDate = params.queryDate[1];
      }*/
      this.mealsUserPage.currentPage = 1
      console.log(params)
      this.onLoadMealsUser(this.mealsUserPage, params);
      done();
    },
    dinnerSearchChange(params, done) {
      this.dinnerPage.currentPage = 1
      this.onLoad(this.dinnerPage, params);
      done();
    },
    searchChangeMonth(params, done) {
      this.searchForm1 = params;
      this.query = params;
      this.monthPage.currentPage = 1
      this.onLoadMonth(this.monthPage, params);
      done();
    },
    searchChangeYear(params, done) {
      this.searchForm2 = params;
      this.query = params;
      this.yearPage.currentPage = 1
      this.onLoadYear(this.yearPage, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    windowSelectionChange(list) {
      this.selectionList = list;
    },
    mealsSelectionChange(list) {
      this.selectionList = list;
    },
    selectionChangeMealsUser(list) {
      this.selectionList = list;
    },
    dinnerSelectionChange(list) {
      this.selectionList = list;
    },
    selectionChangeMonth(list) {
      this.selectionList = list;
    },
    selectionChangeYear(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    windowSelectionClear() {
      this.selectionList = [];
      this.$refs.windowForm.toggleSelection();
    },
    mealsSelectionClear() {
      this.selectionList = [];
      this.$refs.mealsForm.toggleSelection();
    },
    dinnerSelectionClear() {
      this.selectionList = [];
      this.$refs.dinnerForm.toggleSelection();
    },
    selectionClearMonth() {
      this.selectionList = [];
      this.$refs.monthForm.toggleSelection();
    },
    selectionClearYear() {
      this.selectionList = [];
      this.$refs.yearForm.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    windowCurrentChange(currentPage){
      this.windowPage.currentPage = currentPage;
    },
    mealsCurrentChange(currentPage){
      this.mealsPage.currentPage = currentPage;
    },
    currentChangeMealsUser(currentPage){
      this.mealsUserPage.currentPage = currentPage;
    },
    dinnerCurrentChange(currentPage){
      this.dinnerPage.currentPage = currentPage;
    },
    currentChangeMonth(currentPage){
      this.monthPage.currentPage = currentPage;
    },
    currentChangeYear(currentPage){
      this.yearPage.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    windowSizeChange(pageSize){
      this.windowPage.pageSize = pageSize;
    },
    mealsSizeChange(pageSize){
      this.mealsPage.pageSize = pageSize;
    },
    sizeChangeMealsUser(pageSize){
      this.mealsUserPage.pageSize = pageSize;
    },
    dinnerSizeChange(pageSize){
      this.dinnerPage.pageSize = pageSize;
    },
    sizeChangeMonth(pageSize){
      this.monthPage.pageSize = pageSize;
    },
    sizeChangeYear(pageSize){
      this.yearPage.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    windowOnLoad(page, params = {}) {
      this.windowLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getWindowList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.windowPage.total = data.total;
        this.windowData = data.records;
        this.windowLoading = false;
        this.windowSelectionClear();
      });
    },
    mealsOnLoad(page, params = {}) {
      this.mealsLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getMealsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.mealsPage.total = data.total;
        this.mealsData = data.records;
        this.mealsLoading = false;
        this.mealsSelectionClear();
      });
    },
    onLoadMealsUser(page, params = {}) {
      this.mealsUserLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getListForStatisticsPersonnel(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.mealsUserPage.total = data.total;
        this.mealsUserData = data.records;
        console.log(this.mealsUserData)
        // this.selectionClearMealsUser();
        this.mealsUserLoading = false;
      });
    },
    dinnerOnLoad(page, params = {}) {
      params.businessOutletId = this.businessOutletId;
      params.companyId = this.companyId;
      params.beginDate = this.beginDate;
      params.endDate = this.endDate;
      getDinnerList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.dinnerPage.total = data.total;
        this.dinnerData = data.records;
        this.dinnerLoading = false;
        this.dinnerSelectionClear();
      });
    },
    onLoadMonth(page, params = {}) {
      this.monthLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getMonthList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.monthPage.total = data.total;
        this.monthData = data.records;
        this.monthLoading = false;
        this.selectionClearMonth();
      });
    },
    onLoadYear(page, params = {}) {
      this.yearLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getYearList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.yearPage.total = data.total;
        this.yearData = data.records;
        this.yearLoading = false;
        this.selectionClearYear();
      });
    },
    exportRechargeDetailData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportDateFoodData(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '消费统计报表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    exportPersonCountData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      console.log(this.searchForm6);
      exportForStatisticsPersonnel(Object.assign(this.searchForm6)).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '人员统计.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    exportRechargeDetailData2(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportMonthFoodData(this.searchForm1).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '消费统计报表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    exportRechargeDetailData3(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportYearFoodData(this.searchForm2).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '消费统计报表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },

    openDetails1(row){
      this.isShow2 = false;
      this.title = "按日期统计";
      this.context = "日期";
      this.diningDate = row.dateTime;
      this.businessOutletsName = row.businessOutletName;
      getConsumDetails(row.businessOutletId,row.companyId,row.dateTime,"1",null,null,null).then(res => {
        this.dateConsumData = res.data.data;
      });
      this.dateDetailsVisible = true;
    },
    openDetails2(row){
      this.isShow2 = false;
      this.title = "按月份统计";
      this.context = "月份";
      this.diningDate = row.createTime;
      this.businessOutletsName = row.businessOutletName;
      getConsumDetails(row.businessOutletId,row.schoolId,row.createTime,"2",null,null,null).then(res => {
        this.dateConsumData = res.data.data;
      });
      this.dateDetailsVisible = true;
    },
    openDetails3(row){
      this.isShow2 = false;
      this.title = "按年份统计";
      this.context = "年份";
      this.diningDate = row.createTime;
      this.businessOutletsName = row.businessOutletName;
      getConsumDetails(row.businessOutletId,row.schoolId,row.createTime,"3",null,null,null).then(res => {
        this.dateConsumData = res.data.data;
      });
      this.dateDetailsVisible = true;
    },
    openDetails4(row){
      this.isShow1 = false;
      this.title = "按窗口统计";
      this.diningDate = row.startAndEndDate;
      this.businessOutletsName = row.businessOutletName;
      this.windowGroupName = row.windowGroupName;
      this.windowName = row.windowName;
      this.windowId = row.windowId;
      getConsumDetails(row.businessOutletId,row.companyId,row.createTime,"4",row.windowId,row.beginDate,row.endDate).then(res => {
        this.dateConsumData = res.data.data;
      });
      this.dateDetailsVisible = true;
    },

    openDetails5(row){
      this.title = "餐次详情";
      getBusinessDetails(row.businessOutletId,row.beginDate,row.endDate).then(res => {
        this.businessMealsData = res.data.data;
      });
      this.businessOutletId = row.businessOutletId;
      this.companyId = row.companyId;
      this.beginDate = row.beginDate;
      this.endDate = row.endDate;
      this.dinnerPage.currentPage = 1;
      this.dinnerOnLoad(this.dinnerPage);
      this.mealsDetailsVisible = true;

    },
    exportWindowData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportWindowData(this.searchForm3).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '消费统计报表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    exportMealsData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportMealsData(this.searchForm4).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '消费统计报表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
  }
}
</script>

<style lang="scss" scoped>
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    ::v-deep div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>
