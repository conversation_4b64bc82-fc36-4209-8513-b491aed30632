<template>
    <basic-container>
        <el-dialog title="选择商品"
                   :visible.sync="isShowGoods"
                   v-if="isShowGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeGoodsForm" width="60%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crudSelectGoods"
                       @search-change="goodsSearchChange"
                       @search-reset="goodsSearchReset"
                       @selection-change="goodsSelectionChange"
                       @current-change="goodsCurrentChange"
                       @size-change="goodsSizeChange"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
    </basic-container>
</template>
<script>
import {getGoodsList} from "@/api/supplier/supplier";
export default {
    emits: ['choiceClick'],
    data() {
        return {
            isShowGoods: false,
            queryGoodsList: {},
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品类型",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品类型",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "商品大类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品大类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品小类",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品小类",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
        }

    },
    methods:{

        closeGoodsForm(){
            this.isShowGoods = false;
        },
        goodsSearchChange(params, done) {
            this.queryGoodsList = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        goodsSearchReset() {
            this.queryGoodsList = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        goodsSelectionChange(list) {
            this.selectionList = list;
        },
        goodsCurrentChange(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        goodsSizeChange(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        selectionClearGoodsList() {
            this.selectionList = [];
            this.$refs.crudSelectGoods.toggleSelection();
        },
        goodsListOnLoad(page, params = {}) {
            this.goodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.queryGoodsList)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.length;
                this.goodsListData = data;
                this.goodsListLoading = false;
                this.selectionClearGoodsList();
            });
        },
        addGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.$emit('choiceClick',row);
            this.isShowGoods = false;

        },
    }
}
</script>
