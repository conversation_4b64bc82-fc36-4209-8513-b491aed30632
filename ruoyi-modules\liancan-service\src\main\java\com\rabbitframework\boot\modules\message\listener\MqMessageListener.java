package com.rabbitframework.boot.modules.message.listener;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.Action;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.MessageListener;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.rabbitframework.boot.common.cache.SysCache;
import com.rabbitframework.boot.constant.NumberConstant;
import com.rabbitframework.boot.constant.OrderConstant;
import com.rabbitframework.boot.common.utils.*;
import com.rabbitframework.boot.core.result.R;
import com.rabbitframework.boot.core.utils.$;
import com.rabbitframework.boot.core.utils.DateUtil;
import com.rabbitframework.boot.core.utils.DigestUtil;
import com.rabbitframework.boot.data.base.BaseEntity;
import com.rabbitframework.boot.modules.businessManage.entity.*;
import com.rabbitframework.boot.modules.businessManage.service.*;
import com.rabbitframework.boot.modules.businessOutlets.entity.BusinessOutletsEntity;
import com.rabbitframework.boot.modules.businessOutlets.entity.WindowEquipmentEntity;
import com.rabbitframework.boot.modules.businessOutlets.service.IBusinessOutletsService;
import com.rabbitframework.boot.modules.businessOutlets.service.IWindowEquipmentService;
import com.rabbitframework.boot.modules.finance.entity.FinancialOutCanteenRecord;
import com.rabbitframework.boot.modules.finance.service.IFinancialOutCanteenRecordService;
import com.rabbitframework.boot.modules.homehaha.entity.UserFeeCountEntity;
import com.rabbitframework.boot.modules.homehaha.entity.UserFeeInfoEntity;
import com.rabbitframework.boot.modules.homehaha.service.IUserFeeCountService;
import com.rabbitframework.boot.modules.homehaha.service.IUserFeeInfoService;
import com.rabbitframework.boot.modules.liancan.entity.CanteenBindingDeptEntity;
import com.rabbitframework.boot.modules.liancan.entity.DiningTypeEntity;
import com.rabbitframework.boot.modules.liancan.entity.IllegalWarnLogEntity;
import com.rabbitframework.boot.modules.liancan.entity.IllegalWarnSettingsEntity;
import com.rabbitframework.boot.modules.liancan.service.CanteenBindingDeptService;
import com.rabbitframework.boot.modules.liancan.service.IDiningTypeService;
import com.rabbitframework.boot.modules.liancan.service.IIllegalWarnLogService;
import com.rabbitframework.boot.modules.liancan.service.IIllegalWarnSettingsService;
import com.rabbitframework.boot.modules.liancan.vo.CardDiningTypeVO;
import com.rabbitframework.boot.modules.liancan2.entity.LiancanCancellationPersonnelRecordEntity;
import com.rabbitframework.boot.modules.liancan2.service.ILiancan2CancellationPersonnelRecordService;
import com.rabbitframework.boot.modules.log.entity.OperationLogEntity;
import com.rabbitframework.boot.modules.message.RocketMQProducer;
import com.rabbitframework.boot.modules.personnel.entity.RelationPersonnelDeviceEntity;
import com.rabbitframework.boot.modules.personnel.entity.SystemPersonnelEntity;
import com.rabbitframework.boot.modules.personnel.service.IRelationPersonnelDeviceService;
import com.rabbitframework.boot.modules.personnel.service.ISystemPersonnelService;
import com.rabbitframework.boot.modules.personnel.service.SystemPersonnelByStudentService;
import com.rabbitframework.boot.modules.personnel.vo.CardUserVO;
import com.rabbitframework.boot.modules.personnel.vo.SrnStudentVO;
import com.rabbitframework.boot.modules.personnel.vo.StuTeaPersonnelVO;
import com.rabbitframework.boot.modules.queryStatistics.entity.*;
import com.rabbitframework.boot.modules.queryStatistics.service.*;
import com.rabbitframework.boot.modules.setting.entity.SystemDeptSettingEntity;
import com.rabbitframework.boot.modules.setting.service.ISystemDeptSettingService;
import com.rabbitframework.boot.modules.setting.vo.CardDeptVO;
import com.rabbitframework.boot.modules.system.entity.Dept;
import com.rabbitframework.boot.modules.topic.entity.TopicMessageEntity;
import com.rabbitframework.boot.modules.topic.service.ITopicMessageService;
import com.rabbitframework.boot.object.CommonEntity;
import com.rabbitframework.boot.util.SnowflakeIdUtil;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 普通（默认同步）MQ消息监听消费
 * 
 * <AUTHOR>
 */
@Slf4j
@Component
@AllArgsConstructor
public class MqMessageListener implements MessageListener {

    private final IWindowEquipmentService windowEquipmentService;

    private final IRelationPersonnelDeviceService relationPersonnelDeviceService;

    private final StudentUniteDinnerOrderService studentUniteDinnerOrderService;

    private final StudentUniteDinnerOrderMongoService studentUniteDinnerOrderMongoService;

    private final ISystemPersonnelService systemPersonnelService;

    private final IWindowMealsDetailService windowMealsDetailService;

    private final IConsumDetailsService consumDetailsService;

    private final IErrorCorrectionDetailsService errorCorrectionDetailsService;

    private final IIllegalWarnSettingsService illegalWarnSettingsService;

    private final IIllegalWarnLogService illegalWarnLogService;

    private final ISystemDeptSettingService systemDeptSettingService;

    private final IBusinessOutletsService businessOutletsService;

    private final IDiningTypeService diningTypeService;

    private final PayConfigureService payConfigureService;

    private final PayMoneyConfigureService payMoneyConfigureService;

    private final IFoodRechargeDetailService foodRechargeDetailService;

    private final IMealsOrderBalanceRecordService mealsOrderBalanceRecordService;

    private final MealsIncomeStatisticsService mealsIncomeStatisticsService;

    private final IUserMealTimesService userMealTimesService;

    private final IPersonnelConsumAmountService personnelConsumAmountService;

    private final IMqLogService mqLogService;

    private final MealRestrictionsConfigureService mealRestrictionsConfigureService;

    private final IMealCollectionStatisticsService mealCollectionStatisticsService;

    private final IUserFeeInfoService userFeeInfoService;

    private final IUserFeeCountService userFeeCountService;

    private static final PasswordEncoder ENCODER = new BCryptPasswordEncoder();

    private final ILiancan2CancellationPersonnelRecordService liancan2CancellationPersonnelRecordService;

    private final ITopicMessageService topicNessageService;

    private final SystemPersonnelByStudentService personnelByStudentService;

    private final IFinancialOutCanteenRecordService outCanteenRecordService;

    private final CanteenBindingDeptService canteenBindingDeptService;

    private RocketMQProducer rocketMQProducer;

    @Override
    public synchronized Action consume(Message message, ConsumeContext context) {
        System.out.println("集群-接收到MQ消息. Topic :" + message.getTopic() + ", tag :" + message.getTag() + " msgId : "
                + message.getMsgID() + ", Key :" + message.getKey() + ", body:" + new String(message.getBody()));
        try {
            MqLogEntity mqLogEntity = new MqLogEntity();
            mqLogEntity.setMsgTag(message.getTag());
            mqLogEntity.setMsgId(message.getMsgID());
            mqLogEntity.setMsgBody(new String(message.getBody()));
            mqLogEntity.setMsgKey(message.getKey());
            mqLogEntity.setMsgTopic(message.getTopic());
            mqLogService.save(mqLogEntity);
            String body = new String(message.getBody());
            JSONObject obj = JSONObject.parseObject(body);
            SimpleDateFormat formmat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            // SimpleDateFormat simDate = new SimpleDateFormat("yyyy-MM-dd");
            if ($.isEmpty(message.getTag())) {
                return Action.CommitMessage;
            }
            // // 监听初始化消费机信息 (已迁移到liancan-consume模块)
            // if (message.getTag().equals("CONSUMER_MACH_TAG")) {
            // WindowEquipmentEntity result = windowEquipmentService
            // .getOne(Wrappers.<WindowEquipmentEntity>query().lambda().eq(WindowEquipmentEntity::getIsDel,
            // 0)
            // .eq(WindowEquipmentEntity::getEquipmentCode, obj.get("machine_code")));
            // if (result == null) {
            // WindowEquipmentEntity windowEquipmentEntity =
            // CommonEntity.windowEquipmentEntitys(
            // (String) obj.get("machine_ip"), (String) obj.get("face_machine_ip"),
            // (String) obj.get("machine_type"),
            // (String) obj.get("school_id"), (String) obj.get("is_blank"),
            // (String) obj.get("machine_code"), new Date());
            // if (windowEquipmentService.save(windowEquipmentEntity)) {
            // return Action.CommitMessage;
            // }
            // } else {
            // if (result.getDeptId().equals(obj.get("school_id"))) {
            // Boolean flag =
            // windowEquipmentService.update(Wrappers.<WindowEquipmentEntity>update().lambda()
            // .set(WindowEquipmentEntity::getConsumerComputerIp, obj.get("machine_ip"))
            // .eq(WindowEquipmentEntity::getId, result.getId())
            // .eq(WindowEquipmentEntity::getIsDel, 0));
            // if (flag) {
            // return Action.CommitMessage;
            // }
            // }
            // }
            // }
            // // 监听消费机信息修改 (已迁移到liancan-consume模块)
            // else if (message.getTag().equals("WINDOW_DEVICE_TAG")) {
            // WindowEquipmentEntity equipment = windowEquipmentService
            // .getOne(Wrappers.<WindowEquipmentEntity>query().lambda().eq(WindowEquipmentEntity::getIsDel,
            // 0)
            // .eq(WindowEquipmentEntity::getWindowId, $.toLong(obj.get("window_id"))));
            // if (equipment != null) {
            // Boolean update =
            // windowEquipmentService.update(Wrappers.<WindowEquipmentEntity>update().lambda()
            // .set(WindowEquipmentEntity::getWindowId, $.toLong((String)
            // obj.get("window_id")))
            // .set(WindowEquipmentEntity::getSdCard, obj.get("sd_card"))
            // .set(WindowEquipmentEntity::getOnlineStatus, obj.get("online_status"))
            // .set(WindowEquipmentEntity::getUploadTime, obj.get("upload_time"))
            // .set(WindowEquipmentEntity::getMessageId, obj.get("message_id"))
            // .set(WindowEquipmentEntity::getFaceRecognitionIp, obj.get("face_machine_ip"))
            // .set(WindowEquipmentEntity::getConsumerComputerIp, obj.get("machine_ip"))
            // .set(WindowEquipmentEntity::getWorkPersonnelId,
            // $.toLong(obj.get("work_personnel_id")))
            // .set(WindowEquipmentEntity::getBusinessType,
            // $.toLong(obj.get("business_type")))
            // .set(WindowEquipmentEntity::getUploadTime, new Date())
            // .eq(WindowEquipmentEntity::getWindowId, equipment.getWindowId())
            // .eq(WindowEquipmentEntity::getIsDel, 0)
            // .eq(WindowEquipmentEntity::getDeptId, equipment.getDeptId()));
            // if (update) {
            // return Action.CommitMessage;
            // }
            // } else {
            // return Action.ReconsumeLater;
            // }
            // }
            // // 监听人员下传设备是否成功
            // else if (message.getTag().equals("PERSONNEL_DOWN_TAG")) {
            // RelationPersonnelDeviceEntity personnel =
            // relationPersonnelDeviceService.getOne(Wrappers
            // .<RelationPersonnelDeviceEntity>query().lambda().eq(RelationPersonnelDeviceEntity::getIsDel,
            // 0)
            // .eq(RelationPersonnelDeviceEntity::getPersonnelId,
            // $.toLong(obj.get("user_id")))
            // .eq(RelationPersonnelDeviceEntity::getDeptId, $.toLong(obj.get("school_id")))
            // .eq(RelationPersonnelDeviceEntity::getWindowId,
            // $.toLong(obj.get("window_id"))));
            // if ($.isNotEmpty(personnel)) {
            // Boolean flage = relationPersonnelDeviceService.update(Wrappers
            // .<RelationPersonnelDeviceEntity>update().lambda()
            // .set(RelationPersonnelDeviceEntity::getStatus, obj.get("upload_status"))
            // .set(RelationPersonnelDeviceEntity::getUpdateTime, new Date())
            // .eq(RelationPersonnelDeviceEntity::getPersonnelId, $.toLong((String)
            // obj.get("user_id")))
            // .eq(RelationPersonnelDeviceEntity::getDeptId, $.toLong(obj.get("school_id")))
            // .eq(RelationPersonnelDeviceEntity::getWindowId,
            // $.toLong(obj.get("window_id"))));
            // if (flage) {
            // return Action.CommitMessage;
            // }
            // } else {
            // RelationPersonnelDeviceEntity device = new RelationPersonnelDeviceEntity();
            // device.setDeptId($.toLong(obj.get("school_id")));
            // device.setDeviceCode((String) obj.get("machine_code"));
            // device.setPersonnelId($.toLong(obj.get("user_id")));
            // device.setWindowId($.toLong(obj.get("window_id")));
            // device.setStatus((String) obj.get("upload_status"));
            // device.setCreateTime(new Date());
            // if (relationPersonnelDeviceService.save(device)) {
            // return Action.CommitMessage;
            // }
            // }
            // }
            // // 监听统缴餐、预订餐次、预订菜品是否取餐
            // else if (message.getTag().equals("MEAL_TAG")) {
            // if (obj.get("status").equals(NumberConstant.FIRST.toString())) {
            // StudentUniteDinnerOrder order = null;
            // // 判断是否统缴餐取餐的订单
            // if (obj.get("order_type").equals(NumberConstant.ZERO.toString())) {
            // System.out.println("取餐时间" + (String) obj.get("meal_date"));
            // String yesterdayDate = (String) obj.get("meal_date") + " 00:00:00";
            // Date dateTime = formmat.parse(yesterdayDate);
            // // 查询用餐当天是否有统缴餐这个订单
            // order =
            // studentUniteDinnerOrderService.getOne(Wrappers.<StudentUniteDinnerOrder>query().lambda()
            // .eq(StudentUniteDinnerOrder::getIsDel, 0)
            // .eq(StudentUniteDinnerOrder::getStudentId, $.toLong(obj.get("user_id")))
            // .eq(StudentUniteDinnerOrder::getMealType, obj.get("meal_type"))
            // .eq(StudentUniteDinnerOrder::getMealCategory, NumberConstant.ZERO.toString())
            // .eq(StudentUniteDinnerOrder::getDinnerDate, dateTime)
            // .eq(StudentUniteDinnerOrder::getCompanyId, $.toLong(obj.get("school_id"))));
            // // 判断是否自选餐取餐的订单
            // } else if (obj.get("order_type").equals(NumberConstant.FIRST.toString())) {
            // // 查询用餐当天是否有自选餐这个订单
            // order =
            // studentUniteDinnerOrderService.getOne(Wrappers.<StudentUniteDinnerOrder>query().lambda()
            // .eq(StudentUniteDinnerOrder::getIsDel, 0)
            // .eq(StudentUniteDinnerOrder::getId, $.toLong(obj.get("order_id")))
            // .eq(StudentUniteDinnerOrder::getCompanyId, $.toLong(obj.get("school_id"))));
            // }
            // Long businessOutletsId = $.toLong(obj.get("business_outlets_id"));// 营业网点id
            // Long windowGroupId = $.toLong(obj.get("window_group_id"));// 窗口组id
            // Long windowId = $.toLong(obj.get("window_id"));// 窗口id
            // Date takeMealDate = formmat.parse((String) obj.get("take_meal_time"));// 取餐时间
            // String mealOrderNo = (String) obj.get("meal_order_no");// 取餐单号
            // String getType = (String) obj.get("get_type");
            // String workPersonnelId = (String) obj.get("work_personnel_id");// 工作人员id
            // String mealType = (String) obj.get("meal_type");// 餐次类型
            // String windowNumber = (String) obj.get("window_number");
            // String orderTypes = (String) obj.get("order_type");// 0：统缴餐，1：自选餐
            // String week = DayUtils.dateToWeek((String) obj.get("meal_date"));
            // Date dinnerDate = formmat.parse(obj.get("meal_date") + " 00:00:00");
            // String dinnerTypeId = (String) obj.get("meal_category_id");// 用餐类别
            // Long companyId = $.toLong(obj.get("school_id"));// 学校id
            // Long canteenId = $.toLong(obj.get("cannteen_id"));// 食堂id
            // Long userId = $.toLong(obj.get("user_id"));// 取餐人员id
            // String base64 = (String) obj.get("meal_photo");// 取餐照片

            // // 统缴餐取餐
            // if (orderTypes.equals(NumberConstant.ZERO.toString())) {
            // if (order != null) {
            // // 判断是否已经用过餐了，如果用过了就不执行
            // if (order.getDinnerStatus().equals(NumberConstant.ZERO.toString())) {
            // // 获取用餐人员信息
            // List<SystemPersonnelEntity> personnel = systemPersonnelService.list(Wrappers
            // .<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel,
            // 0)
            // .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
            // .eq(SystemPersonnelEntity::getId, order.getStudentId())
            // .eq(SystemPersonnelEntity::getCompanyId, companyId));
            // if (personnel.size() > 0) {
            // String classId = null;
            // // 这里一个操作就是如果部门是多个拼接的，就指定一个部门id
            // if ($.isNotEmpty(personnel.get(0).getDeptId())) {
            // String[] dList = personnel.get(0).getDeptId().split(",");
            // if (dList.length > 1) {
            // classId = "8888888888888888888";
            // } else {
            // classId = personnel.get(0).getDeptId();
            // }
            // } else {
            // classId = "8888888888888888888";
            // }
            // BigDecimal beforeBalance = personnel.get(0).getBalance();
            // BigDecimal newBalance = BigDecimal.ZERO;
            // BigDecimal price = BigDecimal.ZERO;
            // BigDecimal consumBalance = BigDecimal.ZERO;
            // // 这个主要在消费配置-用餐限制模块当中的统缴餐钱包余额比餐价少时进行判断这部分，可以去模块中了解
            // /*
            // * (默认)0: 统缴餐钱包余额比餐价少时,可以刷脸用餐，并必须要自动扣费，该次消费记录的实收金额等于钱包余额。操作完成后此用餐人员的钱包余额变为0元
            // * 1: 统缴餐钱包余额比餐价少时,不能刷脸用餐，也不需要自动扣费
            // * 2: 统缴餐钱包余额比餐价少时,不能刷脸用餐，但必须要自动扣费，该次消费记录的实收金额等于钱包余额。操作完成后此用餐人员的钱包余额变为0元
            // * 3: 统缴餐钱包余额比餐价少时,正常刷脸用餐，并且钱包余额变为负数
            // */
            // List<MealRestrictionsConfigure> mealRestrictionsConfigureList =
            // mealRestrictionsConfigureService
            // .list(Wrappers.<MealRestrictionsConfigure>query().lambda()
            // .eq(MealRestrictionsConfigure::getIsDel, 0)
            // .eq(MealRestrictionsConfigure::getCompanyId, companyId)
            // .eq(MealRestrictionsConfigure::getStatus,
            // NumberConstant.ZERO.toString()));
            // if ($.isNotEmpty(mealRestrictionsConfigureList)
            // && mealRestrictionsConfigureList.size() > 0
            // && mealRestrictionsConfigureList.get(0).getMealAmountType()
            // .equals(NumberConstant.THIRD.toString())) {
            // // 3: 统缴餐钱包余额比餐价少时,正常刷脸用餐，并且钱包余额变为负数
            // newBalance = order.getPrice().setScale(2, BigDecimal.ROUND_DOWN);
            // price = personnel.get(0).getBalance().subtract(order.getPrice()).setScale(2,
            // BigDecimal.ROUND_DOWN);
            // consumBalance = order.getPrice();
            // } else {
            // // 如果用餐人员的统缴餐余额比用餐金额小，值扣除剩下的统缴餐余额
            // if (personnel.get(0).getBalance().compareTo(order.getPrice()) == -1) {
            // newBalance = personnel.get(0).getBalance().setScale(2,
            // BigDecimal.ROUND_DOWN);
            // price = personnel.get(0).getBalance()
            // .subtract(personnel.get(0).getBalance())
            // .setScale(2, BigDecimal.ROUND_DOWN);
            // consumBalance = personnel.get(0).getBalance();
            // } else {
            // // 如果不是就扣除相应的统缴餐余额
            // newBalance = order.getPrice().setScale(2, BigDecimal.ROUND_DOWN);
            // price = personnel.get(0).getBalance().subtract(order.getPrice()).setScale(2,
            // BigDecimal.ROUND_DOWN);
            // consumBalance = order.getPrice();
            // }
            // }
            // BigDecimal afterBalance = price;
            // // 修改统缴餐订单的状态
            // Boolean update = studentUniteDinnerOrderService.update(Wrappers
            // .<StudentUniteDinnerOrder>update().lambda()
            // .set(StudentUniteDinnerOrder::getDinnerStatus,
            // NumberConstant.FIRST.toString())
            // .set(StudentUniteDinnerOrder::getIsStopEating,
            // NumberConstant.ZERO.toString())
            // .set(StudentUniteDinnerOrder::getBusinessOutletsId, businessOutletsId)
            // .set(StudentUniteDinnerOrder::getWindowGroupId, windowGroupId)
            // .set(StudentUniteDinnerOrder::getDiningSigns,
            // NumberConstant.ZERO.toString())
            // .set(StudentUniteDinnerOrder::getClassId, classId)
            // .set(StudentUniteDinnerOrder::getWorkPersonnelId, $.toLong(workPersonnelId))
            // .set(StudentUniteDinnerOrder::getWindowId, windowId)
            // .set(StudentUniteDinnerOrder::getTakeMealType, Integer.valueOf(getType))
            // .set(StudentUniteDinnerOrder::getTakeMealTime, obj.get("take_meal_time"))
            // .set(StudentUniteDinnerOrder::getLeftPrice, price)
            // .set(StudentUniteDinnerOrder::getConsumPrice, consumBalance)
            // .set(StudentUniteDinnerOrder::getMealOrderNo, mealOrderNo)
            // .eq(StudentUniteDinnerOrder::getCompanyId, order.getCompanyId())
            // .eq(StudentUniteDinnerOrder::getIsDel, 0)
            // .eq(StudentUniteDinnerOrder::getId, order.getId()));
            // //// 2024-07-23 新增Mongodb写入
            // // StudentUniteDinnerOrderSearchCriteria criteria = new
            // //// StudentUniteDinnerOrderSearchCriteria();
            // // criteria.setCompanyId(order.getCompanyId());
            // // criteria.setId(order.getId());
            // // StudentUniteDinnerOrderMongoDomain studentUniteDinnerOrderMongoDomain =
            // new
            // //// StudentUniteDinnerOrderMongoDomain();
            // //
            // studentUniteDinnerOrderMongoDomain.setDinnerStatus(NumberConstant.FIRST.toString());
            // //
            // studentUniteDinnerOrderMongoDomain.setIsStopEating(NumberConstant.ZERO.toString());
            // //
            // studentUniteDinnerOrderMongoDomain.setBusinessOutletsId(businessOutletsId);
            // // studentUniteDinnerOrderMongoDomain.setWindowGroupId(windowGroupId);
            // //
            // studentUniteDinnerOrderMongoDomain.setDiningSigns(NumberConstant.ZERO.toString());
            // // studentUniteDinnerOrderMongoDomain.setClassId(Long.valueOf(classId));
            // //
            // studentUniteDinnerOrderMongoDomain.setWorkPersonnelId($.toLong(workPersonnelId));
            // // studentUniteDinnerOrderMongoDomain.setWindowId(windowId);
            // // studentUniteDinnerOrderMongoDomain.setTakeMealType(getType);
            // //
            // studentUniteDinnerOrderMongoDomain.setTakeMealTime(DateUtil.parse(obj.getString("take_meal_time"),
            // //// DatePattern.NORM_DATETIME_PATTERN));
            // // studentUniteDinnerOrderMongoDomain.setLeftPrice(price);
            // // studentUniteDinnerOrderMongoDomain.setConsumPrice(consumBalance);
            // // studentUniteDinnerOrderMongoDomain.setMealOrderNo(mealOrderNo);

            // // studentUniteDinnerOrderMongoService.update(criteria,
            // // studentUniteDinnerOrderMongoDomain);
            // if (update) {
            // Long orderId = SnowflakeIdUtil.nextId();
            // ConsumDetailsEntity consumDetailsEntity = CommonEntity.consumDetailsEntitys(
            // takeMealDate, orderId, personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(), businessOutletsId, windowGroupId,
            // windowId, order.getDinnerTypeId(), personnel.get(0).getUserName(),
            // order.getMealType(), consumBalance, order.getId(), mealOrderNo,
            // takeMealDate,
            // price, personnel, NumberConstant.ZERO.toString(),
            // NumberConstant.ZERO.toString(), NumberConstant.ZERO.toString(),
            // NumberConstant.ZERO.toString(), NumberConstant.ZERO.toString(), "学生",
            // null, NumberConstant.FIRST.toString(), $.toLong(classId),
            // order.getCanteenId());
            // consumDetailsEntity.setUserName(personnel.get(0).getUserName());
            // consumDetailsEntity.setSex(personnel.get(0).getSex());
            // consumDetailsEntity.setStudentJobNo(personnel.get(0).getStudentJobNo());
            // // 添加消费明细
            // if (consumDetailsService.save(consumDetailsEntity)) {
            // // 修改用餐人员剩余的统缴餐余额
            // if (systemPersonnelService.update(Wrappers.<SystemPersonnelEntity>update()
            // .lambda().set(SystemPersonnelEntity::getBalance, price)
            // .set(SystemPersonnelEntity::getUpdateTime, takeMealDate)
            // .set(SystemPersonnelEntity::getRemarks, "监听统缴餐、预订餐次、预订菜品是否取餐")
            // .eq(SystemPersonnelEntity::getIsDel, 0)
            // .eq(SystemPersonnelEntity::getId, personnel.get(0).getId()))) {
            // // 判断添加的账户明细是教师还是学生，1：学生，2：教职工
            // if (personnel.get(0).getPersonnelType()
            // .equals(NumberConstant.FIRST.toString())) {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.ZERO.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.THIRD.toString(),
            // NumberConstant.FIRST.toString(), null,
            // beforeBalance, newBalance, afterBalance, null,
            // OrderConstant.userFlag.STUDENT.toString(),
            // OrderConstant.userFlag.STUDENT.toString(),
            // personnel.get(0), order.getCanteenId());
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // } else {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.ZERO.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.THIRD.toString(),
            // NumberConstant.FIRST.toString(), null,
            // beforeBalance, newBalance, afterBalance, null,
            // OrderConstant.userFlag.TEACHER.toString(),
            // OrderConstant.userFlag.TEACHER.toString(),
            // personnel.get(0), order.getCanteenId());
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // }
            // }
            // // 追加取餐人数统计
            // MealCollectionStatisticsEntity mealCollectionStatisticsEntity =
            // mealCollectionStatisticsService
            // .getOne(Wrappers.<MealCollectionStatisticsEntity>query().lambda()
            // .eq(MealCollectionStatisticsEntity::getDinnerDate,
            // obj.get("meal_date"))
            // .eq(MealCollectionStatisticsEntity::getCanteenId,
            // canteenId));
            // if ($.isNotEmpty(mealCollectionStatisticsEntity)) {
            // if (order.getMealType().equals(NumberConstant.ZERO.toString())) {
            // mealCollectionStatisticsEntity.setBreakfastMealsNumber(
            // mealCollectionStatisticsEntity.getBreakfastMealsNumber()
            // + 1);
            // mealCollectionStatisticsEntity
            // .setBreakfastNormalAmount(mealCollectionStatisticsEntity
            // .getBreakfastNormalAmount().add(order.getPrice())
            // .setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // if (order.getMealType().equals(NumberConstant.FIRST.toString())) {
            // mealCollectionStatisticsEntity.setLunchMealsNumber(
            // mealCollectionStatisticsEntity.getLunchMealsNumber() + 1);
            // mealCollectionStatisticsEntity
            // .setLunchNormalAmount(mealCollectionStatisticsEntity
            // .getLunchNormalAmount().add(order.getPrice())
            // .setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // if (order.getMealType().equals(NumberConstant.SECOND.toString())) {
            // mealCollectionStatisticsEntity.setSupperMealsNumber(
            // mealCollectionStatisticsEntity.getSupperMealsNumber() + 1);
            // mealCollectionStatisticsEntity
            // .setSupperNormalAmount(mealCollectionStatisticsEntity
            // .getSupperNormalAmount().add(order.getPrice())
            // .setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // if (order.getMealType().equals(NumberConstant.THIRD.toString())) {
            // mealCollectionStatisticsEntity.setNightMealsNumber(
            // mealCollectionStatisticsEntity.getNightMealsNumber() + 1);
            // mealCollectionStatisticsEntity
            // .setNightNormalAmount(mealCollectionStatisticsEntity
            // .getNightNormalAmount().add(order.getPrice())
            // .setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // mealCollectionStatisticsService
            // .updateById(mealCollectionStatisticsEntity);
            // }
            // // 写入一条学校用餐类别餐次取餐的记录，用于餐次是否自动扣费查询时间快慢操作
            // // 因为这里需要根据每天用餐类别下哪个餐次需要用于判断是否需要自动扣费操作
            // List<UserMealTimesEntity> userMealTimesEntityList = userMealTimesService
            // .list(Wrappers.<UserMealTimesEntity>query().lambda()
            // .eq(UserMealTimesEntity::getIsDel, 0)
            // .eq(UserMealTimesEntity::getCanteenId, order.getCanteenId())
            // .eq(UserMealTimesEntity::getDinnerDate,
            // order.getDinnerDate())
            // .eq(UserMealTimesEntity::getDinnerTypeId,
            // order.getDinnerTypeId())
            // .eq(UserMealTimesEntity::getMealType, order.getMealType()));
            // if (userMealTimesEntityList.size() == 0) {
            // UserMealTimesEntity userMealTimesEntity = new UserMealTimesEntity();
            // userMealTimesEntity.setCompanyId(personnel.get(0).getCompanyId());
            // userMealTimesEntity.setDinnerDate(order.getDinnerDate());
            // userMealTimesEntity.setDinnerTypeId(order.getDinnerTypeId());
            // userMealTimesEntity.setMealType(order.getMealType());
            // userMealTimesEntity.setCreateTime(new Date());
            // userMealTimesEntity.setCanteenId(order.getCanteenId());
            // userMealTimesService.save(userMealTimesEntity);
            // }
            // }

            // // //发送家哈哈用餐人员的菜谱信息
            // //
            // if(personnel.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString()))
            // // homehahaService.sendMsgForHomehaha(personnel.get(0), businessOutletsId,
            // // dinnerDate, Integer.valueOf(order.getMealType()), order.getId(), base64,
            // // NumberConstant.ZERO.toString());

            // // 20231008 更新通讯费次数和插入提醒记录
            // List<UserFeeInfoEntity> userFeeInfoList = userFeeInfoService
            // .list(Wrappers.<UserFeeInfoEntity>query().lambda()
            // .eq(UserFeeInfoEntity::getUserId, personnel.get(0).getId()));
            // if (userFeeInfoList.size() > 0) {
            // if (userFeeInfoList.get(0).getStatus()
            // .equals(NumberConstant.ZERO.toString())
            // && userFeeInfoList.get(0).getRemindStatus()
            // .equals(NumberConstant.FIRST.toString())) {
            // userFeeInfoList.get(0)
            // .setRemainingQty(userFeeInfoList.get(0).getRemainingQty() - 1);
            // userFeeInfoService.updateById(userFeeInfoList.get(0));
            // UserFeeCountEntity userFeeCount = new UserFeeCountEntity();
            // userFeeCount.setUserId(personnel.get(0).getId());
            // userFeeCount.setRemindTime(takeMealDate);
            // userFeeCountService.save(userFeeCount);
            // }
            // }

            // // 获取字符串类型的日期
            // String takeMealTimeStr = (String) obj.get("take_meal_time");
            // // 这里主要是添加一条窗口取餐明细
            // WindowMealsDetailEntity windowMealsDetailEntity = CommonEntity
            // .windowMealsDetailEntitys($.toLong(workPersonnelId), companyId,
            // businessOutletsId, windowGroupId, windowId,
            // windowNumber, mealOrderNo, getType,
            // formmat.parse(takeMealTimeStr), mealType,
            // order.getMealCategory(), order.getStudentId(),
            // $.toLong(classId), order.getCanteenId());
            // windowMealsDetailEntity.setUserName(personnel.get(0).getUserName());
            // windowMealsDetailEntity.setSex(personnel.get(0).getSex());
            // windowMealsDetailEntity.setStudentJobNo(personnel.get(0).getStudentJobNo());
            // if (windowMealsDetailService.save(windowMealsDetailEntity)) {
            // return Action.CommitMessage;
            // }
            // return Action.CommitMessage;
            // }
            // }
            // }
            // // 这部分主要是没有生成统缴餐订单，取餐写入一条计划外的订单，有订单的是计划内的
            // } else {
            // BigDecimal price = new BigDecimal((String) obj.get("meal_price"));
            // LinkedList<String> linkedList = new LinkedList();
            // linkedList.add(NumberConstant.FIRST.toString());
            // linkedList.add(NumberConstant.SECOND.toString());
            // Long orderId = SnowflakeIdUtil.nextId();
            // // 获取用餐人员信息
            // List<SystemPersonnelEntity> personnel = systemPersonnelService.list(Wrappers
            // .<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel,
            // 0)
            // .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
            // .eq(SystemPersonnelEntity::getId, $.toLong(obj.get("user_id")))
            // .eq(SystemPersonnelEntity::getCompanyId, companyId));
            // if (personnel.size() > 0) {
            // String classId = null;
            // // 这里一个操作就是如果部门是多个拼接的，就指定一个部门id
            // if ($.isNotEmpty(personnel.get(0).getDeptId())) {
            // String[] dList = personnel.get(0).getDeptId().split(",");
            // if (dList.length > 1) {
            // classId = "8888888888888888888";
            // } else {
            // classId = personnel.get(0).getDeptId();
            // }
            // } else {
            // classId = "8888888888888888888";
            // }
            // BigDecimal beforeBalance = personnel.get(0).getBalance();
            // BigDecimal newBalance = BigDecimal.ZERO;
            // BigDecimal balance = BigDecimal.ZERO;
            // BigDecimal consumBalance = BigDecimal.ZERO;
            // // 这个主要在消费配置-用餐限制模块当中的统缴餐钱包余额比餐价少时进行判断这部分，可以去模块中了解
            // /*
            // * (默认)0: 统缴餐钱包余额比餐价少时,可以刷脸用餐，并必须要自动扣费，该次消费记录的实收金额等于钱包余额。操作完成后此用餐人员的钱包余额变为0元
            // * 1: 统缴餐钱包余额比餐价少时,不能刷脸用餐，也不需要自动扣费
            // * 2: 统缴餐钱包余额比餐价少时,不能刷脸用餐，但必须要自动扣费，该次消费记录的实收金额等于钱包余额。操作完成后此用餐人员的钱包余额变为0元
            // * 3: 统缴餐钱包余额比餐价少时,正常刷脸用餐，并且钱包余额变为负数
            // */
            // List<MealRestrictionsConfigure> mealRestrictionsConfigureList =
            // mealRestrictionsConfigureService
            // .list(Wrappers.<MealRestrictionsConfigure>query().lambda()
            // .eq(MealRestrictionsConfigure::getIsDel, 0)
            // .eq(MealRestrictionsConfigure::getCompanyId, companyId)
            // .eq(MealRestrictionsConfigure::getStatus,
            // NumberConstant.ZERO.toString()));
            // if ($.isNotEmpty(mealRestrictionsConfigureList)
            // && mealRestrictionsConfigureList.size() > 0
            // && mealRestrictionsConfigureList.get(0).getMealAmountType()
            // .equals(NumberConstant.THIRD.toString())) {
            // // 3: 统缴餐钱包余额比餐价少时,正常刷脸用餐，并且钱包余额变为负数
            // newBalance = price.setScale(2, BigDecimal.ROUND_DOWN);
            // balance = personnel.get(0).getBalance().subtract(price).setScale(2,
            // BigDecimal.ROUND_DOWN);
            // consumBalance = price;
            // } else {
            // // 如果用餐人员的统缴餐余额比用餐金额小，值扣除剩下的统缴餐余额
            // if (personnel.get(0).getBalance().compareTo(price) == -1) {
            // newBalance = personnel.get(0).getBalance().setScale(2,
            // BigDecimal.ROUND_DOWN);
            // balance =
            // personnel.get(0).getBalance().subtract(personnel.get(0).getBalance())
            // .setScale(2, BigDecimal.ROUND_DOWN);
            // consumBalance = personnel.get(0).getBalance();
            // } else {
            // // 如果不是就扣除相应的统缴餐余额
            // newBalance = price.setScale(2, BigDecimal.ROUND_DOWN);
            // balance = personnel.get(0).getBalance().subtract(price).setScale(2,
            // BigDecimal.ROUND_DOWN);
            // consumBalance = price;
            // }
            // }
            // BigDecimal afterBalance = balance;
            // StudentUniteDinnerOrder studentUniteDinnerOrder =
            // CommonEntity.studentUniteDinnerOrders(
            // orderId, companyId, mealType, dinnerDate, $.toLong((String)
            // obj.get("user_id")),
            // dinnerTypeId, getType,
            // NumberConstant.FIRST.toString(), businessOutletsId, windowGroupId, windowId,
            // balance, workPersonnelId, mealOrderNo, (String) obj.get("consum_no"),
            // takeMealDate, week, price, $.toLong(classId), canteenId);
            // studentUniteDinnerOrder.setConsumPrice(consumBalance);
            // // 添加一条计划外的用餐订单
            // if (studentUniteDinnerOrderService.save(studentUniteDinnerOrder)) {
            // //// 2024-07-23 新增Mongodb写入
            // // StudentUniteDinnerOrderMongoDomain domain = new
            // //// StudentUniteDinnerOrderMongoDomain(studentUniteDinnerOrder);
            // // studentUniteDinnerOrderMongoService.save(domain);//异步执行

            // ConsumDetailsEntity consumDetailsEntity = CommonEntity.consumDetailsEntitys(
            // takeMealDate, orderId, personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(), businessOutletsId, windowGroupId, windowId,
            // $.toLong(dinnerTypeId), personnel.get(0).getUserName(), mealType,
            // consumBalance, studentUniteDinnerOrder.getId(), mealOrderNo, takeMealDate,
            // balance, personnel, NumberConstant.FIRST.toString(),
            // NumberConstant.ZERO.toString(), NumberConstant.ZERO.toString(),
            // NumberConstant.ZERO.toString(), NumberConstant.ZERO.toString(), "学生", null,
            // NumberConstant.FIRST.toString(), $.toLong(classId), canteenId);
            // consumDetailsEntity.setUserName(personnel.get(0).getUserName());
            // consumDetailsEntity.setSex(personnel.get(0).getSex());
            // consumDetailsEntity.setStudentJobNo(personnel.get(0).getStudentJobNo());
            // // 添加用餐消费明细
            // if (consumDetailsService.save(consumDetailsEntity)) {
            // // // 2023-04-18 支付成功后生成凭证
            // // createVoucherController.saveVoucherForConsumDetails(consumDetailsEntity);

            // // 修改用餐人员剩下的统缴餐余额
            // if (systemPersonnelService.update(Wrappers.<SystemPersonnelEntity>update()
            // .lambda().set(SystemPersonnelEntity::getBalance, balance)
            // .set(SystemPersonnelEntity::getUpdateTime, takeMealDate)
            // .set(SystemPersonnelEntity::getRemarks, "监听统缴餐、预订餐次、预订菜品是否取餐")
            // .eq(SystemPersonnelEntity::getIsDel, 0)
            // .eq(SystemPersonnelEntity::getId, personnel.get(0).getId()))) {
            // // 判断添加的账户明细是教师还是学生，1：学生，2：教职工
            // if (personnel.get(0).getPersonnelType()
            // .equals(NumberConstant.FIRST.toString())) {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.ZERO.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.THIRD.toString(),
            // NumberConstant.FIRST.toString(), null,
            // beforeBalance, newBalance, afterBalance, null,
            // OrderConstant.userFlag.STUDENT.toString(),
            // OrderConstant.userFlag.STUDENT.toString(),
            // personnel.get(0), canteenId);
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // } else {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.ZERO.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.THIRD.toString(),
            // NumberConstant.FIRST.toString(), null,
            // beforeBalance, newBalance, afterBalance, null,
            // OrderConstant.userFlag.TEACHER.toString(),
            // OrderConstant.userFlag.TEACHER.toString(),
            // personnel.get(0), canteenId);
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // }
            // }
            // // 追加取餐人数统计
            // MealCollectionStatisticsEntity mealCollectionStatisticsEntity =
            // mealCollectionStatisticsService
            // .getOne(Wrappers.<MealCollectionStatisticsEntity>query().lambda()
            // .eq(MealCollectionStatisticsEntity::getDinnerDate,
            // (String) obj.get("meal_date"))
            // .eq(MealCollectionStatisticsEntity::getCanteenId, canteenId));
            // if ($.isNotEmpty(mealCollectionStatisticsEntity)) {
            // if (mealType.equals(NumberConstant.ZERO.toString())) {
            // mealCollectionStatisticsEntity.setBreakfastMealsNumber(
            // mealCollectionStatisticsEntity.getBreakfastMealsNumber() + 1);
            // mealCollectionStatisticsEntity.setBreakfastNormalAmount(
            // mealCollectionStatisticsEntity.getBreakfastNormalAmount()
            // .add(price).setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // if (mealType.equals(NumberConstant.FIRST.toString())) {
            // mealCollectionStatisticsEntity.setLunchMealsNumber(
            // mealCollectionStatisticsEntity.getLunchMealsNumber() + 1);
            // mealCollectionStatisticsEntity.setLunchNormalAmount(
            // mealCollectionStatisticsEntity.getLunchNormalAmount().add(price)
            // .setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // if (mealType.equals(NumberConstant.SECOND.toString())) {
            // mealCollectionStatisticsEntity.setSupperMealsNumber(
            // mealCollectionStatisticsEntity.getSupperMealsNumber() + 1);
            // mealCollectionStatisticsEntity.setSupperNormalAmount(
            // mealCollectionStatisticsEntity.getSupperNormalAmount()
            // .add(price).setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // if (mealType.equals(NumberConstant.THIRD.toString())) {
            // mealCollectionStatisticsEntity.setNightMealsNumber(
            // mealCollectionStatisticsEntity.getNightMealsNumber() + 1);
            // mealCollectionStatisticsEntity.setNightNormalAmount(
            // mealCollectionStatisticsEntity.getNightNormalAmount().add(price)
            // .setScale(2, BigDecimal.ROUND_DOWN));
            // }
            // mealCollectionStatisticsService.updateById(mealCollectionStatisticsEntity);
            // }
            // // 写入一条学校用餐类别餐次取餐的记录，用于餐次是否自动扣费查询时间快慢操作
            // // 因为这里需要根据每天用餐类别下哪个餐次需要用于判断是否需要自动扣费操作
            // List<UserMealTimesEntity> userMealTimesEntityList = userMealTimesService
            // .list(Wrappers.<UserMealTimesEntity>query().lambda()
            // .eq(UserMealTimesEntity::getIsDel, 0)
            // .eq(UserMealTimesEntity::getCanteenId, canteenId)
            // .eq(UserMealTimesEntity::getDinnerDate, dinnerDate)
            // .eq(UserMealTimesEntity::getDinnerTypeId, dinnerTypeId)
            // .eq(UserMealTimesEntity::getMealType, mealType));
            // if (userMealTimesEntityList.size() == 0) {
            // UserMealTimesEntity userMealTimesEntity = new UserMealTimesEntity();
            // userMealTimesEntity.setCompanyId(personnel.get(0).getCompanyId());
            // userMealTimesEntity.setDinnerDate(dinnerDate);
            // userMealTimesEntity.setDinnerTypeId($.toLong(dinnerTypeId));
            // userMealTimesEntity.setMealType(mealType);
            // userMealTimesEntity.setCreateTime(new Date());
            // userMealTimesEntity.setCanteenId(canteenId);
            // userMealTimesService.save(userMealTimesEntity);
            // }
            // }

            // // //发送家哈哈用餐人员的菜谱信息
            // //
            // if(personnel.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString()))
            // // homehahaService.sendMsgForHomehaha(personnel.get(0), businessOutletsId,
            // // dinnerDate, Integer.valueOf(order.getMealType()), order.getId(), base64,
            // // NumberConstant.ZERO.toString());

            // // 获取字符串类型的日期
            // String takeMealTimeStr = (String) obj.get("take_meal_time");
            // // 添加一条窗口取餐明细
            // WindowMealsDetailEntity windowMealsDetailEntity = CommonEntity
            // .windowMealsDetailEntitys($.toLong(workPersonnelId), companyId,
            // businessOutletsId, windowGroupId, windowId, windowNumber,
            // mealOrderNo, getType, formmat.parse(takeMealTimeStr), mealType,
            // NumberConstant.ZERO.toString(), userId, $.toLong(classId),
            // canteenId);
            // windowMealsDetailEntity.setUserName(personnel.get(0).getUserName());
            // windowMealsDetailEntity.setSex(personnel.get(0).getSex());
            // windowMealsDetailEntity.setStudentJobNo(personnel.get(0).getStudentJobNo());
            // if (windowMealsDetailService.save(windowMealsDetailEntity)) {
            // return Action.CommitMessage;
            // }
            // return Action.CommitMessage;
            // }
            // }
            // }
            // // 自选餐取餐
            // } else if (orderTypes.equals(NumberConstant.FIRST.toString())) {
            // if (order != null) {
            // // 判断是否已经取过餐了
            // if (order.getDinnerStatus().equals(NumberConstant.ZERO.toString())) {
            // // 用户用餐人员信息
            // List<SystemPersonnelEntity> personnel = systemPersonnelService.list(Wrappers
            // .<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel,
            // 0)
            // .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
            // .eq(SystemPersonnelEntity::getId, order.getStudentId())
            // .eq(SystemPersonnelEntity::getCompanyId, companyId));
            // if (personnel.size() > 0) {
            // String classId = null;
            // // 这里一个操作就是如果部门是多个拼接的，就指定一个部门id
            // if ($.isNotEmpty(personnel.get(0).getDeptId())) {
            // String[] dList = personnel.get(0).getDeptId().split(",");
            // if (dList.length > 1) {
            // classId = "8888888888888888888";
            // } else {
            // classId = personnel.get(0).getDeptId();
            // }
            // } else {
            // classId = "8888888888888888888";
            // }
            // // 修改自选餐订单的状态
            // Boolean update = studentUniteDinnerOrderService.update(Wrappers
            // .<StudentUniteDinnerOrder>update().lambda()
            // .set(StudentUniteDinnerOrder::getIsStopEating,
            // NumberConstant.ZERO.toString())
            // .set(StudentUniteDinnerOrder::getBusinessOutletsId, businessOutletsId)
            // .set(StudentUniteDinnerOrder::getWindowGroupId, windowGroupId)
            // .set(StudentUniteDinnerOrder::getDiningSigns,
            // NumberConstant.ZERO.toString())
            // .set(StudentUniteDinnerOrder::getWindowId, windowId)
            // .set(StudentUniteDinnerOrder::getClassId, classId)
            // .set(StudentUniteDinnerOrder::getDinnerStatus,
            // NumberConstant.FIRST.toString())
            // .set(StudentUniteDinnerOrder::getTakeMealType, Integer.valueOf(getType))
            // .set(StudentUniteDinnerOrder::getTakeMealTime, obj.get("take_meal_time"))
            // .set(StudentUniteDinnerOrder::getMealOrderNo, mealOrderNo)
            // .set(StudentUniteDinnerOrder::getCanteenId, canteenId)
            // .eq(StudentUniteDinnerOrder::getCompanyId, companyId)
            // .eq(StudentUniteDinnerOrder::getIsDel, 0)
            // .eq(StudentUniteDinnerOrder::getId, order.getId()));
            // //// 2024-07-23 新增Mongodb写入
            // // StudentUniteDinnerOrderSearchCriteria criteria = new
            // //// StudentUniteDinnerOrderSearchCriteria();
            // // criteria.setCompanyId(order.getCompanyId());
            // // criteria.setId(order.getId());
            // // StudentUniteDinnerOrderMongoDomain studentUniteDinnerOrderMongoDomain =
            // new
            // //// StudentUniteDinnerOrderMongoDomain();
            // //
            // studentUniteDinnerOrderMongoDomain.setIsStopEating(NumberConstant.ZERO.toString());
            // //
            // studentUniteDinnerOrderMongoDomain.setBusinessOutletsId(businessOutletsId);
            // // studentUniteDinnerOrderMongoDomain.setWindowGroupId(windowGroupId);
            // //
            // studentUniteDinnerOrderMongoDomain.setDiningSigns(NumberConstant.ZERO.toString());
            // // studentUniteDinnerOrderMongoDomain.setWindowId(windowId);
            // // studentUniteDinnerOrderMongoDomain.setClassId(Long.valueOf(classId));
            // //
            // studentUniteDinnerOrderMongoDomain.setDinnerStatus(NumberConstant.FIRST.toString());
            // // studentUniteDinnerOrderMongoDomain.setTakeMealType(getType);
            // //
            // studentUniteDinnerOrderMongoDomain.setTakeMealTime(DateUtil.parse(obj.getString("take_meal_time"),
            // //// DatePattern.NORM_DATETIME_PATTERN));
            // // studentUniteDinnerOrderMongoDomain.setMealOrderNo(mealOrderNo);
            // // studentUniteDinnerOrderMongoDomain.setCanteenId(canteenId);
            // // studentUniteDinnerOrderMongoDomain.setMealOrderNo(mealOrderNo);

            // if (update) {
            // Long orderId = SnowflakeIdUtil.nextId();
            // String consumType = null;
            // String orderType = null;
            // // 判断自选餐类型是菜品还是餐次取餐
            // if (order.getMealCategory().equals(NumberConstant.SECOND.toString())) {
            // orderType = NumberConstant.THIRD.toString();
            // consumType = order.getMealCategory();
            // } else if (order.getMealCategory().equals(NumberConstant.THIRD.toString())) {
            // orderType = NumberConstant.FIVE.toString();
            // consumType = order.getMealCategory();
            // }
            // ConsumDetailsEntity consumDetailsEntity = CommonEntity.consumDetailsEntitys(
            // takeMealDate, orderId, personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(), businessOutletsId, windowGroupId,
            // windowId, personnel.get(0).getMealsType(),
            // personnel.get(0).getUserName(),
            // order.getMealType(), order.getPrice(), order.getId(), mealOrderNo,
            // takeMealDate, BigDecimal.ZERO, personnel,
            // NumberConstant.ZERO.toString(), orderType,
            // NumberConstant.ZERO.toString(), NumberConstant.FIRST.toString(),
            // consumType, "学生", null, NumberConstant.FIRST.toString(),
            // $.toLong(classId), canteenId);
            // consumDetailsEntity.setUserName(personnel.get(0).getUserName());
            // consumDetailsEntity.setSex(personnel.get(0).getSex());
            // consumDetailsEntity.setStudentJobNo(personnel.get(0).getStudentJobNo());
            // // 添加取餐消费明细
            // consumDetailsService.save(consumDetailsEntity);
            // // // 2023-04-18 支付成功后生成凭证
            // // createVoucherController.saveVoucherForConsumDetails(consumDetailsEntity);

            // // 判断添加的账户明细是教师还是学生，1：学生，2：教职工
            // if (personnel.get(0).getPersonnelType()
            // .equals(NumberConstant.FIRST.toString())) {
            // if (order.getMealCategory().equals(NumberConstant.SECOND.toString())) {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.FIRST.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.FIFTEEN.toString(),
            // NumberConstant.SECOND.toString(),
            // "从预订菜品-预付款中扣除" + order.getPrice() + "元",
            // personnel.get(0).getOptionalBalance(), order.getPrice(),
            // personnel.get(0).getOptionalBalance(), null,
            // OrderConstant.userFlag.STUDENT.toString(),
            // OrderConstant.userFlag.STUDENT.toString(),
            // personnel.get(0), canteenId);
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // } else if (order.getMealCategory()
            // .equals(NumberConstant.THIRD.toString())) {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.FIRST.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.ELEVEN.toString(),
            // NumberConstant.SECOND.toString(),
            // "从预订餐次-预付款中扣除" + order.getPrice() + "元",
            // personnel.get(0).getOptionalBalance(), order.getPrice(),
            // personnel.get(0).getOptionalBalance(), null,
            // OrderConstant.userFlag.STUDENT.toString(),
            // OrderConstant.userFlag.STUDENT.toString(),
            // personnel.get(0), canteenId);
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // }
            // } else {
            // if (order.getMealCategory().equals(NumberConstant.SECOND.toString())) {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.FIRST.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.FIFTEEN.toString(),
            // NumberConstant.SECOND.toString(),
            // "从预订菜品-预付款中扣除" + order.getPrice() + "元",
            // personnel.get(0).getOptionalBalance(), order.getPrice(),
            // personnel.get(0).getOptionalBalance(), null,
            // OrderConstant.userFlag.TEACHER.toString(),
            // OrderConstant.userFlag.TEACHER.toString(),
            // personnel.get(0), canteenId);
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // } else if (order.getMealCategory()
            // .equals(NumberConstant.THIRD.toString())) {
            // PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
            // .personnelConsumAmountEntitys(personnel.get(0).getId(),
            // personnel.get(0).getCompanyId(),
            // NumberConstant.FIRST.toString(),
            // consumDetailsEntity.getId(),
            // NumberConstant.ELEVEN.toString(),
            // NumberConstant.SECOND.toString(),
            // "从预订餐次-预付款中扣除" + order.getPrice() + "元",
            // personnel.get(0).getOptionalBalance(), order.getPrice(),
            // personnel.get(0).getOptionalBalance(), null,
            // OrderConstant.userFlag.TEACHER.toString(),
            // OrderConstant.userFlag.TEACHER.toString(),
            // personnel.get(0), canteenId);
            // personnelConsumAmountService.save(personnelConsumAmountEntity);
            // }
            // }
            // // 获取字符串类型的日期
            // String takeMealTimeStr = (String) obj.get("take_meal_time");
            // WindowMealsDetailEntity windowMealsDetailEntity = CommonEntity
            // .windowMealsDetailEntitys($.toLong(workPersonnelId), companyId,
            // businessOutletsId, windowGroupId, windowId, windowNumber,
            // mealOrderNo, getType, formmat.parse(takeMealTimeStr), mealType,
            // order.getMealCategory(), order.getStudentId(),
            // $.toLong(classId), canteenId);
            // windowMealsDetailEntity.setUserName(personnel.get(0).getUserName());
            // windowMealsDetailEntity.setSex(personnel.get(0).getSex());
            // windowMealsDetailEntity.setStudentJobNo(personnel.get(0).getStudentJobNo());
            // // 添加窗口取餐明细
            // if (windowMealsDetailService.save(windowMealsDetailEntity)) {
            // return Action.CommitMessage;
            // }
            // return Action.CommitMessage;
            // }
            // }
            // }
            // }
            // }
            // }
            // }
            // // 监听自由消费信息
            else if (message.getTag().equals("FREE_CONSUM_TAG")) {
                Long companyId = $.toLong(obj.get("school_id"));// 学校id
                Long canteenId = $.toLong(obj.get("cannteen_id"));// 食堂id
                String orderId = (String) obj.get("order_id");// 订单id
                String consumeType = (String) obj.get("consume_type");// 消费类型
                Long userId = $.toLong(obj.get("user_id"));
                Long workPersonnelId = $.toLong(obj.get("work_personnel_id"));// 工作人员
                // 自由消费订单：0是正常订单,1是纠错订单？
                if (consumeType.equals(NumberConstant.ZERO.toString())) {
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    // 查询是否已经存在这个消费订单了，如果存在不执行，不存在就执行以下判断里面的操作。
                    ConsumDetailsEntity cList = consumDetailsService
                            .getOne(Wrappers.<ConsumDetailsEntity>query().lambda().eq(ConsumDetailsEntity::getIsDel, 0)
                                    .eq(ConsumDetailsEntity::getCompanyId, companyId)
                                    .eq(ConsumDetailsEntity::getOrderId, $.toLong(orderId)));
                    if (cList == null) {
                        // 获取用餐人员信息
                        List<SystemPersonnelEntity> sList = systemPersonnelService.list(
                                Wrappers.<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                                        .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
                                        .eq(SystemPersonnelEntity::getId, userId)
                                        .eq(SystemPersonnelEntity::getCompanyId, companyId));
                        if (sList.size() > 0) {
                            String classId = null;
                            // 判断是否部门是拼接的，如果是就指定一个部门id。
                            if ($.isNotEmpty(sList.get(0).getDeptId())) {
                                String[] dList = sList.get(0).getDeptId().split(",");
                                if (dList.length > 1) {
                                    classId = "8888888888888888888";
                                } else {
                                    classId = sList.get(0).getDeptId();
                                }
                            } else {
                                classId = "8888888888888888888";
                            }
                            // 之前余额
                            BigDecimal beforeBalance = sList.get(0).getOptionalBalance();
                            // 消费金额
                            BigDecimal newBalance = new BigDecimal((String) obj.get("consume_meal_price"));
                            // 扣减后余额
                            BigDecimal balance = sList.get(0).getOptionalBalance()
                                    .subtract(new BigDecimal((String) obj.get("consume_meal_price")))
                                    .setScale(2, BigDecimal.ROUND_DOWN);

                            BigDecimal afterBalance = BigDecimal.ZERO;
                            if (BigDecimal.ZERO.compareTo(balance) == 1) {
                                balance = BigDecimal.ZERO;
                                newBalance = beforeBalance;
                            } else {
                                afterBalance = balance;
                            }

                            String dinnerDate = (String) obj.get("meal_date");// 餐次日期(yyyy-MM-dd)
                            String takeMealTime = (String) obj.get("take_meal_time");// 取餐时间
                            String mealType = (String) obj.get("meal_type");// 0早餐1午餐2晚餐3夜宵)
                            Long studentId = $.toLong(obj.get("user_id"));// 用户ID
                            BigDecimal price = new BigDecimal((String) obj.get("meal_price"));// 餐次价格(固定餐有)
                            Long windowGroupId = $.toLong(obj.get("window_group_id"));// 窗口组
                            Long windowId = $.toLong(obj.get("window_id"));// 窗口号id
                            Long businessOutletsId = $.toLong(obj.get("business_outlets_id"));// 营业网点
                            String mealOrderNo = (String) obj.get("meal_order_no");// 取餐单号
                            String consumNo = (String) obj.get("consum_no");// 关联消费单号
                            String isFree = (String) obj.get("is_free");// 是否免单(0不免费1是免费）
                            BigDecimal consumeMealPrice = new BigDecimal((String) obj.get("consume_meal_price"));// 实际消费金额(跟餐次价格一样，如果免单则为0)
                            String user_type = (String) obj.get("user_type");// 是教师还是学生
                            String getType = (String) obj.get("get_type");
                            String windowNumber = (String) obj.get("window_number");
                            Date simpleDate = simpleDateFormat.parse(takeMealTime);
                            ConsumDetailsEntity consumDetails = new ConsumDetailsEntity();
                            consumDetails.setOrderId($.toLong(orderId));
                            consumDetails.setOptionalBalance(balance);
                            if (user_type.equals("student")) {
                                consumDetails.setOperatorName("学生");
                                consumDetails.setOperatorIdentity("STUDENT");
                                consumDetails.setStayUserFlag("STUDENT");
                                consumDetails.setConsumOperationUser(studentId);
                            } else {
                                consumDetails.setOperatorName("老师");
                                consumDetails.setOperatorIdentity("TEACHER");
                                consumDetails.setStayUserFlag("TEACHER");
                                consumDetails.setConsumOperationUser(studentId);
                            }
                            consumDetails.setIsFree(isFree);
                            consumDetails.setActualConsumBalance(consumeMealPrice);
                            consumDetails.setPaySuccessTime(simpleDate);
                            consumDetails.setOutletsId(businessOutletsId);
                            consumDetails.setWindowGroupId(windowGroupId);
                            consumDetails.setWindowId(windowId);
                            consumDetails.setUserId(studentId);
                            consumDetails.setConsumPrices(price);
                            consumDetails.setPlaceOrderName(sList.get(0).getUserName());
                            consumDetails.setStatus("PAID");
                            consumDetails.setMealOrderNo(mealOrderNo);
                            consumDetails.setPayMethod(NumberConstant.SECOND.toString());
                            consumDetails.setConsumType(NumberConstant.FIRST.toString());
                            consumDetails.setDinnerStatus(NumberConstant.ZERO.toString());
                            consumDetails.setMealsType(NumberConstant.FIRST.toString());
                            consumDetails.setDiningSigns(NumberConstant.FIRST.toString());
                            consumDetails.setDinnerType(mealType);
                            consumDetails.setConsumBillNo($.toLong(consumNo));
                            consumDetails.setCompanyId(companyId);
                            consumDetails.setConsumTime(simpleDateFormat.parse(dinnerDate));
                            consumDetails.setConsumBillNo($.toLong(consumNo));
                            consumDetails.setRefundStatus(NumberConstant.ZERO.toString());
                            consumDetails.setIsPatching(NumberConstant.ZERO.toString());
                            consumDetails.setOrderType("1");
                            consumDetails.setClassId($.toLong(classId));
                            consumDetails.setIsErrorCorrection(NumberConstant.ZERO.toString());
                            consumDetails.setCanteenId(canteenId);
                            consumDetails.setUserName(sList.get(0).getUserName());
                            consumDetails.setSex(sList.get(0).getSex());
                            consumDetails.setStudentJobNo(sList.get(0).getStudentJobNo());
                            // 添加一条消费明细
                            if (consumDetailsService.save(consumDetails)) {
                                // 判断自由消费取餐金额是否为0，如果是就触发一条违规预警
                                if (price.compareTo(BigDecimal.ZERO) < 1) {
                                    if (sList.size() > 0) {
                                        if (sList.get(0).getPersonnelType().equals(NumberConstant.SECOND.toString())) {
                                            SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒");
                                            SimpleDateFormat sdf3 = new SimpleDateFormat("yyyy年MM月dd日");
                                            Date date = new Date();
                                            String dateStringParse = sdf2.format(date);
                                            Dept dept = SysCache.getDept(canteenId);
                                            IllegalWarnLogEntity logEntity = new IllegalWarnLogEntity();
                                            // 查看是否启用了预警配置 启用了才生成记录
                                            IllegalWarnSettingsEntity one = illegalWarnSettingsService.getOne(Wrappers
                                                    .<IllegalWarnSettingsEntity>lambdaQuery()
                                                    .eq(IllegalWarnSettingsEntity::getCategory,
                                                            NumberConstant.TEN.toString())
                                                    .eq(IllegalWarnSettingsEntity::getTenantId, dept.getTenantId())
                                                    .eq(IllegalWarnSettingsEntity::getIsUsing,
                                                            NumberConstant.FIRST.toString()));
                                            if (one != null) {
                                                logEntity.setType(NumberConstant.TEN.toString());
                                                logEntity.setTableId(consumDetails.getId());
                                                logEntity.setDeptId(canteenId);
                                                logEntity.setTenantId(dept.getTenantId());
                                                logEntity.setStatus(NumberConstant.FIRST.toString());
                                                logEntity.setInquiryStatus(NumberConstant.FIRST.toString());
                                                logEntity.setIllegalDate(new Date());
                                                logEntity.setFindDate(new Date());
                                                logEntity.setCategory(NumberConstant.FIRST.toString());
                                                logEntity.setUnitType(dept.getAgencyType());
                                                logEntity.setContent("角质功能在自选餐（自由消费）中的实收金额为零");
                                                /*
                                                 * logEntity.setEvidence(dept.getDeptName() + "于" + sdf.format(new
                                                 * Date()) + "在'" + bidding.getEntryName() + "'的中标评选中未上传评标会现场图片。");
                                                 */
                                                logEntity.setEvidence(dept.getDeptName() + "系统于" + dateStringParse
                                                        + "，对消费记录进行查询，发现在" + sdf3.parse(dinnerDate) + "，发生了教职工【"
                                                        + sList.get(0).getUserName() + "】免费用餐行为。");
                                                logEntity.setCreateTime(new Date());
                                                illegalWarnLogService.save(logEntity);
                                            }
                                        }
                                    }
                                }
                                // 修改用餐人员剩下的余额
                                if (systemPersonnelService.update(Wrappers.<SystemPersonnelEntity>update().lambda()
                                        .set(SystemPersonnelEntity::getOptionalBalance, balance)
                                        .set(SystemPersonnelEntity::getUpdateTime, simpleDate)
                                        .set(SystemPersonnelEntity::getRemarks, "监听自由消费信息")
                                        .eq(SystemPersonnelEntity::getIsDel, 0)
                                        .eq(SystemPersonnelEntity::getId, sList.get(0).getId()))) {
                                    if (sList.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString())) {
                                        PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
                                                .personnelConsumAmountEntitys(sList.get(0).getId(),
                                                        sList.get(0).getCompanyId(),
                                                        NumberConstant.FIRST.toString(), consumDetails.getId(),
                                                        NumberConstant.SEVEN.toString(),
                                                        NumberConstant.FIRST.toString(), null,
                                                        beforeBalance, newBalance, afterBalance, null,
                                                        OrderConstant.userFlag.STUDENT.toString(),
                                                        OrderConstant.userFlag.STUDENT.toString(), sList.get(0),
                                                        canteenId);
                                        personnelConsumAmountService.save(personnelConsumAmountEntity);
                                    } else {
                                        PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
                                                .personnelConsumAmountEntitys(sList.get(0).getId(),
                                                        sList.get(0).getCompanyId(),
                                                        NumberConstant.FIRST.toString(), consumDetails.getId(),
                                                        NumberConstant.SEVEN.toString(),
                                                        NumberConstant.FIRST.toString(), null,
                                                        beforeBalance, newBalance, afterBalance, null,
                                                        OrderConstant.userFlag.TEACHER.toString(),
                                                        OrderConstant.userFlag.TEACHER.toString(), sList.get(0),
                                                        canteenId);
                                        personnelConsumAmountService.save(personnelConsumAmountEntity);
                                    }

                                }

                                // //发送家哈哈用餐人员的菜谱信息(只发送学生信息)
                                // if(sList.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString())){
                                // String base64 = (String) obj.get("meal_photo");//取餐照片
                                // homehahaService.sendMsgForHomehaha(sList.get(0), businessOutletsId,
                                // simpleDateFormat.parse(dinnerDate), Integer.valueOf(mealType),
                                // $.toLong(orderId), base64, NumberConstant.FIRST.toString());
                                // }
                                String takeMealTimeStr = (String) obj.get("take_meal_time");
                                WindowMealsDetailEntity windowMealsDetailEntity = CommonEntity.windowMealsDetailEntitys(
                                        workPersonnelId, companyId, businessOutletsId, windowGroupId, windowId,
                                        windowNumber, mealOrderNo, getType, simpleDateFormat.parse(takeMealTimeStr),
                                        mealType, NumberConstant.FIRST.toString(), studentId, $.toLong(classId),
                                        canteenId);
                                windowMealsDetailEntity.setUserName(sList.get(0).getUserName());
                                windowMealsDetailEntity.setSex(sList.get(0).getSex());
                                windowMealsDetailEntity.setStudentJobNo(sList.get(0).getStudentJobNo());
                                // 添加一条窗口取餐明细
                                if (windowMealsDetailService.save(windowMealsDetailEntity)) {
                                    return Action.CommitMessage;
                                }
                            }
                        }
                    } else {
                        return Action.CommitMessage;
                    }
                    // 以下是自由消费取餐金额扣费错误的操作，多扣钱和少扣钱
                } else {
                    ErrorCorrectionDetailsEntity errorCorrectionDetails = new ErrorCorrectionDetailsEntity();
                    // 查询自由消费的订单是否存在
                    ConsumDetailsEntity find = consumDetailsService
                            .getOne(Wrappers.<ConsumDetailsEntity>query().lambda().eq(ConsumDetailsEntity::getIsDel, 0)
                                    .eq(ConsumDetailsEntity::getOrderId, orderId));
                    if (find != null) {
                        // 获取消费人员信息
                        List<SystemPersonnelEntity> systemPersonnelEntityList = systemPersonnelService.list(
                                Wrappers.<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                                        .eq(SystemPersonnelEntity::getId, find.getUserId())
                                        .eq(SystemPersonnelEntity::getCompanyId, find.getCompanyId()));
                        String classId = null;
                        if (systemPersonnelEntityList.size() > 0) {
                            // 判断是否部门是拼接的，如果是就指定一个部门id。
                            if ($.isNotEmpty(systemPersonnelEntityList.get(0).getDeptId())) {
                                String[] dList = systemPersonnelEntityList.get(0).getDeptId().split(",");
                                if (dList.length > 1) {
                                    classId = "8888888888888888888";
                                } else {
                                    classId = systemPersonnelEntityList.get(0).getDeptId();
                                }
                            } else {
                                classId = "8888888888888888888";
                            }
                        }
                        errorCorrectionDetails.setCanteenId(canteenId);
                        errorCorrectionDetails.setConsumNo(find.getConsumBillNo());
                        errorCorrectionDetails.setConsumOrderId(find.getId());
                        errorCorrectionDetails.setErrorCorrectionNo(SnowflakeIdUtil.nextId());
                        errorCorrectionDetails.setCompanyId(find.getCompanyId());
                        errorCorrectionDetails.setUserId(find.getUserId());
                        errorCorrectionDetails.setClassId($.toLong(classId));
                        errorCorrectionDetails.setOrderAmount(find.getConsumPrices());
                        errorCorrectionDetails.setPaidAmount(new BigDecimal((String) obj.get("consume_meal_price")));
                        errorCorrectionDetails
                                .setDifferenceAmount((new BigDecimal((String) obj.get("consume_meal_price")))
                                        .setScale(2, BigDecimal.ROUND_DOWN).subtract(find.getConsumPrices()));
                        if (errorCorrectionDetails.getDifferenceAmount().compareTo(new BigDecimal(0)) == -1) {
                            errorCorrectionDetails.setStatus(NumberConstant.SECOND.toString());
                            errorCorrectionDetails.setErrorRemark("自由消费多收钱");
                        } else {
                            errorCorrectionDetails.setStatus(NumberConstant.FIRST.toString());
                            errorCorrectionDetails.setErrorRemark("自由消费少收钱");
                        }
                        errorCorrectionDetails.setCreateUser(workPersonnelId);
                        errorCorrectionDetails.setCreateTime(new Date());
                        errorCorrectionDetails.setUserName(systemPersonnelEntityList.get(0).getUserName());
                        errorCorrectionDetails.setSex(systemPersonnelEntityList.get(0).getSex());
                        errorCorrectionDetails.setStudentJobNo(systemPersonnelEntityList.get(0).getStudentJobNo());
                        errorCorrectionDetails.setType(NumberConstant.ZERO.toString());
                        errorCorrectionDetails.setPaySuccessTime(find.getPaySuccessTime());
                        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                        String date = sdf.format(new Date());
                        // 添加一条消费纠错记录
                        if (errorCorrectionDetailsService.saveOrUpdate(errorCorrectionDetails)) {
                            // // 2023-04-18 支付成功后生成凭证
                            // createVoucherController.saveVoucherForErrorCorrection(errorCorrectionDetails);

                            // BusinessIncomeStatisticsEntity businnesIncome =
                            // businessIncomeStatisticsService.getIncomeData(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),
                            // find.getCompanyId().toString(), date,canteenId);
                            BigDecimal blanace = BigDecimal.ZERO;
                            // 自由消费少收钱纠错逻辑处理
                            if (errorCorrectionDetails.getStatus().equals(NumberConstant.FIRST.toString())) {
                                List<SystemPersonnelEntity> sList = systemPersonnelService.list(Wrappers
                                        .<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                                        .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
                                        .eq(SystemPersonnelEntity::getId, errorCorrectionDetails.getUserId()));
                                if (sList.size() > 0) {
                                    blanace = errorCorrectionDetails.getPaidAmount()
                                            .subtract(errorCorrectionDetails.getOrderAmount());
                                    BigDecimal beforeBalance = sList.get(0).getOptionalBalance();
                                    BigDecimal newBalance = blanace;
                                    BigDecimal afterBalance = sList.get(0).getOptionalBalance().subtract(blanace);
                                    if (systemPersonnelService.update(Wrappers.<SystemPersonnelEntity>update().lambda()
                                            .set(SystemPersonnelEntity::getOptionalBalance, afterBalance)
                                            .set(SystemPersonnelEntity::getUpdateTime, new Date())
                                            .set(SystemPersonnelEntity::getRemarks, "监听自由消费信息")
                                            .eq(SystemPersonnelEntity::getId, sList.get(0).getId())
                                            .eq(SystemPersonnelEntity::getIsDel, 0))) {
                                        if (sList.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString())) {
                                            PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
                                                    .personnelConsumAmountEntitys(sList.get(0).getId(),
                                                            sList.get(0).getCompanyId(),
                                                            NumberConstant.FIRST.toString(),
                                                            errorCorrectionDetails.getId(),
                                                            NumberConstant.NINE.toString(),
                                                            NumberConstant.FIRST.toString(), null,
                                                            beforeBalance, newBalance, afterBalance, null,
                                                            OrderConstant.userFlag.STUDENT.toString(),
                                                            OrderConstant.userFlag.STUDENT.toString(), sList.get(0),
                                                            canteenId);
                                            personnelConsumAmountEntity.setCreateTime(find.getConsumTime());// 收费管理-账户明细-自选餐明细
                                                                                                            // 显示的操作时间应为取餐时间
                                            personnelConsumAmountService.save(personnelConsumAmountEntity);
                                        } else {
                                            PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
                                                    .personnelConsumAmountEntitys(sList.get(0).getId(),
                                                            sList.get(0).getCompanyId(),
                                                            NumberConstant.FIRST.toString(),
                                                            errorCorrectionDetails.getId(),
                                                            NumberConstant.NINE.toString(),
                                                            NumberConstant.FIRST.toString(), null,
                                                            beforeBalance, newBalance, afterBalance, null,
                                                            OrderConstant.userFlag.TEACHER.toString(),
                                                            OrderConstant.userFlag.TEACHER.toString(), sList.get(0),
                                                            canteenId);
                                            personnelConsumAmountEntity.setCreateTime(find.getConsumTime());// 收费管理-账户明细-自选餐明细
                                                                                                            // 显示的操作时间应为取餐时间
                                            personnelConsumAmountService.save(personnelConsumAmountEntity);
                                        }
                                        // 2024-01-04 消费明细统计表会在凌晨定时程序执行统计，不会垮天去纠错，因此屏蔽该段逻辑
                                        // if (businnesIncome == null) {
                                        // BusinessIncomeStatisticsEntity businessIncomeStatisticsEntity =
                                        // CommonEntity.businessIncomeStatisticsEntitys(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),blanace,companyId,new
                                        // Date(),NumberConstant.SECOND.toString(),NumberConstant.FIRST.toString(),canteenId);
                                        // businessIncomeStatisticsService.save(businessIncomeStatisticsEntity);
                                        // } else {
                                        // businessIncomeStatisticsService.update(Wrappers.<BusinessIncomeStatisticsEntity>update().lambda().
                                        // set(BusinessIncomeStatisticsEntity::getFreeConsumBalance,businnesIncome.getFreeConsumBalance().add(blanace)).
                                        // eq(BusinessIncomeStatisticsEntity::getIsDel, 0).
                                        // eq(BusinessIncomeStatisticsEntity::getId, businnesIncome.getId()));
                                        // }
                                        MealsIncomeStatisticsEntity mealsIncomeStatisticsEntity = mealsIncomeStatisticsService
                                                .getMealsIncomeData(find.getOutletsId(), find.getWindowGroupId(),
                                                        find.getWindowId(), find.getCompanyId().toString(), date,
                                                        canteenId);
                                        if (mealsIncomeStatisticsEntity == null) {
                                            MealsIncomeStatisticsEntity mealsIncomeStatisticsEntity1 = CommonEntity
                                                    .mealsIncomeStatisticsEntitys2(find.getOutletsId(),
                                                            find.getWindowGroupId(), find.getWindowId(),
                                                            find.getDinnerType(), blanace, find.getCompanyId(),
                                                            new Date(), "2", canteenId);
                                            mealsIncomeStatisticsService.save(mealsIncomeStatisticsEntity1);
                                        } else {
                                            if (find.getDinnerType().equals(NumberConstant.ZERO.toString())) {
                                                BigDecimal brekkerBalance = mealsIncomeStatisticsEntity
                                                        .getBrekkerBalance().add(blanace);
                                                mealsIncomeStatisticsService
                                                        .update(Wrappers.<MealsIncomeStatisticsEntity>update().lambda()
                                                                .set(MealsIncomeStatisticsEntity::getBrekkerBalance,
                                                                        brekkerBalance)
                                                                .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                                .eq(MealsIncomeStatisticsEntity::getId,
                                                                        mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.FIRST.toString())) {
                                                BigDecimal lunchBalance = mealsIncomeStatisticsEntity.getLunchBalance()
                                                        .add(blanace);
                                                mealsIncomeStatisticsService.update(Wrappers
                                                        .<MealsIncomeStatisticsEntity>update().lambda()
                                                        .set(MealsIncomeStatisticsEntity::getLunchBalance, lunchBalance)
                                                        .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                        .eq(MealsIncomeStatisticsEntity::getId,
                                                                mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.SECOND.toString())) {
                                                BigDecimal supperBalance = mealsIncomeStatisticsEntity
                                                        .getSupperBalance().add(blanace);
                                                mealsIncomeStatisticsService
                                                        .update(Wrappers.<MealsIncomeStatisticsEntity>update().lambda()
                                                                .set(MealsIncomeStatisticsEntity::getSupperBalance,
                                                                        supperBalance)
                                                                .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                                .eq(MealsIncomeStatisticsEntity::getId,
                                                                        mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.THIRD.toString())) {
                                                BigDecimal nightBalance = mealsIncomeStatisticsEntity.getNightBalance()
                                                        .add(blanace);
                                                mealsIncomeStatisticsService.update(Wrappers
                                                        .<MealsIncomeStatisticsEntity>update().lambda()
                                                        .set(MealsIncomeStatisticsEntity::getNightBalance, nightBalance)
                                                        .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                        .eq(MealsIncomeStatisticsEntity::getId,
                                                                mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.FREEFIR)) {
                                                BigDecimal otherBalance = mealsIncomeStatisticsEntity.getOtherBalance()
                                                        .add(blanace);
                                                mealsIncomeStatisticsService.update(Wrappers
                                                        .<MealsIncomeStatisticsEntity>update().lambda()
                                                        .set(MealsIncomeStatisticsEntity::getOtherBalance, otherBalance)
                                                        .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                        .eq(MealsIncomeStatisticsEntity::getId,
                                                                mealsIncomeStatisticsEntity.getId()));
                                            }
                                        }
                                        // // 2024-01-04 窗口明细统计报表因为统计此逻辑会导致与消费明细报表不一致,因此要在窗口明细统计报表加上扣减去的部分
                                        // WindowConsumStatisticsEntity windowConsumStatistics1 =
                                        // windowConsumStatisticsService.getWindowIncomeData(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId().toString(),simDate.format(new
                                        // Date()),"1",canteenId);
                                        // if (windowConsumStatistics1 == null ){
                                        // WindowConsumStatisticsEntity windowConsumStatisticsEntity1 =
                                        // CommonEntity.windowConsumStatisticsEntitys(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId(),NumberConstant.FIRST.toString(),NumberConstant.SECOND.toString(),blanace,new
                                        // Date(),NumberConstant.FIRST.toString(),canteenId);
                                        // windowConsumStatisticsService.save(windowConsumStatisticsEntity1);
                                        // }else {
                                        // BigDecimal freeConsumBalance =
                                        // windowConsumStatistics1.getFreeConsumBalance().add(blanace);
                                        // windowConsumStatisticsService.update(Wrappers.<WindowConsumStatisticsEntity>update().lambda().set(WindowConsumStatisticsEntity::getFreeConsumBalance,freeConsumBalance).
                                        // eq(WindowConsumStatisticsEntity::getIsDel,0).
                                        // eq(WindowConsumStatisticsEntity::getId,windowConsumStatistics1.getId()));
                                        // }
                                        // WindowConsumStatisticsEntity windowConsumStatisticsEntity =
                                        // windowConsumStatisticsService.getWindowIncomeData(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId().toString(),simDate.format(new
                                        // Date()),"4",canteenId);
                                        // if (windowConsumStatisticsEntity == null ){
                                        // WindowConsumStatisticsEntity windowConsumStatisticsEntity1 =
                                        // CommonEntity.windowConsumStatisticsEntitys(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId(),NumberConstant.FOUR.toString(),NumberConstant.SECOND.toString(),blanace,new
                                        // Date(),NumberConstant.FIRST.toString(),canteenId);
                                        // windowConsumStatisticsService.save(windowConsumStatisticsEntity1);
                                        // }else {
                                        // BigDecimal freeConsumBalance =
                                        // windowConsumStatisticsEntity.getFreeConsumBalance().add(blanace);
                                        // windowConsumStatisticsService.update(Wrappers.<WindowConsumStatisticsEntity>update().lambda().set(WindowConsumStatisticsEntity::getFreeConsumBalance,freeConsumBalance).
                                        // eq(WindowConsumStatisticsEntity::getIsDel,0).
                                        // eq(WindowConsumStatisticsEntity::getId,windowConsumStatisticsEntity.getId()));
                                        // }

                                        // 修改消费订单为已纠错
                                        consumDetailsService.update(Wrappers.<ConsumDetailsEntity>update().lambda()
                                                .set(ConsumDetailsEntity::getActualConsumBalance,
                                                        errorCorrectionDetails.getPaidAmount())
                                                .set(ConsumDetailsEntity::getIsErrorCorrection,
                                                        NumberConstant.FIRST.toString())
                                                .eq(ConsumDetailsEntity::getIsDel, 0)
                                                .eq(ConsumDetailsEntity::getId, find.getId()));
                                        return Action.CommitMessage;
                                    }
                                }
                            }
                            // 自由消费多收钱纠错逻辑处理
                            if (errorCorrectionDetails.getStatus().equals(NumberConstant.SECOND.toString())) {
                                // 获取消费人员信息
                                List<SystemPersonnelEntity> sList = systemPersonnelService.list(Wrappers
                                        .<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                                        .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
                                        .eq(SystemPersonnelEntity::getId, errorCorrectionDetails.getUserId()));
                                if (sList.size() > 0) {
                                    blanace = errorCorrectionDetails.getOrderAmount()
                                            .subtract(errorCorrectionDetails.getPaidAmount());

                                    BigDecimal beforeBalance = sList.get(0).getOptionalBalance();
                                    BigDecimal newBalance = blanace;
                                    BigDecimal afterBalance = sList.get(0).getOptionalBalance().add(blanace);
                                    // 修改用餐人员剩下的余额
                                    if (systemPersonnelService.update(Wrappers.<SystemPersonnelEntity>update().lambda()
                                            .set(SystemPersonnelEntity::getOptionalBalance, afterBalance)
                                            .set(SystemPersonnelEntity::getUpdateTime, new Date())
                                            .set(SystemPersonnelEntity::getRemarks, "自由消费纠错")
                                            .eq(SystemPersonnelEntity::getId, sList.get(0).getId())
                                            .eq(SystemPersonnelEntity::getIsDel, 0))) {
                                        if (sList.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString())) {
                                            PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
                                                    .personnelConsumAmountEntitys(sList.get(0).getId(),
                                                            sList.get(0).getCompanyId(),
                                                            NumberConstant.FIRST.toString(),
                                                            errorCorrectionDetails.getId(),
                                                            NumberConstant.NINE.toString(),
                                                            NumberConstant.ZERO.toString(), null,
                                                            beforeBalance, newBalance, afterBalance, null,
                                                            OrderConstant.userFlag.STUDENT.toString(),
                                                            OrderConstant.userFlag.STUDENT.toString(), sList.get(0),
                                                            canteenId);
                                            personnelConsumAmountService.save(personnelConsumAmountEntity);
                                        } else {
                                            PersonnelConsumAmountEntity personnelConsumAmountEntity = CommonEntity
                                                    .personnelConsumAmountEntitys(sList.get(0).getId(),
                                                            sList.get(0).getCompanyId(),
                                                            NumberConstant.FIRST.toString(),
                                                            errorCorrectionDetails.getId(),
                                                            NumberConstant.NINE.toString(),
                                                            NumberConstant.ZERO.toString(), null,
                                                            beforeBalance, newBalance, afterBalance, null,
                                                            OrderConstant.userFlag.TEACHER.toString(),
                                                            OrderConstant.userFlag.TEACHER.toString(), sList.get(0),
                                                            canteenId);
                                            personnelConsumAmountService.save(personnelConsumAmountEntity);
                                        }
                                        // //2024-01-04 消费明细统计表会在凌晨定时程序执行统计，不会垮天去纠错，因此屏蔽该段逻辑
                                        // if (businnesIncome == null) {
                                        // BusinessIncomeStatisticsEntity businessIncomeStatisticsEntity =
                                        // CommonEntity.businessIncomeStatisticsEntitys(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),BigDecimal.ZERO.subtract(blanace),companyId,new
                                        // Date(),NumberConstant.SECOND.toString(),NumberConstant.FIRST.toString(),canteenId);
                                        // businessIncomeStatisticsService.save(businessIncomeStatisticsEntity);
                                        // } else {
                                        // businessIncomeStatisticsService.update(Wrappers.<BusinessIncomeStatisticsEntity>update().lambda().
                                        // set(BusinessIncomeStatisticsEntity::getFreeConsumBalance,businnesIncome.getFreeConsumBalance().subtract(blanace)).
                                        // eq(BusinessIncomeStatisticsEntity::getIsDel, 0).
                                        // eq(BusinessIncomeStatisticsEntity::getId, businnesIncome.getId()));
                                        // }
                                        MealsIncomeStatisticsEntity mealsIncomeStatisticsEntity = mealsIncomeStatisticsService
                                                .getMealsIncomeData(find.getOutletsId(), find.getWindowGroupId(),
                                                        find.getWindowId(), find.getCompanyId().toString(), date,
                                                        canteenId);
                                        if (mealsIncomeStatisticsEntity == null) {
                                            MealsIncomeStatisticsEntity mealsIncomeStatisticsEntity1 = CommonEntity
                                                    .mealsIncomeStatisticsEntitys2(find.getOutletsId(),
                                                            find.getWindowGroupId(), find.getWindowId(),
                                                            find.getDinnerType(), BigDecimal.ZERO.subtract(blanace),
                                                            find.getCompanyId(), new Date(), "2", canteenId);
                                            mealsIncomeStatisticsService.save(mealsIncomeStatisticsEntity1);
                                        } else {
                                            if (find.getDinnerType().equals(NumberConstant.ZERO.toString())) {
                                                BigDecimal brekkerBalance = mealsIncomeStatisticsEntity
                                                        .getBrekkerBalance().subtract(blanace);
                                                mealsIncomeStatisticsService
                                                        .update(Wrappers.<MealsIncomeStatisticsEntity>update().lambda()
                                                                .set(MealsIncomeStatisticsEntity::getBrekkerBalance,
                                                                        brekkerBalance)
                                                                .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                                .eq(MealsIncomeStatisticsEntity::getId,
                                                                        mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.FIRST.toString())) {
                                                BigDecimal lunchBalance = mealsIncomeStatisticsEntity.getLunchBalance()
                                                        .subtract(blanace);
                                                mealsIncomeStatisticsService.update(Wrappers
                                                        .<MealsIncomeStatisticsEntity>update().lambda()
                                                        .set(MealsIncomeStatisticsEntity::getLunchBalance, lunchBalance)
                                                        .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                        .eq(MealsIncomeStatisticsEntity::getId,
                                                                mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.SECOND.toString())) {
                                                BigDecimal supperBalance = mealsIncomeStatisticsEntity
                                                        .getSupperBalance().subtract(blanace);
                                                mealsIncomeStatisticsService
                                                        .update(Wrappers.<MealsIncomeStatisticsEntity>update().lambda()
                                                                .set(MealsIncomeStatisticsEntity::getSupperBalance,
                                                                        supperBalance)
                                                                .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                                .eq(MealsIncomeStatisticsEntity::getId,
                                                                        mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.THIRD.toString())) {
                                                BigDecimal nightBalance = mealsIncomeStatisticsEntity.getNightBalance()
                                                        .subtract(blanace);
                                                mealsIncomeStatisticsService.update(Wrappers
                                                        .<MealsIncomeStatisticsEntity>update().lambda()
                                                        .set(MealsIncomeStatisticsEntity::getNightBalance, nightBalance)
                                                        .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                        .eq(MealsIncomeStatisticsEntity::getId,
                                                                mealsIncomeStatisticsEntity.getId()));
                                            }
                                            if (find.getDinnerType().equals(NumberConstant.FREEFIR)) {
                                                BigDecimal otherBalance = mealsIncomeStatisticsEntity.getOtherBalance()
                                                        .subtract(blanace);
                                                mealsIncomeStatisticsService.update(Wrappers
                                                        .<MealsIncomeStatisticsEntity>update().lambda()
                                                        .set(MealsIncomeStatisticsEntity::getOtherBalance, otherBalance)
                                                        .eq(MealsIncomeStatisticsEntity::getIsDel, 0)
                                                        .eq(MealsIncomeStatisticsEntity::getId,
                                                                mealsIncomeStatisticsEntity.getId()));
                                            }
                                        }
                                        // // 2024-01-04 窗口明细统计报表因为统计此逻辑会导致与消费明细报表不一致,因此要在窗口明细统计报表加上扣减去的部分
                                        // WindowConsumStatisticsEntity windowConsumStatistics1 =
                                        // windowConsumStatisticsService.getWindowIncomeData(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId().toString(),simDate.format(new
                                        // Date()),"1",canteenId);
                                        // if (windowConsumStatistics1 == null ){
                                        // WindowConsumStatisticsEntity windowConsumStatisticsEntity1 =
                                        // CommonEntity.windowConsumStatisticsEntitys(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId(),NumberConstant.FIRST.toString(),NumberConstant.SECOND.toString(),blanace,new
                                        // Date(),NumberConstant.FIRST.toString(),canteenId);
                                        // windowConsumStatisticsService.save(windowConsumStatisticsEntity1);
                                        // }else {
                                        // BigDecimal freeConsumBalance =
                                        // windowConsumStatistics1.getFreeConsumBalance().add(blanace);
                                        // windowConsumStatisticsService.update(Wrappers.<WindowConsumStatisticsEntity>update().lambda().set(WindowConsumStatisticsEntity::getFreeConsumBalance,freeConsumBalance).
                                        // eq(WindowConsumStatisticsEntity::getIsDel,0).
                                        // eq(WindowConsumStatisticsEntity::getId,windowConsumStatistics1.getId()));
                                        // }
                                        // WindowConsumStatisticsEntity windowConsumStatisticsEntity =
                                        // windowConsumStatisticsService.getWindowIncomeData(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId().toString(),simDate.format(new
                                        // Date()),"4",canteenId);
                                        // if (windowConsumStatisticsEntity == null ){
                                        // WindowConsumStatisticsEntity windowConsumStatisticsEntity1 =
                                        // CommonEntity.windowConsumStatisticsEntitys(find.getOutletsId(),find.getWindowGroupId(),find.getWindowId(),find.getCompanyId(),NumberConstant.FOUR.toString(),NumberConstant.SECOND.toString(),BigDecimal.ZERO.subtract(blanace),new
                                        // Date(),NumberConstant.FIRST.toString(),canteenId);
                                        // windowConsumStatisticsService.save(windowConsumStatisticsEntity1);
                                        // }else {
                                        // BigDecimal freeConsumBalance =
                                        // windowConsumStatisticsEntity.getFreeConsumBalance().subtract(blanace);
                                        // windowConsumStatisticsService.update(Wrappers.<WindowConsumStatisticsEntity>update().lambda().set(WindowConsumStatisticsEntity::getFreeConsumBalance,freeConsumBalance).
                                        // eq(WindowConsumStatisticsEntity::getIsDel,0).
                                        // eq(WindowConsumStatisticsEntity::getId,windowConsumStatisticsEntity.getId()));
                                        // }
                                        // 修改消费订单为已纠错
                                        consumDetailsService.update(Wrappers.<ConsumDetailsEntity>update().lambda()
                                                .set(ConsumDetailsEntity::getActualConsumBalance,
                                                        errorCorrectionDetails.getPaidAmount())
                                                .set(ConsumDetailsEntity::getIsErrorCorrection,
                                                        NumberConstant.FIRST.toString())
                                                .eq(ConsumDetailsEntity::getIsDel, 0)
                                                .eq(ConsumDetailsEntity::getId, find.getId()));
                                    }
                                }
                            }
                            return Action.CommitMessage;
                        }
                    }
                }
            }

            // 监听全部删除该窗口下的人员
            else if (message.getTag().equals("DEL_DEVICE_PER_TAG")) {
                if (obj.get("action").toString().equals("delete_all")) {
                    String str = (String) obj.get("window_id");
                    String[] deviceIds = str.split(",");
                    List<String> dList = Arrays.asList(deviceIds);
                    if (relationPersonnelDeviceService.update(Wrappers.<RelationPersonnelDeviceEntity>update().lambda()
                            .set(RelationPersonnelDeviceEntity::getIsDel, 1)
                            .set(RelationPersonnelDeviceEntity::getStatus, 0)
                            .eq(RelationPersonnelDeviceEntity::getDeptId, $.toLong((String) obj.get("school_id")))
                            .eq(RelationPersonnelDeviceEntity::getIsDel, 0)
                            .in(RelationPersonnelDeviceEntity::getWindowId, dList))) {
                        return Action.CommitMessage;
                    }
                }
            }

            // 监听同步接收一卡通部门的数据
            else if (message.getTag().equals("SYNCHRON_CARD_DEPT_TAG")) {
                List<CardDeptVO> deptList = new Gson().fromJson(obj.getString("list"),
                        new TypeToken<List<CardDeptVO>>() {
                        }.getType());
                if (deptList.size() > 0) {
                    List<SystemDeptSettingEntity> dList = deptList.stream().map(dept -> {
                        SystemDeptSettingEntity systemDeptSettingEntity = new SystemDeptSettingEntity();
                        if (dept.getAction().equals("insert") || dept.getAction().equals("update")) {
                            systemDeptSettingEntity.setId(dept.getId());
                            systemDeptSettingEntity.setDeptId(dept.getSchoolId().toString());
                            systemDeptSettingEntity.setDeptName(dept.getDeptName());
                            systemDeptSettingEntity.setSort(0);
                            systemDeptSettingEntity.setParentId(dept.getPid());
                            systemDeptSettingEntity.setCreateTime(new Date());
                            systemDeptSettingEntity.setIsDel(0);
                            systemDeptSettingEntity.setDeptCategory(1);
                        } else {
                            systemDeptSettingEntity.setId(dept.getId());
                            systemDeptSettingEntity.setDeptId(dept.getSchoolId().toString());
                            systemDeptSettingEntity.setDeptName(dept.getDeptName());
                            systemDeptSettingEntity.setSort(0);
                            systemDeptSettingEntity.setParentId(dept.getPid());
                            systemDeptSettingEntity.setCreateTime(new Date());
                            systemDeptSettingEntity.setDeptCategory(1);
                            systemDeptSettingEntity.setIsDel(1);
                            systemDeptSettingEntity.setUpdateTime(new Date());
                        }
                        return systemDeptSettingEntity;
                    }).collect(Collectors.toList());
                    systemDeptSettingService.saveOrUpdateBatch(dList);
                }
                return Action.CommitMessage;
            }

            // 监听同步接收一卡通用户的数据
            else if (message.getTag().equals("SYNCHRON_CARD_USER_TAG")) {
                List<CardUserVO> userList = new Gson().fromJson(obj.getString("list"),
                        new TypeToken<List<CardUserVO>>() {
                        }.getType());
                if (userList.size() > 0) {
                    Long companyId = userList.get(0).getSchoolId();
                    List<SystemPersonnelEntity> systemPersonnelEntityList = systemPersonnelService
                            .list(Wrappers.<SystemPersonnelEntity>query().lambda()
                                    .eq(SystemPersonnelEntity::getIsDel, 0).eq(SystemPersonnelEntity::getStatus, "1")
                                    .eq(SystemPersonnelEntity::getCompanyId, companyId));
                    Map<Long, SystemPersonnelEntity> sMap = new HashMap<Long, SystemPersonnelEntity>();
                    if (systemPersonnelEntityList.size() > 0) {
                        sMap = systemPersonnelEntityList.stream()
                                .collect(Collectors.toMap(SystemPersonnelEntity::getId, Function.identity()));
                    }
                    final int maxThreadNums = 1;
                    ExecutorService executorService = Executors.newFixedThreadPool(maxThreadNums);
                    Map<Long, SystemPersonnelEntity> finalSMap = sMap;
                    executorService.execute(new Runnable() {
                        @SneakyThrows
                        @Override
                        public void run() { // 解析
                            // 定义线程数
                            int threadCount = 10;
                            /*
                             * int pageSize = new BigDecimal(rCnt).divide(new
                             * BigDecimal(threadCount)).setScale(0, BigDecimal.ROUND_UP).intValue();
                             */
                            // 初始化任务列表
                            List<Callable<List<SystemPersonnelEntity>>> tasks = new ArrayList<>();
                            // 定义线程池 10个
                            ExecutorService exec = Executors.newFixedThreadPool(threadCount);
                            // 每批插入数目
                            int batchCount = 1000;
                            int batchLastIndex = batchCount;
                            List<List<CardUserVO>> shareList = new ArrayList<>();
                            for (int index = 0; index < userList.size();) {
                                if (batchLastIndex >= userList.size()) {
                                    batchLastIndex = userList.size();
                                    shareList.add(userList.subList(index, batchLastIndex));
                                    break;
                                } else {
                                    shareList.add(userList.subList(index, batchLastIndex));
                                    // 设置下一批下标
                                    index = batchLastIndex;
                                    batchLastIndex = index + (batchCount - 1);
                                }
                            }
                            for (List<CardUserVO> item : shareList) {
                                // 初始化线程
                                BatchCardSynchronThread batchCardSynchronThread = new BatchCardSynchronThread();
                                batchCardSynchronThread.setPersonnelList(item);
                                batchCardSynchronThread.setCompanyId(companyId);
                                batchCardSynchronThread.setSMap(finalSMap);
                                batchCardSynchronThread.setENCODER(ENCODER);
                                // 初始化回调
                                Callable<List<SystemPersonnelEntity>> listCallable = batchCardSynchronThread;
                                // 加入任务
                                tasks.add(listCallable);
                            }
                            // Future用于获取结果
                            List<Future<List<SystemPersonnelEntity>>> futures = exec.invokeAll(tasks);
                            // 处理线程返回结果
                            if (futures != null && futures.size() > 0) {
                                for (Future<List<SystemPersonnelEntity>> future : futures) {
                                    systemPersonnelService.saveOrUpdateBatch(future.get());
                                }
                            }
                            // 关闭线程池
                            exec.shutdown();
                        }
                    });
                    executorService.shutdown();
                }
                return Action.CommitMessage;
            }
            // 监听同步接收一卡通用餐类别的数据
            else if (message.getTag().equals("SYNCHRON_CARD_MEALS_TAG")) {
                List<CardDiningTypeVO> userList = new Gson().fromJson(obj.getString("list"),
                        new TypeToken<List<CardDiningTypeVO>>() {
                        }.getType());
                if (userList.size() > 0) {
                    for (CardDiningTypeVO diningType : userList) {
                        List<BusinessOutletsEntity> businessOutletsEntityList = businessOutletsService
                                .list(Wrappers.<BusinessOutletsEntity>query().lambda()
                                        .eq(BusinessOutletsEntity::getIsDel, 0)
                                        .eq(BusinessOutletsEntity::getDeptId, diningType.getSchoolId())
                                        .orderByDesc(BusinessOutletsEntity::getCreateTime))
                                .stream().limit(1).collect(Collectors.toList());
                        if (diningType.getAction().equals("insert")) {
                            DiningTypeEntity diningTypeEntity = new DiningTypeEntity();
                            diningTypeEntity.setId(diningType.getId());
                            diningTypeEntity.setName(diningType.getCategoryName());
                            diningTypeEntity.setBusinessOutletsId(businessOutletsEntityList.get(0).getId());
                            diningTypeEntity.setIsStart(NumberConstant.FIRST.toString());
                            diningTypeEntity.setDeptId(diningType.getSchoolId());
                            diningTypeEntity.setCreateTime(new Date());
                            diningTypeEntity.setCreateDept(diningType.getSchoolId());
                            // 往用餐类别表中写入一卡通的信息
                            if (diningTypeService.save(diningTypeEntity)) {
                                // 查询该学校是否配置了充值配置的功能
                                PayConfigureEntity one = payConfigureService
                                        .getOne(Wrappers.<PayConfigureEntity>lambdaQuery()
                                                .eq(PayConfigureEntity::getCompanyId, diningType.getSchoolId())
                                                .eq(PayConfigureEntity::getIsDel, 0).select(PayConfigureEntity::getId));
                                if (one != null) {
                                    // 如果已经配置了，获取是否已经有过关联了
                                    PayMoneyConfigureEntity payMoneyConfigureEntity = payMoneyConfigureService
                                            .getOne(Wrappers.<PayMoneyConfigureEntity>query().lambda()
                                                    .eq(PayMoneyConfigureEntity::getIsDel, 0)
                                                    .eq(PayMoneyConfigureEntity::getPayConfigId, one.getId())
                                                    .eq(PayMoneyConfigureEntity::getDiningType, diningType.getId()));
                                    // 如果有则添加就好
                                    if (payMoneyConfigureEntity != null) {
                                        payMoneyConfigureService.update(Wrappers.<PayMoneyConfigureEntity>update()
                                                .lambda()
                                                .set(PayMoneyConfigureEntity::getDiningName,
                                                        diningType.getCategoryName())
                                                .eq(PayMoneyConfigureEntity::getIsDel, 0)
                                                .eq(PayMoneyConfigureEntity::getPayConfigId, one.getId())
                                                .eq(PayMoneyConfigureEntity::getDiningType, diningType.getId()));
                                        // 没有就添加一条数据
                                    } else {
                                        PayMoneyConfigureEntity payMoneyConfigureEntity1 = new PayMoneyConfigureEntity();
                                        payMoneyConfigureEntity1.setType(NumberConstant.THIRD.toString());
                                        payMoneyConfigureEntity1.setPayConfigId(one.getId());
                                        payMoneyConfigureEntity1.setDiningType(diningType.getId().toString());
                                        payMoneyConfigureEntity1.setCreateTime(new Date());
                                        payMoneyConfigureEntity1.setCreateDept(diningType.getSchoolId());
                                        payMoneyConfigureEntity1.setDiningName(diningType.getCategoryName());
                                        payMoneyConfigureService.save(payMoneyConfigureEntity1);
                                    }
                                    // 如果没有配置就默认添加一条关联
                                } else {
                                    PayConfigureEntity payConfigure = new PayConfigureEntity();
                                    Date now = DateUtil.now();
                                    payConfigure.setCompanyId(diningType.getSchoolId());
                                    payConfigure.setCreateTime(now);
                                    payConfigure.setOptionalPayDateSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setUnifyPayDateSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setOptionalPayInputSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setUnifyPayInputSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setOptionalPayMoneySwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setUnifyPayMoneySwitch(NumberConstant.THIRD.toString());
                                    payConfigure.setUnifyPlanPaySwitch(NumberConstant.ZERO.toString());
                                    if (payConfigureService.save(payConfigure)) {
                                        PayMoneyConfigureEntity payMoneyConfigureEntity = new PayMoneyConfigureEntity();
                                        payMoneyConfigureEntity.setType(NumberConstant.THIRD.toString());
                                        payMoneyConfigureEntity.setPayConfigId(payConfigure.getId());
                                        payMoneyConfigureEntity.setDiningType(diningType.getId().toString());
                                        payMoneyConfigureEntity.setCreateTime(new Date());
                                        payMoneyConfigureEntity.setCreateDept(diningType.getSchoolId());
                                        payMoneyConfigureEntity.setDiningName(diningType.getCategoryName());
                                        payMoneyConfigureService.save(payMoneyConfigureEntity);
                                    }
                                }
                            }
                            // 以上是同步修改的数据进行相应的修改
                        } else if (diningType.getAction().equals("update")) {
                            DiningTypeEntity diningTypeEntity = new DiningTypeEntity();
                            diningTypeEntity.setId(diningType.getId());
                            diningTypeEntity.setName(diningType.getCategoryName());
                            diningTypeEntity.setUpdateTime(new Date());
                            if (diningTypeService.saveOrUpdate(diningTypeEntity)) {
                                PayConfigureEntity one = payConfigureService
                                        .getOne(Wrappers.<PayConfigureEntity>lambdaQuery()
                                                .eq(PayConfigureEntity::getCompanyId, diningType.getSchoolId())
                                                .eq(PayConfigureEntity::getIsDel, 0).select(PayConfigureEntity::getId));
                                if (one != null) {
                                    PayMoneyConfigureEntity payMoneyConfigureEntity = payMoneyConfigureService
                                            .getOne(Wrappers.<PayMoneyConfigureEntity>query().lambda()
                                                    .eq(PayMoneyConfigureEntity::getIsDel, 0)
                                                    .eq(PayMoneyConfigureEntity::getPayConfigId, one.getId())
                                                    .eq(PayMoneyConfigureEntity::getDiningType, diningType.getId()));
                                    if (payMoneyConfigureEntity != null) {
                                        payMoneyConfigureService.update(Wrappers.<PayMoneyConfigureEntity>update()
                                                .lambda()
                                                .set(PayMoneyConfigureEntity::getDiningName,
                                                        diningType.getCategoryName())
                                                .eq(PayMoneyConfigureEntity::getIsDel, 0)
                                                .eq(PayMoneyConfigureEntity::getPayConfigId, one.getId())
                                                .eq(PayMoneyConfigureEntity::getDiningType, diningType.getId()));
                                    } else {
                                        PayMoneyConfigureEntity payMoneyConfigureEntity1 = new PayMoneyConfigureEntity();
                                        payMoneyConfigureEntity1.setType(NumberConstant.THIRD.toString());
                                        payMoneyConfigureEntity1.setPayConfigId(one.getId());
                                        payMoneyConfigureEntity1.setDiningType(diningType.getId().toString());
                                        payMoneyConfigureEntity1.setCreateTime(new Date());
                                        payMoneyConfigureEntity1.setCreateDept(diningType.getSchoolId());
                                        payMoneyConfigureEntity1.setDiningName(diningType.getCategoryName());
                                        payMoneyConfigureService.save(payMoneyConfigureEntity1);
                                    }
                                } else {
                                    PayConfigureEntity payConfigure = new PayConfigureEntity();
                                    Date now = DateUtil.now();
                                    payConfigure.setCompanyId(diningType.getSchoolId());
                                    payConfigure.setCreateTime(now);
                                    payConfigure.setOptionalPayDateSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setUnifyPayDateSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setOptionalPayInputSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setUnifyPayInputSwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setOptionalPayMoneySwitch(NumberConstant.ZERO.toString());
                                    payConfigure.setUnifyPayMoneySwitch(NumberConstant.THIRD.toString());
                                    payConfigure.setUnifyPlanPaySwitch(NumberConstant.ZERO.toString());
                                    if (payConfigureService.save(payConfigure)) {
                                        PayMoneyConfigureEntity payMoneyConfigureEntity = new PayMoneyConfigureEntity();
                                        payMoneyConfigureEntity.setType(NumberConstant.THIRD.toString());
                                        payMoneyConfigureEntity.setPayConfigId(payConfigure.getId());
                                        payMoneyConfigureEntity.setDiningType(diningType.getId().toString());
                                        payMoneyConfigureEntity.setCreateTime(new Date());
                                        payMoneyConfigureEntity.setCreateDept(diningType.getSchoolId());
                                        payMoneyConfigureEntity.setDiningName(diningType.getCategoryName());
                                        payMoneyConfigureService.save(payMoneyConfigureEntity);
                                    }
                                }
                            }
                        } else {
                            DiningTypeEntity diningTypeEntity = diningTypeService.getById(diningType.getId());
                            if (diningTypeEntity != null) {
                                Boolean remove = diningTypeService.remove(
                                        Wrappers.<DiningTypeEntity>query().lambda().eq(DiningTypeEntity::getIsDel, 0)
                                                .eq(DiningTypeEntity::getId, diningTypeEntity.getId()));
                                if (remove) {
                                    PayConfigureEntity one = payConfigureService.getOne(Wrappers
                                            .<PayConfigureEntity>lambdaQuery()
                                            .eq(PayConfigureEntity::getCompanyId, diningType.getSchoolId())
                                            .eq(PayConfigureEntity::getIsDel, 0).select(PayConfigureEntity::getId));
                                    if (one != null) {
                                        payMoneyConfigureService.remove(Wrappers.<PayMoneyConfigureEntity>query()
                                                .lambda().eq(PayMoneyConfigureEntity::getIsDel, 0)
                                                .eq(PayMoneyConfigureEntity::getPayConfigId, one.getId())
                                                .eq(PayMoneyConfigureEntity::getDiningType, diningType.getId()));
                                    }
                                }
                            }
                        }
                    }
                }
                return Action.CommitMessage;
            }

            // 监听同步接收一卡通充值金额消费状态
            else if (message.getTag().equals("SYNCHRON_CARD_STATUS_TAG")) {
                List<FoodRechargeDetailEntity> foodRechargeDetailEntityList = foodRechargeDetailService.list(
                        Wrappers.<FoodRechargeDetailEntity>query().lambda().eq(FoodRechargeDetailEntity::getIsDel, 0)
                                .eq(FoodRechargeDetailEntity::getId, obj.get("order_id"))
                                .eq(FoodRechargeDetailEntity::getCompanyId, obj.get("school_id")));
                // 充值数据是一边同步一边接收回调的数据的，以下这么多个查询主要是为了预防还没有写入就充值信息，同步就先回调了
                if (foodRechargeDetailEntityList.size() == 0) {
                    Thread.sleep(2000);
                    foodRechargeDetailEntityList = foodRechargeDetailService.list(Wrappers
                            .<FoodRechargeDetailEntity>query().lambda().eq(FoodRechargeDetailEntity::getIsDel, 0)
                            .eq(FoodRechargeDetailEntity::getId, obj.get("order_id"))
                            .eq(FoodRechargeDetailEntity::getCompanyId, obj.get("school_id")));
                    if (foodRechargeDetailEntityList.size() == 0) {
                        Thread.sleep(3000);
                        foodRechargeDetailEntityList = foodRechargeDetailService.list(Wrappers
                                .<FoodRechargeDetailEntity>query().lambda().eq(FoodRechargeDetailEntity::getIsDel, 0)
                                .eq(FoodRechargeDetailEntity::getId, obj.get("order_id"))
                                .eq(FoodRechargeDetailEntity::getCompanyId, obj.get("school_id")));
                        if (foodRechargeDetailEntityList.size() == 0) {
                            Thread.sleep(5000);
                            foodRechargeDetailEntityList = foodRechargeDetailService
                                    .list(Wrappers.<FoodRechargeDetailEntity>query().lambda()
                                            .eq(FoodRechargeDetailEntity::getIsDel, 0)
                                            .eq(FoodRechargeDetailEntity::getId, obj.get("order_id"))
                                            .eq(FoodRechargeDetailEntity::getCompanyId, obj.get("school_id")));
                        }
                    }
                }
                if (foodRechargeDetailEntityList.size() > 0) {
                    Long orderId = SnowflakeIdUtil.nextId();
                    ConsumDetailsEntity consumDetailsEntity = new ConsumDetailsEntity();
                    consumDetailsEntity.setUserId(foodRechargeDetailEntityList.get(0).getRechargeUser());
                    consumDetailsEntity.setCompanyId(foodRechargeDetailEntityList.get(0).getCompanyId());
                    consumDetailsEntity.setConsumBillNo(orderId);
                    consumDetailsEntity.setConsumTime(DateUtil.now());
                    consumDetailsEntity.setConsumType(foodRechargeDetailEntityList.get(0).getMealsType());
                    consumDetailsEntity.setOutletsId(foodRechargeDetailEntityList.get(0).getBusinessOutletsId());
                    consumDetailsEntity.setMealsId(null);
                    consumDetailsEntity
                            .setConsumOperationUser(foodRechargeDetailEntityList.get(0).getRechargeOperationUser());
                    consumDetailsEntity.setOperatorName(foodRechargeDetailEntityList.get(0).getRechargeOperationName());
                    consumDetailsEntity
                            .setPlaceOrderName(foodRechargeDetailEntityList.get(0).getRechargeOperationName());
                    consumDetailsEntity.setStayUserFlag(foodRechargeDetailEntityList.get(0).getUserFlag());
                    consumDetailsEntity
                            .setOperatorIdentity(foodRechargeDetailEntityList.get(0).getRechargeOperationType());
                    consumDetailsEntity.setPayMethod(NumberConstant.FIRST.toString());
                    consumDetailsEntity.setDinnerStatus(NumberConstant.SECOND.toString());
                    consumDetailsEntity.setMealsType(foodRechargeDetailEntityList.get(0).getMealsType());
                    consumDetailsEntity.setIsPatching(NumberConstant.ZERO.toString());
                    consumDetailsEntity.setDinnerType(NumberConstant.FIRST.toString());
                    consumDetailsEntity.setOrderType(foodRechargeDetailEntityList.get(0).getMealsType());
                    consumDetailsEntity.setConsumPrices(foodRechargeDetailEntityList.get(0).getFoodRechargeBalance());
                    consumDetailsEntity
                            .setActualConsumBalance(foodRechargeDetailEntityList.get(0).getFoodRechargeBalance());
                    consumDetailsEntity.setOrderId(foodRechargeDetailEntityList.get(0).getOrderId());
                    consumDetailsEntity.setCreateTime(DateUtil.now());
                    consumDetailsEntity.setCreateUser(foodRechargeDetailEntityList.get(0).getRechargeUser());
                    consumDetailsEntity.setPaySuccessTime(DateUtil.now());
                    consumDetailsEntity.setStatus(OrderConstant.payStatus.PAID.toString());
                    consumDetailsEntity.setCurrentBalance(foodRechargeDetailEntityList.get(0).getFoodRechargeBalance());
                    // 充值成功写入到消费明细中
                    if (consumDetailsService.save(consumDetailsEntity)) {
                        // // 2023-04-18 支付成功后生成凭证
                        // createVoucherController.saveVoucherForConsumDetails(consumDetailsEntity);

                        List<SystemPersonnelEntity> systemPersonnelEntityList = systemPersonnelService
                                .list(Wrappers.<SystemPersonnelEntity>query().lambda()
                                        .eq(SystemPersonnelEntity::getIsDel, 0).eq(SystemPersonnelEntity::getId,
                                                foodRechargeDetailEntityList.get(0).getRechargeUser()));
                        if ($.isNotEmpty(systemPersonnelEntityList)) {
                            if (foodRechargeDetailEntityList.get(0).getMealsType()
                                    .equals(NumberConstant.ZERO.toString())) {
                                systemPersonnelEntityList.get(0)
                                        .setBalance(systemPersonnelEntityList.get(0).getBalance().subtract(
                                                foodRechargeDetailEntityList.get(0).getFoodRechargeBalance()));
                            } else {
                                systemPersonnelEntityList.get(0).setOptionalBalance(
                                        systemPersonnelEntityList.get(0).getOptionalBalance().subtract(
                                                foodRechargeDetailEntityList.get(0).getFoodRechargeBalance()));
                            }
                            systemPersonnelEntityList.get(0).setUpdateTime(DateUtil.now());
                            systemPersonnelEntityList.get(0).setInitialStatus(NumberConstant.SECOND.toString());
                            systemPersonnelEntityList.get(0).setRemarks("一卡通充值方法充值");
                            // 修改用户金额
                            Boolean flag = systemPersonnelService.updateById(systemPersonnelEntityList.get(0));
                            if (flag) {
                                // 修改充值金额的状态是否成功，是否需要重新发送
                                mealsOrderBalanceRecordService.update(Wrappers.<MealsOrderBalanceRecordEntity>update()
                                        .lambda()
                                        .set(MealsOrderBalanceRecordEntity::getStatus, NumberConstant.SECOND.toString())
                                        .eq(MealsOrderBalanceRecordEntity::getIsDel, 0)
                                        .eq(MealsOrderBalanceRecordEntity::getOrderId,
                                                foodRechargeDetailEntityList.get(0).getOrderId()));
                            }
                        }
                    }
                }
                return Action.CommitMessage;
            }

            // 充值金额到设备回调是否成功并且修改充值订单为成功或者失败
            else if (message.getTag().equals("RECHARGE_BALANCE_CALLBACK_TAG")) {
                String schoolId = (String) obj.get("school_id");
                String orderId = (String) obj.get("order_id");
                if (foodRechargeDetailService.update(Wrappers.<FoodRechargeDetailEntity>update().lambda()
                        .set(FoodRechargeDetailEntity::getRechargeStatus, NumberConstant.FIRST.toString())
                        .eq(FoodRechargeDetailEntity::getCompanyId, schoolId)
                        .eq(FoodRechargeDetailEntity::getId, orderId))) {
                    return Action.CommitMessage;
                } else {
                    return Action.CommitMessage;
                }
            }

            // 监听同步云校人员信息 （该段逻辑已废弃，后续如果再启用MQ同步云校人员可用上，记得删除解释）
            else if (message.getTag().equals("YUNXIAO_PERSONNEL_TAG")) {
                String homeType = obj.getString("homeType");
                String json = obj.getString("context");
                // 监听初始化消费机信息
                if (homeType.equals("HOME_3001") || homeType.equals("HOME_3002")) {
                    /*
                     * add,update 调用的是/rabbit-personnel/systemPersonnel/saveOrUpdate
                     * addBath 调用的是/rabbit-personnel/systemPersonnel/batchSaveOrUpdate
                     * delete 调用的是 /rabbit-personnel/systemPersonnel/batchDelete
                     */
                    if (obj.getString("operationType").equals("add")
                            || obj.getString("operationType").equals("update")) {
                        saveOrUpdate(json);
                    } else if (obj.getString("operationType").equals("addBath")) {
                        batchSaveOrUpdate(json);
                    } else if (obj.getString("operationType").equals("delete")) {
                        batchDelete(json);
                    }
                    return Action.CommitMessage;
                } else {
                    return Action.ReconsumeLater;
                }
            }
        } catch (Exception e) {
            System.out.println("消费MQ消息失败,tag消息id:" + message.getMsgID() + "异常错误:" + e.getMessage());
            e.printStackTrace();
            return Action.ReconsumeLater;
        }
        return Action.ReconsumeLater;
    }

    private R saveOrUpdate(String json) {
        try {
            System.out.println("获取云校同步到廉餐数据：" + json);
            String type = null;
            LinkedList<String> list = new LinkedList<>();
            JSONObject obj = JSONObject.parseObject(json);
            JSONObject jsonObject = new JSONObject();
            if (obj.get("type").equals(OrderConstant.userFlag.STUDENT.toString())) {
                SrnStudentVO student = new Gson().fromJson(json, new TypeToken<SrnStudentVO>() {
                }.getType());
                List<SystemPersonnelEntity> personnel = systemPersonnelService
                        .list(Wrappers.<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                                .eq(SystemPersonnelEntity::getId, student.getId()));
                List<TopicMessageEntity> topicMessageEntityList = topicNessageService
                        .list(Wrappers.<TopicMessageEntity>query().lambda().eq(TopicMessageEntity::getIsDel, 0)
                                .eq(TopicMessageEntity::getDeptId, student.getLiancanSchoolId()));
                SystemPersonnelEntity entity = new SystemPersonnelEntity();
                if (personnel.size() == 0) {
                    entity.setId(student.getId());
                    entity.setUserName(student.getUserName());
                    if ($.isNotEmpty(student.getSex())) {
                        if (student.getSex().equals(NumberConstant.ZERO.toString())) {
                            entity.setSex(NumberConstant.FIRST.toString());
                        } else if (student.getSex().equals(NumberConstant.FIRST.toString())) {
                            entity.setSex(NumberConstant.SECOND.toString());
                        }
                    }
                    if ($.isNotEmpty(student.getPassword())) {
                        entity.setPassword(student.getPassword());
                    } else {
                        entity.setPassword(DigestUtil.encrypt("123456"));
                    }
                    jsonObject.put("cardStatus", NumberConstant.ZERO.toString());
                    entity.setStudentJobNo(student.getStudentJobNo());
                    entity.setMobile(student.getMobile());
                    entity.setAvatar(student.getAvatar());
                    /* entity.setCardStatus(NumberConstant.ZERO.toString()); */
                    entity.setNation(student.getNation());
                    entity.setCloudSchoolId(student.getCloudSchoolId());
                    entity.setCompanyId(Long.parseLong(student.getLiancanSchoolId()));
                    entity.setBoardOpenStatus(NumberConstant.FIRST.toString());
                    entity.setStatus(NumberConstant.FIRST.toString());
                    entity.setCreateTime(DateUtil.now());
                    entity.setDeptId(student.getDeptId());
                    if ($.isNotEmpty(student.getGradeId())) {
                        entity.setGradeId(String.valueOf(student.getGradeId()));
                    }
                    entity.setNameLetter(student.getNameLetter());
                    entity.setPersonnelType(NumberConstant.FIRST.toString());
                    entity.setInitialStatus(NumberConstant.FIRST.toString());
                    entity.setCategoryStatus(NumberConstant.FIRST.toString());
                    if (systemPersonnelService.save(entity)) {
                        type = "insert";
                    }
                } else {
                    if ($.isNotEmpty(student.getDeptId())) {
                        if (!student.getDeptId().equals(personnel.get(0).getDeptId())) {
                            /*
                             * 2023-05-19 财务转出食堂账务处理\转入食堂账务处理 需求:
                             * 1.判断学生教师，统缴餐、自选餐是否存在金额
                             * 1.通过部门查找对应的食堂，校验是否为统一食堂
                             * 2.如果食堂不一样，获取该学生统缴餐、自选餐金额添加到表，定时程序次日生成对应凭证
                             */
                            if (personnel.get(0).getBalance().compareTo(new BigDecimal("0")) == 1
                                    || personnel.get(0).getOptionalBalance().compareTo(new BigDecimal("0")) == 1) {
                                List<CanteenBindingDeptEntity> newList1 = canteenBindingDeptService
                                        .list(Wrappers.<CanteenBindingDeptEntity>query().lambda()
                                                .eq(CanteenBindingDeptEntity::getDeptId, student.getDeptId()));
                                List<CanteenBindingDeptEntity> oldList2 = canteenBindingDeptService
                                        .list(Wrappers.<CanteenBindingDeptEntity>query().lambda()
                                                .eq(CanteenBindingDeptEntity::getDeptId, personnel.get(0).getDeptId()));
                                if (newList1.size() > 0 && oldList2.size() > 0) {
                                    if (!newList1.get(0).getCanteenId().equals(oldList2.get(0).getCanteenId())) {
                                        FinancialOutCanteenRecord outCanteenRecord = new FinancialOutCanteenRecord();
                                        outCanteenRecord.setUserId(personnel.get(0).getId());
                                        outCanteenRecord.setOutCanteenId(oldList2.get(0).getCanteenId());// 转出的食堂
                                        outCanteenRecord.setInCanteenId(newList1.get(0).getCanteenId());// 转入的食堂
                                        outCanteenRecord.setBalance(personnel.get(0).getBalance());// 统缴餐金额
                                        outCanteenRecord.setOptionalBalance(personnel.get(0).getOptionalBalance());// 自选餐金额
                                        outCanteenRecord.setOperationTime(new Date());
                                        outCanteenRecord.setPersonnelType(personnel.get(0).getPersonnelType());
                                        outCanteenRecordService.save(outCanteenRecord);
                                    }
                                }
                            }
                        }
                    }
                    personnel.get(0).setId(student.getId());
                    personnel.get(0).setUserName(student.getUserName());
                    if ($.isNotEmpty(student.getSex())) {
                        if (student.getSex().equals(NumberConstant.ZERO.toString())) {
                            personnel.get(0).setSex(NumberConstant.FIRST.toString());
                        } else if (student.getSex().equals(NumberConstant.FIRST.toString())) {
                            personnel.get(0).setSex(NumberConstant.SECOND.toString());
                        }
                    }
                    if ($.isNotEmpty(student.getNameLetter())) {
                        personnel.get(0).setNameLetter(student.getNameLetter());
                    }
                    jsonObject.put("account", personnel.get(0).getAccount());
                    jsonObject.put("cardStatus", personnel.get(0).getCardStatus());
                    jsonObject.put("card_no", personnel.get(0).getCardId());
                    jsonObject.put("balance", personnel.get(0).getBalance());
                    jsonObject.put("optional_balance", personnel.get(0).getOptionalBalance());
                    jsonObject.put("optional_meal_balance", personnel.get(0).getConsumQuota());
                    personnel.get(0).setStudentJobNo(student.getStudentJobNo());
                    personnel.get(0).setMobile(student.getMobile());
                    personnel.get(0).setAvatar(student.getAvatar());
                    if ($.isEmpty(personnel.get(0).getPassword())) {
                        if ($.isNotEmpty(student.getPassword())) {
                            personnel.get(0).setPassword(student.getPassword());
                        } else {
                            personnel.get(0).setPassword(DigestUtil.encrypt("123456"));
                        }
                    }
                    personnel.get(0).setNation(student.getNation());
                    personnel.get(0).setCloudSchoolId(student.getCloudSchoolId());
                    personnel.get(0).setCompanyId(Long.parseLong(student.getLiancanSchoolId()));
                    personnel.get(0).setBoardOpenStatus(NumberConstant.FIRST.toString());
                    personnel.get(0).setUpdateTime(new Date());
                    if ($.isNotEmpty(student.getDeptId())) {
                        String[] str = student.getDeptId().split(",");
                        if (str.length == 1) {
                            personnel.get(0).setDeptId(student.getDeptId());
                        }
                    }
                    if ($.isNotEmpty(student.getGradeId())) {
                        personnel.get(0).setGradeId(String.valueOf(student.getGradeId()));
                    }
                    personnel.get(0).setPersonnelType(NumberConstant.FIRST.toString());
                    personnel.get(0).setRemarks("修改用户信息");
                    if (personnel.get(0).getMealsType().toString().equals(NumberConstant.ZERO.toString())) {
                        personnel.get(0).setCategoryStatus(NumberConstant.FIRST.toString());
                    }
                    personnel.get(0).setStatus(NumberConstant.FIRST.toString());// 2023-09-20
                                                                                // 能编辑代表是在校学生,避免云校毕业生复读后，廉餐未同步更新
                    if (systemPersonnelService.updateById(personnel.get(0))) {
                        type = "update";
                    }
                }
                jsonObject.put("user_id", student.getId());
                jsonObject.put("school_id", student.getLiancanSchoolId());
                jsonObject.put("user_name", student.getUserName());
                jsonObject.put("id_number", student.getStudentJobNo());
                jsonObject.put("photo", student.getAvatar());
                jsonObject.put("sex", student.getSex());
                jsonObject.put("user_type", "student");
                jsonObject.put("dept_id", student.getDeptId());
                jsonObject.put("nation", student.getNation());
                jsonObject.put("is_need_upload", NumberConstant.FIRST.toString());
                jsonObject.put("window_id", null);
                if (entity != null) {
                    jsonObject.put("action", type);
                }
                list.add(jsonObject.toString());
                if (topicMessageEntityList.size() > 0) {
                    for (TopicMessageEntity topic : topicMessageEntityList) {
                        Message msg = new Message(topic.getTopicName(), "BATCH_USER_TAG",
                                list.toString().getBytes("UTF-8"));
                        rocketMQProducer.sendPush(msg, topic, student.getUserName(), "同步消息成功,用户");
                    }
                }
                // 清除查询使用的人员缓存
                systemPersonnelService.delRedisDictCache(Long.valueOf(student.getLiancanSchoolId()));
                return R.status(Boolean.TRUE);
            } else {
                StuTeaPersonnelVO user = new Gson().fromJson(json, new TypeToken<StuTeaPersonnelVO>() {
                }.getType());
                List<SystemPersonnelEntity> personnel = systemPersonnelService
                        .list(Wrappers.<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                                .eq(SystemPersonnelEntity::getId, user.getId()));
                List<TopicMessageEntity> topicMessageEntityList = topicNessageService
                        .list(Wrappers.<TopicMessageEntity>query().lambda().eq(TopicMessageEntity::getIsDel, 0)
                                .eq(TopicMessageEntity::getDeptId, user.getLiancanSchoolId()));
                SystemPersonnelEntity entity = new SystemPersonnelEntity();
                OperationLogEntity operationLogEntity = new OperationLogEntity();
                if (personnel.size() == 0) {
                    entity.setId(user.getId());
                    entity.setUserName(user.getUserName());
                    if ($.isNotEmpty(user.getSex())) {
                        if (user.getSex().equals(NumberConstant.ZERO.toString())) {
                            entity.setSex(NumberConstant.FIRST.toString());
                        } else if (user.getSex().equals(NumberConstant.FIRST.toString())) {
                            entity.setSex(NumberConstant.SECOND.toString());
                        }
                    }
                    if ($.isNotEmpty(user.getPassword())) {
                        entity.setPassword(user.getPassword());
                    }
                    jsonObject.put("cardStatus", NumberConstant.ZERO.toString());
                    entity.setNameLetter(user.getNameLetter());
                    entity.setStudentJobNo(user.getStudentJobNo());
                    entity.setMobile(user.getMobile());
                    entity.setAvatar(user.getAvatar());
                    entity.setNation(user.getNation());
                    entity.setCloudSchoolId(user.getCloudSchoolId());
                    entity.setCompanyId($.toLong(user.getLiancanSchoolId()));
                    entity.setBoardOpenStatus(NumberConstant.FIRST.toString());
                    entity.setStatus(NumberConstant.FIRST.toString());
                    entity.setCreateTime(new Date());
                    String deptId = String.join(",", user.getDeptIds());
                    entity.setDeptId(deptId);
                    entity.setPersonnelType(NumberConstant.SECOND.toString());
                    entity.setInitialStatus(NumberConstant.FIRST.toString());
                    entity.setCategoryStatus(NumberConstant.FIRST.toString());
                    if (systemPersonnelService.save(entity)) {
                        type = "insert";
                    }
                } else {
                    personnel.get(0).setId(user.getId());
                    personnel.get(0).setUserName(user.getUserName());
                    if ($.isNotEmpty(user.getSex())) {
                        if (user.getSex().equals(NumberConstant.ZERO.toString())) {
                            personnel.get(0).setSex(NumberConstant.FIRST.toString());
                        } else if (user.getSex().equals(NumberConstant.FIRST.toString())) {
                            personnel.get(0).setSex(NumberConstant.SECOND.toString());
                        }
                    }
                    if ($.isEmpty(personnel.get(0).getPassword())) {
                        if ($.isNotEmpty(user.getPassword())) {
                            personnel.get(0).setPassword(user.getPassword());
                        }
                    }
                    if ($.isEmpty(personnel.get(0).getNameLetter())) {
                        personnel.get(0).setNameLetter(user.getNameLetter());
                    }
                    jsonObject.put("account", personnel.get(0).getAccount());
                    jsonObject.put("cardStatus", personnel.get(0).getCardStatus());
                    jsonObject.put("card_no", personnel.get(0).getCardId());
                    jsonObject.put("balance", personnel.get(0).getBalance());
                    jsonObject.put("optional_balance", personnel.get(0).getOptionalBalance());
                    jsonObject.put("optional_meal_balance", personnel.get(0).getConsumQuota());
                    personnel.get(0).setStudentJobNo(user.getStudentJobNo());
                    personnel.get(0).setMobile(user.getMobile());
                    personnel.get(0).setAvatar(user.getAvatar());
                    personnel.get(0).setNation(user.getNation());
                    personnel.get(0).setCloudSchoolId(user.getCloudSchoolId());
                    personnel.get(0).setCompanyId(Long.parseLong(user.getLiancanSchoolId()));
                    personnel.get(0).setBoardOpenStatus(NumberConstant.FIRST.toString());
                    personnel.get(0).setUpdateTime(new Date());
                    if ($.isNotEmpty(user.getDeptId())) {
                        String deptId = String.join(",", user.getDeptId());
                        String[] str = deptId.split(",");
                        if (str.length == 1) {
                            personnel.get(0).setDeptId(deptId);
                        }
                    }
                    personnel.get(0).setPersonnelType(NumberConstant.SECOND.toString());
                    personnel.get(0).setRemarks("修改用户信息");
                    if (personnel.get(0).getMealsType().toString().equals(NumberConstant.ZERO.toString())) {
                        personnel.get(0).setCategoryStatus(NumberConstant.FIRST.toString());
                    }
                    if (systemPersonnelService.updateById(personnel.get(0))) {
                        type = "update";
                    }
                }
                jsonObject.put("user_id", user.getId());
                jsonObject.put("school_id", user.getLiancanSchoolId());
                jsonObject.put("user_name", user.getUserName());
                jsonObject.put("id_number", user.getStudentJobNo());
                jsonObject.put("photo", user.getAvatar());
                jsonObject.put("sex", user.getSex());
                jsonObject.put("user_type", "teacher");
                jsonObject.put("dept_id", user.getDeptId());
                jsonObject.put("nation", user.getNation());
                jsonObject.put("is_need_upload", NumberConstant.FIRST.toString());
                jsonObject.put("window_id", null);
                if (entity != null) {
                    jsonObject.put("action", type);
                }
                list.add(jsonObject.toString());
                if (topicMessageEntityList.size() > 0) {
                    for (TopicMessageEntity topic : topicMessageEntityList) {
                        Message msg = new Message(topic.getTopicName(), "BATCH_USER_TAG",
                                list.toString().getBytes("UTF-8"));
                        rocketMQProducer.sendPush(msg, topic, user.getUserName(), "同步消息成功,用户");
                    }
                }
                return R.status(Boolean.TRUE);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.status(Boolean.FALSE);
    }

    private R batchSaveOrUpdate(String json) {
        System.out.println("获取云校同步到廉餐数据,批量处理：" + json);
        List<SrnStudentVO> paramsList = JSONArray.parseArray(json, SrnStudentVO.class);
        SrnStudentVO srnStudentVO = paramsList.get(0);
        // 获取孩子或老师信息
        if (CollectionUtils.isEmpty(paramsList)) {
            return R.status(Boolean.FALSE);
        }
        List<Long> userIds = paramsList.stream().map(SrnStudentVO::getId).collect(Collectors.toList());
        List<SystemPersonnelEntity> personnel = systemPersonnelService
                .list(Wrappers.<SystemPersonnelEntity>query().lambda()
                        .eq(SystemPersonnelEntity::getIsDel, 0).in(SystemPersonnelEntity::getId, userIds));
        Map<Long, SystemPersonnelEntity> personnelMap = personnel.stream()
                .collect(Collectors.toMap(BaseEntity::getId, entity -> entity));
        List<SystemPersonnelEntity> waitSaveSystemPersonnelEntityList = new ArrayList<>();
        List<SystemPersonnelEntity> waitUpdateSystemPersonnelEntityList = new ArrayList<>();
        if (srnStudentVO.getType().equals(OrderConstant.userFlag.STUDENT.toString())) {
            for (SrnStudentVO studentVO : paramsList) {
                // 2023-09-08 云校发送过来的数据会有ID值，没有的话是异常数据(但暂未找到原因为什么会传空值)
                if (studentVO.getId() == null) {
                    continue;
                }
                SystemPersonnelEntity dbPersonnel = personnelMap.get(studentVO.getId());
                if (null == dbPersonnel) {
                    waitSaveSystemPersonnelEntityList.add(personnelByStudentService.setEntityBySave(studentVO,
                            OrderConstant.userFlag.STUDENT.toString()));
                } else {
                    dbPersonnel = personnelByStudentService.setEntityByUpdate(studentVO, dbPersonnel,
                            OrderConstant.userFlag.STUDENT.toString());
                    waitUpdateSystemPersonnelEntityList.add(dbPersonnel);
                }
            }
            // 清除查询使用的人员缓存
            systemPersonnelService.delRedisDictCache(Long.valueOf(srnStudentVO.getLiancanSchoolId()));
        } else {
            for (SrnStudentVO teacherVO : paramsList) {
                // 2023-09-08 云校发送过来的数据会有ID值，没有的话是异常数据(但暂未找到原因为什么会传空值)
                if (teacherVO.getId() == null) {
                    continue;
                }
                SystemPersonnelEntity dbPersonnel = personnelMap.get(teacherVO.getId());
                if (null == dbPersonnel) {
                    waitSaveSystemPersonnelEntityList.add(personnelByStudentService.setEntityBySave(teacherVO,
                            OrderConstant.userFlag.TEACHER.toString()));
                } else {
                    dbPersonnel = personnelByStudentService.setEntityByUpdate(teacherVO, dbPersonnel,
                            OrderConstant.userFlag.TEACHER.toString());
                    waitUpdateSystemPersonnelEntityList.add(dbPersonnel);
                }
            }
        }
        if (CollectionUtils.isNotEmpty(waitSaveSystemPersonnelEntityList)) {
            systemPersonnelService.saveBatch(waitSaveSystemPersonnelEntityList);
        }
        if (CollectionUtils.isNotEmpty(waitUpdateSystemPersonnelEntityList)) {
            systemPersonnelService.updateBatchById(waitUpdateSystemPersonnelEntityList);
        }
        return R.status(Boolean.TRUE);
    }

    private R batchDelete(String json) {
        System.out.print("返回值:" + json);
        JSONObject obj = JSONObject.parseObject(json);
        System.out.print("解析的值:" + obj.get("ids") + "----" + obj.get("liancanSchoolId"));
        String[] str = obj.get("ids").toString().split(",");
        List<TopicMessageEntity> topicMessageEntityList = topicNessageService
                .list(Wrappers.<TopicMessageEntity>query().lambda().eq(TopicMessageEntity::getIsDel, 0)
                        .eq(TopicMessageEntity::getDeptId, obj.get("liancanSchoolId")));
        /*
         * if (topicMessageEntityList.size() == 0 ){
         * return R.fail("没有配置消息发送的主题");
         * }
         */
        String operatorName = (String) obj.get("operatorName");
        List<SystemDeptSettingEntity> dsList = systemDeptSettingService
                .list(Wrappers.<SystemDeptSettingEntity>query().lambda().eq(SystemDeptSettingEntity::getIsDel, 0)
                        .eq(SystemDeptSettingEntity::getDeptId, obj.get("liancanSchoolId")));
        Map<Long, SystemDeptSettingEntity> dsMap = new HashMap<Long, SystemDeptSettingEntity>();
        if (dsList.size() > 0) {
            dsMap = dsList.stream().collect(Collectors.toMap(SystemDeptSettingEntity::getId, Function.identity()));
        }
        String ids = "";
        Integer count = 0;
        for (String item : str) {
            List<SystemPersonnelEntity> systemPersonnelEntityList = systemPersonnelService
                    .list(Wrappers.<SystemPersonnelEntity>query().lambda().eq(SystemPersonnelEntity::getIsDel, 0)
                            .eq(SystemPersonnelEntity::getStatus, NumberConstant.FIRST.toString())
                            .eq(SystemPersonnelEntity::getId, item));
            if ($.isNotEmpty(systemPersonnelEntityList) && systemPersonnelEntityList.size() > 0) {
                systemPersonnelEntityList.get(0).setStatus(NumberConstant.SECOND.toString());
                systemPersonnelEntityList.get(0).setLogoutTime(new Date());
                systemPersonnelEntityList.get(0).setUpdateTime(new Date());
                systemPersonnelEntityList.get(0).setUpdateUser(systemPersonnelEntityList.get(0).getId());
                systemPersonnelEntityList.get(0).setLogoutUser(systemPersonnelEntityList.get(0).getId());
                systemPersonnelEntityList.get(0).setRemarks("注销");
                if (systemPersonnelService.updateById(systemPersonnelEntityList.get(0))) {
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("user_id", systemPersonnelEntityList.get(0).getId());
                    jsonObject.put("school_id", systemPersonnelEntityList.get(0).getCompanyId());
                    jsonObject.put("user_name", systemPersonnelEntityList.get(0).getUserName());
                    jsonObject.put("id_number", systemPersonnelEntityList.get(0).getStudentJobNo());
                    jsonObject.put("photo", systemPersonnelEntityList.get(0).getAvatar());
                    jsonObject.put("sex", systemPersonnelEntityList.get(0).getSex());
                    jsonObject.put("dept_id", systemPersonnelEntityList.get(0).getDeptId());
                    jsonObject.put("card_no", systemPersonnelEntityList.get(0).getCardId());
                    if (systemPersonnelEntityList.get(0).getPersonnelType().equals(NumberConstant.FIRST.toString())) {
                        SystemDeptSettingEntity dept = dsMap
                                .get(Long.parseLong(systemPersonnelEntityList.get(0).getDeptId()));
                        if (dept != null) {
                            jsonObject.put("status", dept.getStatus());
                        }
                    }
                    jsonObject.put("person_category_id", systemPersonnelEntityList.get(0).getMealsType());
                    jsonObject.put("person_label_ids", systemPersonnelEntityList.get(0).getAttribute());
                    jsonObject.put("is_need_upload", NumberConstant.FIRST.toString());
                    jsonObject.put("machine_id", null);
                    if ($.isNotEmpty(systemPersonnelEntityList) && systemPersonnelEntityList.size() > 0) {
                        jsonObject.put("action", "delete");
                    }
                    for (TopicMessageEntity topic : topicMessageEntityList) {
                        Message msg = null;
                        try {
                            msg = new Message(topic.getTopicName(), "USER_TAG",
                                    jsonObject.toString().getBytes("UTF-8"));
                        } catch (UnsupportedEncodingException e) {
                            e.printStackTrace();
                        }
                        rocketMQProducer.sendAsyncPush(msg, topic.getGroupName());
                    }
                }
                ids += ",";
                count++;
            }
        }
        if ($.isNotBlank(ids)) {
            ids = ids.substring(0, ids.length() - 1);
        }
        LiancanCancellationPersonnelRecordEntity record = new LiancanCancellationPersonnelRecordEntity();
        record.setPersonnelIds(ids);
        record.setCancellationCount(count);
        record.setWay("2");
        liancan2CancellationPersonnelRecordService.save(record);
        return R.status(Boolean.TRUE);
    }

}
