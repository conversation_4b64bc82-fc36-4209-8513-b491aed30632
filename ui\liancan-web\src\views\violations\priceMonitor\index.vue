<template>
  <basic-container>
    <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
      <el-tab-pane label="价格查询" name="first">
        <avue-crud :option="option"
                    :table-loading="loading"
                    :data="data"
                    :page="page"
                    :permission="permissionList"
                    :before-open="beforeOpen"
                    v-model="form"
                    ref="form"
                    @search-change="searchChange"
                    @search-reset="searchReset"
                    @selection-change="selectionChange"
                    @current-change="currentChange"
                    @size-change="sizeChange"
                    @on-load="onLoad">
          <template slot-scope="{row,type,size}" slot="menu">
            <el-button icon="el-icon-search" :size="size" :type="type" @click="openFoodDiaglog(row,'1')">查看</el-button>
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane label="价格查询(按食堂)" name="second">
        <avue-crud :option="option"
                    :table-loading="loading"
                    :data="data"
                    :page="page"
                    :permission="permissionList"
                    :before-open="beforeOpen"
                    v-model="form"
                    ref="form"
                    @search-change="searchChange"
                    @search-reset="searchReset"
                    @selection-change="selectionChange"
                    @current-change="currentChange"
                    @size-change="sizeChange"
                    @on-load="onLoad">
          <template slot-scope="{row,type,size}" slot="menu">
            <el-button icon="el-icon-search" :size="size" :type="type" @click="openFoodDiaglog(row,'1')">查看</el-button>
          </template>
        </avue-crud>
      </el-tab-pane>

      <el-tab-pane label="价格预警提示" name="third">
        <avue-crud :option="warningOption"
                 :table-loading="warningLoading"
                 :data="warningData"
                 :page="warningPage"
                 :permission="permissionList"
                 v-model="warningForm"
                 ref="warningForm"
                 @search-change="searchChangeWarning"
                 @search-reset="searchResetWarning"
                 @selection-change="selectionChangeWarning"
                 @current-change="currentChangeWarning"
                 @size-change="sizeChangeWarning"
                 @on-load="onLoadWarning">
          <template slot="menu" slot-scope="{row}">
            <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="openWarning(row,'1')">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="handOpenWarningPush(row)">去函询
            </el-button>
            <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="openWarning(row,'2')">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="checkReply(row)">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="management(row)">处置
            </el-button>
            <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="checkNanagement(row)">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="fileOpen(row)">归档
            </el-button>
            <el-button v-if="row.inquiryStatus == '5'" size="mini" type="text" @click="checkFile(row)">查看
            </el-button>
            <!--<el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="handOpenInquiry(row)">查看函询
            </el-button>
            <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="handOpenInquiry(row)">查看函询
            </el-button>-->
          </template>
        </avue-crud>
      </el-tab-pane>
    </el-tabs>

    <el-dialog title="价格查询(按食材)" :visible.sync="foodVisible" width="80%" left :append-to-body="true" @close="closeFoodVisible">
      <avue-crud :option="option1"
                  :table-loading="loading1"
                  :data="data1"
                  :page="page1"
                  :permission="permissionList"
                  :before-open="beforeOpen1"
                  v-model="form1"
                  ref="form1"
                  @search-change="searchChange1"
                  @search-reset="searchReset1"
                  @current-change="currentChange1"
                  @size-change="sizeChange1"
                  @on-load="onLoad1">
        <template slot-scope="{row,type,size}" slot="menu">
          <el-button icon="el-icon-search" :size="size" :type="type" @click="openFoodPriceDiaglog(row,'1')">查看</el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <el-dialog title="食材价格趋势图" :visible.sync="foodPriceVisible" width="80%" left :append-to-body="true" @close="closeFoodPriceVisible">
      <div id="myChart" :style="{ width: '1000px', height: '200px' }"></div>
      <avue-crud :option="option2"
               :table-loading="loading2"
               :data="data2"
               :page="page2"
               v-model="form2"
               ref="crud"
               @search-change="searchChange2"
               @search-reset="searchReset2"
               @selection-change="selectionChange2"
               @current-change="currentChange2"
               @size-change="sizeChange2"
               @on-load="onLoad2">
        <template slot="menu" slot-scope="scope">
          <el-button type="text" size="small" icon="el-icon-view" @click="opentView(scope.row)">查看
          </el-button>
        </template>
      </avue-crud>
      <!-- <template slot-scope="{type,size}" slot="menu">
        <el-button icon="el-icon-search" :size="size" :type="type" @click="openFoodPriceDiaglog(row,'1')">查看</el-button>
      </template> -->
    </el-dialog>

    <el-dialog title="查看" :visible.sync="viewDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <!-- <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div> -->
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">商品名称：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">价格：</span>{{this.totalPrices}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">计量单位：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">入库数量：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">入库小计：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">采购单号：</span>{{this.viewId}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">供应商：</span>{{this.supplierName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">采购人：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">采购时间：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">进仓登记人：</span>{{this.accepterUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">验收进仓时间：</span>{{this.accepterTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">确认状态：</span>{{this.accepterStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">进仓确认人：</span>{{this.accepterUserNameb}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">确认时间：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">确认备注：</span></div>
        </el-col>
      </el-row>
    </el-dialog>

    <el-dialog title="查看" :visible.sync="warningDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div>
        <el-col :span="24">
          <div sstyle="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">预警日期：</span>{{this.warningDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">当前状态：</span>{{this.inquiryStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">办理单位：</span>{{this.handlingUnit}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规单位：</span>{{this.schoolName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">单位类型：</span>{{this.unitType}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规分类：</span>{{this.category}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规内容：</span>{{this.content}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规取证内容：</span>{{this.evidence}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType == '2'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">已发函询</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询日期：</span>{{this.inquiryDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询单位：</span>{{this.inquiryDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询人姓名：</span>{{this.pushInquerUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询内容：</span><a href="javascript:void(0);" style="color: #1e9fff" @click="openMakeInquiry">点此查看</a></div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType3 == '3'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">食堂回复</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复日期：</span>{{this.replyTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复单位：</span>{{this.replyDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复人姓名：</span>{{this.replyName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复意见：</span>{{this.replyContent}}</div>
        </el-col>
        <el-col :span="24">
          <div>图片:</div>
          <span v-for="(item,index) in this.fileList">
        <img :src="item.url" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item.url)" class="avatar">
        </span>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType4 == '4'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">处置信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置日期：</span>{{this.handleDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置单位：</span>{{this.handleDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置人姓名：</span>{{this.handleUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置意见：</span>{{this.handleContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType5 == '5'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">归档信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档日期：</span>{{this.fileDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档单位：</span>{{this.fileDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档人姓名：</span>{{this.fileUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档意见：</span>{{this.fileContent}}</div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :title="函询" :visible.sync="warningRecordVisible1"
               :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <div style="margin-top: -20px">
        <hr />
      </div>
      <inquiryDetailVue :query="pageParams"></inquiryDetailVue>
      <div style="margin-top: 65px;text-align: center;">
        <el-button @click="pushInquiry(pageParams)" type="primary">发送</el-button>
        <el-button @click=" warningRecordVisible1 = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="`处置`" :visible.sync="managementVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="managementForm" :option="managementOption" v-model="managementForm" @submit="saveManage">
      </avue-form>
    </el-dialog>

  </basic-container>
</template>

<script>
import {getList} from "@/api/violations/priceMonitor";
// import {getList, getDetail, add, update, remove, passOrNo, openAccount} from "@/api/finance/subject";
import {mapGetters} from "vuex";
import {getViolationWarningList,getWarningById,saveInquiry,getInquiryById,saveManage,saveFile} from "@/api/liancan/illegalWarnLog";
import inquiryDetailVue from "@/views/liancan2/home/<USER>/inquiryDetail";
import {getAccepterComfirmPage,orderDetailList,getAccepterLog,getAccepterLogging} from "@/api/liancan/order";

const echarts = require('echarts/lib/echarts');
// 引入折线图
require('echarts/lib/chart/line');

// 引入提示框和标题组件
require('echarts/lib/component/tooltip');
require('echarts/lib/component/title');
require('echarts/theme/macarons')

var DIC = {
  isBuy: [{
      label: '未确认',
      value: "0"
  },{
      label: '确认通过',
      value: "1"
  },{
      label: '确认拒绝',
      value: "2"
  }],
  payStatus: [{
      label: '未付款',
      value: "0"
  },{
      label: '已付款',
      value: "1"
  }],
  accepterStatus: [{
      label: ' ',
      value: "0"
  },{
      label: '未确认',
      value: "1"
  },{
      label: '验收通过',
      value: "2"
  },{
      label: '验收不通过',
      value: "3"
  }]
}
export default {
  data() {
    return {
      form: {},
      form1: {},
      query: {},
      query1: {},
      detailRow: {},
      loading: true,
      loading1: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      page1: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      form2: {},
      query2: {},
      loading2: true,
      page2: {
          pageSize: 10,
          currentPage: 1,
          total: 0
      },
      activeName: 'first',
      selectionList: [],
      checkVisible: false,
      detail: {},
      option: {
        height:'auto',
        calcHeight: 60,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "月份",
            prop: "monthStr",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            // 调试用：使用本地数据
            dicData: [
              { label: "2024-01", value: "2024-01" },
              { label: "2024-02", value: "2024-02" },
              { label: "2024-03", value: "2024-03" },
              { label: "2024-04", value: "2024-04" },
              { label: "2024-05", value: "2024-05" },
              { label: "2024-06", value: "2024-06" },
              { label: "2024-07", value: "2024-07" },
              { label: "2024-08", value: "2024-08" },
              { label: "2024-09", value: "2024-09" },
              { label: "2024-10", value: "2024-10" },
              { label: "2024-11", value: "2024-11" },
              { label: "2024-12", value: "2024-12" }
            ]
          },
          // {
          //   label: "地区",
          //   formatter: (row,value,label,column) => {
          //     return label = ''
          //   }
          // },
          {
            label: '省份',
            prop: 'province',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            // 调试用：使用本地数据替代API
            // dicUrl: `/api/rabbit-system/region/getProvince`,
            dicData: [
              { regionName: "北京市", id: "110000" },
              { regionName: "天津市", id: "120000" },
              { regionName: "河北省", id: "130000" },
              { regionName: "山西省", id: "140000" },
              { regionName: "内蒙古自治区", id: "150000" },
              { regionName: "辽宁省", id: "210000" },
              { regionName: "吉林省", id: "220000" },
              { regionName: "黑龙江省", id: "230000" },
              { regionName: "上海市", id: "310000" },
              { regionName: "江苏省", id: "320000" },
              { regionName: "浙江省", id: "330000" },
              { regionName: "安徽省", id: "340000" },
              { regionName: "福建省", id: "350000" },
              { regionName: "江西省", id: "360000" },
              { regionName: "山东省", id: "370000" }
            ],
            cascaderItem: ['city', 'area'],
            cascaderChange: true,
            rules: [
              {
                required: true,
                message: '请选择省份',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // hide: true
          },
          {
            label: '城市',
            prop: 'city',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            // 调试用：使用本地数据替代API
            // dicFlag: false,
            // dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
            dicData: [
              { regionName: "北京市", id: "110100" },
              { regionName: "天津市", id: "120100" },
              { regionName: "石家庄市", id: "130100" },
              { regionName: "太原市", id: "140100" },
              { regionName: "呼和浩特市", id: "150100" },
              { regionName: "沈阳市", id: "210100" },
              { regionName: "长春市", id: "220100" },
              { regionName: "哈尔滨市", id: "230100" },
              { regionName: "上海市", id: "310100" },
              { regionName: "南京市", id: "320100" },
              { regionName: "杭州市", id: "330100" },
              { regionName: "合肥市", id: "340100" },
              { regionName: "福州市", id: "350100" },
              { regionName: "南昌市", id: "360100" },
              { regionName: "济南市", id: "370100" }
            ],
            // 调试用：添加formatter来显示中文名称
            formatter: (row, value, label, column) => {
              const cityMap = {
                "110100": "北京市",
                "120100": "天津市",
                "130100": "石家庄市",
                "140100": "太原市",
                "150100": "呼和浩特市",
                "210100": "沈阳市",
                "220100": "长春市",
                "230100": "哈尔滨市",
                "310100": "上海市",
                "320100": "南京市",
                "330100": "杭州市",
                "340100": "合肥市",
                "350100": "福州市",
                "360100": "南昌市",
                "370100": "济南市"
              };
              return cityMap[value] || value;
            },
            cascaderItem: ['area'],
            cascaderChange: true,
            rules: [
              {
                required: true,
                message: '请选择城市',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // hide: true
          },
          {
            label: '地区',
            prop: 'area',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            // 调试用：使用本地数据替代API
            // dicFlag: false,
            // dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
            dicData: [
              { regionName: "东城区", id: "110101" },
              { regionName: "西城区", id: "110102" },
              { regionName: "朝阳区", id: "110105" },
              { regionName: "丰台区", id: "110106" },
              { regionName: "石景山区", id: "110107" },
              { regionName: "海淀区", id: "110108" },
              { regionName: "门头沟区", id: "110109" },
              { regionName: "房山区", id: "110111" },
              { regionName: "通州区", id: "110112" },
              { regionName: "顺义区", id: "110113" },
              { regionName: "昌平区", id: "110114" },
              { regionName: "大兴区", id: "110115" },
              { regionName: "怀柔区", id: "110116" },
              { regionName: "平谷区", id: "110117" },
              { regionName: "密云区", id: "110118" },
              { regionName: "延庆区", id: "110119" }
            ],
            // 调试用：添加formatter来显示中文名称
            formatter: (row, value, label, column) => {
              const areaMap = {
                // 北京市
                "110101": "东城区",
                "110102": "西城区",
                "110105": "朝阳区",
                "110106": "丰台区",
                "110107": "石景山区",
                "110108": "海淀区",
                "110109": "门头沟区",
                "110111": "房山区",
                "110112": "通州区",
                "110113": "顺义区",
                "110114": "昌平区",
                "110115": "大兴区",
                "110116": "怀柔区",
                "110117": "平谷区",
                "110118": "密云区",
                "110119": "延庆区",
                // 天津市
                "120101": "和平区",
                "120102": "河东区",
                "120103": "河西区",
                "120104": "南开区",
                "120105": "河北区",
                "120106": "红桥区",
                "120110": "东丽区",
                "120111": "西青区",
                "120112": "津南区",
                "120113": "北辰区",
                "120114": "武清区",
                "120115": "宝坻区",
                "120116": "滨海新区",
                "120117": "宁河区",
                "120118": "静海区",
                "120119": "蓟州区",
                // 河北省石家庄市
                "130101": "市辖区",
                "130102": "长安区",
                "130104": "桥西区",
                "130105": "新华区",
                "130107": "井陉矿区",
                "130108": "裕华区",
                "130109": "藁城区",
                "130110": "鹿泉区",
                "130111": "栾城区",
                // 山西省太原市
                "140101": "市辖区",
                "140105": "小店区",
                "140106": "迎泽区",
                "140107": "杏花岭区",
                "140108": "尖草坪区",
                "140109": "万柏林区",
                "140110": "晋源区",
                // 内蒙古呼和浩特市
                "150101": "市辖区",
                "150102": "新城区",
                "150103": "回民区",
                "150104": "玉泉区",
                "150105": "赛罕区",
                "150121": "土默特左旗",
                "150122": "托克托县",
                // 辽宁省沈阳市
                "210101": "市辖区",
                "210102": "和平区",
                "210103": "沈河区",
                "210104": "大东区",
                "210105": "皇姑区",
                "210106": "铁西区",
                "210111": "苏家屯区",
                "210112": "浑南区",
                "210113": "沈北新区",
                "210114": "于洪区",
                // 吉林省长春市
                "220101": "市辖区",
                "220102": "南关区",
                "220103": "宽城区",
                "220104": "朝阳区",
                "220105": "二道区",
                "220106": "绿园区",
                "220112": "双阳区",
                "220113": "九台区",
                // 黑龙江省哈尔滨市
                "230101": "市辖区",
                "230102": "道里区",
                "230103": "南岗区",
                "230104": "道外区",
                "230108": "平房区",
                "230109": "松北区",
                "230110": "香坊区",
                "230111": "呼兰区",
                "230112": "阿城区",
                "230113": "双城区",
                // 上海市
                "310101": "黄浦区",
                "310104": "徐汇区",
                "310105": "长宁区",
                "310106": "静安区",
                "310107": "普陀区",
                "310109": "虹口区",
                "310110": "杨浦区",
                "310112": "闵行区",
                "310113": "宝山区",
                "310114": "嘉定区",
                "310115": "浦东新区",
                "310116": "金山区",
                "310117": "松江区",
                "310118": "青浦区",
                "310120": "奉贤区",
                "310151": "崇明区",
                // 江苏省南京市
                "320101": "市辖区",
                "320102": "玄武区",
                "320104": "秦淮区",
                "320105": "建邺区",
                "320106": "鼓楼区",
                "320111": "浦口区",
                "320113": "栖霞区",
                "320114": "雨花台区",
                "320115": "江宁区",
                "320116": "六合区",
                "320117": "溧水区",
                "320118": "高淳区",
                // 浙江省杭州市
                "330101": "市辖区",
                "330102": "上城区",
                "330103": "下城区",
                "330104": "江干区",
                "330105": "拱墅区",
                "330106": "西湖区",
                "330108": "滨江区",
                "330109": "萧山区",
                "330110": "余杭区",
                "330111": "富阳区",
                "330112": "临安区",
                "330113": "临平区",
                "330114": "钱塘区"
              };
              return areaMap[value] || value;
            },
            rules: [
              {
                required: true,
                message: '请选择地区',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // hide: true
          },
          {
            label: "单位名称",
            prop: "deptId",
            type: "tree",
            // 调试用：使用本地数据替代API
            // dicUrl: "/api/service/rabbit-system/dept/dict",
            dicData: [
              {
                deptName: "北京师范大学",
                id: "1001",
                children: [
                  { deptName: "第一食堂", id: "2001" },
                  { deptName: "第二食堂", id: "2002" },
                  { deptName: "第三食堂", id: "2003" }
                ]
              },
              {
                deptName: "清华大学",
                id: "1002",
                children: [
                  { deptName: "紫荆园食堂", id: "2004" },
                  { deptName: "清芬园食堂", id: "2005" },
                  { deptName: "听涛园食堂", id: "2006" }
                ]
              },
              {
                deptName: "北京大学",
                id: "1003",
                children: [
                  { deptName: "学一食堂", id: "2007" },
                  { deptName: "学五食堂", id: "2008" },
                  { deptName: "农园食堂", id: "2009" }
                ]
              },
              {
                deptName: "中国人民大学",
                id: "1004",
                children: [
                  { deptName: "东区食堂", id: "2010" },
                  { deptName: "西区食堂", id: "2011" },
                  { deptName: "北区食堂", id: "2012" }
                ]
              },
              {
                deptName: "北京理工大学",
                id: "1005",
                children: [
                  { deptName: "良乡食堂", id: "2013" },
                  { deptName: "中关村食堂", id: "2014" },
                  { deptName: "西山食堂", id: "2015" }
                ]
              }
            ],
              props: {
                label: "deptName",
                value: "id"
              },
              editDisplay: false,
              viewDisplay: false,
              search:false,
          },
          {
            label: "食堂名称",
            prop: "canteenId",
            type: "select",
            // 调试用：使用本地数据替代API
            // dicUrl: "/api/service/rabbit-system/dept/dict",
            dicData: [
              { deptName: "第一食堂", id: "2001" },
              { deptName: "第二食堂", id: "2002" },
              { deptName: "第三食堂", id: "2003" },
              { deptName: "紫荆园食堂", id: "2004" },
              { deptName: "清芬园食堂", id: "2005" },
              { deptName: "听涛园食堂", id: "2006" },
              { deptName: "学一食堂", id: "2007" },
              { deptName: "学五食堂", id: "2008" },
              { deptName: "农园食堂", id: "2009" },
              { deptName: "东区食堂", id: "2010" },
              { deptName: "西区食堂", id: "2011" },
              { deptName: "北区食堂", id: "2012" },
              { deptName: "良乡食堂", id: "2013" },
              { deptName: "中关村食堂", id: "2014" },
              { deptName: "西山食堂", id: "2015" }
            ],
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false
          },
          // {
          //     label: "食材大类",
          //     prop: "bidding",
          //     type: "select",
          //     dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall",
          //     props: {
          //       label: "name",
          //       value: "id"
          //     },
          //     rules: [{
          //       required: true,
          //       message: "请选择食材大类",
          //       trigger: "blur"
          //     }],
          //     search:true,
          // },
          {
              label: "食材大类",
              prop: "bidding",
              type: "select",
              // 调试用：使用本地数据替代API
              // dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall",
              dicData: [
                { name: "蔬菜类", id: "3001" },
                { name: "肉类", id: "3002" },
                { name: "水产类", id: "3003" },
                { name: "蛋奶类", id: "3004" },
                { name: "粮油类", id: "3005" },
                { name: "调味品类", id: "3006" },
                { name: "干货类", id: "3007" },
                { name: "水果类", id: "3008" }
              ],
                props: {
                    label: "name",
                    value: "id"
                },
              rules: [{
                required: true,
                message: "请选择食材大类",
                trigger: "blur"
              }],
              search:true,
          },
          {
            label: "食材小类",
            prop: "biddingTypeId",
            type: "select",
            // 调试用：使用本地数据替代API
            // dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
            dicData: [
              { name: "白菜", id: "4001" },
              { name: "土豆", id: "4002" },
              { name: "胡萝卜", id: "4003" },
              { name: "西红柿", id: "4004" },
              { name: "黄瓜", id: "4005" },
              { name: "猪肉", id: "4006" },
              { name: "牛肉", id: "4007" },
              { name: "鸡肉", id: "4008" },
              { name: "鱼肉", id: "4009" },
              { name: "虾仁", id: "4010" },
              { name: "鸡蛋", id: "4011" },
              { name: "牛奶", id: "4012" },
              { name: "大米", id: "4013" },
              { name: "面粉", id: "4014" },
              { name: "食用油", id: "4015" },
              { name: "盐", id: "4016" },
              { name: "酱油", id: "4017" },
              { name: "醋", id: "4018" },
              { name: "木耳", id: "4019" },
              { name: "香菇", id: "4020" },
              { name: "苹果", id: "4021" },
              { name: "香蕉", id: "4022" },
              { name: "橙子", id: "4023" },
              { name: "葡萄", id: "4024" }
            ],
            props: {
              label: "name",
              value: "id"
            },
          },
          {
            label: "最低采购单价(元/斤)",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            minWidth: 90,
          },
          {
            label: "最低采购单价与上月相比",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            minWidth: 90,
          },
          {
            label: "最高采购单价(元/斤)",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            minWidth: 90,
          },
          {
            label: "最高采购单价与上月相比",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            minWidth: 90,
          },
          {
            label: "平均采购单价(元/斤)",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            minWidth: 90,
          },
          {
            label: "平均采购单价与上月相比",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            minWidth: 90,
          },
        ]
      },
      option1: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "月份",
            prop: "monthStr",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true
          },
          {
            label: "地区",
            prop: "area",
            formatter: (row, value) => {
              // 临时格式化函数 - 将地区代码转换为中文名称，后续可删除
              const areaMap = {
                "110101": "东城区",
                "110102": "西城区",
                "110105": "朝阳区",
                "110106": "丰台区",
                "110107": "石景山区",
                "110108": "海淀区",
                "110109": "门头沟区",
                "110111": "房山区",
                "110112": "通州区",
                "110113": "顺义区",
                "110114": "昌平区",
                "110115": "大兴区",
                "110116": "怀柔区",
                "110117": "平谷区",
                "110118": "密云区",
                "110119": "延庆区",
                "120101": "和平区",
                "120102": "河东区",
                "120103": "河西区",
                "120104": "南开区",
                "120105": "河北区",
                "120106": "红桥区",
                "120110": "东丽区",
                "120111": "西青区",
                "120112": "津南区",
                "120113": "北辰区",
                "120114": "武清区",
                "120115": "宝坻区",
                "120116": "滨海新区",
                "120117": "宁河区",
                "120118": "静海区",
                "120119": "蓟州区",
                "310101": "黄浦区",
                "310104": "徐汇区",
                "310105": "长宁区",
                "310106": "静安区",
                "310107": "普陀区",
                "310109": "虹口区",
                "310110": "杨浦区",
                "310112": "闵行区",
                "310113": "宝山区",
                "310114": "嘉定区",
                "310115": "浦东新区",
                "310116": "金山区",
                "310117": "松江区",
                "310118": "青浦区",
                "310120": "奉贤区",
                "310151": "崇明区",
                "320101": "南京市",
                "320102": "玄武区",
                "320104": "秦淮区",
                "320105": "建邺区",
                "320106": "鼓楼区",
                "320111": "浦口区",
                "320113": "栖霞区",
                "320114": "雨花台区",
                "320115": "江宁区",
                "320116": "六合区",
                "320117": "溧水区",
                "320118": "高淳区",
                "330101": "杭州市",
                "330102": "上城区",
                "330103": "下城区",
                "330104": "江干区",
                "330105": "拱墅区",
                "330106": "西湖区",
                "330108": "滨江区",
                "330109": "萧山区",
                "330110": "余杭区",
                "330111": "富阳区",
                "330112": "临安区",
                "330113": "临平区",
                "330114": "钱塘区",
                "440101": "广州市",
                "440103": "荔湾区",
                "440104": "越秀区",
                "440105": "海珠区",
                "440106": "天河区",
                "440111": "白云区",
                "440112": "黄埔区",
                "440113": "番禺区",
                "440114": "花都区",
                "440115": "南沙区",
                "440117": "从化区",
                "440118": "增城区",
                "500101": "万州区",
                "500102": "涪陵区",
                "500103": "渝中区",
                "500104": "大渡口区",
                "500105": "江北区",
                "500106": "沙坪坝区",
                "500107": "九龙坡区",
                "500108": "南岸区",
                "500109": "北碚区",
                "500110": "綦江区",
                "500111": "大足区",
                "500112": "渝北区",
                "500113": "巴南区",
                "500114": "黔江区",
                "500115": "长寿区",
                "500116": "江津区",
                "500117": "合川区",
                "500118": "永川区",
                "500119": "南川区",
                "500120": "璧山区",
                "500151": "铜梁区",
                "500152": "潼南区",
                "500153": "荣昌区",
                "500154": "开州区",
                "500155": "梁平区",
                "500156": "武隆区",
                "510101": "成都市",
                "510104": "锦江区",
                "510105": "青羊区",
                "510106": "金牛区",
                "510107": "武侯区",
                "510108": "成华区",
                "510112": "龙泉驿区",
                "510113": "青白江区",
                "510114": "新都区",
                "510115": "温江区",
                "510116": "双流区",
                "510117": "郫都区",
                "510118": "新津区",
                "510121": "金堂县",
                "510129": "大邑县",
                "510131": "蒲江县",
                "510181": "都江堰市",
                "510182": "彭州市",
                "510183": "邛崃市",
                "510184": "崇州市",
                "510185": "简阳市"
              };
              return areaMap[value] || value;
            }
          },
          {
            label: "省份",
            prop: "province",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            search: true,
            // 临时格式化函数 - 将省份代码转换为中文名称，后续可删除
            formatter: (row, value) => {
              const provinceMap = {
                "110000": "北京市",
                "120000": "天津市",
                "310000": "上海市",
                "320000": "江苏省",
                "330000": "浙江省",
                "440000": "广东省",
                "500000": "重庆市",
                "510000": "四川省"
              };
              return provinceMap[value] || value;
            }
          },
          {
            label: "城市",
            prop: "city",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            search: true,
            // 临时格式化函数 - 将城市代码转换为中文名称，后续可删除
            formatter: (row, value) => {
              const cityMap = {
                "110100": "北京市",
                "120100": "天津市",
                "310100": "上海市",
                "320100": "南京市",
                "330100": "杭州市",
                "440100": "广州市",
                "500100": "重庆市",
                "510100": "成都市"
              };
              return cityMap[value] || value;
            }
          },

          {
            label: "单位名称",
            prop: "deptId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/dict",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            // 临时格式化函数 - 将单位ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const deptMap = {
                "1001": "北京市第一中学",
                "1002": "北京市第二中学",
                "1003": "天津市第一中学",
                "1004": "天津市第二中学",
                "1005": "上海市第一中学",
                "1006": "上海市第二中学",
                "1007": "南京市第一中学",
                "1008": "南京市第二中学",
                "1009": "杭州市第一中学",
                "1010": "杭州市第二中学",
                "1011": "广州市第一中学",
                "1012": "广州市第二中学",
                "1013": "重庆市第一中学",
                "1014": "重庆市第二中学",
                "1015": "成都市第一中学"
              };
              return deptMap[value] || value;
            }
          },
          {
            label: "食堂名称",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            // 临时格式化函数 - 将食堂ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const canteenMap = {
                "2001": "第一食堂",
                "2002": "第二食堂",
                "2003": "第三食堂",
                "2004": "第四食堂",
                "2005": "第五食堂",
                "2006": "第六食堂",
                "2007": "第七食堂",
                "2008": "第八食堂",
                "2009": "第九食堂",
                "2010": "第十食堂",
                "2011": "第十一食堂",
                "2012": "第十二食堂",
                "2013": "第十三食堂",
                "2014": "第十四食堂",
                "2015": "第十五食堂"
              };
              return canteenMap[value] || value;
            }
          },
          {
              label: "食材大类",
              prop: "bidding",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall",
              props: {
                label: "name",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请输入采购大类",
                trigger: "blur"
              }],
              search:true,
              // 临时格式化函数 - 将食材大类ID转换为中文名称，后续可删除
              formatter: (row, value) => {
                const biddingMap = {
                  "1": "蔬菜类",
                  "2": "肉类",
                  "3": "水产类",
                  "4": "蛋奶类",
                  "5": "粮油类",
                  "6": "调味品类",
                  "7": "水果类",
                  "8": "豆制品类",
                  "9": "干货类",
                  "10": "饮料类"
                };
                return biddingMap[value] || value;
              },
          },
          {
              label: "食材小类",
              prop: "biddingTypeId",
              type: "select",
              dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall",
                props: {
                    label: "name",
                    value: "id"
                },
              rules: [{
                required: true,
                message: "请选择商品小类",
                trigger: "blur"
              }],
              search:true,
              // 临时格式化函数 - 将食材小类ID转换为中文名称，后续可删除
              formatter: (row, value) => {
                const biddingTypeMap = {
                  "101": "叶菜类",
                  "102": "根茎类",
                  "103": "瓜果类",
                  "104": "茄果类",
                  "105": "豆类",
                  "106": "菌菇类",
                  "107": "葱蒜类",
                  "108": "其他蔬菜"
                };
                return biddingTypeMap[value] || value;
              },
          },
          {
            label: "最低采购单价(元/斤)",
            prop: "minimumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "最低采购单价与上月相比",
            prop: "minimumPurchaseChange",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "最高采购单价(元/斤)",
            prop: "maximumPurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "最高采购单价与上月相比",
            prop: "maximumPurchaseChange",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "平均采购单价(元/斤)",
            prop: "averagePurchase",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "平均采购单价与上月相比",
            prop: "averagePurchaseChange",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
        ]
      },
      managementOption:{
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        labelWidth: 150,
        column: [
          {
            label: "请输入处置意见",
            prop: "content",
            type: "textarea",
            minRows:10,
            maxlength:1000,
            span:24,
            showWordLimit:true,
            placeholder:"请具体说明情况，限1000字。",
            rules: [{
              required: true,
              message: "请输入处置意见",
              trigger: "click"
            }]

          },
        ]
      },
      option2: {
          align: "center",
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: false,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: false,
          column: [
              {
                label: "所属食堂",
                prop: "deptId",
                type: "tree",
                dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
                props: {
                  label: "deptName",
                  value:"id"
                },
                hide: true,
                addDisplay: false,
                search:true,
              },
              {
                label: "所属食堂",
                prop: "deptName",
                type: "input",
              },
              {
                  label: "采购单号",
                  prop: "id",
                  type: "input",
                  display: false,
              },
              {
                  label: "供应商",
                  prop: "supplierName",
                  type: "input",
                  overHidden: true,
                  width: 190,
              },
              {
                  label: "进仓金额",
                  prop: "totalPrices",
                  type: "number",
                  precision:2,
                  mock:{
                      type:'number',
                      max:1,
                      min:2,
                      precision:2
                  },
              },
              {
                  label: "进仓登记人",
                  prop: "accepterUserName",
                  type: "input",
              },
              {
                  label: "验收进仓时间",
                  prop: "accepterTime",
                  type: "datetime",
                  format: "yyyy-MM-dd HH:mm:ss",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  width: 160,
              },
              {
                  label: "进仓确认人",
                  prop: "accepterUserNameb",
                  type: "input",
              },
              {
                  label: "确认状态",
                  prop: "accepterStatus",
                  type: "select",
                  dicData: DIC.accepterStatus,
                  // 临时格式化函数 - 将确认状态代码转换为中文名称，后续可删除
                  formatter: (row, value) => {
                    const statusMap = {
                      "0": "待确认",
                      "1": "已确认",
                      "2": "已拒绝",
                      "3": "待审核",
                      "4": "审核通过",
                      "5": "审核不通过"
                    };
                    return statusMap[value] || value;
                  }
              }
          ]
      },
      data2: [],
      data: [],
      data1: [],
      warningPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      pageParams: {},
      managementForm:{},
      fileList:[],
      foodVisible:false,
      foodPriceVisible:false,
      warningRecordVisible1:false,
      warningVisible:false,
      viewDetailsVisible:false,
      warningDetailsVisible:false,
      managementVisible:false,
      fileVisible:false,
      deptId:undefined,
      adminName:undefined,
      phone:undefined,
      schoolName:undefined,
      replyName:undefined,
      replyTime:undefined,
      replyDeptName:undefined,
      unitType:undefined,
      fileList:undefined,
      pushInquerUserName:undefined,
      replyContent:undefined,
      inquiryName:undefined,
      inquiryDeptName:undefined,
      inquiryDate:undefined,
      evidence:undefined,
      handleUserName:undefined,
      handleContent:undefined,
      handleDeptName:undefined,
      handleDate:undefined,
      content:undefined,
      handlingUnit:undefined,
      warningType:undefined,
      warningType2:undefined,
      warningType3:undefined,
      warningType4:undefined,
      warningType5:undefined,
      category:undefined,
      inquiryStatus:undefined,
      fileUserName:undefined,
      fileContent:undefined,
      fileDate:undefined,
      fileDeptName:undefined,
      warningForm:{},
      warningId:undefined,
      warningData:[],
      warningLoading:true,
      warningOption: {
        /* height:'auto',*/
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "食堂名称",
            prop: "deptName",
            type: "input",
          },
          {
            label: '预警时间',
            prop: 'illegalDate',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            width: 90,
          },
          {
            label: "违规分类",
            prop: "category",
            type: "select",
            search:true,
            width: 80,
            dicData: [
              {
                label: "收入违规",
                value: "0"
              },
              {
                label: "支出违规",
                value: "1"
              },
              {
                label: "采购违规",
                value: "2"
              },
              {
                label: "财务违规",
                value: "3"
              },
              {
                label: "招标违规",
                value: "4"
              },
              {
                label: "仓管违规",
                value: "5"
              }
            ],
          },
          {
            label: "违规内容",
            prop: "content",
            type: "input",
            width:600,
          },
          {
            label: "当前状态",
            prop: "inquiryStatus",
            type: "select",
            width:90,
            search:true,
            dicData: [
              {
                label: "待函询",
                value: "1"
              },
              {
                label: "待食堂回复",
                value: "2"
              },
              {
                label: "待处置",
                value: "3"
              },
              {
                label: "待归档",
                value: "4"
              },
              {
                label: "已归档",
                value: "5"
              }
            ],
          },
          {
            label: "办理单位",
            prop: "handlingUnit",
            type: "input",
            width:150,
          },
        ]
      },
      viewId: '',
      supplierName: '',
      totalPrices: '',
      accepterUserName: '',
      accepterTime: '',
      accepterUserNameb: '',
      accepterStatus: ''
    };
  },
  mounted() {
    // this.drawLine();
    // console.log(this.$echarts);
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
    }
  },
  components: {
    'inquiryDetailVue': inquiryDetailVue,
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchReset1() {
      this.query1 = {};
      this.onLoad1(this.page1);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchChange1(params, done) {
      this.query1 = params;
      this.page1.currentPage = 1
      this.onLoad1(this.page1, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    currentChange1(currentPage){
      this.page1.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    sizeChange1(pageSize){
      this.page1.pageSize = pageSize;
    },
    currentChange2(currentPage){
        this.page2.currentPage = currentPage;
    },
    sizeChange2(pageSize){
        this.page2.pageSize = pageSize;
    },
    onLoad2(page, params = {}) {
        this.loading2 = true;
        params.queryType = 1//按商品查询
        params.biddingTypeId = this.detailRow.biddingTypeId
        console.log(params)
        getAccepterLogging(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
            const data = res.data.data;
            this.page2.total = data.total;
            this.data2 = data.records;
            this.loading2 = false;

            // 临时模拟数据 - 当API返回空数据时使用，后续可删除此段代码
            // 开始：临时模拟数据块 - option2
            if (!this.data2 || this.data2.length === 0) {
              this.data2 = [
                {
                  id: "PO202401001",
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  supplierName: "北京蔬菜批发市场",
                  totalPrices: 12580.50,
                  accepterUserName: "张验收",
                  accepterTime: "2024-01-15 09:30:00",
                  accepterUserNameb: "李确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401002",
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  supplierName: "北京肉类供应商",
                  totalPrices: 23450.80,
                  accepterUserName: "王验收",
                  accepterTime: "2024-01-16 14:20:00",
                  accepterUserNameb: "赵确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401003",
                  deptId: "1002",
                  deptName: "北京市第二中学",
                  supplierName: "北京水产批发市场",
                  totalPrices: 18920.30,
                  accepterUserName: "刘验收",
                  accepterTime: "2024-01-17 10:15:00",
                  accepterUserNameb: "陈确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401004",
                  deptId: "1002",
                  deptName: "北京市第二中学",
                  supplierName: "北京粮油供应商",
                  totalPrices: 15680.90,
                  accepterUserName: "孙验收",
                  accepterTime: "2024-01-18 16:45:00",
                  accepterUserNameb: "周确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401005",
                  deptId: "1003",
                  deptName: "天津市第一中学",
                  supplierName: "天津蔬菜批发市场",
                  totalPrices: 11230.40,
                  accepterUserName: "吴验收",
                  accepterTime: "2024-01-19 08:30:00",
                  accepterUserNameb: "郑确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401006",
                  deptId: "1003",
                  deptName: "天津市第一中学",
                  supplierName: "天津肉类供应商",
                  totalPrices: 20180.60,
                  accepterUserName: "冯验收",
                  accepterTime: "2024-01-20 11:20:00",
                  accepterUserNameb: "褚确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401007",
                  deptId: "1004",
                  deptName: "天津市第二中学",
                  supplierName: "天津水产批发市场",
                  totalPrices: 16750.20,
                  accepterUserName: "卫验收",
                  accepterTime: "2024-01-21 13:40:00",
                  accepterUserNameb: "蒋确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401008",
                  deptId: "1004",
                  deptName: "天津市第二中学",
                  supplierName: "天津粮油供应商",
                  totalPrices: 13420.70,
                  accepterUserName: "沈验收",
                  accepterTime: "2024-01-22 15:10:00",
                  accepterUserNameb: "韩确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401009",
                  deptId: "1005",
                  deptName: "上海市第一中学",
                  supplierName: "上海蔬菜批发市场",
                  totalPrices: 14560.30,
                  accepterUserName: "杨验收",
                  accepterTime: "2024-01-23 09:50:00",
                  accepterUserNameb: "朱确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401010",
                  deptId: "1005",
                  deptName: "上海市第一中学",
                  supplierName: "上海肉类供应商",
                  totalPrices: 25680.90,
                  accepterUserName: "秦验收",
                  accepterTime: "2024-01-24 14:30:00",
                  accepterUserNameb: "尤确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401011",
                  deptId: "1006",
                  deptName: "上海市第二中学",
                  supplierName: "上海水产批发市场",
                  totalPrices: 19870.40,
                  accepterUserName: "许验收",
                  accepterTime: "2024-01-25 10:20:00",
                  accepterUserNameb: "何确认",
                  accepterStatus: "1"
                },
                {
                  id: "PO202401012",
                  deptId: "1006",
                  deptName: "上海市第二中学",
                  supplierName: "上海粮油供应商",
                  totalPrices: 17890.60,
                  accepterUserName: "吕验收",
                  accepterTime: "2024-01-26 16:15:00",
                  accepterUserNameb: "施确认",
                  accepterStatus: "1"
                }
              ];
              this.page2.total = this.data2.length;
            }
            // 结束：临时模拟数据块 - option2
            // 临时模拟数据结束
        });
        this.$nextTick(() => {
          this.drawLine()
        })
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;

        // 调试用：如果返回数据为空，使用模拟数据
        if (!this.data || this.data.length === 0) {
          console.log('数据为空，使用模拟数据进行调试');
          this.page.total = 12;
          this.data = [
            {
              id: 1,
              status: "1",
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "3001",
              biddingTypeId: "4001",
              minimumPurchase: "5.20",
              minimumPurchaseCompare: "+0.30",
              maximumPurchase: "6.80",
              maximumPurchaseCompare: "-0.20",
              averagePurchase: "6.00",
              averagePurchaseCompare: "+0.10"
            },
            {
              id: 2,
              status: "1",
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110102",
              deptId: "1002",
              canteenId: "2004",
              bidding: "3002",
              biddingTypeId: "4006",
              minimumPurchase: "18.50",
              minimumPurchaseCompare: "+0.50",
              maximumPurchase: "22.20",
              maximumPurchaseCompare: "-0.30",
              averagePurchase: "20.35",
              averagePurchaseCompare: "+0.20"
            },
            {
              id: 3,
              status: "1",
              monthStr: "2024-02",
              province: "120000",
              city: "120100",
              area: "120101",
              deptId: "1003",
              canteenId: "2007",
              bidding: "3003",
              biddingTypeId: "4009",
              minimumPurchase: "12.30",
              minimumPurchaseCompare: "-0.20",
              maximumPurchase: "15.50",
              maximumPurchaseCompare: "+0.40",
              averagePurchase: "13.90",
              averagePurchaseCompare: "+0.10"
            },
            {
              id: 4,
              status: "1",
              monthStr: "2024-02",
              province: "130000",
              city: "130100",
              area: "130101",
              deptId: "1004",
              canteenId: "2010",
              bidding: "3004",
              biddingTypeId: "4011",
              minimumPurchase: "8.80",
              minimumPurchaseCompare: "+0.15",
              maximumPurchase: "9.20",
              maximumPurchaseCompare: "-0.10",
              averagePurchase: "9.00",
              averagePurchaseCompare: "+0.05"
            },
            {
              id: 5,
              status: "1",
              monthStr: "2024-03",
              province: "140000",
              city: "140100",
              area: "140101",
              deptId: "1005",
              canteenId: "2013",
              bidding: "3005",
              biddingTypeId: "4013",
              minimumPurchase: "3.60",
              minimumPurchaseCompare: "+0.80",
              maximumPurchase: "4.80",
              maximumPurchaseCompare: "-0.40",
              averagePurchase: "4.20",
              averagePurchaseCompare: "+0.20"
            },
            {
              id: 6,
              status: "1",
              monthStr: "2024-03",
              province: "150000",
              city: "150100",
              area: "150101",
              deptId: "1001",
              canteenId: "2002",
              bidding: "3006",
              biddingTypeId: "4016",
              minimumPurchase: "2.20",
              minimumPurchaseCompare: "-0.30",
              maximumPurchase: "2.90",
              maximumPurchaseCompare: "+0.20",
              averagePurchase: "2.55",
              averagePurchaseCompare: "-0.05"
            },
            {
              id: 7,
              status: "1",
              monthStr: "2024-04",
              province: "210000",
              city: "210100",
              area: "210101",
              deptId: "1002",
              canteenId: "2005",
              bidding: "3007",
              biddingTypeId: "4019",
              minimumPurchase: "25.80",
              minimumPurchaseCompare: "+0.60",
              maximumPurchase: "28.50",
              maximumPurchaseCompare: "-0.25",
              averagePurchase: "27.15",
              averagePurchaseCompare: "+0.35"
            },
            {
              id: 8,
              status: "1",
              monthStr: "2024-04",
              province: "220000",
              city: "220100",
              area: "220101",
              deptId: "1003",
              canteenId: "2008",
              bidding: "3008",
              biddingTypeId: "4021",
              minimumPurchase: "6.50",
              minimumPurchaseCompare: "+0.20",
              maximumPurchase: "7.90",
              maximumPurchaseCompare: "-0.15",
              averagePurchase: "7.20",
              averagePurchaseCompare: "+0.05"
            },
            {
              id: 9,
              status: "1",
              monthStr: "2024-05",
              province: "230000",
              city: "230100",
              area: "230101",
              deptId: "1004",
              canteenId: "2011",
              bidding: "3001",
              biddingTypeId: "4002",
              minimumPurchase: "4.80",
              minimumPurchaseCompare: "-0.40",
              maximumPurchase: "6.10",
              maximumPurchaseCompare: "+0.30",
              averagePurchase: "5.45",
              averagePurchaseCompare: "-0.25"
            },
            {
              id: 10,
              status: "1",
              monthStr: "2024-05",
              province: "310000",
              city: "310100",
              area: "310101",
              deptId: "1005",
              canteenId: "2014",
              bidding: "3002",
              biddingTypeId: "4007",
              minimumPurchase: "35.20",
              minimumPurchaseCompare: "+0.45",
              maximumPurchase: "38.30",
              maximumPurchaseCompare: "-0.20",
              averagePurchase: "36.75",
              averagePurchaseCompare: "+0.25"
            },
            {
              id: 11,
              status: "1",
              monthStr: "2024-06",
              province: "320000",
              city: "320100",
              area: "320101",
              deptId: "1001",
              canteenId: "2003",
              bidding: "3003",
              biddingTypeId: "4010",
              minimumPurchase: "28.60",
              minimumPurchaseCompare: "-0.60",
              maximumPurchase: "32.70",
              maximumPurchaseCompare: "+0.30",
              averagePurchase: "30.65",
              averagePurchaseCompare: "-0.30"
            },
            {
              id: 12,
              status: "1",
              monthStr: "2024-06",
              province: "330000",
              city: "330100",
              area: "330101",
              deptId: "1002",
              canteenId: "2006",
              bidding: "3004",
              biddingTypeId: "4012",
              minimumPurchase: "12.90",
              minimumPurchaseCompare: "+0.75",
              maximumPurchase: "14.80",
              maximumPurchaseCompare: "-0.40",
              averagePurchase: "13.85",
              averagePurchaseCompare: "+0.35"
            }
          ];
        }

        this.loading = false;
        this.selectionClear();
      });
    },
    onLoad1(page, params = {}) {
      this.loading1 = true;
      params.deptId = this.detailRow.deptId
      params.canteenId = this.detailRow.canteenId
      params.bidding = this.detailRow.bidding
      params.biddingTypeId = this.detailRow.biddingTypeId
      console.log(params)
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query1)).then(res => {
        const data = res.data.data;
        this.page1.total = data.total;
        this.data1 = data.records;
        this.loading1 = false;

        // 临时模拟数据 - 当API返回空数据时使用，后续可删除此段代码
        // 开始：临时模拟数据块 - option1
        if (!this.data1 || this.data1.length === 0) {
          this.data1 = [
            {
              id: 1,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "101",
              minimumPurchase: 2.50,
              minimumPurchaseChange: "+0.30",
              maximumPurchase: 4.20,
              maximumPurchaseChange: "-0.15",
              averagePurchase: 3.35,
              averagePurchaseChange: "+0.08"
            },
            {
              id: 2,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "102",
              minimumPurchase: 1.80,
              minimumPurchaseChange: "-0.20",
              maximumPurchase: 3.50,
              maximumPurchaseChange: "+0.25",
              averagePurchase: 2.65,
              averagePurchaseChange: "+0.05"
            },
            {
              id: 3,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "103",
              minimumPurchase: 1.20,
              minimumPurchaseChange: "+0.10",
              maximumPurchase: 2.80,
              maximumPurchaseChange: "-0.30",
              averagePurchase: 2.00,
              averagePurchaseChange: "-0.10"
            },
            {
              id: 4,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "104",
              minimumPurchase: 3.80,
              minimumPurchaseChange: "+0.50",
              maximumPurchase: 6.50,
              maximumPurchaseChange: "+0.80",
              averagePurchase: 5.15,
              averagePurchaseChange: "+0.65"
            },
            {
              id: 5,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "105",
              minimumPurchase: 2.20,
              minimumPurchaseChange: "-0.15",
              maximumPurchase: 4.80,
              maximumPurchaseChange: "+0.40",
              averagePurchase: 3.50,
              averagePurchaseChange: "+0.12"
            },
            {
              id: 6,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "106",
              minimumPurchase: 4.50,
              minimumPurchaseChange: "+0.60",
              maximumPurchase: 7.20,
              maximumPurchaseChange: "+0.90",
              averagePurchase: 5.85,
              averagePurchaseChange: "+0.75"
            },
            {
              id: 7,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "107",
              minimumPurchase: 1.50,
              minimumPurchaseChange: "+0.20",
              maximumPurchase: 3.20,
              maximumPurchaseChange: "-0.10",
              averagePurchase: 2.35,
              averagePurchaseChange: "+0.05"
            },
            {
              id: 8,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "108",
              minimumPurchase: 2.80,
              minimumPurchaseChange: "-0.25",
              maximumPurchase: 5.50,
              maximumPurchaseChange: "+0.35",
              averagePurchase: 4.15,
              averagePurchaseChange: "+0.05"
            }
          ];
          this.page1.total = this.data1.length;
        }
        // 结束：临时模拟数据块 - option1
        // 临时模拟数据结束
      });
    },
    openAccount(row){
      this.checkVisible = true;
      getDetail(row.id).then(res => {
        let data = res.data.data;
        this.$confirm("您确定要启用【"+data.accountName+"】么？", {
          confirmButtonText: "我确认启用帐套",
          cancelButtonText: "我再检查一下",
          type: "warning"
        })
          .then(() => {
            return openAccount(row);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "启用成功!"
            });
          });
      });
    },
    handleClick(tab) {
      console.log(tab)
    },
    selectionChangeWarning(list) {
      this.selectionList = list;
    },
    searchResetWarning() {
      this.query = {};
      this.onLoadWarning(this.warningPage);
    },
    currentChangeWarning(currentPage){
      this.warningPage.currentPage = currentPage;
    },
    searchChangeWarning(params, done) {
      this.query = params;
      this.warningPage.currentPage = 1
      this.onLoadWarning(this.warningPage, params);
      done();
    },
    sizeChangeWarning(pageSize){
      this.warningPage.pageSize = pageSize;
    },
    handOpenWarningPush(row){
      this.pageParams = row;
      this.warningRecordVisible1 = true;
    },
    management(row){
      this.warningId = row.id;
      this.managementVisible = true;
    },
    fileOpen(row){
      this.warningId = row.id;
      this.fileVisible = true;
    },
    onLoadWarning(page, params = {}) {
      this.warningLoading = true;
      getViolationWarningList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.deptId).then(res => {
        const data = res.data.data;
        this.warningPage.total = data.total;
        this.warningData = data.records;
        this.warningLoading = false;
        this.selectionClearWarning();
      });
    },
    selectionClearWarning() {
      this.selectionList = [];
      this.$refs.warningForm.toggleSelection();
    },
    hanbleOpenWarningList(row){
      this.deptId = row.id;
      this.adminName = row.userName;
      this.phone = row.userPhone;
      this.schoolName = row.schoolName;
      this.warningPage.currentPage = 1;
      this.onLoadWarning(this.warningPage)
      this.warningVisible = true;
    },
    pushInquiry(row){
      saveInquiry(row).then(() => {
        this.warningPage.currentPage = 1;
        this.onLoadWarning(this.warningPage);
        this.warningRecordVisible1 = false;
        this.$message({
          type: "success",
          message: "发送成功!"
        });
      }, error => {
        this.warningRecordVisible1 = false;
        window.console.log(error);
      });
    },
    saveManage(row,done,loading){
      this.managementForm.warningId = this.warningId;
      saveManage(this.managementForm).then(() => {
        this.warningPage.currentPage = 1;
        this.onLoadWarning(this.warningPage);
        this.managementVisible = false;
        this.managementForm.content = "";
        done();
        this.$message({
          type: "success",
          message: "处置成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    fileManage(row,done,loading){
      this.fileForm.warningId = this.warningId;
      saveFile(this.fileForm).then(() => {
        this.warningPage.currentPage = 1;
        this.onLoadWarning(this.warningPage);
        this.fileVisible = false;
        this.fileForm.content = "";
        done();
        this.$message({
          type: "success",
          message: "处置成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    openFoodDiaglog(row, status){
      console.log(row)
      this.detailRow.deptId = row.deptId
      this.detailRow.canteenId = row.canteenId
      this.detailRow.bidding = row.bidding
      this.detailRow.biddingTypeId = row.biddingTypeId
      this.foodVisible = true;
    },
    closeFoodVisible(){
      this.detailRow = {}
      this.foodVisible = false;
    },
    openFoodPriceDiaglog(row,status){
      console.log(row)
      this.foodPriceVisible = true;
    },
    closeFoodPriceVisible(){
      this.foodPriceVisible = false;
    },
    opentView(row, status){
      this.viewId = row.viewId
      this.supplierName = row.supplierName
      this.totalPrices = row.totalPrices
      this.accepterUserName = row.accepterUserName
      this.accepterTime = row.accepterTime
      this.accepterUserNameb = row.accepterUserNameb
      this.accepterStatus = row.accepterStatus
      this.viewDetailsVisible = true
    },
    openWarning(row,status){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = status;
      this.warningType3 = status;
      this.warningType4 = status;
      this.warningType5 = status;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.unitType = row.unitType;

      this.warningDetailsVisible = true;
    },
    checkReply(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    checkNanagement(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.warningType4 = 4;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.handleUserName = row.handleUserName;
      this.handleContent = row.handleContent;
      this.handleDeptName = row.handleDeptName;
      this.handleDate = row.handleDate;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    checkFile(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.warningType4 = 4;
      this.warningType5 = 5;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.handleUserName = row.handleUserName;
      this.handleContent = row.handleContent;
      this.handleDeptName = row.handleDeptName;
      this.handleDate = row.handleDate;
      this.fileUserName = row.fileUserName;
      this.fileContent = row.fileContent;
      this.fileDate = row.fileDate;
      this.fileDeptName = row.fileDeptName;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    pushInquiry(row){
      saveInquiry(row).then(() => {
        this.warningPage.currentPage = 1;
        this.onLoadWarning(this.warningPage);
        this.warningRecordVisible1 = false;
        this.$message({
          type: "success",
          message: "发送成功!"
        });
      }, error => {
        this.warningRecordVisible1 = false;
        window.console.log(error);
      });
    },
    drawLine(){
      // 基于准备好的dom，初始化echarts实例
      let myChart = echarts.init(document.getElementById("myChart"));

      // 临时模拟数据 - 食材价格趋势图数据，后续可删除此段代码
      // 开始：临时模拟数据块 - myChart
      const chartData = {
        dates: ["10.1", "10.2", "10.3", "10.4", "10.15", "10.20", "10.31"],
        prices: [2.50, 2.65, 2.80, 2.45, 2.90, 3.15, 2.95]
      };
      // 结束：临时模拟数据块 - myChart
      // 临时模拟数据结束

      // 绘制图表
      myChart.setOption({
        title: {
          text: '采购单价示意图',
          left: 'center',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold'
          }
        },
        tooltip: {
          trigger: 'axis',
          formatter: function(params) {
            return params[0].name + '<br/>' +
                   params[0].seriesName + ': ' + params[0].value + ' 元/斤';
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: "category",
          data: chartData.dates,
          axisLabel: {
            rotate: 45
          }
        },
        yAxis: {
          type: "value",
          name: '价格 (元/斤)',
          axisLabel: {
            formatter: '{value} 元'
          }
        },
        series: [
          {
            name: '采购单价',
            data: chartData.prices,
            type: "line",
            smooth: true,
            lineStyle: {
              color: '#409EFF',
              width: 3
            },
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [{
                  offset: 0, color: 'rgba(64, 158, 255, 0.3)'
                }, {
                  offset: 1, color: 'rgba(64, 158, 255, 0.1)'
                }]
              }
            }
          },
        ],
      });
    }
  }
};
</script>
<style>
  .tabs > .el-tabs__content {
    padding: 32px;
    color: #6b778c;
    font-size: 32px;
    font-weight: 600;
  }
  </style>
