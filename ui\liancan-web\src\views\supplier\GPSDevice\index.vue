<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">

            <template slot="ifOnLine" slot-scope="scope">
                <el-tag v-if="scope.row.ifOnLine == '0'" size=" medium" type="primary">在线</el-tag>
                <el-tag v-if="scope.row.ifOnLine == '1'" size="medium" type="danger">离线</el-tag>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {getList, add,update,remove} from "@/api/supplier/supplierGpsDevice";
import {mapGetters} from "vuex";
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            data:[],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                calcHeight: 30,
                refreshBtn: false,
                searchMenuSpan: 4, //搜索按钮长度
                tip: false,
                border: true,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                index: true,
                viewBtn: true,
                selection: true,
                labelWidth: 150,
                dialogWidth: 900,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "设备编码",
                        prop: "deviceSn",
                        type: "input",
                        rules: [{
                            required: true,
                            message: '请输入设备码',
                            trigger: 'blur'
                        }],
                    },
                    {
                        label: "设备名称",
                        prop: "deviceName",
                        type: "input",
                        search: true,
                        rules: [{
                            required: true,
                            message: '请输入设备名称',
                            trigger: 'blur'
                        }],
                    },
                    {
                        label: "绑定车俩",
                        prop: "carId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/deliveryCar-list",
                        props: {
                            label: "carName",
                            value: "id"
                        },
                    },
                    {
                        label: "设备状态",
                        prop: "ifOnLine",
                        type: "select",
                        dicData: [{
                            value: "0",
                            label: "在线"
                        }, {
                            value: "1",
                            label: "离线"
                        }],
                        slot: true,
                        search: true,
                        addDisplay: false,
                        editDisplay: false
                    },
                    {
                        label: "编辑日期",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd",
                        display: false,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                ]
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        permissionList() {
            return {
                addBtn: this.vaildData(this.permission.supplier_gps_device_add, false),
                viewBtn: this.vaildData(this.permission.supplier_gps_device_view, false),
                delBtn: this.vaildData(this.permission.supplier_gps_device_delete, false),
                editBtn: this.vaildData(this.permission.supplier_gps_device_edit, false)
            };
        },
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
    },
    methods: {
        rowSave(row, loading, done) {
            add(row).then(res => {
                loading();
                this.onLoad(this.page);
                console.log(">>>>>>>>>>>>>>>>>",res.data.success)
                if (res.data.success) {
                    this.$message({
                        type: "success",
                        message: res.data.message
                    });
                } else {
                    this.$message({
                        type: "error",
                        message: res.data.message
                    });
                }
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
