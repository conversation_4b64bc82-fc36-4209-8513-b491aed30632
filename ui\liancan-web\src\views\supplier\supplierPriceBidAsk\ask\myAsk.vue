<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @refresh-change="searchReset"
                   @row-del="rowDel"
                   @on-load="onLoad">
            <template slot="menuLeft">
                <el-button type="primary" icon="el-icon-plus" size="small" @click="addHandle">新增</el-button>
            </template>
            <template slot="menu" slot-scope="scope">
                <el-button type="text" size="mini" icon="el-icon-view" @click="viewHandle(scope.row)">查看
                </el-button>
                <el-button type="text" size="mini" icon="el-icon-edit" @click="editHandle(scope.row)">编辑
                </el-button>
            </template>
        </avue-crud>
        <!-- 打开新增页面 开始 -->
        <el-dialog :title="dialogTitle"
                   :visible.sync="isShowHandle"
                   v-if="isShowHandle"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm"
                   width="60%"
                   style="height: 90%;">
            <el-form ref="formAdd" label-width="120px">
                <el-form-item label="询价截止日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="5">
                        <el-date-picker type="date"
                                        placeholder="选择日期"
                                        :picker-options="pickerOptions"
                                        v-model="askDate"
                                        format="yyyy-MM-dd"
                                        value-format="yyyy-MM-dd"
                                        style="width: 100%;"></el-date-picker>
                    </el-col>
                    <el-col :span="7">
                        <div style="align-items: center;margin-left: 20px;">注：截止日期过后，供货商将不能再报价</div>
                    </el-col>
                </el-form-item>
                <el-form-item label="说明">
                    <el-col :span="12">
                        <el-input type="textarea" v-model="askRemarks"></el-input>
                    </el-col>
                </el-form-item>
                <el-form-item v-if="editType==='add'" label="询价商品" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                    <el-col :span="6">
                        <el-button type="primary" size="small" @click="selectGoodHandler">从本单位[商品信息]中选择</el-button>
                    </el-col>
                    <el-col :span="6">
                        <el-button type="success" size="small" @click="selectPublicHandler">从公共[商品子项库]中选择</el-button>
                    </el-col>
                </el-form-item>
            </el-form>
            <div style="height: 350px;overflow-y: scroll;">
                <avue-crud :option="stockInOption"
                           :table-loading="stockInLoading"
                           :data="stockInData"
                           :page="stockInPage"
                           :before-open="beforeOpenStockIn"
                           v-model="stockInForm"
                           ref="crudStockIn"
                           @row-update="rowUpdateStockIn"
                           @row-save="rowSaveStockIn"
                           @row-del="rowDelStockIn"
                           @search-change="searchChangeStockIn"
                           @search-reset="searchResetStockIn"
                           @selection-change="selectionChangeStockIn"
                           @current-change="currentChangeStockIn"
                           @size-change="sizeChangeStockIn"
                           @cell-click="handleRowClick"
                           @on-load="stockInOnLoad">
                    <template slot="menu" slot-scope="{row,index}">
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-edit"
                            v-if="!row.$cellEdit"
                            @click="rowCellStockIn(row,index)"
                        >修改</el-button>
                        <el-button
                            type="text"
                            size="small"
                            icon="el-icon-check"
                            v-if="row.$cellEdit"
                            @click="rowSaveStockIn(row,index)"
                        >保存</el-button>
                        <el-button
                            v-if="editType === 'add'"
                            type="text"
                            size="mini"
                            icon="el-icon-delete"
                            @click="deleteRowStockIn(row,index)">删除
                        </el-button>
                    </template>
                </avue-crud>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="closeAddForm">取消</el-button>
                <el-button type="primary" @click="saveSelectHandle">保存</el-button>
            </span>
        </el-dialog>
        <!-- 从本单位[商品信息]中选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowSelectGoods"
                   v-if="isShowSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeSelectForm" width="60%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crudSelectGoods"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text" size="mini" @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 从本单位[商品信息]中选择 结束 -->
        <!-- 从公共[商品子项库]中选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowSelectPublic"
                   v-if="isShowSelectPublic"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeSelectForm3" width="60%">
            <avue-crud :option="publicListOption"
                       :table-loading="publicListLoading"
                       :data="publicListData"
                       :page="publicListPage"
                       v-model="publicListForm"
                       ref="crudSelectPublic"
                       @search-change="searchChange3"
                       @search-reset="searchReset3"
                       @selection-change="selectionChange3"
                       @current-change="currentChange3"
                       @size-change="sizeChange3"
                       @on-load="publicListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text" size="mini" @click="addPublic(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 从公共[商品子项库]中选择 结束 -->
        <!-- 查看 开始 -->
        <el-dialog title="查看"
                   :visible.sync="isShowView"
                   v-if="isShowView"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeViewForm"
                   width="60%">
            <el-form :model="viewForm" label-width="120px">
                <el-form-item label="标题：">
                    <span>{{ viewForm.title }}</span>
                </el-form-item>
                <el-form-item label="询价截止日期：">
                    <span>{{ viewForm.deadLine }}</span>
                </el-form-item>
                <el-form-item label="说明：">
                    <span>{{ viewForm.remarks }}</span>
                </el-form-item>
                <el-table :data="stockInData" border>
                    <el-table-column prop="goodsName" label="商品"></el-table-column>
                    <el-table-column prop="foodSpec" label="规格"></el-table-column>
                    <el-table-column prop="unitName" label="计量单位"></el-table-column>
                    <el-table-column prop="brand" label="品牌"></el-table-column>
                    <el-table-column prop="needNum" label="所需数量"></el-table-column>
                </el-table>
            </el-form>
        </el-dialog>
        <!-- 查看 结束 -->
    </basic-container>
</template>

<script>
import { getList, getGoodList, add, edit, remove } from "@/api/supplier/supplierPriceAsk";
import { getGoodsListWithShop } from "@/api/supplier/supplier";
import { getList as getPublicGoodsList } from "@/api/liancan/shopGoodsPublic";
export default {
    data() {
        return {
            loading: false,
            data: [],
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            form: {},
            query: {},
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 200,
                tip: false,
                border: true,
                addBtn: false,
                delBtn: true,
                editBtn: false,
                viewBtn: false,
                align: "center",
                column: [
                    {
                        label: "标题",
                        prop: "title",
                        type: 'input'
                    },
                    {
                        label: "询价商品",
                        prop: "goodsTotal",
                        type: 'input'
                    },
                    {
                        label: "截止日期",
                        prop: "deadLine",
                        type: 'date',
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                    },
                    {
                        label: "询价状态",
                        prop: "status",
                        type: 'select',
                        dicData: [
                            { label: '进行中', value: '0' },
                            { label: '已结束', value: '1' }
                        ],
                        search: true
                    },
                    {
                        label: "报价供货商（家）",
                        prop: "bidCount",
                    },
                    {
                        label: "编辑人",
                        prop: "updateUserName",
                        search: true
                    },
                    {
                        label: "编辑时间",
                        prop: "updateTime",
                        type: "input",
                    }
                ]
            },
            editType: 'add',
            isShowHandle: false,
            askId: '',
            askDate: '',
            askRemarks: '',
            pickerOptions: {
                // disabledDate(time) {
                //     return time.getTime() > Date.now();
                // }
            },
            selectionList: [],
            // 新增
            dialogTitle: '',
            stockInLoading: true,
            stockInData: [],
            stockInPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            stockInForm: {},
            stockInOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "商品名称",
                        prop: "goodsName",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "所需数量",
                        prop: "needNum",
                        type: "number",
                        min: 1,
                        cell: true,
                    },
                ]
            },
            // 查看
            isShowView: false,
            viewForm: {},
            // 从本单位[商品信息]中选择
            selectionList2: [],
            isShowSelectGoods: false,
            queryGoodsList: {},
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                menuWidth: 120,
                column: [
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品一类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        // cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "publicShopName",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                ]
            },
            // 从公共[商品子项库]中选择
            isShowSelectPublic: false,
            queryPublicList: {},
            publicListLoading: true,
            publicListData: [],
            publicListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            publicListForm: {},
            publicListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                menuWidth: 120,
                column: [
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        // cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    // {
                    //     label: "商品名称",
                    //     prop: "name",
                    //     type: "input",
                    //     rules: [{
                    //         required: true,
                    //         message: "请输入商品名称",
                    //         trigger: "blur"
                    //     }],
                    //     search: true,
                    // },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        search: true,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                ]
            },
        }
    },
    methods: {
        currentChange(current) {
            this.page.currentPage = current;
            this.onLoad(this.page, this.query);
        },
        sizeChange(size) {
            this.page.pageSize = size;
            this.onLoad(this.page, this.query);
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
            })
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                return remove(row.id);
            }).then(() => {
                this.onLoad(this.page);
                this.$message.success("删除成功")
            });
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1;
            this.onLoad(this.page, params);
            done()
        },
        searchReset() {
            this.query = {}
            this.page.currentPage = 1;
            this.onLoad(this.page, this.query);
        },
        // 新增
        addHandle() {
            this.dialogTitle = '新增';
            this.askId = '';
            this.askDate = '';//new Date();
            this.askRemarks = '';
            this.form = {};
            this.editType = 'add';
            this.isShowHandle = true;
        },
        // 编辑
        editHandle(row) {
            this.dialogTitle = '编辑';
            this.askId = row.id;
            this.askDate = row.deadLine;
            this.askRemarks = row.remarks;
            this.form = {};
            this.editType = 'edit';
            this.isShowHandle = true;
            this.getAskPriceGoods(row.id)
        },
        getAskPriceGoods(id) {
            getGoodList(id).then((res) => {
                this.stockInData = []
                if (res.data.success) {
                    this.stockInData = res.data.data
                }
            });
        },
        closeAddForm() {
            if (this.stockInData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowHandle = false;
                    });
            }else{
                this.isShowHandle = false;
            }
        },
        // 查看
        viewHandle(row) {
            this.viewForm = row;
            this.isShowView = true;
            this.getAskPriceGoods(row.id)
        },
        closeViewForm() {
          this.isShowView = false;
        },
        askGoodsRefreshData(row) {
            this.stockInData.push(row);
        },
        beforeOpenStockIn(done, type) {
            done();
        },
        rowUpdateStockIn(row, index, loading, done) {
            done()
        },
        rowSaveStockIn(row, index) {
            this.stockInData[index] = row;
            // this.$refs.crudStockIn.rowCell(row, index)
            row.$cellEdit = false;
            this.$refs.crudStockIn.rowCellUpdate();
        },
        deleteRowStockIn(row, index) {
            // console.log(row)
            this.stockInData.splice(index,1);
        },
        rowCellStockIn(row, index) {
            this.rowStockIn= row;
            this.$refs.crudStockIn.rowCell(row, index)
            // this.$refs.crudStockIn.rowCancel(row, row.$index);
        },
        rowDelStockIn(row) {
        },
        handleRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["needNum"].includes(column.property)) {
                    this.$refs.crudStockIn.rowCell(row, row.$index);
                }
            }
        },
        searchChangeStockIn(params, done) {
            this.queryStockIn = params;
            this.stockInPage.currentPage = 1
            this.stockInOnLoad(this.stockInPage, params);
            done();
        },
        searchResetStockIn() {
            this.queryStockIn = {};
            this.stockInOnLoad(this.stockInPage);
        },
        selectionChangeStockIn(list) {
            this.selectionList = list;
        },
        currentChangeStockIn(currentPage) {
            this.stockInPage.currentPage = currentPage;
        },
        sizeChangeStockIn(pageSize) {
            this.stockInPage.pageSize = pageSize;
        },
        stockInOnLoad(page, params = {}) {
            this.stockInLoading = true;
            if (this.form.id!==undefined && this.form.id!=='') {

            } else {
                this.stockInPage.total = 0;
                this.stockInData = [];
                this.stockInData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.stockInLoading = false;
                this.selectionClearStockIn();
            }
        },
        selectionClearStockIn() {
            this.selectionList = [];
            try{
                this.$refs.crudStockIn.toggleSelection();
            }catch(err){console.error(err);}
        },
        saveSelectHandle() {
            if (this.askDate === undefined || this.askDate === '') {
                this.$message.warning("请选择截止日期");
                return;
            }
            //如果当前保存为空，则不提示
            if (this.stockInData.length==0) {
                this.$message.warning("请选择商品")
                return;
            }
            var isSave = true;
            this.stockInData.forEach((item,index)=>{
                var needNum = item.needNum;
                if (needNum==undefined || needNum=='' || needNum==0) {
                    isSave = false;
                    var rowIndex = index + 1
                    this.$message.warning("第"+rowIndex+"行,所需数量不正确");
                    return;
                }
            });
            if (!isSave) {
                return;
            }
            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                this.form.deadLine = this.askDate;
                this.form.remarks = this.askRemarks;
                console.log(this.form);
                if (this.editType === 'edit') {
                    this.stockInLoading = true;
                    this.form.id = this.askId
                    edit(this.form, this.stockInData).then((res) => {
                        this.stockInLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message.success(res.data.message);
                            this.isShowStockIn = false;
                        } else{
                            this.$message.error(res.data.message)
                        }
                    })
                } else {
                    this.stockInLoading = true;
                    add(this.form, this.stockInData).then((res) => {
                        this.stockInLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message.success(res.data.message);
                            this.isShowStockIn = false;
                        } else{
                            this.$message.error(res.data.message)
                        }
                    })
                }
            }).then(() => {
                this.isShowHandle = false;
            });
        },
        // 从本单位[商品信息]中选择
        selectGoodHandler() {
            this.isShowSelectGoods = true;
        },
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        selectionClearGoodsList() {
            this.selectionList = [];
            this.$refs.crudSelectGoods.toggleSelection();
        },
        searchChange2(params, done) {
            this.queryGoodsList = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        searchReset2() {
            this.queryGoodsList = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange2(list) {
            this.selectionList = list;
        },
        currentChange2(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        goodsListOnLoad(page, params = {}) {
            this.goodsListLoading = true;
            getGoodsListWithShop(page.currentPage, page.pageSize, Object.assign(params, this.queryGoodsList)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.length;
                this.goodsListData = data;
                this.goodsListLoading = false;
                this.selectionClearGoodsList();
            });
        },
        addGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;

            row.goodsName = row.name;
            row.goodsType = 0
            row.warehouseId = '';

            this.askGoodsRefreshData(row);
            this.isShowSelectGoods = false
        },
        // 从公共商品子库选择
        selectPublicHandler() {
            this.isShowSelectPublic = true;
        },
        closeSelectForm3() {
            this.isShowSelectPublic = false;
        },
        searchChange3(params, done) {
            this.queryPublicList = params;
            this.publicListPage.currentPage = 1
            this.publicListOnLoad(this.publicListPage, params);
            done();
        },
        searchReset3() {
            this.queryPublicList = {};
            this.publicListOnLoad(this.publicListPage);
        },
        selectionChange3(list) {
            this.selectionList2 = list;
        },
        currentChange3(currentPage) {
            this.publicListPage.currentPage = currentPage;
        },
        sizeChange3(pageSize) {
            this.publicListPage.pageSize = pageSize;
        },
        publicListOnLoad(page, params = {}) {
            this.publicListLoading = true;
            getPublicGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.queryPublicList)).then(res => {
                const data = res.data.data;
                this.publicListPage.total = data.total;
                this.publicListData = data.records;
                this.publicListLoading = false;
                this.selectionClearPublicList();
            });
        },
        selectionClearPublicList() {
            this.selectionList2 = [];
            this.$refs.crudSelectPublic.toggleSelection();
        },
        addPublic(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;

            row.goodsName = row.name;
            row.goodsType = 1
            row.warehouseId = '';

            this.askGoodsRefreshData(row);
            this.isShowSelectPublic = false
        },
    }
}
</script>
