<template>
  <el-row>
    <basic-container>
      <avue-crud :option="option" :table-loading="loading" :data="data" ref="crud" v-model="form" :permission="permissionList"
                 @row-del="rowDel" @row-update="rowUpdate" @row-save="rowSave" :before-open="beforeOpen" :page="page"
                 @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
                 @size-change="sizeChange" @refresh-change="refreshChange" @on-load="onLoad">
        <template slot-scope="{row}" slot="roleId">
          <el-tag>{{row.roleName}}</el-tag>
        </template>
        <template slot-scope="{row}" slot="deptId">
          <el-tag>{{row.deptName}}</el-tag>
        </template>
      </avue-crud>
    </basic-container>
  </el-row>
</template>

<script>
import {
  getList,
  getUser,
  remove,
  update,
  add,
  resetPassword
} from "@/api/system/user";
import {
  getDeptTree,
  getDeptLazyTree
} from "@/api/system/dept";
import {
  getRoleTree,
  getRoleTree2
} from "@/api/system/role";
import {
  mapGetters
} from "vuex";
import website from '@/config/website';
const DIC = {
  STATUS: [
    {
      label: '在职',
      value: "1"
    },{
      label: '已离职',
      value: "2"
    }
  ],
  PERSONSTATUS: [
    {
      label: '否',
      value: "0"
    },
    {
      label: '是',
      value: "1"
    }
  ],}
export default {
  props: {
    schoolId: String,
  },
  data() {
    const validatePass = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入密码'));
      } else {
        callback();
      }
    };
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'));
      } else if (value !== this.form.password) {
        callback(new Error('两次输入密码不一致!'));
      } else {
        callback();
      }
    };
    return {
      form: {},
      selectionList: [],
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      init: {
        roleTree: [],
        deptTree: [],
      },
      treeDeptId: '',
      treeData: [],
      treeOption: {
        nodeKey: 'id',
        lazy: true,
        treeLoad: function(node, resolve) {
          const parentId = (node.level === 0) ? 0 : node.data.id;
          getDeptLazyTree(parentId).then(res => {
            resolve(res.data.data.map(item => {
              return {
                ...item,
                leaf: !item.hasChildren
              }
            }))
          });
        },
        addBtn: false,
        menu: false,
        size: 'small',
        props: {
          labelText: '标题',
          label: 'title',
          value: 'value',
          children: 'children'
        }
      },
      option: {
        height: 'auto',
        calcHeight: 80,
        tip: false,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        index: true,
        indexLabel:"序号",
        selection: true,
        viewBtn: true,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        dialogType: 'drawer',
        dialogClickModal: false,
        column: [
          {
            label: "用户编号",
            prop: "number",
            type: 'number',
            precision:0,
            mock:{
              type:'number',
              max:1,
              min:10,
              precision:0
            },
            width:100,
            row:true,
          },{
            label: "登录账号",
            prop: "account",
            search: false,
            hide: true,
            rules: [{
              required: true,
              message: "请输入登录账号",
              trigger: "blur"
            }],
            span: website.tenantMode ? 12 : 24,
          },
          {
            label: "所属租户",
            prop: "tenantId",
            type: "tree",
            dicUrl: "/api/service/rabbit-system/tenant/select",
            props: {
              label: "tenantName",
              value: "tenantId"
            },
            // hide: !website.tenantMode,
            hide: true,
            addDisplay: website.tenantMode,
            editDisplay: website.tenantMode,
            viewDisplay: website.tenantMode,
            // search: website.tenantMode,
            search: false,
            rules: [{
              required: true,
              message: "请输入所属租户",
              trigger: "click"
            }],
          },
          {
            label: '密码',
            prop: 'password',
            hide: true,
            placeholder:'登录密码为数字+英文字母结合，限6-8位',
            editDisplay: false,
            viewDisplay: false,
            rules: [{
              required: true,
              validator: validatePass,
              trigger: 'blur'
            }]
          },
          {
            label: '确认密码',
            prop: 'password2',
            hide: true,
            placeholder:'登录密码为数字+英文字母结合，限6-8位',
            editDisplay: false,
            viewDisplay: false,
            rules: [{
              required: true,
              validator: validatePass2,
              trigger: 'blur'
            }]
          },
          {
            label: "用户昵称",
            prop: "name",
            hide: true,
            rules: [{
              required: true,
              message: "请输入用户昵称",
              trigger: "blur"
            }]
          },
          {
            label: "用户姓名",
            prop: "realName",
            search: true,
            rules: [{
              required: true,
              message: "请输入用户姓名",
              trigger: "blur"
            }, {
              min: 2,
              max: 5,
              message: '姓名长度在2到5个字符'
            }]
          },
          {
            label: "所属角色",
            prop: "roleId",
            multiple: true,
            type: "tree",
            dicData: [],
            props: {
              label: "title"
            },
            checkStrictly: true,
            slot: true,
            search:true,
            rules: [{
              required: true,
              message: "请选择所属角色",
              trigger: "click"
            }]
          },
          {
            label: "所属部门",
            prop: "deptId",
            type: "tree",
            //  multiple: true,
            dicData: [],
            props: {
              label: "title"
            },
            checkStrictly: true,
            slot: true,
            rules: [{
              required: true,
              message: "请选择所属部门",
              trigger: "click"
            }]
          },
          {
            label: "身份证号",
            prop: "idNumber",
            type: "input",
            search:true,
            maxlength: 18,
            minlength: 2,
            mock:{
              type:'name',
              en:true,
            },
            rules: [{
              required: true,
              message: "请输入身份证号",
              trigger: "blur"
            }],
            width:160,
          },
          {
            label: "手机号码",
            prop: "phone",
            overHidden: true
          },
          {
            label: "电子邮箱",
            prop: "email",
            hide: true,
            overHidden: true
          },
          {
            label: "用户性别",
            prop: "sex",
            type: "select",
            dicData: [{
              label: "男",
              value: 1
            },
              {
                label: "女",
                value: 2
              },
              {
                label: "未知",
                value: 3
              }
            ],
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }],
            hide: true
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            slot: true,
            hide: true,
            rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],
            width: 70,
          },
          {
            label: "健康证照片",
            prop: "healthyCardPhoto",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            slot: true,
            hide: true,
            width: 90,
          },
          {
            label: "职位",
            prop: "position",
            type: "textarea",
            span: 24,
            hide: true
          },
          {
            label: '是否负责人',
            prop: 'outletsPerson',
            type: "radio",
            //slot: true,
            dicData: DIC.PERSONSTATUS,
            search:true,
            hide: true
          },
          {
            label: "网点名称",
            prop: "outletsId",
            type: "select",
            span: 24,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
            props: {
              label: "name",
              value: "id"
            },
            hide: true
          },
          {
            label: '在职状态',
            prop: 'status',
            type: "select",
            //slot: true,
            dicData: DIC.STATUS,
            rules: [{
              required: true,
              message: '请选择在职状态',
              trigger: 'blur'
            }],
            hide: true
          },
          {
            label: "用户生日",
            type: "date",
            prop: "birthday",
            format: "yyyy-MM-dd hh:mm:ss",
            valueFormat: "yyyy-MM-dd hh:mm:ss",
            hide: true
          },
          {
            label: "账号状态",
            prop: "statusName",
            hide: true,
            display: false
          }
        ]
      },
      data: []
    };
  },
  watch: {
    'form.tenantId'() {
      if (this.form.tenantId !== '') {
        getDeptTree(this.form.tenantId).then(res => {
          const index = this.$refs.crud.findColumnIndex("deptId");
          this.option.column[index].dicData = res.data.data;
        });
        /* getRoleTree(this.form.tenantId, '2').then(res => {
          const index = this.$refs.crud.findColumnIndex("roleId");
          this.option.column[index].dicData = res.data.data;
        }); */
      }
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.user_add, false),
        viewBtn: this.vaildData(this.permission.user_view, false),
        delBtn: this.vaildData(this.permission.user_delete, false),
        editBtn: this.vaildData(this.permission.user_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
  },
  created() {
    this.initData();
  },
  methods: {
    nodeClick(data) {
      this.treeDeptId = data.id;
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    initData() {
      getDeptTree(this.form.tenantId).then(res => {
        const index = this.$refs.crud.findColumnIndex("deptId");
        this.option.column[index].dicData = res.data.data;
      });
      getRoleTree2(this.form.tenantId).then(res => {
        const index = this.$refs.crud.findColumnIndex("roleId");
        this.option.column[index].dicData = res.data.data;
      });
    },
    rowSave(row, loading, done) {
      // row.deptId = row.deptId.join(",");
      row.roleId = row.roleId.join(",");
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      // row.deptId = row.deptId.join(",");
      row.roleId = row.roleId.join(",");
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    searchReset() {
      this.query = {};
      this.treeDeptId = '';
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    handleReset() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择账号密码重置为lc123456?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return resetPassword(this.ids);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getUser(this.form.id).then(res => {
          this.form = res.data;
          /* if (this.form.hasOwnProperty("deptId")) {
            this.form.deptId = this.form.deptId.split(",");
            this.form.deptId.forEach((ele, index) => {
             // this.form.deptId[index] = Number(ele);
              this.form.deptId[index] = ele;
            });
          } */
          if (this.form.hasOwnProperty("roleId")) {
            this.form.roleId = this.form.roleId.split(",");
            this.form.roleId.forEach((ele, index) => {
              // this.form.roleId[index] = Number(ele);
              this.form.roleId[index] = ele;
            });
          }
        });
      }
      done();
    },
    currentChange(currentPage) {
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
    },
    refreshChange() {
      this.onLoad(this.page, this.query);
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(this.schoolId != null && this.schoolId != ''){
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.schoolId).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }

    }
  }
};
</script>

<style>
</style>
