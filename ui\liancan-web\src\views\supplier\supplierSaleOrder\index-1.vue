<template>
  <basicContainer>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="scope" slot="menu">
        <el-button size="mini" class="el-icon-view" type="text" @click="showDetail(scope.row)">详情</el-button>
      </template>
      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   icon="el-icon-edit"
                   v-if="permission.order_add"
                   @click="opentForm">下  单
        </el-button>
      </template>
      <template slot="totalQuantity" slot-scope="scope">
        <span size="medium" type="blue">共{{scope.row.totalQuantity}}种商品</span>
      </template>
      <template slot="sendTime" slot-scope="scope">
        <span>{{scope.row.sendTime}}之前</span>
      </template>
    </avue-crud>

    <el-dialog title="详情" :visible.sync="detailVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <detailVue :query="pageParams"></detailVue>
    </el-dialog>

    <el-dialog title="采购订单" :visible.sync="isShow" :append-to-body="true" @close="closeForm">
      <avue-form ref="orderForm" :option="orderFormOption" v-model="orderForm" @submit="saveOrder">
        <template slot="goodsList" slot-scope="scope">
          <avue-crud :option="goodsOption" :table-loading="goodsLoading" :data="goodsData" :page="goodsPage"
                     :before-open="beforeOpen" v-model="goodsForm" ref="crud"
                     @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange"
                     @current-change="currentChange" @size-change="sizeChange" @on-load="goodsOnLoad">
            <template slot="optional" slot-scope="scope">
              <el-button type="primary" size="small" @click="getSupplier(scope.row)">选择供应商</el-button>
            </template>
            <template slot="menu" slot-scope="scope">
              <el-button type="text" size="mini" @click.native="deleteGoods(scope.row)">删 除
              </el-button>
            </template>
            <template slot="menuLeft">
              <el-button type="primary"
                         size="small"
                         @click="goodsTable">添加下单商品
              </el-button>
            </template>
          </avue-crud>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="商品" :visible.sync="isShow2" :append-to-body="true" @close="closeForm2" width="60%">
      <avue-crud :option="goodsListOption"
                 :table-loading="goodsListLoading"
                 :data="goodsListData"
                 :page="goodsListPage"
                 :before-open="beforeOpen"
                 v-model="goodsListForm"
                 ref="crud"
                 @search-change="searchChange2"
                 @search-reset="searchReset2"
                 @selection-change="selectionChange2"
                 @current-change="currentChange2"
                 @size-change="sizeChange2"
                 @on-load="goodsListOnLoad">
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     v-if="scope.row.isCheck == null || scope.row.isCheck == 0"
                     @click="addGoods(scope.row)">选择
          </el-button>
          <el-button type="text"
                     size="mini"
                     v-if="scope.row.isCheck == 1"
                     @click="delGoods(scope.row)">取消选择
          </el-button>
        </template>
      </avue-crud>
      <div style="text-align: center;">
        <el-button type="primary"
                   size="medium"
                   @click="addGoodsList()">确定
        </el-button>
      </div>
    </el-dialog>
    <el-dialog title="供应商选择" :visible.sync="isShow3" :append-to-body="true" @close="closeForm3" width="25%">
      <avue-form ref="supplierForm" :option="supplierFormOption" v-model="supplierForm" @submit="saveSupplier">
      </avue-form>
    </el-dialog>
  </basicContainer>
</template>
<script>
  import {getList, getPrevious, add, update, remove,findSupplier,goodsOnLoad} from "@/api/liancan/order";
  import {optionOne as orderFormOption,goodsOption} from "@/const/order/index";
  import {getList as getGoodsList} from "@/api/liancan/schoolGoods";
  import detailVue from "@/views/liancan/order/orderDetail";
  import {getColumn} from "@/api/liancan2/common";
  import {mapGetters} from "vuex";
  var DIC = {
      isPutaway: [{
          label: '纸质发票',
          value: "0"
      },{
          label: '电子发票',
          value: "1"
      }],
      orderStatus: [{
            label: '未接单',
            value: "0"
        },{
            label: '食堂已收',
            value: "1"
        },{
            label: '取消/拒单',
            value: "2"
        },{
            label: '已送达',
            value: "3"
        },{
            label: '配送中',
            value: "4"
        }],
      isBuy: [{
          label: '未确认',
          value: "0"
      },{
          label: '确认通过',
          value: "1"
      },{
          label: '确认拒绝',
          value: "2"
      }],
  }
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        goodsListPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        goodsPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        pageParams: {},
        detailVisible: false,
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          selection: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menu: false,
          menuWidth: 120,
          align: "center",
          searchSpan:130,
          column: [
              {//5
                  label: "收单时间",
                  prop: "createTime",
                  type: "datetime",
                  format: "yyyy-MM-dd HH:mm:ss",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  display: false,
                  width: 150,
              },
              {//0
                  label: "订单号",
                  prop: "id",
                  type: "input",
                  width: 160,
                  search: true,
              },
              {
              label: "客户单位",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
              props: {
                label: "deptName",
                value:"id"
              },
              hide: true,
              addDisplay: false,
              viewDisplay: false,
              editDisplay: false,
              search:true,
            },
              {
                  label: "客户单位",
                  prop: "deptName",
                  type: "input",
              },
            {
              label: "食堂",
              prop: "canteenName",
              type: "input",
            },
              {
                  label: "食堂",
                  prop: "canteenId",
                  type: "select",
                  dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                  props: {
                      label: "deptName",
                      value:"id"
                  },
                  search: false,
                  hide: true,
                  addDisplay: false,
                  editDisplay: false,
                  viewDisplay: false,
              },
              {//1
                  label: "订单商品",
                  prop: "goodsName",
                  type: "input",
                  width: 170,
              },
              {//10
                  label: "订单总额",
                  prop: "totalPrices",
                  type: "number",
                  precision:2,
                  mock:{
                      type:'number',
                      max:1,
                      min:2,
                      precision:2
                  },
                  display: false,
                  width: 90,
              },
              {//12
                  label: "流程进展",
                  prop: "orderStatus",
                  type: "select",
                  dicData: DIC.orderStatus,
                  width: 100,
              },
              {//15
                  label: "联系人",
                  prop: "userName",
                  type: "input",
                  hide: true,
              },
              {//14
                  label: "联系电话",
                  prop: "phone",
                  type: "input",
                  hide: true,
              },
              {//13
                  label: "配送地址",
                  prop: "site",
                  type: "input",
                  hide: true,
              },
              {//11
                  label: "配送时间",
                  prop: "sendTime",
                  type: "datetime",
                  format: "yyyy-MM-dd HH:mm",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  slot: true,
              },
//以下不显示////////////////////////////////////////////////////////////////////////////////////////////
              {//1
                  label: "供应商",
                  prop: "supplierName",
                  type: "input",
                  viewDisplay: true,
                  addDisplay: false,
                  editDisplay: false,
                  width: 170,
                  overHidden: true,
                  hide: true,
              },
              {//2
                  label: "供应商",
                  prop: "supplierId",
                  type: "select",
                  dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
                  props: {
                      label: "name",
                      value: "id"
                  },
                  search: false,
                  hide: true,
                  viewDisplay: false,
                  addDisplay: true,
                  editDisplay: true,
                  showClose: true,
              },
            {//4
              label: "采购人",
              prop: "createUser",
              type: "select",
              dicUrl: '/api/service/rabbit-liancan2/user/getUserList?roleAlias=placeorder',
              props: {
                  label: "realName",
                  value: "id"
              },
              search: false,
              viewDisplay: false,
              addDisplay: true,
              editDisplay: true,
              width: 120,
              hide: true
            },

            {//6
              label: "发票",
              prop: "invoiceId",
              hide: true,
                type: "select",
                dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
                props: {
                    label: "num",
                    value: "id"
                },
                //dicFlag: true,
            },
            {//7
              label: "采购确认人",
              prop: "verifyUserName",
                type: "input",
                /*dicUrl: '/api/service/rabbit-user/user-list',
                props: {
                    label: "realName",
                    value: "id"
                },*/
                width: 120,
                hide: true,
            },
              {//8
                  label: "确认状态",
                  prop: "isBuy",
                  type: "select",
                  dicData: DIC.isBuy,
                  width: 100,
                  hide: true,
              },
            {//9
                label: "商品种类数",
                prop: "totalQuantity",
                type: "input",
                //type: 'radio',
                slot: true,
                //align: 'center',
                display: false,
                width: 110,
                hide: true,
            },
            {//16
              label: "拒单理由",
              prop: "rejectReason",
                maxlength: 300,
                showWordLimit:true,
                type: "textarea",
                display: false,
                hide: true,
                span: 24,
            },
              {//17
                  label: "审单备注",
                  prop: "remark",
                  maxlength: 300,
                  showWordLimit:true,
                  type: "textarea",
                  display: false,
                  hide: true,
                  span: 24,
              },
            {//17
              label: "收货状态",
              prop: "receiveStatus",
              type: "input",
                display: false,
                hide: true,
            },
            {//19
              label: "收货时间",
              prop: "receiveTime",
              type: "input",
                display: false,
                hide: true,
            },
            {//20
              label: "订单入库状态",
              prop: "putStatus",
              type: "input",
              display: false,
              hide: true,
            },
            {
              label: "采购开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
              display: false,
            },
            {
              label: "采购结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
              display: false,
            },
 /*             {
                  label: '采购时间',
                  prop: 'timeRange',
                  type: 'date',
                  format: 'yyyy-MM-dd',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                  search: true,
                  searchRange:true,
                  showColumn: false,
                  hide: true,
              },*/
              {//
                  label: "采购商品",
                  prop: 'goodsList',
                  type: 'dynamic',
                  hide: true,
                  span: 24,
                  children: {
                      align: 'center',
                      headerAlign: 'center',
                      width: '100%',
                      addBtn: false,
                      delBtn: false,
                      column: [{
                          label: 'id',
                          prop: 'id',
                          type: 'input',
                          hide: true,
                          display: false,
                          showColumn: false,
                      },
                          {
                              label: "商品",
                              prop: "goodsId",
                              type: "select",
                              //dicFlag: false,
                              dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
                              props: {
                                  label: "name",
                                  value: "id"
                              },
                              disabled: false,
                          },
                          {
                              label: "采购单价",
                              prop: "price",
                              type: "number",
                              disabled: false,
                              precision:2,
                              mock:{
                                  type:'number',
                                  max:1,
                                  min:2,
                                  precision:2
                              },
                              //minRows: 0,
                          },
                          {
                              label: "计量单位",
                              prop: "unit",
                              type: "select",
                              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                              props: {
                                  label: "dictValue",
                                  value: "dictKey"
                              },
                              disabled: false,
                          },
                          {
                              label: "采购数量",
                              prop: "quantity",
                              type: "number",
                          },
                          {
                              label: "小计",
                              prop: "subtotal",
                              type: "number",
                              precision:2,
                              mock:{
                                  type:'number',
                                  max:1,
                                  min:2,
                                  precision:2
                              },
                              //minRows: 0,
                          },
                      ]
                  },
                  rules: [{
                      required: true,
                      message: '请选择商品',
                      trigger: 'blur'
                  }]
              },
          ],
          disabledFlag:true,
        },
        data: [],
          orderFormOption: orderFormOption,
          isShow: false,
          isShow2: false,
          isShow3: false,
          godsid: null,
          orderForm: {
              goodsList: []
          },
          goodsListForm: {},
          supplierForm: {},
          goodsForm: {},
          goodsListData: [],
          goodsData: [],
          isclick: true,
          goodsListLoading: true,
          goodsLoading: true,
          goodsOption: goodsOption,
          goodsListOption: {
              height:'auto',
              calcHeight: 30,
              searchShow: true,
              searchMenuSpan: 6,
              tip: false,
              border: true,
              index: false,
              addBtn: false,
              delBtn: false,
              viewBtn: false,
              editBtn: false,
              selection: false,
              align: 'center',
              column: [
                  {
                      label: "id",
                      prop: "id",
                      type: "input",
                      addDisplay: false,
                      editDisplay: false,
                      viewDisplay: false,
                      hide: true,
                  },
                  {
                      label: "商品名称",
                      prop: "name",
                      type: "input",
                      rules: [{
                          required: true,
                          message: "请输入商品名称",
                          trigger: "blur"
                      }],
                      search: true,
                  },
                  {
                      label: "商品类型",
                      prop: "type",
                      type: "select",
                      rules: [{
                          required: true,
                          message: "请选择商品类型",
                          trigger: "blur"
                      }],
                      dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                      props: {
                          label: "name",
                          value: "id"
                      },
                      filterable: true,
                      searchFilterable: true,
                      cascaderItem: ['bidding'],
                      search: true,
                  },
                  {
                      label: "商品大类",
                      prop: "bidding",
                      type: "select",
                      rules: [{
                          required: true,
                          message: "请选择商品大类",
                          trigger: "blur"
                      }],
                      dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                      props: {
                          label: "name",
                          value: "id"
                      },
                      filterable: true,
                      searchFilterable: true,
                      cascaderItem: ['biddingTypeId'],
                      search: true,
                      dicFlag: false,
                  },
                  {
                      label: "商品小类",
                      prop: "biddingTypeId",
                      type: "select",
                      rules: [{
                          required: true,
                          message: "请选择商品小类",
                          trigger: "blur"
                      }],
                      dicFlag: false,
                      dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                      props: {
                          label: "name",
                          value: "id"
                      },
                      search: true,
                  },
                  {
                      label: "计量单位",
                      prop: "unit",
                      type: "select",
                      dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                      props: {
                          label: "dictValue",
                          value: "dictKey"
                      },
                      rules: [{
                          required: true,
                          message: "请选择计量单位",
                          trigger: "blur"
                      }],
                  },
                  {
                      label: "商品图片",
                      prop: "imgUrl",
                      type: 'upload',
                      listType: 'picture-img',
                      propsHttp: {
                          res: 'data',
                          url: 'link'
                      },
                      tip: '只能上传jpg/png文件，大小不超过 500KB',
                      action: '/api/service/rabbit-resource/oss/endpoint/put-file',
                      rules: [{
                          required: false,
                          message: '请上传图片',
                          trigger: 'click'
                      }],
                      slot: true,
                      span: 24,
                      hide: true,
                  },
                  {
                      label: "isCheck",
                      prop: "id",
                      type: "input",
                      addDisplay: false,
                      editDisplay: false,
                      viewDisplay: false,
                      hide: true,
                  },
              ]
          },
          checkList: [],
          checkListObj: [],
          supplierFormOption: {
              emptyBtn: true,
              submitBtn: true,
              submitText: '确定',
              column: [
                  {
                      label: "供应商",
                      prop: "supplierId",
                      type: "select",
                      span: 24,
                      dicData: [],
                      props: {
                          label: "name",
                          value: "id"
                      },
                      rules: [{
                          required: true,
                          message: "请选择供应商",
                          trigger: "blur"
                      }],
                      //dicFlag: false,
                  },
              ]
          },
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.order_add, false),
          viewBtn: this.vaildData(this.permission.order_view, false),
          delBtn: this.vaildData(this.permission.order_delete, false),
          editBtn: this.vaildData(this.permission.order_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
      mounted(){
          this.getPrevious()
      },
      watch: {
      },
    created(){
      if (this.userInfo.userType === 'canteen'){
        this.option.column[0].search = false;
        this.option.column[1].hide = true;
      }
    },
    components: {
      'detailVue': detailVue  //将别名demo 变成 组件 Demo
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["view"].includes(type)) {
            if(this.form.isBuy == "2"){
                getColumn(this.option.column, 'remark').display = true;
                getColumn(this.option.column, 'rejectReason').display = false;
            }else{
              if(this.form.orderStatus == "2"){
                  getColumn(this.option.column, 'rejectReason').display = true;
              }else{
                  getColumn(this.option.column, 'rejectReason').display = false;
              }
                getColumn(this.option.column, 'remark').display = false;
            }
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('采购结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('采购开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('采购开始时间不能大于结束时间');
            }
          }
        }
        this.query = params;
          this.page.currentPage = 1;
/*          if (params.timeRange != '' && params.timeRange != null && params.timeRange != undefined) {
              params.startDate = params.timeRange[0];
              params.endDate = params.timeRange[1];
          }*/
          params.timeRange = null;
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
        searchReset2() {
            this.query = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        searchChange2(params, done) {
            this.query = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        selectionChange2(list) {
            this.selectionList = list;
        },
        selectionClear2() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange2(currentPage){
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize){
            this.goodsListPage.pageSize = pageSize;
        },
      onLoad(page, params = {}) {
        this.loading = true;
        if (!!this.query.id && (isNaN(this.query.id) || this.query.id.length > 19)) {
          this.data = [];
          this.loading = false;
          return;
        }

        params.type = '';
        this.query.type = '';

        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      goodsListOnLoad(page, params = {}) {
          this.goodsListLoading = true;
          getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
              const data = res.data.data;
              this.goodsListPage.total = data.total;
              this.goodsListData = data.records;
              this.goodsListLoading = false;
              this.selectionClear();
          });
      },
      goodsOnLoad(page, params = {}) {
          this.goodsLoading = true;
          getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
              const data = res.data.data;
              this.goodsPage.total = 0;
              //this.goodsData = data.records;
              this.goodsLoading = false;
              this.selectionClear();
          });
          this.goodsData.forEach(function(e) {
              e.$cellEdit = true;
          });
      },
        //打开下单弹窗
        opentForm: function() {
            this.isShow = true
        },
        //关闭下单弹窗
        closeForm: function() {
            this.isShow = false
            this.$refs.orderForm.resetForm()
        },
        closeForm2: function() {
            this.isShow2 = false
            this.goodsListOnLoad(this.goodsListPage);
        },
        closeForm3: function() {
            this.isShow3 = false
            //this.goodsListOnLoad(this.goodsListPage);
        },
        //提交订单
        saveOrder: function(form, done) {
            let odk = true;
            this.goodsData.forEach(e => {
                if(e.quantity == null || e.quantity == undefined || e.quantity <= 0) {
                    odk = false
                    if(e.supplierId == null || e.supplierId == '' || e.supplierId == undefined) {
                        this.$message({
                            showClose: true,
                            message: e.goodsName+"  商品数量不能小于1，请选择供应商！",
                            type: 'error'
                        })
                    }else{
                        this.$message({
                            showClose: true,
                            message: e.goodsName+"  商品数量不能小于1！",
                            type: 'error'
                        })
                    }
                }else{
                    if(e.supplierId == null || e.supplierId == '' || e.supplierId == undefined) {
                        odk = false
                        this.$message({
                            showClose: true,
                            message: e.goodsName+"  请选择供应商！",
                            type: 'error'
                        })
                    }
                }
            });
            if(odk){
                form.goodsList = this.goodsData
                add(form).then(res => {
                    this.onLoad(this.page);
                    if (res.data.success) {
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'success'
                        })
                        this.$refs.orderForm.resetForm()
                        this.isShow = false
                        this.goodsData = [];
                    }else {
                        this.$message({
                            showClose: true,
                            message: res.data.message,
                            type: 'error'
                        })
                    }
                });
            }
            done()
        },
        //上一个订单收货信息
        getPrevious() {
            getPrevious().then(res => {
                this.orderForm.userName = res.data.data.userName
                this.orderForm.phone = res.data.data.phone
                this.orderForm.site = res.data.data.site
                if(res.data.data.invoiceId != null && res.data.data.invoiceId != 0) {
                    this.orderForm.invoiceId = res.data.data.invoiceId
                }

            });
        },
        goodsTable(){
          this.isShow2 = true
        },
        isExist(objList,id){
            for(let i=0; i<objList.length; i++){
                if(objList[i].id == id){
                    return true;
                }
            }
            return false;
        },
        addGoods(row){
          if(this.isclick){
              this.isclick = false;
              let list = [];
              this.goodsData.forEach(x => {
                  list.push(x.id)
              })
              if(list == null || list.length <= 0 || list.indexOf(row.id) < 0){
                  this.checkList.push(row.id);
                  if(!this.isExist(this.checkListObj,row.id)){
                      let o = {
                          id: row.id,
                          goodsName: row.name,
                          price: row.price,
                          unit: row.unit,
                          supplierName: null
                      }
                      this.checkListObj.push(o);
                  }
                  getGoodsList(this.goodsListPage.currentPage, this.goodsListPage.pageSize, Object.assign(this.query)).then(res => {
                      const data = res.data.data;
                      this.goodsListPage.total = data.total;
                      this.goodsListData = data.records;
                      this.goodsListLoading = false;
                      this.selectionClear();
                      this.goodsListData.forEach(obj => {
                          if(this.checkList.indexOf(obj.id) > -1){
                              obj.isCheck = 1;
                          }else{
                              obj.isCheck = 0;
                          }
                      })
                  });
              }else{
                  this.$message.error("商品已添加，请不要重复添加");
              }
              setTimeout(()=>{
                  this.isclick = true;
              },1000)
          }
        },
        delGoods(row){
            this.checkList.forEach((item,index) => {
                if(item == row.id){
                    this.checkList.splice(index, 1)
                }
            })
            /*this.goodsData.forEach((item,index) => {
                if(item == row.id){
                    this.goodsData.splice(index, 1)
                }
            })*/
            getGoodsList(this.goodsListPage.currentPage, this.goodsListPage.pageSize, Object.assign(this.query)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.total;
                this.goodsListData = data.records;
                this.goodsListLoading = false;
                this.selectionClear();
                this.goodsListData.forEach(obj => {
                    if(this.checkList.indexOf(obj.id) > -1){
                        obj.isCheck = 1;
                    }else{
                        obj.isCheck = 0;
                    }
                })
            });
        },
        addGoodsList(){
            this.isShow2 = false
            this.checkList.forEach(s => {
                this.checkListObj.forEach(obj => {
                    if(s == obj.id){
                        let o = {
                            id: obj.id,
                            goodsName: obj.goodsName,
                            //price: obj.price,
                            unit: obj.unit,
                            supplierName: null
                        }
                        this.goodsData.push(o)
                    }
                })
            })
                //清空之前选择的
                this.checkList = []
                this.goodsData.forEach(function(e) {
                    e.$cellEdit = true;
                });
        },
        getSupplier(row){
            this.isShow3 = true
            findSupplier(row.id).then(res => {
                this.supplierFormOption.column[0].dicData = res.data.data;
            });
            this.godsid = row.id
        },
        deleteGoods(row){
            for(var i = this.goodsData.length-1; i >= 0; i--){
                if(this.goodsData[i].id == row.id){
                    this.goodsData.splice(i, 1);
                }
            }
        },
        saveSupplier(form, done){
            this.goodsData.forEach((item,index) => {
                if(item.id == this.godsid){
                    item.supplierId = form.supplierId
                }
            })
            this.goodsOnLoad2(this.goodsData);
            this.isShow3 = false
            this.$refs.supplierForm.resetForm()
            done()
        },
        goodsOnLoad2: function(param) {
            goodsOnLoad(param).then(res=>{
                this.goodsData = res.data.data;
                this.goodsData.forEach((item,index) => {
                    if(item.supplierId == null || item.supplierId == '0'){
                        item.price = null;
                        item.quantity = null;
                    }
                });
                this.goodsData.forEach(function(e) {
                    e.$cellEdit = true;
                });
                this.goodsPage.total = this.goodsData.length;
                this.goodsLoading = false;
            });
        },
      showDetail (row) {
        this.detailVisible = true;
        this.pageParams = row;
      },
    }
  };
</script>

<style>
</style>
