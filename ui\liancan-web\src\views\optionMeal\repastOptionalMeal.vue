<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary"
                   size="small"
                   v-if="permission.repastOptionalMeal_add && this.modeType2 != '1'"
                   @click="handleAdd">新增
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="新增" :visible.sync="addVisible" :append-to-body="true" @close="addFormClose" width="60%">
      <avue-form ref="addForm" :option="addOption" v-model="addForm"  :table-loading="loadingDinnerTable" @submit="saveForm">
        <template slot="button" slot-scope="scope">
          <el-button type="primary"
                     size="small"
                     @click="dataList">列出日期明细
          </el-button>
        </template>
        <template slot="dinnerTable" slot-scope="scope">
          <avue-crud :option="optionDinnerTable" :data="dataDinnerTable" v-model="formDinnerTable"
                     :table-loading="loadingDinnerTable" ref="formDinnerTable" :page="optionDinnerPage">
            <template slot="breakfast" slot-scope="scope">
              <el-tag v-if="scope.row.breakfast == '1'" size="medium" type="success">供餐</el-tag>
              <el-tag v-if="scope.row.breakfast == '0'" size="medium" type="danger">休息</el-tag>
            </template>
            <template slot="lunch" slot-scope="scope">
              <el-tag v-if="scope.row.lunch == '1'" size="medium" type="success">供餐</el-tag>
              <el-tag v-if="scope.row.lunch == '0'" size="medium" type="danger">休息</el-tag>
            </template>
            <template slot="dinner" slot-scope="scope">
              <el-tag v-if="scope.row.dinner == '1'" size="medium" type="success">供餐</el-tag>
              <el-tag v-if="scope.row.dinner == '0'" size="medium" type="danger">休息</el-tag>
            </template>
            <template slot="midnight" slot-scope="scope">
              <el-tag v-if="scope.row.midnight == '1'" size="medium" type="success">供餐</el-tag>
              <el-tag v-if="scope.row.midnight == '0'" size="medium" type="danger">休息</el-tag>
            </template>
          </avue-crud>
        </template>
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, getTempCostDetail, save, haveConsume} from "@/api/optionMeal/repastOptionalMeal";
  import { getBusinessSettingDetail } from "@/api/liancan/businessSetting";
  import {
    getDeptAgencyType,
  } from "@/api/system/dept";
  import {mapGetters} from "vuex";
  var DIC = {
    week: [{
      value: "1",
      label: "星期一"
    },
      {
        value: "2",
        label: "星期二"
      },
      {
        value: "3",
        label: "星期三"
      },
      {
        value: "4",
        label: "星期四"
      },
      {
        value: "5",
        label: "星期五"
      },
      {
        value: "6",
        label: "星期六"
      },
      {
        value: "7",
        label: "星期日"
      }
    ],
  }
  export default {
    data() {
      return {
        form: {},
        addForm: {payCostDetailList:[]},
        formDinnerTable: {},
        query: {},
        addVisible: false,
        loading: true,
        loadingDinnerTable: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        optionDinnerPage: {
          pageSize: 50,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          selection: true,
          column: [
            {
              label: "所属食堂",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
              {
                  label: "营业网点",
                  prop: "businessOutletsId",
                  type: "select",
                  search: true,
                  editDisplay: false,
                  dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
                  props: {
                      res: "data",
                      label: "name",
                      value: "id"
                  },
                  span: 20,
                  rules: [{
                      required: true,
                      message: '请选择所属网点',
                      trigger: 'click'
                  }],
              },
            {
              label: "日期",
              prop: "validityDate",
              type: "date",
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              addDisplay: false,
              editDisabled: true
            },
            {
              label: "星期",
              prop: "week",
              type: 'select',
              dicData: DIC.week,
              addDisplay: false,
              editDisabled: true
            },
            {
              label: "早餐",
              prop: "breakfast",
              type: "select",
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              addDisplay: false,
              viewDisplay: false,
            },
            {
              label: "午餐",
              prop: "lunch",
              type: "select",
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              addDisplay: false,
              viewDisplay: false,
            },
            {
              label: "晚餐",
              prop: "dinner",
              type: "select",
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              addDisplay: false,
              viewDisplay: false,
            },
            {
              label: "夜餐",
              prop: "midnight",
              type: "select",
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              addDisplay: false,
              viewDisplay: false,
            },
            {
              label: '最后一次编辑用户',
              prop: 'updateUserName',
              type: 'input',
              display: false
            },
            {
              label: '最后一次编辑时间',
              prop: 'updateTime',
              type: 'input',
              display: false,
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
            },
            {
              label: "早餐价格",
              prop: "breakfastPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "午餐价格",
              prop: "lunchPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "晚餐价格",
              prop: "dinnerPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "夜餐价格",
              prop: "midnightPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
            },
              {
                  label: "开始日期",
                  prop: "startTime",
                  type: "date",
                  format: "yyyy-MM-dd",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  hide: true,
                  search: true,
                  showColumn:false,
                  display: false,
              },
              {
                  label: "结束日期",
                  prop: "endTime",
                  format: "yyyy-MM-dd",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  type: "date",
                  hide: true,
                  search: true,
                  showColumn:false,
                  display: false,
              },
          ]
        },
        addOption: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          selection: true,
          //menuBtn: false,
          column: [
/*            {
              label: "营业网点",
              prop: "businessOutletsId",
              type: "select",
              search: true,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict/0",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
              span: 20,
              rules: [{
                required: true,
                message: '请选择所属网点',
                trigger: 'click'
              }]
            },*/
            {
              label: "营业网点",
              prop: "businessOutletsId",
              type: "select",
              search: true,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
              span: 20,
              rules: [{
                required: true,
                message: '请选择所属网点',
                trigger: 'click'
              }],
              change: ({value,column,item,dic}) => {
                //获取当前网点类型是否为预订餐次，如果是则加载对应的价格
                console.log(value);
                this.getBusinessSettingDetail(value);
              },
            },
            {
              label: "早餐价格",
              prop: "breakfastPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
              rules: [{
                required: true,
                message: '请输入早餐价格',
                trigger: 'click'
              }],
              display: false,
            },
            {
              label: "午餐价格",
              prop: "lunchPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
              rules: [{
                required: true,
                message: '请输入午餐价格',
                trigger: 'click'
              }],
              display: false,
            },
            {
              label: "晚餐价格",
              prop: "dinnerPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
              rules: [{
                required: true,
                message: '请输入晚餐价格',
                trigger: 'click'
              }],
              display: false,
            },
            {
              label: "夜餐价格",
              prop: "midnightPrice",
              type: "number",
              hide: true,
              span: 6,
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
              rules: [{
                required: true,
                message: '请输入夜餐价格',
                trigger: 'click'
              }],
              display: false,
            },
            {
              label: '供餐日期1',
              prop: 'daterange',
              type: 'daterange',
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < Date.now() - 86400000;
              }
            },
              span: 20,
              rules: [{
                required: true,
                message: '请选择缴费时间',
                trigger: 'click'
              }],
              hide: true
            },
            {
              prop: "button",
              slot: true,
              formslot: true,
              span: 24,
              hide: true
            },
            {//4
              label: '快速选择',
              prop: 'quickChoose',
              type: 'radio',
              span: 24,
              hide: true,
              display: false,
              dicData: [{
                label: "周一至周五早中晚供餐",
                value: '0'
              },
                {
                  label: "全部供餐",
                  value: '1'
                },
                {
                  label: "早餐供餐",
                  value: '2'
                },
                {
                  label: "中餐供餐",
                  value: '3'
                },
                {
                  label: "晚餐供餐",
                  value: '4'
                },
                {
                  label: "夜餐供餐",
                  value: '5'
                }
              ],
              rules: [{
                required: true,
                message: '请选择供餐类型',
                trigger: 'click'
              }]
            },
            {
              prop: "dinnerTable",
              slot: true,
              formslot: true,
              span: 24,
              hide: true,
              display: false
            }
          ]
        },
        optionDinnerTable :{
          page: false,
          stripe: true,
          tip: false,
          border: true,
          index: true,
          indexLabel: "序号",
          refreshBtn: false,
          columnBtn: false,
          align: 'center',
          editBtn: false,
          addBtn: false,
          menu: false,
          menuBtn: false,
          width: '100%',
          column: [
            {//0
            label: '日期',
            type: 'date',
            prop: 'dinnerDate',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd'
            },
            {//1
              label: '星期',
              prop: 'week',
              type: 'select',
              dicData: DIC.week
            },
            {//2
              label: '早餐',
              type: 'select',
              prop: 'breakfastStatus',
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              cell: true
            },
            {//3
              label: '中餐',
              type: 'select',
              prop: 'lunchStatus',
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              cell: true
            },
            {//4
              label: '晚餐',
              type: 'select',
              prop: 'supperStatus',
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              cell: true
            },
            {//5
              label: '夜餐',
              prop: 'nightStatus',
              type: 'select',
              dicData: [{
                label: "供餐",
                value: '1'
              },
                {
                  label: "休息",
                  value: '0'
                }
              ],
              cell: true
            }
          ]
        },
        data: [],
        dataDinnerTable: [],
      };
    },
    watch: {
      'addForm.quickChoose'() {
        let quickChoose = this.addForm.quickChoose;
        if (quickChoose != "" && quickChoose != null) {
          let param = {
            quickChoose: quickChoose,
            dinnerStartDate: this.addForm.daterange[0],
            dinnerEndDate: this.addForm.daterange[1]
          };
          this.getTempCostDetail(param);
        }
        //console.log(JSON.stringify(this.formDinnerTable.quickChoose))
      }
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.repastOptionalMeal_add, false),
          viewBtn: this.vaildData(this.permission.repastOptionalMeal_view, false),
          delBtn: this.vaildData(this.permission.repastOptionalMeal_delete, false),
          editBtn: this.vaildData(this.permission.repastOptionalMeal_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      if (this.userInfo.userType === 'canteen') {
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
      }
      getDeptAgencyType().then(res =>{
        if (res.data.data.applicationMode === '1'){
          this.modeType = '1';
          this.modeType2 = '2';
        }else if (res.data.data.applicationMode === '2'){
          this.modeType = '2';
          this.modeType2 = '2';
        }else if (res.data.data.applicationMode === '3'){
          this.modeType = '1';
          this.modeType2 = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }else if (res.data.data.applicationMode === '0'){
          this.modeType = '1';
          this.modeType2 = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }
      })
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        haveConsume(row.id).then(res=>{
          if(res.data.data){
            this.$confirm("已有人预订该餐次，确定删除该条数据?", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                return remove(row.id);
              })
              .then(() => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!"
                });
              });
          }else{
            this.$confirm("确定将选择数据删除?", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                return remove(row.id);
              })
              .then(() => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!"
                });
              });
          }
        })
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleAdd(){
        this.addVisible = true;
        this.dataDinnerTable = [];
        this.loadingDinnerTable = false;
      },
      dataList(){
        if (this.addForm.daterange === null || this.addForm.daterange === ''){
          this.$message.warning("请选择供餐日期");
        }else if(this.addForm.businessOutletsId === null || this.addForm.businessOutletsId === ''){
          this.$message.warning("请选择营业网点");
        }else{
          let param = {
            dinnerStartDate: this.addForm.daterange[0],
            dinnerEndDate: this.addForm.daterange[1],
            outletsId: this.addForm.businessOutletsId
          };
          this.getTempCostDetail(param);
        }
      },
      getTempCostDetail(param) {
        getTempCostDetail(param).then(res=>{
          this.dataDinnerTable = res.data.data;
          this.dataDinnerTable.forEach(function(e) {
            e.$cellEdit = true;
          });
          this.optionDinnerPage.total = this.dataDinnerTable.length;
          this.loadingDinnerTable = false;
          this.addOption.column[7].display = true;
          this.addOption.column[8].display = true;
        })
      },
      saveForm(row, done){
        console.log(JSON.stringify(this.dataDinnerTable))
        if (this.dataDinnerTable.length == 0){
          this.$message.error('请添加日期明细');
          done();
        }
        /*else if (this.addForm.breakfastPrice == null || this.addForm.breakfastPrice == ''){
          this.dataDinnerTable.forEach(ele=>{
            if (ele.breakfastStatus === "1"){
              this.$message.error('早餐供餐,早餐价格不能为空');
              done();
            }
          })
        }
        else if(this.addForm.lunchPrice == null || this.addForm.lunchPrice == ''){
          this.dataDinnerTable.forEach(ele=>{
            if (ele.lunchStatus === "1"){
              this.$message.error('午餐供餐,午餐价格不能为空');
              done();
            }
          })
        }
        else if(this.addForm.dinnerPrice == null || this.addForm.dinnerPrice == ''){
          this.dataDinnerTable.forEach(ele=>{
            if (ele.supperStatus === "1"){
              this.$message.error('晚餐供餐,晚餐价格不能为空');
              done();
            }
          })
        }
        else if(this.addForm.midnightPrice == null || this.addForm.midnightPrice == ''){
          this.dataDinnerTable.forEach(ele=>{
            if (ele.nightStatus === "1"){
              this.$message.error('夜餐供餐,夜餐价格不能为空');
              done();
            }
          })
        }*/
        else{
          console.log(JSON.stringify(this.dataDinnerTable))
          this.addForm.payCostDetailList = this.dataDinnerTable
          console.log(this.addForm)
          save(this.addForm).then(() => {
            this.addVisible = false;
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.addForm.resetForm();
            this.addOption.column[7].display = false;
            this.addOption.column[8].display = false;
            }, error => {
              window.console.log(error);
          });
          done();
        }
      },
      getBusinessSettingDetail(businessOutletsId){
        getBusinessSettingDetail(businessOutletsId).then(res => {
          const data = res.data.data;
          //校验是否为预订餐次，不用订餐的选项
          if(data.advanceType && data.advanceType == '0'){
            if(data.advanceTypeRule && data.advanceTypeRule == '2'){
              this.addOption.column[1].display = false;
              this.addOption.column[2].display = false;
              this.addOption.column[3].display = false;
              this.addOption.column[4].display = false;
              this.addOption.column[1].disabled = false;
              this.addOption.column[2].disabled = false;
              this.addOption.column[3].disabled = false;
              this.addOption.column[4].disabled = false;
              this.optionDinnerTable.column[2].disabled = true
              this.optionDinnerTable.column[3].disabled = true
              this.optionDinnerTable.column[4].disabled = true
              this.optionDinnerTable.column[5].disabled = true
               let advanceTypeRuleObj = JSON.parse(data.advanceTypeRuleJson)
               console.log(advanceTypeRuleObj);
               if(advanceTypeRuleObj.breakfastPrice){
                  this.addOption.column[1].display = true;
                  this.addOption.column[1].disabled = true;
                  this.addForm.breakfastPrice = advanceTypeRuleObj.breakfastPrice;
                  this.optionDinnerTable.column[2].disabled = false
               }
               if(advanceTypeRuleObj.lunchPrice){
                  this.addOption.column[2].display = true;
                  this.addOption.column[2].disabled = true;
                  this.addForm.lunchPrice = advanceTypeRuleObj.lunchPrice;
                  this.optionDinnerTable.column[3].disabled = false
               }
               if(advanceTypeRuleObj.dinnerPrice){
                 this.addOption.column[3].display = true;
                 this.addOption.column[3].disabled = true;
                 this.addForm.dinnerPrice = advanceTypeRuleObj.dinnerPrice;
                 this.optionDinnerTable.column[4].disabled = false
               }
               if(advanceTypeRuleObj.nightPrice){
                this.addOption.column[4].display = true;
                this.addOption.column[4].disabled = true;
                this.addForm.nightPrice = advanceTypeRuleObj.nightPrice;
                this.optionDinnerTable.column[5].disabled = false
              }
            }
          } else {
            this.addOption.column[1].display = true
            this.addOption.column[2].display = true
            this.addOption.column[3].display = true
            this.addOption.column[4].display = true
            this.addOption.column[1].disabled = false
            this.addOption.column[2].disabled = false
            this.addOption.column[3].disabled = false
            this.addOption.column[4].disabled = false
          }
        });
      }
    }
  };
</script>

<style>
</style>
