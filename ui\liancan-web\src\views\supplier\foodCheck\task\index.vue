<template>
  <basic-container>
    <avue-crud :option="option" :table-loading="loading" :data="data" :page="page" :permission="permissionList" :before-open="beforeOpen" v-model="form" ref="crud" @row-update="rowUpdate"
      @row-save="rowSave" @row-del="handleDelete" @search-change="searchChange" @search-reset="searchReset" @selection-change="selectionChange" @current-change="currentChange"
      @size-change="sizeChange" @on-load="onLoad">
      <!--<template slot="isPutaway" slot-scope="scope">
		        <el-tag v-if="scope.row.isPutaway == '0'" size="medium" type="danger">{{scope.label}}</el-tag>
		        <el-tag v-if="scope.row.isPutaway == '1'" size="medium" type="success">{{scope.label}}</el-tag>
        </template> -->
      <template slot="menuLeft">
        <el-button type="danger" size="small" icon="el-icon-delete" plain v-if="permission.foodcanteengoodschecktask_remove" @click="handleDelete('')">删除</el-button>
      </template>
      <template slot="spImg" slot-scope="scope">
        <div style="white-space: nowrap;">
        <img v-for="e in scope.row.spImg.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickPreview(e)" style="margin-right:10px;cursor: pointer;">
        </div>
      </template>
      <template slot="rcImg" slot-scope="scope">
        <img v-for="e in scope.row.rcImg.split(',')" :key="e" :src="e" min-width="40" height="50" @click="handleClickPreview(e)" style="margin-right:10px;cursor: pointer;">
      </template>
    </avue-crud>
    <el-dialog :visible.sync="viewImgDialogVisible" @close="viewImgDialogVisible=false" width="30%" height="30%" :modal-append-to-body="false">
      <img width="100%" :src="viewImgDialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>
<script>
import mixinViewModule from '@/mixins/view-module'
import { mapGetters } from "vuex";
import { dateFormat } from "@/util/date";
export default {
  mixins: [mixinViewModule],
  data() {
    return {
      mixinViewModuleOptions: {
        listUrl: '/api/service/rabbit-supplier/foodcheck/list',
        pageUrl: '/api/service/rabbit-supplier/foodcheck/page',
        detailUrl: '/api/service/rabbit-supplier/foodcheck/detail',
        removeUrl: '/api/service/rabbit-supplier/foodcheck/remove',
        saveUrl: '/api/service/rabbit-supplier/foodcheck/save',
        updateUrl: '/api/service/rabbit-supplier/foodcheck/update',
      },
      userList:[],
      viewImgDialogVisible: false,
      viewImgDialogImageUrl: '',
      option: {
        viewBtn: true,
        editBtn:false,
        searchFilterable: true,
        column: [
          { label: '被检单位', prop: 'deptId',span:12, search: true, type: 'select', dicUrl: '/api/service/rabbit-system/dept/getDeptAndSubDept?deptCategory=4', props: { label: 'deptName', value: 'id' }, rules: [{ required: true, message: '请输入食堂', trigger: 'blur' }] }
          , { label: '样本编码', prop: 'simpleCode',addDisplay:false,editDisplay:false,viewDisplay:true}
          ,{label: '样本分类',prop: "goodsType",type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=goods_category",props: {label: "dictValue",value: "dictKey"},addDisplay:false,editDisplay:false,viewDisplay:true}
          , { label: '样本总量', prop: 'sampleWeight',hide:true,addDisplay:false,editDisplay:false,viewDisplay:true,append:'克'}
          , { label: '样本名称', prop: 'goodsIds',span:12, search: false,hide:true, type: 'select',multiple:true, dicUrl: '/api/service/rabbit-basic/basicFood/select-all', props: { label: 'name', value: 'id' }, rules: [{ required: true, message: '请输入检测商品名称', trigger: 'blur' }] }
          , { label: '样本名称', prop: 'goodsName',search:true,addDisplay:false,editDisplay:false,viewDisplay:false,searchWidth:100}
          , { label: '采样员', prop: 'spUserId',span:12, type: 'select', dicUrl: '/api/service/rabbit-food/user/listUser', props: { label: 'realName', value: 'id' }, rules: [{ required: true, message: '请输入采样员', trigger: 'blur' }] ,formatter: (row, value, label, column) => {return this.getUserNameById(value)}}

          , { label: '采样时间期限',hide:true, prop: 'spTimeLimit',span:12, type: 'date', value: dateFormat(new Date(), "yyyy-MM-dd"), disabled: false, format: 'yyyy-MM-dd', valueFormat: 'yyyy-MM-dd', rules: [{ required: true, message: '请输入采样时间', trigger: 'blur' }],viewDisplay:false}

          , {label:'采样时间',prop: 'spTime',type:'datetime',format: 'yyyy-MM-dd HH:mm:ss',valueFormat: 'yyyy-MM-dd HH:mm:ss',searchSpan:4,rules: [{required: false,message: '请输入采样时间',trigger: 'blur'}],addDisplay:false,editDisplay:false,viewDisplay:false}
          , { label: '采样地址', prop: 'spAddress',hide:true,addDisplay:false,editDisplay:false,viewDisplay:true}
          ,{label: '检测类型',prop: "checkType",type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_check_type",props: {label: "dictValue",value: "dictKey"},addDisplay:false,editDisplay:false,viewDisplay:true,hide:true}
          ,{label:'收样员',prop: 'reUserName',searchSpan:4,rules: [{required: false,message: '请输入收样员',trigger: 'blur'}],addDisplay:false,editDisplay:false,viewDisplay:false}
          ,{label:'收样时间',prop: 'rcTime',type:'datetime',format: 'yyyy-MM-dd HH:mm:ss',valueFormat: 'yyyy-MM-dd HH:mm:ss',searchSpan:4,rules: [{required: false,message: '请输入收样时间',trigger: 'blur'}],addDisplay:false,editDisplay:false,viewDisplay:false}
          ,{label: '检单状态',prop: "checkStatus",search:true,type: "select",dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_check_status",props: {label: "dictValue",value: "dictKey"},addDisplay:false,editDisplay:false,viewDisplay:true}
          , { label: '备注', prop: 'taskRemark',hide:true, span:12, rules: [{ required: false, message: '请输入采样备注', trigger: 'blur' }] }
          ,{label:'采样图片',slot:true,align:'center',span:24,class:'spimglist',prop: 'spImg',listType: 'picture-card',dataType:'string',type:'upload',searchSpan:4,rules: [{required: false,message: '请输入采样图片(多个,号分隔)',trigger: 'blur'},{ min: 0, max: 1000, message: '长度不能超过1000个字符', trigger: 'blur' }],addDisplay:false,editDisplay:false,viewDisplay:true}

        ]
      }
    }
  },
  computed: {
    ...mapGetters(["permission"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.foodcanteengoodschecktask_add, false),
        viewBtn: this.vaildData(this.permission.foodcanteengoodschecktask_view, false),
        delBtn: this.vaildData(this.permission.foodcanteengoodschecktask_remove, false),
        editBtn: this.vaildData(this.permission.foodcanteengoodschecktask_edit, false)
      };
    }
  },
  created() {
    this.loadUserData()
  },
  methods: {
    getUserNameById(userId){
      let arr=this.userList.filter(e=>e.id===userId)
      if(arr&&arr.length>0){
        return arr[0].realName||''
      }
      return ''
    },
    loadUserData(){
      this.getData('/api/service/rabbit-food/user/listUser',{},res=>{
        this.userList=res.data.data
      })
    },
    beforeRowSave(row) {
      row['goodNames']=row.$goodsIds.split(' | ');

      if (row.spImg instanceof Array) { row.spImg = '' }
      //row.attachments = row.attachments.join(",");
    }, beforeRowUpdate(row) {
      if (row.spImg instanceof Array) { row.spImg = '' }
      //row.attachments = row.attachments.join(",");
    },
    handleClickPreview: function (url) {
      this.viewImgDialogImageUrl = url;
      this.viewImgDialogVisible = true;
    },
  }
};
</script>
<style>
  .spimglist div.el-upload--picture-card{display: none;}
  .spimglist>div.avue-upload{text-align: left;}
</style>
