<template>
  <basic-container>
    <div class="all-mess">
      <div class="mess-header">
        <div
          :class="{acitve:activeIdx==index}"
          v-for="(item,index) in messList"
          :key="index"
          @click="menuClick(index)"
        >
          {{item}}
        </div>
      </div>
      <!-- :permission="permissionList" -->
      <div class="mess-content" v-if="activeIdx == 0">
        <avue-crud :option="option"
                  :table-loading="loading"
                  :data="data"
                  :page="page"
                  v-model="form"
                  ref="crud"
                  @row-update="rowUpdate"
                  @row-save="rowSave"
                  @row-del="rowDel"
                  @search-change="searchChange"
                  @search-reset="searchReset"
                  @selection-change="selectionChange"
                  @current-change="currentChange"
                  @size-change="sizeChange"
                  @on-load="onLoad">
          <template slot="menuLeft">
            <el-button type="primary" icon="el-icon-upload" size="small" plain @click.stop="exportTemplate()">下载模板
            </el-button>
            <el-button type="warning" icon="el-icon-upload" size="small" plain @click.stop="importOnclick()">导入
            </el-button>
          </template>
        </avue-crud>
      </div>
      <!-- :permission="permissionList" -->
      <div class="mess-content" v-if="activeIdx == 1">
        <avue-crud :option="dishesOption"
                  :table-loading="dishesLoading"
                  :data="dishesData"
                  :page="dishesPage"
                  v-model="dishesForm"
                  ref="crud"
                  @row-update="dishesRowUpdate"
                  @row-save="dishesRowSave"
                  @row-del="dishesRowDel"
                  @search-change="searchChange"
                  @search-reset="searchReset"
                  @selection-change="selectionChange"
                  @current-change="currentChange"
                  @size-change="sizeChange"
                  @on-load="dishesOnLoad">
        </avue-crud>
      </div>
    </div>

    <el-dialog title="导入菜品" :visible.sync="importVisible" :append-to-body="true" @close="successfulBiddingFormClose"
               width="60%">
      <avue-form ref="form" :option="importOption" :upload-after="uploadAfter" :upload-before="uploadBefore" :upload-error="uploadError" @on-load="importOnLoad" :data="importData" v-model="form">
      </avue-form>
    </el-dialog>

  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, exportTemplate} from "@/api/liancan/schoolGoodsPublic";
  import {getList as getListDishes, getDetail as getDetailDishes, add as addDishes, update as updateDishes, remove as removeDishes} from "@/api/supplier/finance/config/dishesCategory";
  import {mapGetters} from "vuex";
  var DIC = {
    enableFlag: [{
      label: '停止',
      value: 0
    },{
      label: '启用',
      value: 1
    }],
    useFlag: [{
      label: '否',
      value: 0
    },{
      label: '是',
      value: 1
    }]
  }

  export default {
    data() {
      return {
        activeIdx: 0,
        messList: [ '商品信息', '菜品分类'],
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        dishesForm: {},
        dishesLoading: true,
        dishesPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          editBtn:true,
          delBtn:true,
          addBtn:true,
          selection: true,
          align: 'center',
          column: [
            {
              label: "菜品编码",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "status",
              prop: "status",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display: false,
              hide: true,
            },
            {
              label: "所属菜品分类",
              prop: "liancanDishesCategoryId",
              type: "select",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              search:true,
              dicUrl: "/api/service/rabbit-supplier/finance/dishesCategory/select",
              props: {
                label: "dishesType",
                value: "id"
              },
              labelWidth:150,
              span: 24,
              rules: [{
                required: true,
                message: "请选择菜品分类",
                trigger: "blur"
              }]
            },
            {
              label: "菜品所属分类内排序",
              prop: "sort",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth:150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入排序序号",
                trigger: "blur"
              }]
            },
            {
              label: "菜品名称",
              prop: "name",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth:150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入菜品名称",
                trigger: "blur"
              }]
            },
            {
              label: "允许食堂选择使用",
              prop: "useFlag",
              type: "select",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              search:true,
              dicData: DIC.useFlag,
              formatter: (row,value,label,column) => {
                if(value == 1){
                  return "是"
                } else {
                  return "否"
                }
              },
              labelWidth:150,
              span: 24,
              rules: [{
                required: true,
                message: "请选择是否允许",
                trigger: "blur"
              }]
            },
            {
              label: "食材配料",
              prop: "ingredients",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              type:'textarea',
              minRows:3,
              maxRows:5,
              span: 24,
              labelWidth:150,
              rules: [{
                required: true,
                message: "请输入食材配料",
                trigger: "blur"
              }]
            },
            {
              label: "已选用该菜品的食堂",
              prop: "useCount",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: true,
              span: 24,
              labelWidth:150,
            },
            {
              label: "油(克)",
              prop: "oil",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入油(克), 有则填写，没有则留空",
                trigger: "blur"
              }]
            },
            {
              label: "盐(克)",
              prop: "salt",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入盐(克), 有则填写，没有则留空",
                trigger: "blur"
              }]
            },
            {
              label: "糖(克)",
              prop: "sugar",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入糖(克), 有则填写，没有则留空",
                trigger: "blur"
              }]
            },
            {
              label: "热量(kcal大卡)",
              prop: "calories",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入热量(kcal大卡), 有则填写，没有则留空",
                trigger: "blur"
              }]
            },
            {
              label: "碳水化合物（克）",
              prop: "carbohydrates",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入碳水化合物(克), 有则填写，没有则留空",
                trigger: "blur"
              }]
            },
            {
              label: "蛋白质",
              prop: "protein",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入蛋白质(克), 有则填写，没有则留空",
                trigger: "blur"
              }]
            },
            {
              label: "脂肪",
              prop: "fat",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              span: 24,
              rules: [{
                required: true,
                message: "请输入脂肪(克), 有则填写，没有则留空",
                trigger: "blur"
              }]
            }
          ]
        },
        dishesOption: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:true,
          delBtn:true,
          addBtn:true,
          selection: true,
          align: 'center',
          column: [
            {
              label: "id",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              labelWidth: 150,
            },
            {
              label: "status",
              prop: "status",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display: false,
              hide: true,
              labelWidth: 150,
            },
            {
              label: "序号",
              prop: "sort",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
            },
            {
              label: "菜品分类名称",
              prop: "dishesType",
              type: "input",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              search:true,
            },
            {
              label: "状态",
              prop: "enableFlag",
              type: "select",
              addDisplay: true,
              editDisplay: true,
              viewDisplay: true,
              labelWidth: 150,
              search:true,
              dicData: DIC.enableFlag,
              formatter: (row,value,label,column) => {
                if(value == 1){
                  return "启用"
                } else {
                  return "停止"
                }
              }
            }
          ]
        },
        importOption: {
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          column: [
            {
              label: '附件上传',
              prop: 'dishes',
              type: 'upload',
              loadText: '附件上传中，请稍等',
              span: 24,
              propsHttp: {
                res: 'data'
              },
              tip: '只能上传xls/xlsx文件，且不超过500kb',
              action: '/api/service/rabbit-liancan/schoolGoodsPublic/importDishes'
            },
          ]
        },
        data: [],
        dishesData: [],
        importVisible: false,
        importData: [],
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      // permissionList() {
      //   return {
      //     addBtn: this.vaildData(this.permission.schoolGoods_add, false),
      //     viewBtn: this.vaildData(this.permission.schoolGoods_view, false),
      //     delBtn: this.vaildData(this.permission.schoolGoods_delete, false),
      //     editBtn: this.vaildData(this.permission.schoolGoods_edit, false)
      //   };
      // },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      // if (this.userInfo.userType === 'canteen'){
      //   this.option.column[0].search = false;
      //   this.option.column[0].hide = true;
      // }
      // // 设置上传接口头部信息
      // const access_token = JSON.parse(localStorage.getItem("rabbit-liancan-userInfo")).content.access_token
      // this.uploadHeaders = {
      //     'Authorization':'Bearer ' + access_token,
      // };
    },
      watch: {
          // 'form.type'() {
          //     let type = this.form.type;
          //     //食材
          //     if(type == "1314471996665397249" || type == "1366636175794667521"){
          //     // if(type == "1315498192049037313"){
          //       this.option.column.forEach(then=>{
          //         // then.prop == 'biddingTypeId'
          //         if(then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
          //             then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
          //           then.addDisplay = true
          //         }
          //       })
          //     }
          //     //燃料
          //     if(type == "1316561056074649602" || type == "1366636571036516354"){
          //     // if(type == "1315613115907416066"){
          //       this.option.column.forEach(then=>{
          //         if(then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
          //             then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
          //           then.addDisplay = false;
          //         }
          //       })
          //     }
          // },
      },
    methods: {
      menuClick(idx) {
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.query = {};
          this.page.currentPage = 1;
          this.onLoad(this.page);
        }
        if (idx == 1){
          this.query = {};
          this.page.currentPage = 1;
          this.dishesOnLoad(this.page);
        }
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        if (row.useFlag === 1) {
          this.$message.warning("已被选用不可删除");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        if (this.selectionList[0].useFlag === 1) {
          this.$message.warning("已被选用不可删除");
          return;
        }
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      // beforeOpen(done, type) {
      //     if(this.form.type == "1314471996665397249" || type == "1366636175794667521" ){
      //         // if(type == "1315498192049037313"){
      //         this.option.column.forEach(then=>{
      //           if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
      //               then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
      //             then.addDisplay = true
      //           }
      //         })
      //     }
      //     //燃料
      //     if(this.form.type == "1316561056074649602" || type == "1366636571036516354"){
      //         // if(type == "1315613115907416066"){
      //         this.option.column.forEach(then=>{
      //           if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
      //               then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
      //             then.addDisplay = false
      //           }
      //         })
      //     }
      //   done();
      // },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      dishesRowSave(row, loading, done) {
        addDishes(row).then(() => {
          loading();
          this.dishesOnLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      dishesRowUpdate(row, index, loading, done) {
        updateDishes(row).then(() => {
          loading();
          this.dishesOnLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      dishesRowDel(row) {
        if (row.useFlag === 1) {
          this.$message.warning("已被选用不可删除");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeDishes(row.id);
          })
          .then(() => {
            this.dishesOnLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      dishesOnLoad(page, params = {}) {
        this.dishesLoading = true;
        getListDishes(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.dishesPage.total = data.total;
          this.dishesData = data.records;
          this.dishesLoading = false;
          this.selectionClear();
        });
      },
      exportTemplate() {
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportTemplate(this.query).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '公共菜品库模板.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      importOnclick() {
        this.importVisible = true;
        this.importData = {};
        this.importOnLoad();
      },
      uploadAfter(res, done, loading, column) {
        loading();
        if(res.code != 200){
          this.$message.error(res.msg);
          return;
        }

        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "导入成功!"
        });
        this.importVisible = false
      },
      uploadBefore(file, done, loading, column) {
        let str = file.name;
        str = str.substring(str.lastIndexOf("\.") + 1, str.length);
        if(str != "xlsx" && str != "xls"){
          loading();
          this.$message.error("文件格式错误,只能上传xlsx, xls");
          return false;
        }
        done();
        return true;
      },
      uploadError(error, column) {
        this.$message.success('上传失败')
        /*        console.log(">>>>>>>>>上传失败",error, column)*/
      },
      importOnLoad(){
        this.importData = {};
      },
    }
  };
</script>

<style lang="scss" scoped>
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    ::v-deep div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>

