<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="scope">
        <el-button size="mini" type="text" @click="lookDetail(scope.row)">详情
        </el-button>
      </template>
    </avue-crud>

    <el-dialog title="窗口详情" :visible.sync="biddingVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <avue-form ref="myBiddingNoticForm" :option="datailOption" v-model="myBiddingNoticForm">
<!--        <template slot-scope="scope" slot="goodsList">
          <avue-crud :option="detailOption"
                     :table-loading="detailTableLoading"
                     :data="detailData"
                     :page="detailPage"
                     :before-open="beforeOpen"
                     v-model="detailForm"
                     ref="detailForm"
                     @search-change="mySearchChange"
                     @search-reset="mySearchReset"
                     @selection-change="mySelectionChange"
                     @current-change="myCurrentChange"
                     @size-change="mySizeChange"
                     @on-load="myBiddingNoticListLoad">
          </avue-crud>
        </template>-->
      </avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail} from "@/api/statistics/windowStatus";
  import {mapGetters} from "vuex";
  // import AvueUeditor from 'avue-plugin-ueditor';
  var DIC = {
    onlineStatus: [{
      label: '',
      value: ""
    },{
      label: '下线',
      value: "0"
    },{
      label: '在线',
      value: "1"
    }],
    startStopStatus: [{
      label: '',
      value: ""
    },{
      label: '休息',
      value: "0"
    },{
      label: '营业',
      value: "1"
    }],
    consumMachineType: [{
      label: '无',
      value: "0"
    },{
      label: '固定机',
      value: "1"
    },{
      label: '取餐机',
      value: "2"
    },{
      label: '消费机',
      value: "3"
    },],
    sex: [{
      label: ' ',
      value: ""
    },{
      label: '男',
      value: "1"
    },{
      label: '女',
      value: "2"
    },]
  };
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        biddingVisible: false,
        myBiddingNoticForm:{},
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          delBtn: false,
          selection: true,
          editBtn:false,
          addBtn: false,
          searchSpan:130,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "窗口号",
              prop: "number",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "",
                trigger: "blur"
              }],
            },
            {
              label: "营业状态",
              prop: "startStopStatus",
              type: "input",
              span: 24,
              dicData: DIC.startStopStatus,
              rules: [{
                required: true,
                message: "",
                trigger: "blur"
              }],
            },
            {
              label: "设备状态",
              prop: "onlineStatus",
              type: "input",
              span: 24,
              dicData: DIC.onlineStatus,
              rules: [{
                required: true,
                message: "",
                trigger: "blur"
              }],
            },
            {
              label: "设备种类",
              prop: "consumMachineType",
              type: "input",
              span: 24,
              dicData: DIC.consumMachineType,
              rules: [{
                required: true,
                message: "",
                trigger: "blur"
              }],
            },
            {
              label: "操作人员",
              prop: "wordPersonnelName",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "",
                trigger: "blur"
              }],
            },
            {
              label: "所属营业网点",
              prop: "searchBusinessOutletsId",
              type: "select",
              search: true,
              hide:true,
              rules: [{
                required: true,
                message: "请输入所属营业网点",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              cascaderItem: ['searchWindowGroupId'],
              props: {
                label: "name",
                value: "id"
              },
              cascaderChange: true
            },
            {
              label: "所属窗口组",
              prop: "searchWindowGroupId",
              type: "select",
              search: true,
              hide:true,
              rules: [{
                required: true,
                message: "请输入所属窗口组",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-statistics/windowStatus/getWindowGroupList?outletsId={{key}}",
              props: {
                label: "name",
                value: "id"
              },
              cascaderItem: ['windowId'],
              cascaderChange: true
            },
            {
              label: "所属营业状态",
              prop: "searchStartStopStatus",
              type: "select",
              search: true,
              hide: true,
              rules: [{
                required: true,
                message: "请输入所属营业状态",
                trigger: "blur"
              }],
              dicData: DIC.startStopStatus
            },
            {
              label: "所属设备状态",
              prop: "seachOnlineStatus",
              type: "select",
              search: true,
              hide: true,
              rules: [{
                required: true,
                message: "请输入所属设备状态",
                trigger: "blur"
              }],
              dicData: DIC.onlineStatus
            },
            {
              label: "所属设备实时种类",
              prop: "seachConsumMachineType",
              type: "select",
              search: true,
              hide:true,
              rules: [{
                required: true,
                message: "请输入设备实时种类",
                trigger: "blur"
              }],
              dicData: DIC.consumMachineType
            }
          ]
        },
        datailOption : {
          //card: true,
          emptyBtn: false,
          submitBtn: false,
          labelWidth: 150,
          group: [
            {
              label: '基本信息',
              collapse: false,
              prop: 'group1',
              column: [{
                label: '所属网点名称',
                prop: 'businessOutletsName',
                type: 'input',
                disabled: true,
                rules: [
                  {
                    required: true,
                    message: '',
                    trigger: 'blur'
                  }
                ]
              },
                {
                  label: '所属窗口组',
                  prop: 'windowGroupName',
                  type: 'input',
                  dicUrl: '/api/service/rabbit-system/dict/dictionary?code=school_type',
                  props: {
                    label: 'dictValue',
                    value: 'dictKey'
                  },
                  disabled: true,
                  rules: [
                    {
                      required: true,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '窗口排序',
                  prop: 'sort',
                  type: 'input',
                  //dicFlag: false,
                  dicUrl: '/api/service/rabbit-system/region/getProvince',
                  props: {
                    label: 'regionName',
                    value: 'id'
                  },
                  disabled: true,
                  rules: [
                    {
                      required: true,
                      message: '',
                      trigger: 'blur'
                    }
                  ],
                  filterable: true,
                  searchFilterable: true,
                  cascaderItem: ['city']
                },
                {
                  label: '窗口编号',
                  prop: 'number',
                  type: 'input',
                  dicFlag: false,
                  dicUrl: '/api/service/rabbit-system/region/getCity/{{key}}',
                  props: {
                    label: 'regionName',
                    value: 'id'
                  },
                  disabled: true,
                  rules: [
                    {
                      required: true,
                      message: '',
                      trigger: 'blur'
                    }
                  ],
                  filterable: true,
                  searchFilterable: true,
                  cascaderItem: ['area']
                },
                {
                  label: '窗口名称',
                  prop: 'name',
                  type: 'input',
                  dicFlag: false,
                  dicUrl: '/api/service/rabbit-system/region/getArea/{{key}}',
                  props: {
                    label: 'regionName',
                    value: 'id'
                  },
                  disabled: true,
                  rules: [
                    {
                      required: true,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '窗口状态',
                  prop: 'startStopStatus',
                  type: 'select',
                  dicData: DIC.startStopStatus,
                  disabled: true,
                  rules: [
                    {
                      required: true,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                }
              ]}, {
              label: '运行信息',
              collapse: false,
              prop: 'group1',
              column: [{

                label: '营业状态',
                prop: 'startStopStatus',
                type: 'select',
                disabled: true,
                dicData: DIC.startStopStatus,
                rules: [
                  {
                    required: false,
                    message: '',
                    trigger: 'blur'
                  }
                ]
              },
                {
                  label: '操作人员',
                  prop: 'wordPersonnelName',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '设备状态',
                  prop: 'onlineStatus',
                  type: 'select',
                  disabled: true,
                  dicData: DIC.onlineStatus,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '设备实时种类',
                  prop: 'consumMachineType',
                  type: 'select',
                  disabled: true,
                  dicData: DIC.consumMachineType,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },

              ]}, {
              label: '设备信息',
              collapse: false,
              prop: 'group1',
              column: [
                {

                  label: '人脸识别IP',
                  prop: 'faceRecognitionIp',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '设备码',
                  prop: 'equipmentCode',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '消费机主板IP',
                  prop: 'consumerComputerIp',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '设备默认种类',
                  prop: 'consumMachineType',
                  type: 'select',
                  disabled: true,
                  dicData: DIC.consumMachineType,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                }
              ]
            }, {
              label: '限窗信息',
              collapse: false,
              prop: 'group1',
              column: [
                {

                  label: '限定部门',
                  prop: 'deptSettingName',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '限定用餐类别',
                  prop: 'diningTypeName',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '限定性别',
                  prop: 'sex',
                  type: 'select',
                  disabled: true,
                  dicData: DIC.sex,
                  rules: [
                    {
                      required: false,
                      message: '',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '限定人员属性',
                  prop: 'attributeName',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: ' ',
                      trigger: 'blur'
                    }
                  ]
                },
                {
                  label: '限定民族',
                  prop: 'nation',
                  type: 'input',
                  disabled: true,
                  rules: [
                    {
                      required: false,
                      message: ' ',
                      trigger: 'blur'
                    }
                  ]
                }
              ]
            }
          ]
        },
      };
    },

    computed: {
      ...mapGetters(["permission", 'userInfo']),
      permissionList() {
        return {
          viewBtn: this.vaildData(this.permission.serviceInformation_view, false),
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      lookDetail(row){
        this.biddingVisible = true;
        console.info("=============="+row);
        getDetail(row.id).then(res => {
          let data = res.data.data;
          let windowLimitSettingVOList = data.windowLimitSettingVOList;
          data.deptSettingName = ' ';
          data.diningTypeName = ' ';
          data.sex = null;
          data.attributeName = ' ';
          data.nation = ' ';
          if(!!windowLimitSettingVOList && windowLimitSettingVOList instanceof Array){
            for(let i=0; i<windowLimitSettingVOList.length; i++){
              if(windowLimitSettingVOList[i].deptSettingName != ''){
                data.deptSettingName += windowLimitSettingVOList[i].deptSettingName + " ";
              }
              if(windowLimitSettingVOList[i].diningTypeName != ''){
                data.diningTypeName += windowLimitSettingVOList[i].diningTypeName + " ";
              }
              if(windowLimitSettingVOList[i].sex != null){
                data.sex = windowLimitSettingVOList[i].sex;
              }
              if(windowLimitSettingVOList[i].attributeName){
                data.attributeName += windowLimitSettingVOList[i].attributeName + " ";
              }
              if(windowLimitSettingVOList[i].nation){
                data.nation += windowLimitSettingVOList[i].nation + " ";
              }
            }
          }
          this.myBiddingNoticForm = res.data.data;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
