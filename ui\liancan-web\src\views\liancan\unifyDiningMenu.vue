<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
     <!-- <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.unifyDiningMenu_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>-->
      <template slot="menu" slot-scope="scope">
        <el-button v-if="permission.unifyDiningMenu_push && scope.row.status === '0'" type="text" size="mini" icon="el-icon-thumb" @click.stop="publish(scope.row)">发布</el-button>
        <el-button v-if="permission.unifyDiningMenu_delete && scope.row.status === '0'" type="text" size="mini" icon="el-icon-delete" @click.stop="rowDel(scope.row)">删除</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, getDict} from "@/api/liancan/unifyDiningMenu";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          indexLabel: '序号',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          delBtn: false,
          selection: true,
          column: [
            {
              label: "所属食堂",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "displayName",
                value:"id"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "营业网点",
              prop: "outletsId",
              type: "select",
              search:true,
                props: {
                    label: "name",
                    value: "id"
                },
                dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
                rules: [{
                    required: true,
                    message: "请输入网点id",
                    trigger: "blur"
                }],
            },
              {
                  label: "早餐",
                  prop: "brekker",
                  type: "checkbox",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  dicData: [],
                  rules: [{
                      required: false,
                      message: "请输入结束日期",
                      trigger: "blur"
                  }],
                  dicFlag: false,
                  span: 24,
                  //all: true,
                  hide: true,
              },
              {
                  label: "中餐",
                  prop: "lunch",
                  type: "checkbox",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  dicData: [],
                  rules: [{
                      required: false,
                      message: "请输入结束日期",
                      trigger: "blur"
                  }],
                  dicFlag: false,
                  span: 24,
                  //all: true,
                  hide: true,
              },
              {
                  label: "晚餐",
                  prop: "supper",
                  type: "checkbox",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  dicData: [],
                  rules: [{
                      required: false,
                      message: "请输入结束日期",
                      trigger: "blur"
                  }],
                  dicFlag: false,
                  span: 24,
                  hide: true,
              },
              {
                  label: "夜餐",
                  prop: "night",
                  type: "checkbox",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  dicData: [],
                  rules: [{
                      required: false,
                      message: "请输入结束日期",
                      trigger: "blur"
                  }],
                  dicFlag: false,
                  span: 24,
                  hide: true,
              },
              {
              label: "菜谱名称",
              prop: "menuName",
              type: "input",
              search:true,
              rules: [{
                required: true,
                message: "请输入菜谱名称",
                trigger: "blur"
              }],
            },
/*            {
              label: '有效期',
              prop: 'validPeriodDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
              {
                  label: "有效期",
                  prop: "validPeriod",
                  type: 'daterange',
                  format: 'yyyy-MM-dd',
                  valueFormat: 'yyyy-MM-dd HH:mm:ss',
                  hide: true,
                  // pickerOptions: {
                  //     disabledDate(time) {
                  //         return time.getTime() < Date.now() - 86400000;
                  //     }
                  // }
              },
              {
                  label: "有效期",
                  prop: "validPeriodView",
                  type: 'input',
                  display: false,
              },
              {
                  label: "发布状态",
                  prop: "status",
                  type: 'select',
                  display: false,
                  search:true,
                  dicData: [
                      {
                          label: "未发布",
                          value: "0"
                      },
                      {
                          label: "已发布",
                          value: "1"
                      }
                  ],
              },
              {
                  label: "编辑人",
                  prop: "updateUser",
                  type: "input",
                  dicUrl: '/api/service/rabbit-user/user-list',
                  props: {
                      label: "realName",
                      value: "id"
                  },
                  display: false,
              },
              {
                  label: "编辑时间",
                  prop: "updateTime",
                  type: "input",
                  display: false,
              },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.unifyDiningMenu_add, false),
          viewBtn: this.vaildData(this.permission.unifyDiningMenu_view, false),
          delBtn: this.vaildData(this.permission.unifyDiningMenu_delete, false),
          editBtn: this.vaildData(this.permission.unifyDiningMenu_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    watch: {
        'form.outletsId'() {
            let outletsId = this.form.outletsId;
            if (outletsId != null && outletsId != '') {
                this.getDict(outletsId);
            }
        }
    },
    created() {
      if (this.userInfo.userType === 'canteen') {
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
      }
    },
    methods: {
      getDict(outletsId) {
          getDict(0,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("brekker");
              this.option.column[index].dicData = res.data.data;
          });
          getDict(1,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("lunch");
              this.option.column[index].dicData = res.data.data;
          });
          getDict(2,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("supper");
              this.option.column[index].dicData = res.data.data;
          });
          getDict(3,outletsId).then(res => {
              const index = this.$refs.crud.findColumnIndex("night");
              this.option.column[index].dicData = res.data.data;
              /*DIC.night.forEach(s => {
                  DIC.night.$remove(s);
              })*/
          });
      },
      rowSave(row, loading, done) {
        row.recipeType = "0"
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      publish(row) {
          row.status = '1';
          update(row).then(() => {
              this.onLoad(this.page);
              this.$message({
                  type: "success",
                  message: "操作成功!"
              });
          }, error => {
              window.console.log(error);
          });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          //this.getDict(this.form.outletsId);
          /*getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });*/
        }
          if (["add"].includes(type)) {
              const index = this.$refs.crud.findColumnIndex("brekker");
              this.option.column[index].dicData = [];
              const index2 = this.$refs.crud.findColumnIndex("lunch");
              this.option.column[index2].dicData = [];
              const index3 = this.$refs.crud.findColumnIndex("supper");
              this.option.column[index3].dicData = [];
              const index4 = this.$refs.crud.findColumnIndex("night");
              this.option.column[index4].dicData = [];
          }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {

        this.query = params;
        if (params.validPeriodDate != '' && params.validPeriodDate != null && params.validPeriodDate != undefined) {
          params.startDate = params.validPeriodDate[0];
          params.endDate = params.validPeriodDate[1];
        }
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        params.recipeType = "0";
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
        /*if(){

        }*/
      }
    }
  };
</script>

<style>
</style>
