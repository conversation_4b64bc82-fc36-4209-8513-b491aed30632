<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.face_equipment_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
      <template slot="status" slot-scope="{row}">
        <el-tag v-if="row.status == '0'" type="success">在线</el-tag>
        <el-tag v-if="row.status == '1'" type="danger">离线</el-tag>
      </template>
      <template slot="menu" slot-scope="{row}">
        <el-button  size="mini" icon="el-icon-view" type="text" @click="personnelManage(row)">人员管理</el-button>
        <el-button  size="mini" icon="iconfont iconicon_airplay" type="text" @click="reboot(row)">重启设备</el-button>
      </template>
    </avue-crud>
    <el-dialog :title="`${this.windowDeviceName}`" :visible.sync="deviceVisible" :append-to-body="true" @close="successfulBiddingFormClose"
               width="60%">
      <avue-crud :option="deviceOption" :table-loading="deviceTableLoading" :data="deviceData" :page="devicePage"
                 :before-open="beforeOpen" v-model="deviceForm" ref="deviceForm" @search-change="deviceSearchChange"
                 @search-reset="deviceSearchReset" @selection-change="deviceSelectionChange" @current-change="deviceCurrentChange"
                 @size-change="deviceSizeChange" @on-load="onLoadDevice">
        <template slot="menuLeft">
          <el-button v-if="this.delDownStatus == 1" class="filter-item" size="small" type="primary" icon="el-icon-upload"
                     @click="downPersonnel()">下传</el-button>
        </template>
        <template slot="menuLeft">
          <el-button v-if="this.delDownStatus == 2" class="filter-item" size="small" type="primary" icon="el-icon-delete"
                     @click="deleteDown">删除</el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <el-dialog :title="`人员管理`" :visible.sync="personnelVisible" :append-to-body="true" @close="closePersonnelDown"
               width="80%">
      <avue-crud :option="personnelOption" :table-loading="personnelLoading" :data="personnelData" :page="personnelPage"
                 :permission="permissionList" :before-open="beforeOpen" v-model="personnelForm" ref="personnelForm"
                 @search-change="personnelSearchChange" @search-reset="personnelSearchReset" @selection-change="personnelSelectionChange"
                 @current-change="personnelCurrentChange" @size-change="personnelSizeChange" >
        <template slot="menuLeft">
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-upload" @click="checkPersonnelDownload">勾选下传</el-button>
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-upload" @click="allCheckDownload2">全部下传</el-button>
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-delete" @click="checkPersonnelDelete">勾选删除</el-button>
          <el-button class="filter-item" size="small" type="primary" icon="el-icon-delete" @click="allCheckDelete2">全部删除</el-button>
          <el-button v-if="addPersonFlag === 1" class="filter-item" size="small" type="success" @click="addPerson">新增人员</el-button>
        </template>
        <template slot="menu" slot-scope="{row}">
          <el-button size="small" type="primary" icon="el-icon-upload" @click="downPersonnel2(row)">下传</el-button>
          <el-button size="small" type="primary" icon="el-icon-delete" @click="deletePersonnel2(row)">删除</el-button>
        </template>
        <template slot="avatar" slot-scope="{row}">
          <el-image style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-dialog :title="`新增人员`" :visible.sync="addPersonVisible" :append-to-body="true"  @close="closeAddPerson" width="80%">
        <avue-form :option="addPersonOption" v-model="addPersonForm" @submit="handleAddPerson"></avue-form>
    </el-dialog>
  </basic-container>
</template>

<script>
import {
  getPersonnelData,
  checkDownFace,
  allDownFace,
  saveCheckDelete,
  allDelete,
  down,
  deleteFace,
  reboot
} from "@/api/personnel/systemPersonnel";
import {
  getDeviceList,
  getWindowDeviceList
} from "@/api/business/device/index";
import {
  getDeptTree
} from "@/api/setting/dept/systemDeptSetting";
import {
  getList, getDetail, add, update, remove
} from "@/api/attendance/faceDevice/faceEquipment";
import {
  getDeptAgencyType,
} from "@/api/system/dept";
import {addForDeliverySign} from "@/api/supplier/supplierAccount"
import {
  mapGetters
} from "vuex";
import website from '@/config/website';
const DIC = {
  sex: [{
    label: '男',
    value: "1"
  },
    {
      label: '女',
      value: "2"
    }
  ],
  VAILD: [{
    label: '学生',
    value: '1'
  }, {
    label: '教职工',
    value: '2'
  }, {
    label: '供应商',
    value: '3'
  }
  ],
  PERSONNEL: [{
    label: '停用',
    value: '0'
  }, {
    label: '启用',
    value: '1'
  }, {
    label: '注销',
    value: '2'
  }, {
    label: '锁定',
    value: '3'
  }],
  OPEN: [{
    label: '开通伙食费钱包',
    value: '1'
  }],
  ONLIN: [{
    label: '下线',
    value: '0'
  }, {
    label: '在线',
    value: '1'
  }],
  DOWN: [{
    label: '未下传',
    value: '0'
  }, {
    label: '已下传',
    value: '1'
  }]
}
export default {
  data() {
    return {
      addPersonVisible: false,
      addPersonFlag: 0,
      tabPosition: '0',
      activeIdx: 0,
      messList: ['按人员列表显示(默认)', '按窗口设备显示'],
      form: {},
      query: {},
      importForm: {},
      searchForm: {},
      addPersonForm: {},
      editForm: {},
      deviceForm: {},
      personnelForm: {},
      personnelDevice: {},
      loading: true,
      deviceTableLoading: true,
      windowDeviceLoading: true,
      personnelLoading: true,
      importDialogVisible: false,
      batchEditVisible: false,
      deviceVisible: false,
      personnelVisible: false,
      dialogVisible: false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      devicePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      windowDevicePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      personnelPage: {
        pageSizes: [ 10, 20, 30, 40, 50, 100, 500],
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      selectionDeviceList: [],
      personnelSelectionList: [],
      deviceData: [],
      windowDeviceData: [],
      personnelData: [],
      outletsId: undefined,
      personnelId: undefined,
      deviceSn:undefined,
      delDownStatus: undefined,
      windowDeviceName: undefined,
      dialogImageUrl: undefined,
      personnelStatus: undefined,
      windowId: undefined,
      deviceCode: undefined,
      personnelSelectionList:[],
      deviceData:[],
      windowDeviceData:[],
      personnelData:[],
      outletsId:undefined,
      personnelId:undefined,
      delDownStatus:undefined,
      downStatus:undefined,
      delDownStatus2:undefined,
      windowDeviceName:undefined,
      dialogImageUrl:undefined,
      personnelStatus:undefined,
      windowId:undefined,
      deviceCode:undefined,
      modeType:undefined,
      searchForm: {
        outletsId: '',
      },
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        selection: true,
        viewBtn:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          /*          {
                      label: "设备编号",
                      prop: "deviceId",
                      type: "input",
                      rules: [{
                        required: true,
                        message: "请输入设备编号",
                        trigger: "blur"
                      }],
                    },*/
          {
            label: "设备名称",
            prop: "deviceName",
            type: "input",
            rules: [{
              required: true,
              message: "请输入设备名称",
              trigger: "blur"
            }],
          },
          {
            label: "IP地址",
            prop: "ipAddress",
            type: "input",
            rules: [{
              required: true,
              message: "请输入IP地址",
              trigger: "blur"
            }],
          },
          {
            label: "设备SN编号",
            prop: "deviceKey",
            type: "input",
            rules: [{
              required: true,
              message: "请输入设备SN编号",
              trigger: "blur"
            }],
          },
          {
            label: "安装位置",
            prop: "position",
            type: "input",
            rules: [{
              required: true,
              message: "请输入安装位置",
              trigger: "blur"
            }],
          },
          {
            label: "设备状态",
            prop: "status",
            type: "select",
            slot:true,
            dicData: [{
              value: "0",
              label: "在线"
            },
              {
                value: "1",
                label: "离线"
              }
            ],
            addDisplay:false,
            editDisplay: false,
          },
          {
            label: "是否用于送货签到",
            prop: "deliverySignFlag",
            type: "select",
            // slot:true,
            dicData: [{
                value: 0,
                label: "否"
              },
              {
                value: 1,
                label: "是"
              }
            ],
            addDisplay:true,
            editDisplay: true,
          },
          {
            label: "租户ID",
            prop: "tenantId",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建人",
            prop: "createUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建部门",
            prop: "createDept",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改人",
            prop: "updateUser",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "修改时间",
            prop: "updateTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
/*          {
            label: "状态",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },*/
          {
            label: "是否已删除",
            prop: "isDel",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "乐观锁",
            prop: "version",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
        ]
      },
      windowDeviceoption: {
        /*    height: 'auto',
            calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        labelWidth: 150,
        dialogWidth: "70%",
        column: [{
          label: "主键",
          prop: "id",
          type: "input",
          addDisplay: false,
          editDisplay: false,
          viewDisplay: false,
          hide: true,
        },
          {
            label: "窗口组",
            prop: "groupName",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
          },
          {
            label: "窗口号",
            prop: "number",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 60,
          },
          {
            label: "窗口名称",
            prop: "windowName",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
          },
          {
            label: "人脸识别IP",
            prop: "faceRecognitionIp",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 120,
          },
          {
            label: "消费机主板IP",
            prop: "consumerComputerIp",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 120,
          },
          {
            label: "设备码",
            prop: "equipmentCode",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 200,
          },
          {
            label: "设备默认种类",
            prop: "equipmentType",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
            width: 120,
          },
          {
            label: "SD卡使用情况",
            prop: "sdCard",
            type: "input",
            span: 24,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
          },
          {
            label: '是否在线',
            prop: 'onlineStatus',
            type: "radio",
            //slot: true,
            dicData: DIC.ONLIN,
            width: 90,
          },
          {
            label: "已下传人员照片数量",
            prop: "downCount",
            type: "input",
            span: 24,
          },
          {
            label: "未下传人员照片数量",
            prop: "notDownCount",
            type: "input",
            span: 24,
          },
        ]
      },
      searchOption: {
        emptyBtn: false,
        submitBtn: false,
        column: [{
          label: "营业网点",
          prop: "outletsId",
          type: "select",
          //dicFlag: false,
          multiple: false,
          dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
          props: {
            label: "name",
            value: "id"
          },
          disabled: false,
        }, ]
      },
      deviceOption: {
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        labelWidth: 150,
        dialogWidth: "70%",
        menu: false,
        column: [{
          label: "窗口设备",
          prop: "windowDevice",
          type: "input",
          span: 24,
        }, {
          label: "状态",
          prop: "deviceStatus",
          type: "input",
          span: 24,
        }]
      },
      personnelOption: {
        height:'auto',
        maxHeight:'900',
        /*    calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: true,
        labelWidth: 150,
        dialogWidth: "70%",
        column: [{
          label: "主键",
          prop: "id",
          type: "input",
          addDisplay: false,
          editDisplay: false,
          viewDisplay: false,
          hide: true,
        },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            span: 24,
            search: true,
            rules: [{
              required: true,
              message: "请输入姓名",
              trigger: "blur"
            }],
          },
          {
            label: '性别',
            prop: 'sex',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            rules: [{
              required: true,
              message: '请选择性别',
              trigger: 'blur'
            }]
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            search: true,
            rules: [{
              required: true,
              message: "请输入学号/工号",
              trigger: "blur"
            }],
          },
          {
            label: "用餐类别",
            prop: "mealsType",
            type: "select",
            dicUrl: "/api/service/rabbit-liancan/diningType/dict",
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
            search: true,
            rules: [{
              required: true,
              message: "请输入用餐类别",
              trigger: "blur"
            }],
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            minWidth: 120,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
            props: {
              label: "title",
              value: "id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            slot: true,
            search: true,
            hide: true,
            rules: [{
              required: true,
              message: "请输入部门",
              trigger: "click"
            }]
          },
          {
            label: '人员类别',
            prop: 'personnelType',
            type: "select",
            //slot: true,
            dicData: DIC.VAILD,
            search: true,
            rules: [{
              required: true,
              message: '请选择人员类别',
              trigger: 'blur'
            }]
          },
          /*            {
                        label: "人员属性",
                        prop: "attribute",
                        span: 20,
                        hide: true,
                        type: "checkbox",
                        dicData: DIC.VAILD,
                        rules: [{
                          required: true,
                          message: "请选择人员属性",
                          trigger: "blur"
                        }],
                        mock: {
                          type: 'dic',
                        },

                      },*/
          {
            label: "人员属性",
            prop: "attribute",
            type: "select",
            search: true,
            dicUrl: '/api/service/rabbit-liancan/userRole/dict',
            props: {
              res: "data",
              label: "name",
              value: "id"
            },
          },
          {
            label: "人员照片",
            prop: "avatar",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span: 24,
            slot: true,
            rules: [{
              required: true,
              message: "请上传人员照片",
              trigger: "blur"
            }],
          },
          {
            label: "人员状态",
            prop: "status",
            type: "radio",
            dicData: DIC.PERSONNEL,
            rules: [{
              required: true,
              message: "请输入人员状态",
              trigger: "blur"
            }],
          },
          {
            label: "一卡通卡号",
            prop: "cardId",
            type: "input",
            rules: [{
              required: true,
              message: "请输入一卡通卡号",
              trigger: "blur"
            }],
          },
          {
            label: "有效期",
            prop: "effectiveTime",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            rules: [{
              required: true,
              message: "请选择有效期",
              trigger: "blur"
            }],
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() < Date.now();
              },
            }
          },
          {
            label: "下传状态",
            prop: "downPerStatus",
            span: 20,
            type: "select",
            dicData: DIC.DOWN,
            search: true,
            mock: {
              type: 'dic',
            },

          },
        ]
      },
      addPersonOption:{
          submitBtn:true,
          column: [{
            label: '供应商公司',
            prop: 'supplierCompanyId',
            type: 'select',
            props: {
              label: 'name',
              value: 'id'
            },
            cascader: ['supplierId'],
            dicUrl: '/api/service/rabbit-supplier/supplierCompany/all/list',
            rules: [
              {
                required: true,
                message: '请选择供应商公司',
                trigger: 'blur'
              }
            ]
          },
          {
            label: '供应商人员',
            prop: 'supplierId',
            type: 'select',
            props: {
              label: 'contactName',
              value: 'id'
            },
            dicUrl: '/api/service/rabbit-supplier/supplierAccount/listForDeliverySignByDeptId?deptId={{key}}',
            rules: [
              {
                required: true,
                message: '请选择供应商人员',
                trigger: 'blur'
              }
            ]
          }]
       },
    }
  },
  computed: {
    ...mapGetters(["permission", 'userInfo']),
    permissionList() {
      return {
        /*          addBtn: this.vaildData(this.permission.system_personnel_add, false),
                  viewBtn: this.vaildData(this.permission.system_personnel_view, false),
                  delBtn: this.vaildData(this.permission.system_personnel_delete, false),
                  editBtn: this.vaildData(this.permission.system_personnel_edit, false)*/
      };
    },
    ids() {
      let ids = [];
      this.personnelSelectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    deviceIds() {
      let deviceIds = [];
      this.selectionDeviceList.forEach(ele => {
        deviceIds.push(ele.windowId);
      });
      return deviceIds.join(",");
    },
  },

  created() {
    getDeptAgencyType().then(res =>{
      if (res.data.data.applicationMode === '3'){
        this.modeType = '1';
        this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        this.option.menu = false;
        this.windowDeviceoption.menu = false;
      }else if (res.data.data.applicationMode === '0'){
        this.modeType = '1';
        this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        this.option.menu = false;
        this.windowDeviceoption.menu = false;
      }else {
        this.modeType = '2';
      }
    })
    const {
      messName
    } = this.$route.query
    if (messName) {
      this.messList.forEach((item, idx) => {
        item == messName ? this.activeIdx = idx : ''
      })
      console.log(this.activeIdx)
    }
    getDeptTree().then(res => {
      const index = this.$refs.crud.findColumnIndex("deptId");
      this.option.column[index].dicData = res.data.data;
    });
  },
  methods: {
    rowSave(row, loading, done) {
      row.type = "1";
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.type = "1";
      // this.query.companyId = "1553993875767554050";
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    personnelSearchReset() {
      this.query = {};
      if(this.addPersonFlag === 1){
        var parm = { personnelType:'3' }//供应商人员
        this.onLoadPersonnel(this.personnelPage, parm)
      } else {
        this.onLoadPersonnel(this.personnelPage)
      }
    },
    deviceSearchReset() {
      this.query = {};
      this.onLoadDevice(this.devicePage);
    },
    windowDeviceSearchReset() {
      this.query = {};
      this.onLoadWindowDevice(this.windowDevicePage);
    },
    personnelSearchChange(params, done) {
      this.query = params;
      this.personnelPage.currentPage = 1
      if(this.addPersonFlag === 1){
        params.personnelType = '3'//供应商人员
        this.onLoadPersonnel(this.personnelPage, parm)
      } else {
        this.onLoadPersonnel(this.personnelPage, params);
      }
      done();
    },
    windowDeviceSearchChange(params, done) {
      this.query = params;
      this.windowDevicePage.currentPage = 1
      this.onLoadWindowDevice(this.windowDevicePage, params);
      done();
    },
    deviceSearchChange(params, done) {
      this.query = params;
      this.devicePage.currentPage = 1
      this.onLoadDevice(this.devicePage, params);
      done();
    },
    personnelSelectionChange(list) {
      this.personnelSelectionList = list;
    },
    windowDeviceSelectionChange(list) {
      this.selectionList = list;
    },
    deviceSelectionChange(list) {
      this.selectionDeviceList = list;
    },
    personnelSelectionClear() {
      this.selectionList = [];
      this.$refs.personnelForm.toggleSelection();
    },
    windowDeviceSelectionClear() {
      this.selectionList = [];
      this.$refs.windowDeviceForm.toggleSelection();
    },
    deviceSelectionClear() {
      this.selectionList = [];
      this.$refs.deviceForm.toggleSelection();
    },
    personnelCurrentChange(currentPage) {
      this.personnelPage.currentPage = currentPage;
    },
    windowDeviceCurrentChange(currentPage) {
      this.windowDevicePage.currentPage = currentPage;
    },
    deviceCurrentChange(currentPage) {
      this.devicePage.currentPage = currentPage;
    },
    personnelSizeChange(pageSize) {
      this.personnelPage.pageSize = pageSize;
    },
    windowDeviceSizeChange(pageSize) {
      this.windowDevicePage.pageSize = pageSize;
    },
    deviceSizeChange(pageSize) {
      this.devicePage.pageSize = pageSize;
    },
    onLoadDevice(page, params = {}) {
      this.deviceTableLoading = true;
      getDeviceList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.outletsId).then(res => {
        const data = res.data.data;
        this.devicePage.total = data.total;
        this.deviceData = data.records;
        this.deviceTableLoading = false;
        this.deviceSelectionClear();
      });
    },
    onLoadWindowDevice(page, params = {}) {
      this.windowDeviceLoading = true;
      getWindowDeviceList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.outletsId).then(
        res => {
          const data = res.data.data;
          this.windowDevicePage.total = data.total;
          this.windowDeviceData = data.records;
          this.windowDeviceLoading = false;
          this.windowDeviceSelectionClear();
        });
    },
    onLoadPersonnel(page, params = {}) {
      this.personnelLoading = true;
      params.companyId = this.companyId;
      getPersonnelData(page.currentPage, page.pageSize, Object.assign(params, this.query), this.deviceSn).then(
        res => {
          const data = res.data.data;
          this.personnelPage.total = data.total;
          this.personnelData = data.records;
          this.personnelLoading = false;
          this.personnelSelectionClear();
        });
    },
    changOutlets(value) {
      this.outletsId = this.searchForm.outletsId;
      this.onLoadWindowDevice(this.windowDevicePage)
    },
    down(row){
      this.downStatus = "find";
      this.delDownStatus = 1;
      this.personnelId = row.id;
      this.personnelSelectionList = [];
      this.personnelSelectionList.push(row.id);
      this.onLoadDevice(this.devicePage);
      this.windowDeviceName = "下传";
      this.deviceVisible = true;
    },
    deteleDevicePersonnel(row) {
      this.delDownStatus = 2;
      this.personnelId = row.id;
      this.personnelSelectionList = [];
      this.personnelSelectionList.push(row.id);
      this.onLoadDevice(this.devicePage);
      this.windowDeviceName = "删除";
      this.delDownStatus2 = "find";
      this.deviceVisible = true;
    },
    checkDownload(row) {
      if (this.personnelSelectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.downStatus = "more";
      this.delDownStatus = 1;
      this.personnelId = row.id;
      this.onLoadDevice(this.devicePage);
      this.windowDeviceName = "下传";
      this.personnelStatus = 1;
      this.deviceVisible = true;
    },
    allCheckDownload(row) {
      this.delDownStatus = 1;
      this.personnelId = row.id;
      this.onLoadDevice(this.devicePage);
      this.windowDeviceName = "下传";
      this.personnelStatus = 3;
      this.deviceVisible = true;
    },
    checkDelete(row) {
      if (this.personnelSelectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.delDownStatus2 = "more";
      this.delDownStatus = 2;
      this.personnelId = row.id;
      this.onLoadDevice(this.devicePage);
      this.windowDeviceName = "删除";
      this.deviceVisible = true;
    },
    allCheckDelete(row) {
      this.delDownStatus = 2;
      this.personnelId = row.id;
      this.onLoadDevice(this.devicePage);
      this.windowDeviceName = "删除";
      this.personnelStatus = 4;
      this.deviceVisible = true;
    },
    saveDown() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
    },
    deleteDown() {
      if (this.selectionDeviceList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.delDownStatus2 === "more"){
        this.personnelDevice.personnelId = this.ids;
      }else {
        this.personnelDevice.personnelId = this.personnelId
      }
      this.personnelDevice.ids = this.deviceIds;
      if (this.personnelStatus == 4) {
        this.personnelDevice.type = "delete_all";
        allDeletePersonnelDevice(this.personnelDevice).then(() => {
          this.onLoadDevice(this.devicePage)
          this.personnelSelectionList = [];
          this.deviceVisible = false;
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈!"
          });
        }, error => {
          this.deviceVisible = false;
          window.console.log(error);
        });
      } else {
        deletePersonnelDevice(this.personnelDevice).then(() => {
          this.onLoad(this.page);
          this.personnelSelectionList = [];
          this.deviceVisible = false;
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          this.deviceVisible = false;
          window.console.log(error);
        });
      }
    },
    personnelManage(row) {
      if(row.deliverySignFlag === 1){
        this.addPersonFlag = 1
      } else {
        this.addPersonFlag = 0
      }
      this.deviceSn = row.deviceKey
      this.companyId = row.companyId;
      this.personnelPage.currentPage = 1

      if(row.deliverySignFlag === 1){
        var parm = { personnelType:'3' }//供应商人员
        this.onLoadPersonnel(this.personnelPage, parm)
      } else {
        this.onLoadPersonnel(this.personnelPage)
      }
      this.personnelVisible = true;
    },
    handleClickPreview: function(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    downPersonnel(row) {
      if (this.selectionDeviceList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      if (this.downStatus === "more"){
        this.personnelDevice.personnelId = this.ids;
      }else {
        this.personnelDevice.personnelId = this.personnelId
      }
      this.personnelDevice.ids = this.deviceIds;
      if (this.personnelStatus == 3) {
        this.personnelDevice.type = "update";
        allDownPersonnelDevice(this.personnelDevice).then(() => {
          this.deviceVisible = false;
          this.onLoad(this.page);
          this.personnelSelectionList = [];
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          this.deviceVisible = false;
          window.console.log(error);
        });
      } else {
        downPersonnelDevice(this.personnelDevice).then(() => {
          this.deviceVisible = false;
          this.onLoad(this.page);
          this.personnelSelectionList = [];
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          this.deviceVisible = false;
          window.console.log(error);
        });
      }
    },
    downPersonnel2(row) {
      this.personnelDevice.deviceSn = this.deviceSn;
      this.personnelDevice.personnelId = row.id
      down(this.personnelDevice).then(() => {
        if(this.addPersonFlag === 1){
          var parm = { personnelType:'3' }//供应商人员
          this.onLoadPersonnel(this.personnelPage, parm)
        } else {
          this.onLoadPersonnel(this.personnelPage)
        }

        this.$message({
          type: "success",
          message: "操作成功，等待人脸机反馈!"
        });
      }, error => {
        window.console.log(error);
      });
    },
    deletePersonnel2(row) {
      this.personnelDevice.deviceSn = this.deviceSn;
      this.personnelDevice.personnelId = row.id
      deleteFace(this.personnelDevice).then(() => {
        if(this.addPersonFlag === 1){
          var parm = { personnelType:'3' }//供应商人员
          this.onLoadPersonnel(this.personnelPage, parm)
        } else {
          this.onLoadPersonnel(this.personnelPage)
        }

        this.$message({
          type: "success",
          message: "操作成功，等待人脸机反馈!"
        });
      }, error => {
        window.console.log(error);
      });
    },
    checkPersonnelDownload() {
      if (this.personnelSelectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.personnelDevice.personnelId = this.ids;
      this.personnelDevice.deviceSn = this.deviceSn;
      checkDownFace(this.personnelDevice).then(() => {
        if(this.addPersonFlag === 1){
          var parm = { personnelType:'3' }//供应商人员
          this.onLoadPersonnel(this.personnelPage, parm)
        } else {
          this.onLoadPersonnel(this.personnelPage)
        }
        this.$message({
          type: "success",
          message: "操作成功，等待人脸机反馈"
        });
      }, error => {
        window.console.log(error);
      });
    },
    checkPersonnelDelete() {
      if (this.personnelSelectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.personnelDevice.personnelId = this.ids;
      this.personnelDevice.deviceSn = this.deviceSn;
      saveCheckDelete(this.personnelDevice).then(() => {
        if(this.addPersonFlag === 1){
          var parm = { personnelType:'3' }//供应商人员
          this.onLoadPersonnel(this.personnelPage, parm)
        } else {
          this.onLoadPersonnel(this.personnelPage)
        }
        this.$message({
          type: "success",
          message: "操作成功，等待人脸机反馈"
        });
      }, error => {
        window.console.log(error);
      });
    },
    allCheckDownload2() {
      this.personnelDevice.deviceSn = this.deviceSn;
      allDownFace(this.personnelDevice).then(() => {
        if(this.addPersonFlag === 1){
          var parm = { personnelType:'3' }//供应商人员
          this.onLoadPersonnel(this.personnelPage, parm)
        } else {
          this.onLoadPersonnel(this.personnelPage)
        }
        this.$message({
          type: "success",
          message: "操作成功，等待人脸机反馈"
        });
      }, error => {
        window.console.log(error);
      });
    },
    allCheckDelete2() {
      this.personnelDevice.deviceSn = this.deviceSn;
      allDelete(this.personnelDevice).then(() => {
        if(this.addPersonFlag === 1){
          var parm = { personnelType:'3' }//供应商人员
          this.onLoadPersonnel(this.personnelPage, parm)
        } else {
          this.onLoadPersonnel(this.personnelPage)
        }
        this.$message({
          type: "success",
          message: "操作成功，等待人脸机反馈"
        });
      }, error => {
        window.console.log(error);
      });
    },
    closePersonnelDown() {
      this.deviceCode = "";
      this.query = {};
      this.personnelVisible = false;
    },
    reboot(row) {
      this.$confirm("是否确定重启设备【" + row.deviceKey + "】?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return reboot(row.deviceKey);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    addPerson(){
      this.addPersonVisible = true
    },
    handleAddPerson(){
      console.log(this.addPersonForm)
      let row = {deptId: this.addPersonForm.supplierCompanyId, id: this.addPersonForm.supplierId}
      console.log(row)
      addForDeliverySign(row).then(() => {
        var parm = { personnelType:'3' }//供应商人员
        this.onLoadPersonnel(this.personnelPage, parm)
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        done();
        window.console.log(error);
      });
    },
    closeAddPerson(){
      this.addPersonVisible = false
      this.addPersonForm = {}
    }
  }
}
</script>

<style lang="scss" scoped>
.all-mess {
  background: #fff;
  overflow: hidden;

  .mess-header {
    display: flex;
    height: 50px;
    background: #F1F6FF;

    ::v-deep div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;

      &:hover {
        color: #3775da;
      }

      &.acitve {
        color: #3775da;
        background: #fff;
      }
    }
  }

  .mess-content {
    height: 1100px;
    padding: 0 20px;
    box-sizing: border-box;

    .mess-item {
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;

      .mess-name {
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }

      .mess-detail {
        color: #3775da;
        text-decoration: underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }

  .mess-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;

    .mess-but {
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>
