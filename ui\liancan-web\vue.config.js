/*
 * @Author: wq
 * @Date: 2020-07-18 18:48:07
 */
// const target = 'http://liancan-test-api.mmdiandi.com' // 测试环境
// const target = 'http://liancan-api.mmdiandi.com' // 旧线上地址
// const target = 'http://localhost:7799' // 本地地址
// const target = 'http://************:7799' // 本地地址
const target = 'http://localhost:8080' // 本地微服务地址
// const target = 'http://*************:8080' // 本地测试微服务地址
// const target = 'https://liancan-api.liancanjd.com' // 新线上地址


// const target = 'http://liancan-cloud-api.liancanjd.com' // 本地微服务地址


// let apiPathRewrite = '/service'

module.exports = {
  //路径前缀
  publicPath: "/",
  lintOnSave: true,
  productionSourceMap: false,
  chainWebpack: (config) => {
    //忽略的打包文件
    config.externals({
      'vue': 'Vue',
      'vue-router': 'VueRouter',
      'vuex': 'Vuex',
      'axios': 'axios',
      'element-ui': 'ELEMENT'
    });
    const entry = config.entry('app');
    entry.add('babel-polyfill').end();
    entry.add('classlist-polyfill').end();
    /*    entry.add('@/mock').end();*/
  },
  devServer: {
    port: 7798,
    proxy: {
      '/api': {
        //本地服务接口地址
        target: target,
        //远程演示服务地址,可用于直接启动项目
        ws: true,
        // pathRewrite: {
        //   '^/api': apiPathRewrite
        // },
        changeOrigin: true,
        logLevel: 'debug',
      },
      // '/auth': {
      //   //本地服务接口地址
      //   target: target,
      //   //远程演示服务地址,可用于直接启动项目
      //   ws: true,
      //   changeOrigin: true,
      //   logLevel: 'debug',
      // },
      // '/sfinance': {
      //     //本地服务接口地址
      //     target: target,
      //     //远程演示服务地址,可用于直接启动项目
      //     ws: true,
      //     changeOrigin: true,
      //     logLevel: 'debug',
      // },
      // '/finance': {
      //     //本地服务接口地址
      //     target: target,
      //     //远程演示服务地址,可用于直接启动项目
      //     ws: true,
      //     changeOrigin: true,
      //     logLevel: 'debug',
      // },
      // '/supplier': {
      //     //本地服务接口地址
      //     target: target,
      //     //远程演示服务地址,可用于直接启动项目
      //     ws: true,
      //     changeOrigin: true,
      //     logLevel: 'debug',
      // },
    }
  },
  transpileDependencies: ['vue-echarts'],
};
