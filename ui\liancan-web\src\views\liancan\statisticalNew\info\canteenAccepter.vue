<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="small" icon="el-icon-view" @click="opentView(scope.row)">查看
        </el-button>
      </template>
      <template slot="sendTime" slot-scope="scope">
        <span>{{scope.row.sendTime}}之前</span>
      </template>
    </avue-crud>

    <el-dialog title="验收详情" :visible.sync="isShow" :append-to-body="true" @close="closeForm" width="60%">
      <div  class="row-table">
        <div class="row-table-first">订单号</div><div class="row-table-second">{{orderForm.id}}</div>
        <div class="row-table-first">供应商</div><div class="row-table-second">{{orderForm.supplierName}}</div>
        <div class="row-table-first">采购方</div><div class="row-table-second">{{orderForm.deptName}}</div>
      </div>
      <div  class="row-table">
        <div class="row-table-first">采购人</div><div class="row-table-second">{{orderForm.userName}}</div>
        <div class="row-table-first">发票</div><div class="row-table-second">{{orderForm.invoiceId}}</div>
        <div class="row-table-first">采购确认人</div><div class="row-table-second">{{orderForm.verifyUserName}}</div>
      </div>
      <div  class="row-table">
        <div class="row-table-first">确认状态</div><div class="row-table-second">{{orderForm.isBuy==1?'确认通过':orderForm.isBuy==2?'确认拒绝':'未确认'}}</div>
        <div class="row-table-first">商品种类数</div><div class="row-table-second">{{orderForm.totalQuantity}}</div>
        <div class="row-table-first">总价</div><div class="row-table-second">{{orderForm.totalPrices}}</div>
      </div>
      <div  class="row-table">
        <div class="row-table-first">配送时间</div><div class="row-table-second">{{orderForm.sendTime}}</div>
        <div class="row-table-first">订单状态</div><div class="row-table-second">{{orderForm.orderStatus==1?'食堂已收':
        orderForm.orderStatus==2?'取消/拒单':orderForm.orderStatus==3?'已送达':orderForm.orderStatus==4?'配送中':'未接单'}}</div>
        <div class="row-table-first">送货地址</div><div class="row-table-second">{{orderForm.site}}</div>
      </div>
      <div  class="row-table">
        <div class="row-table-first">联系人</div><div class="row-table-second">{{orderForm.userName}}</div>
        <div class="row-table-first">电话</div><div class="row-table-second" style="width: 56.2%;">{{orderForm.phone}}</div>
      </div>

      <!-- <div>
        <avue-form ref="orderForm" :option="orderOption" v-model="orderForm">
        </avue-form>
      </div> -->


      <div style="margin-top: 20px;">
        <span class="fontsize">订单商品信息</span>
        <avue-crud ref="crud" v-model="orderDetailForm" :option="orderDetailOption" :data="orderDetailData" @on-load="orderDetailOnLoad" :page="orderDetailPage"
                   :table-loading="orderDetailLoading">
        </avue-crud>
      </div>
      <div style="margin-top: 20px;">
        <span class="fontsize">进仓确认历史</span>
        <avue-crud ref="crud" v-model="orderDetailForm2" :option="orderDetailOption2" :data="orderDetailData2" @on-load="orderDetailOnLoad2" :page="orderDetailPage2"
                   :table-loading="orderDetailLoading2">
        </avue-crud>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getAccepterLogging, orderDetailList, getAccepterLog} from "@/api/liancan/order";
import {mapGetters} from "vuex";
var DIC = {
  isBuy: [{
    label: '未确认',
    value: "0"
  },{
    label: '确认通过',
    value: "1"
  },{
    label: '确认拒绝',
    value: "2"
  }],
  payStatus: [{
    label: '未付款',
    value: "0"
  },{
    label: '已付款',
    value: "1"
  }],
  orderStatus:[{
    label: '未接单',
    value: "0"
  },{
    label: '食堂已收',
    value: "1"
  },{
    label: '取消/拒单',
    value: "2"
  },{
    label: '已送达',
    value: "3"
  },{
    label: '配送中',
    value: "4"
  }],
  accepterStatus: [{
    label: '未确认',
    value: "1"
  },{
    label: '验收通过',
    value: "2"
  },{
    label: '验收不通过',
    value: "3"
  },
    {
      label: ' ',
      value: "0"
    },],
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      data: [],
      option: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: "采购单号",
            prop: "id",
            type: "input",
            display: false,
          },
          {
            label: "所属食堂",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            hide: true,
            addDisplay: false,
            search: false,
          },
          {
            label: "所属食堂",
            prop: "deptName",
            type: "input",
            hide: true,
          },
          {
            label: "供应商",
            prop: "supplierName",
            type: "input",
            overHidden: true,
            width: 190,
          },
          {
            label: "采购人",
            prop: "userName",
            type: "input",
          },
          {
            label: "下单时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 160,
          },
          {
            label: "验收进仓人",
            prop: "accepterUserName",
            type: "input",
          },
          {
            label: "验收进仓时间",
            prop: "accepterTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 160,
          },
          {
            label: "验收总价",
            prop: "totalPrices",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
          },
          {
            label: "进仓确认人",
            prop: "accepterUserNameb",
            type: "input",
          },
          {
            label: "确认状态",
            prop: "accepterStatus",
            type: "select",
            dicData: DIC.accepterStatus,
          },
          {
            label: "是否支付",
            prop: "payStatus",
            type: "select",
            dicData: DIC.payStatus,
            search: true,
          },
          {
            label: "采购人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            search: true,
            showColumn: false,
            hide: true,
            searchFilterable: true,
          },
          {
            label: "采购开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            searchLabelWidth: 100,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "采购结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            searchLabelWidth: 100,
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan2/purchaseExpenditure/schoolSupplier',
            props: {
              label: "name",
              value: "id"
            },
            search: true,
            showColumn: false,
            hide: true,
            searchFilterable: true,
          },
          {
            label: "确认状态",
            prop: "status",
            type: "select",
            dicData: [{
              label: '验收通过',
              value: "0"
            },{
              label: '未确认',
              value: "1"
            }],
            search: true,
            showColumn: false,
            hide: true,
          },
        ]
      },
      isShow: false,
      orderForm: {},
      resOrderId: null,
      orderDetailPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      orderDetailPage2: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      orderDetailData: [],
      orderDetailData2: [],
      orderDetailLoading: true,
      orderDetailLoading2: true,
      orderOption:{
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: "订单号",
            prop: "id",
            type: "input",
            width: 150,
            disabled: true,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
            props: {
              label: "name",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "采购方",
            prop: "deptId",
            type: "select",
            dicUrl: '/api/service/rabbit-system/dept/dict',
            props: {
              label: "deptName",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "采购人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "发票",
            prop: "invoiceId",
            hide: true,
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
            props: {
              label: "num",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "采购确认人",
            prop: "verifyUserId",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "确认状态",
            prop: "isBuy",
            type: "select",
            dicData: DIC.isBuy,
            disabled: true,
          },
          {
            label: "商品种类数",
            prop: "totalQuantity",
            type: "input",
            slot: true,
            disabled: true,
          },
          {
            label: "总价",
            prop: "totalPrices",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            display: false,
          },
          {
            label: "配送时间",
            prop: "sendTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            slot: true,
            disabled: true,
          },
          {
            label: "订单状态",
            prop: "orderStatus",
            type: "select",
            dicData: DIC.orderStatus,
            disabled: true,
          },
          {
            label: "送货地址",
            prop: "site",
            type: "input",
            disabled: true,
          },
          {
            label: "电话",
            prop: "phone",
            type: "input",
            hide: true,
            disabled: true,
          },
          {
            label: "联系人",
            prop: "userName",
            type: "input",
            hide: true,
            disabled: true,
          },
        ],
      },
      orderDetailForm: {},
      orderDetailForm2: {},
      orderDetailOption:{
        align: "center",
        height: 250,
        calcHeight: 30,
        searchShow: false,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        selection: false,
        menu: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        column: [{
          label: 'id',
          prop: 'id',
          type: 'input',
          hide: true,
          display: false,
          showColumn: false,
        },
          {
            label: "商品",
            prop: "goodsId",
            type: "select",
            dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
            props: {
              label: "name",
              value: "id"
            },
          },
          {
            label: "价格",
            prop: "price",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            //minRows: 0,
          },
          {
            label: "计量单位",
            prop: "unit",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
          {
            label: "采购数量",
            prop: "quantity",
            type: "number",
          },
          {
            label: "验收数量",
            prop: "accepterQuantity",
            type: "number",
          },
          {
            label: "小计",
            prop: "subtotal",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            //minRows: 0,
          },
        ],
      },
      orderDetailOption2:{
        align: "center",
        height: 200,
        calcHeight: 30,
        searchShow: false,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        selection: false,
        menu: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        column: [{
          label: 'id',
          prop: 'id',
          type: 'input',
          hide: true,
          display: false,
          showColumn: false,
        },
          {
            label: "进仓确认人",
            prop: "name",
            type: "input",
            width: 150,
          },
          {
            label: "进仓确认时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 170,
          },
          {
            label: "进仓确认结果",
            prop: "accepterStatus",
            type: "select",
            dicData: [
              {
                label: "验收通过",
                value: '0'
              },
              {
                label: "验收不通过",
                value: '1'
              },
            ],
            width: 120,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
          },
        ],
      },




    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
  },
  created(){
  },
  methods: {
    beforeOpen(done, type) {
      done();
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('采购结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('采购开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('采购开始时间不能大于结束时间');
          }
        }
      }
      this.query = params;
      this.page.currentPage = 1
      /*          if (params.timeRange != '' && params.timeRange != null && params.timeRange != undefined) {
                    params.startDate = params.timeRange[0];
                    params.endDate = params.timeRange[1];
                }*/
      params.timeRange = null;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.deptId = this.schoolId
      params.queryType = 0;
      getAccepterLogging(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    closeForm: function() {
      this.isShow = false
    },
    opentView(row) {
      console.log(row)
      this.isShow = true;
      this.orderForm = row;
      if(this.orderForm.verifyUserId == 0){
        this.orderForm.verifyUserId = ' ';
      }
      this.resOrderId = row.id;
      this.orderDetailOnLoad(this.orderDetailPage);
      this.orderDetailOnLoad2(this.orderDetailPage);
    },
    orderDetailOnLoad(orderDetailPage) {
      this.orderDetailLoading = true;
      orderDetailList(orderDetailPage.currentPage, orderDetailPage.pageSize, Object.assign({
        orderId : this.resOrderId
      })).then(res => {
        const data = res.data.data;
        this.orderDetailPage.total = data.total;
        this.orderDetailData = data.records;
        this.orderDetailLoading = false;
      });
    },
    orderDetailOnLoad2(orderDetailPage) {
      this.orderDetailLoading2 = true;
      getAccepterLog(orderDetailPage.currentPage, orderDetailPage.pageSize, Object.assign({
        orderId : this.resOrderId
      })).then(res => {
        const data = res.data.data;
        this.orderDetailPage2.total = data.total;
        this.orderDetailData2 = data.records;
        this.orderDetailLoading2 = false;
      });
    },






  }
}
</script>

<style scoped>
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}
.fontsize{
  font-size: 20px;
}
</style>
