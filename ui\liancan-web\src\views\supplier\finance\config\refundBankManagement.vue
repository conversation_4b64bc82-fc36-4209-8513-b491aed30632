<template>
  <basic-container>
    <div class="all-mess">
      <div class="mess-header">
        <div
          :class="{active: activeIdx === index}"
          v-for="(item, index) in tabList"
          :key="index"
          @click="handleTabClick(index)"
        >
          {{item}}
        </div>
      </div>

      <!-- 退款银行卡 Tab -->
      <div class="mess-content" v-if="activeIdx === 0">
        <avue-crud
          :option="refundOption"
          :table-loading="refundLoading"
          :data="refundData"
          :page="refundPage"
          v-model="refundForm"
          ref="refundCrud"
          @row-update="handleRefundUpdate"
          @row-save="handleRefundSave"
          @row-del="handleRefundDelete"
          @search-change="handleRefundSearch"
          @search-reset="handleRefundReset"
          @selection-change="handleSelectionChange"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          @on-load="loadRefundData">
        </avue-crud>
      </div>

      <!-- 银行列表 Tab -->
      <div class="mess-content" v-if="activeIdx === 1">
        <avue-crud
          :option="bankOption"
          :table-loading="bankLoading"
          :data="bankData"
          :page="bankPage"
          v-model="bankForm"
          ref="bankCrud"
          @row-update="handleBankUpdate"
          @row-save="handleBankSave"
          @row-del="handleBankDelete"
          @search-change="handleBankSearch"
          @search-reset="handleBankReset"
          @selection-change="handleSelectionChange"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          @on-load="loadBankData">
        </avue-crud>
      </div>
    </div>
  </basic-container>
</template>

<script>
import { getList as getBankList, add as addBank, update as updateBank, remove as removeBank } from "@/api/liancan/refundBankConfig";
import { getList as getRefundList, add as addRefund, update as updateRefund, remove as removeRefund } from "@/api/liancan/refundBankManagement";

export default {
  data() {
    return {
      activeIdx: 0,
      tabList: ['退款银行卡', '银行列表'],

      // 退款银行卡数据
      refundForm: {},
      refundLoading: false,
      refundData: [],
      refundPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },

      // 银行列表数据
      bankForm: {},
      bankLoading: false,
      bankData: [],
      bankPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },

      // 退款银行卡配置
      refundOption: {
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: true,
        delBtn: true,
        addBtn: true,
        selection: true,
        align: 'center',
        column: [
          // {
          //   label: "学校类型",
          //   prop: "companyType",
          //   type: "select",
          //   dicData: [
          //     { label: '幼儿园', value: '幼儿园' },
          //     { label: '中小学', value: '中小学' }
          //   ],
          //   search: true,
          //   rules: [{
          //     required: true,
          //     message: "请选择学校类型",
          //     trigger: "blur"
          //   }]
          // },
          {
            label: "学校名称",
            prop: "companyId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择学校",
                trigger: "click"
              }]
          },
          {
            label: "银行名称",
            prop: "bankName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: "指定退款银行",
            prop: "bankIds",
            type: "select",
            multiple: true,
            dicUrl: "/api/service/rabbit-liancan/refundBankConfig/getList",
            props: {
              label: 'bankName',
              value: 'id'
            },
            rules: [{
              required: true,
              message: "请选择退款银行",
              trigger: "blur"
            }],
            hide: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: [
              { label: '正常', value: '正常' },
              { label: '停用', value: '停用' }
            ],
            search: true
          }
        ]
      },

      // 银行列表配置
      bankOption: {
        height: 'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        editBtn: true,
        delBtn: true,
        addBtn: true,
        selection: true,
        align: 'center',
        column: [
          {
            label: "银行名称",
            prop: "bankName",
            type: "input",
            search: true,
            rules: [{
              required: true,
              message: "请输入银行名称",
              trigger: "blur"
            }]
          },
          {
            label: "logo图片",
            prop: "logoUrl",
            type: 'upload',
            listType: 'picture-img',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
            propsHttp: {
              res: 'data',
              url: 'link',
            },
            span: 24,
            slot: true,
            rules: [{
              required: true,
              message: "请上传logo图片",
              trigger: "blur"
            }],
          }
        ]
      }
    };
  },

  methods: {
    handleTabClick(index) {
      if (this.activeIdx === index) return;
      this.activeIdx = index;
      if (index === 0) {
        this.loadRefundData(this.refundPage);
      } else {
        this.loadBankData(this.bankPage);
      }
    },

    // 退款银行卡方法
    async loadRefundData(page, params = {}) {
      this.refundLoading = true;
      try {
        const res = await getRefundList(page.currentPage, page.pageSize, params);
        const data = res.data.data;
        this.refundPage.total = data.total;
        this.refundData = data.records.map(item => ({
          ...item,
          bankIds: item.bankIds ? item.bankIds.split(',') : []
        }));
      } finally {
        this.refundLoading = false;
      }
    },

    async handleRefundSave(row, loading, done) {
      try {
        const submitData = {
          ...row,
          bankIds: Array.isArray(row.bankIds) ? row.bankIds.join(',') : row.bankIds
        };
        await addRefund(submitData);
        this.$message.success('添加成功');
        this.loadRefundData(this.refundPage);
        loading();
      } catch (error) {
        console.error(error);
        done();
      }
    },

    async handleRefundUpdate(row, index, loading, done) {
      try {
        const submitData = {
          ...row,
          bankIds: Array.isArray(row.bankIds) ? row.bankIds.join(',') : row.bankIds
        };
        await updateRefund(submitData);
        this.$message.success('更新成功');
        this.loadRefundData(this.refundPage);
        loading();
      } catch (error) {
        console.error(error);
        done();
      }
    },

    async handleRefundDelete(row) {
      try {
        await this.$confirm('确认删除该记录？');
        await removeRefund(row.id);
        this.$message.success('删除成功');
        this.loadRefundData(this.refundPage);
      } catch (error) {
        console.error(error);
      }
    },

    // 银行列表方法
    async loadBankData(page, params = {}) {
      this.bankLoading = true;
      try {
        const res = await getBankList(page.currentPage, page.pageSize, params);
        const data = res.data.data;
        this.bankPage.total = data.total;
        this.bankData = data.records;
      } finally {
        this.bankLoading = false;
      }
    },

    async handleBankSave(row, loading, done) {
      try {
        await addBank(row);
        this.$message.success('添加成功');
        this.loadBankData(this.bankPage);
        loading();
      } catch (error) {
        console.error(error);
        done();
      }
    },

    async handleBankUpdate(row, index, loading, done) {
      try {
        await updateBank(row);
        this.$message.success('更新成功');
        this.loadBankData(this.bankPage);
        loading();
      } catch (error) {
        console.error(error);
        done();
      }
    },

    async handleBankDelete(row) {
      try {
        await this.$confirm('确认删除该记录？');
        await removeBank(row.id);
        this.$message.success('删除成功');
        this.loadBankData(this.bankPage);
      } catch (error) {
        console.error(error);
      }
    },

    // 通用方法
    handleRefundSearch(params, done) {
      this.loadRefundData(this.refundPage, params);
      done();
    },

    handleBankSearch(params, done) {
      this.loadBankData(this.bankPage, params);
      done();
    },

    handleRefundReset() {
      this.loadRefundData(this.refundPage);
    },

    handleBankReset() {
      this.loadBankData(this.bankPage);
    },

    handleSelectionChange(selection) {
      this.selectionList = selection;
    },

    handleCurrentChange(currentPage) {
      if (this.activeIdx === 0) {
        this.refundPage.currentPage = currentPage;
        this.loadRefundData(this.refundPage);
      } else {
        this.bankPage.currentPage = currentPage;
        this.loadBankData(this.bankPage);
      }
    },

    handleSizeChange(pageSize) {
      if (this.activeIdx === 0) {
        this.refundPage.pageSize = pageSize;
        this.loadRefundData(this.refundPage);
      } else {
        this.bankPage.pageSize = pageSize;
        this.loadBankData(this.bankPage);
      }
    }
  },

  created() {
    this.loadRefundData(this.refundPage);
  }
};
</script>

<style lang="scss" scoped>
.all-mess {
  background: #fff;
  overflow: hidden;

  .mess-header {
    display: flex;
    height: 50px;
    background: #F1F6FF;

    div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;

      &:hover {
        color: #3775da;
      }

      &.active {
        color: #3775da;
        background: #fff;
      }
    }
  }

  .mess-content {
    padding: 20px;
    box-sizing: border-box;
  }
}
</style>
