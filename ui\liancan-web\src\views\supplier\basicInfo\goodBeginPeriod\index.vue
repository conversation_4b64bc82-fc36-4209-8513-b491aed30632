<template>
    <basic-container id="c-table">
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="menuLeft">
                <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportExcel">导出</el-button>
<!--                <el-button class="filter-item" size="small" type="success" icon="el-icon-printer" @click="handlePrint">打印</el-button>-->
            </template>
            <template slot="status" slot-scope="{row}">
                <el-tag v-if="row.status == '1'" type="success">正常</el-tag>
                <el-tag v-if="row.status == '0'" type="danger">停用</el-tag>
            </template>
        </avue-crud>
        <!-- 公共商品选择 开始 -->
        <el-dialog title="商品" :visible.sync="isShow2" :append-to-body="true" @close="closeForm2" width="90%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crud"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 商品选择 结束 -->
    </basic-container>
</template>

<script>
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";
import {getList, getDetail, add, update, remove, switchWarehouse,exportData} from "@/api/supplier/supplierGoodBeginPeriod";
import {mapGetters} from "vuex";
const DIC = {
}
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            searchForm: {

            },
            currentOpenType: '',
            option: {
                height:'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: true,
                selection: true,
                viewBtn:true,
                printBtn:true,
                // 其他配置...
                exportBtn: true, // 启用导出按钮
                excelExport: true, // 启用Excel导出
                csvExport: true, // 启用CSV导出
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },

                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请选择商品类型",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请选择二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                        overHidden: true,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        disabled: true,
                        rules: [{
                            required: true,
                            message: "请选择商品商品名称",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请填写商品子项名称",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {//6
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入生产厂家",
                            trigger: "blur"
                        }],
                    },
                    {//5
                        label: "所在仓库",
                        prop: "warehouseId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        rules: [{
                            required: true,
                            message: "请选择所在仓库",
                            trigger: "blur"
                        }],
                    },
                    {//6
                        label: "期初数量",
                        prop: "qtyBeginPeriod",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请填写期初数量",
                            trigger: "blur"
                        }],
                    },
                    {//6
                        label: "期初单位成本",
                        prop: "costBeginPeriod",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请填写期初单位成本",
                            trigger: "blur"
                        }],
                    },
                    {//6
                        label: "期初金额",
                        prop: "amtBeginPeriod",
                        type: "input",
                        disabled: true,
                    },

                    {
                        label: "登记人",
                        prop: "updateUser",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                    },
                    {
                        label: "登记时间",
                        prop: "updateTime",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        width: 150,
                    },

                    {
                        label: "状态",
                        prop: "status",
                        type: "select",
                        slot:true,
                        dicData: [{
                            value: "0",
                            label: "停用"
                        },
                            {
                                value: "1",
                                label: "正常"
                            }
                        ],
                        hide: true,
                    },
                    {
                        label: "创建人",
                        prop: "createUser",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "创建部门",
                        prop: "createDept",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "创建时间",
                        prop: "createTime",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },

                    {
                        label: "是否已删除",
                        prop: "isDel",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "乐观锁",
                        prop: "version",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                ]
            },
            isShow2: false,
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择一类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请填写商品子项名称",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
        }
    },
    computed: {
        ...mapGetters(["permission", 'userInfo']),
        permissionList() {
            return {
            };
        },
        ids() {
            let ids = [];
            this.personnelSelectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        },
    },

    created() {
    },
    methods: {
        rowSave(row, loading, done) {
            console.log("1========>",row);
            add(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
            if (this.selectionList.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(this.ids);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                    this.$refs.crud.toggleSelection();
                });
        },
        beforeOpen(done, type) {
            this.currentOpenType = type;
            // if (this.form.type == "1314471996665397249" || type == "1366636175794667521") {
            //
            // }
            // //燃料
            // if (this.form.type == "1316561056074649602" || type == "1366636571036516354") {
            //
            // }
            // 新增初始化
            // if (type == 'add') {
                // this.publicRow = {}
                // this.publicForm = {}
                // this.publicForm.publicName = ''
                // this.$set(this.publicOption.column[0], 'display', true)
            // }
            // if (type == 'edit') {
                // this.$set(this.publicOption.column[0], 'display', false)
            // }
            if (["edit", "view"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                });
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage){
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize){
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            // this.query.companyId = "1553993875767554050";
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                console.log("2=======>",res);
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        /////////////////////////////////////////
        //打印
        handlePrint() {
            // let that = this;
            // this.loading = true;
            // var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
            // xhr.open(
            //     "get",
            //     "/api/service/rabbit-supplier/finance/financialSubject/downloadForSubjectCount?endPeroid="+this.endPeroid+"&accountSetsId="+this.accountSetsValue+"&userName="+this.userInfo.nickname,
            //     true
            // );
            // xhr.responseType = "blob";
            // xhr.onload = function() {
            //     that.loading = false;
            //     const url = window.URL.createObjectURL(this.response);
            //     const link = document.createElement("a");
            //     link.style.display = "none";
            //     link.href = url;
            //     link.setAttribute("download", "资产负债表.pdf");
            //     document.body.appendChild(link);
            //     link.click();
            //     document.body.removeChild(link);
            // };
            // xhr.send();
        },
        //导出
        exportExcel(){
            exportData(Object.assign(this.query)).then(res => {
                if (!res.data) {
                    return;
                }
                const blob = new Blob([res.data], {
                    type: "application/vnd.ms-excel",
                }); // 构造一个blob对象来处理数据，并设置文件类型
                const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
                const a = document.createElement("a"); //创建a标签
                a.style.display = "none";
                a.href = href; // 指定下载链接
                let fileName = res.headers["content-disposition"];
                fileName = fileName.split("=")[1];
                a.download = decodeURIComponent(fileName); //指定下载文件名
                a.click(); //触发下载
                URL.revokeObjectURL(a.href); //释放URL对象
            });
        },

        /////////////////////////////////////////
        goodsListOnLoad(page, params = {}) {
            this.goodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                console.log("3======>",res);
                const data = res.data.data;
                this.goodsListPage.total = data.total;
                this.goodsListData = data;
                this.goodsListLoading = false;
                this.selectionClear();
            });
        },
        addGoods(row) {
            console.log("4=======>",row);
            row.addOrEdit = 'add'//新增
            row.typeQuery = row.type;
            row.biddingQuery = row.bidding;
            row.biddingTypeIdQuery = row.biddingTypeId;


            this.form = row;
            this.isShow2 = false

        },
        choice() {
            this.isShow2 = true
        },
        searchChange2(params, done) {
            this.query = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        searchReset2() {
            this.query = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange2(list) {
            this.selectionList = list;
        },
        currentChange2(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        closeForm2() {

        },
    }
}
</script>
