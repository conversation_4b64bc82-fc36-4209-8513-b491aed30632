<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuRight">
        <el-button type="success"
                   icon="el-icon-shopping-cart-full"
                   round
                   @click="viewShoppingCart">查看购物车
        </el-button>
      </template>

      <template slot-scope="{row}" slot="menu">
        <el-button  type="text" size="mini" icon="el-icon-team" plain  @click="addCart(row)">加入购物车</el-button>
      </template>
    </avue-crud>
    <!-- 上架 开始 -->
    <el-dialog title="上架" :visible.sync="upperShow" :append-to-body="true" width="40%">
      <avue-form :defaults.sync="defaults" :option="upperOption" v-model="upperForm" @submit="upperSubmit" @close="upperClose"></avue-form>
    </el-dialog>
    <!-- 上架 结束 -->

    <!-- 购物车 开始 -->
    <el-dialog title="购物车" :visible.sync="isShow2" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="goodsListOption"
                 :table-loading="goodsListLoading"
                 :data="goodsListData"
                 :page="goodsListPage"
                 v-model="goodsListForm"
                 ref="crud"
                 @row-update="updateGoodsCartRow"
                 @search-change="searchChange2"
                 @search-reset="searchReset2"
                 @selection-change="selectionChange2"
                 @current-change="currentChange2"
                 @size-change="sizeChange2"
                 @on-load="goodsListOnLoad">
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="removeGoodsCartRow(scope.row)">删除
          </el-button>
        </template>
      </avue-crud>
      <br/>
      <div><span>操作说明: [直接下单] 或 [提交确认] 后，系统将按照不同的供应商，拆分成对应的多个订单。</span></div>
      <el-form ref="form" :model="goodsForm" label-width="150px">
        <el-form-item label="送货时间">
          <el-date-picker
            v-model="goodsForm.deliveryDate"
            type="date"
            value-format="yyyy-MM-dd"
            format="yyyy-MM-dd"
            placeholder="选择日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="纳税人识别号">
          <el-select v-model="goodsForm.idNumber" placeholder="请选择">
            <el-option
              v-for="item in idNumberList"
              :key="item.number"
              :label="item.number"
              :value="item.number">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系人">
          <el-input style="width:200px;" v-model="goodsForm.receiptUser" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input style="width:200px;" v-model="goodsForm.receiptPhone" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="地址">
          <el-input style="width:600px;" v-model="goodsForm.receiptAddress" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="saveOrderForDirectly">直接下单</el-button>
          <el-button type="primary" @click="saveOrderForConfirm">提交确认</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <!-- 购物车 结束 -->

  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, updateForStatus} from "@/api/liancan/supplierGoods";
  import {getList as getGoodsList, add as addCart, update as updateCart, remove as removeCart, saveOrderForConfirm} from "@/api/liancan/supplierGoodsCart";
  import {getList as getInvoiceTitle} from "@/api/liancan/invoiceTitle";
  import {add as saveOrder} from "@/api/liancan/order";

  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        goodsForm:{
          deliveryDate:'',
          idNumber:'',
          receiptUser:'',
          receiptPhone:'',
          receiptAddress:''
        },
        idNumberList:[],
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        defaults:{},
        upperShow: false,
        upperRow:{},
        upperForm: {},
        upperOption: {
          column: [
            {
              label: "上架供应量",
              prop: "supplyQty",
              type:'input',
              rules: [{
                required: true,
                message: "请输入上架供应量",
                trigger: "blur"
              }]
            },
            {
              label: "销售单价",
              prop: "price",
              type:'input',
              append:'元',
              rules: [{
                required: true,
                message: "请输入销售单价",
                trigger: "blur"
              }]
            },
          ]
        },
        publicRow:{},
        publicForm:{
          publicId:'',
          publicName:'',
          typePublic: '',
          typeNamePublic: '',
          biddingPublic: '',
          biddingNamePublic: '',
          biddingTypeIdPublic: '',
          biddingTypeIdNamePublic: '',
          unitPublic:'',
          netWeightPublic: '',
          brandPublic: '',
          foodSpecPublic: '',
          shelfLifePublic: '',
          manufacturerPublic: '',
          productionPublic: '',
          originPublic: '',
          qualityLevelPublic: '',
          qualityStandardPublic: '',
          storagePublic: '',
          remarkPublic: '',
          imgUrlPublic:'',
        },
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          addBtn:false,
          editBtn:false,
          delBtn:false,
          viewBtn: false,
          selection: true,
          align: "center",
          column: [
            {
              label: '',
              prop: 'bind',
              span:24,
              hide:true,
              formslot:true
            },
            {
              label: "供应商",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-supplier/supplierCompany/all/listForEvaluationBidding?evaluationBiddingFlag=1",
              props: {
                label: "name",
                value:"id"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              search: true,
            },
            {
              label: "商品名称",
              prop: "name",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              rules: [{
                required: true,
                message: "请输入商品名称",
                trigger: "blur"
              }],
              search: true,
            },
            // {
            //   label: "商品类型",
            //   prop: "typeQuery",
            //   type: "select",
            //   rules: [{
            //       required: true,
            //       message: "请选择商品类型",
            //       trigger: "blur"
            //   }],
            //   dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
            //   props: {
            //       label: "name",
            //       value: "id"
            //   },
            //   filterable: true,
            //   searchFilterable: true,
            //   cascaderItem: ['biddingQuery'],
            //   search: true,
            //   addDisplay: false,
            //   editDisplay: false,
            //   viewDisplay: false,
            // },
            // {
            //   label: "商品大类",
            //   prop: "biddingQuery",
            //   type: "select",
            //   rules: [{
            //     required: true,
            //     message: "请选择商品大类",
            //     trigger: "blur"
            //   }],
            //     dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
            //     props: {
            //         label: "name",
            //         value: "id"
            //     },
            //     filterable: true,
            //     searchFilterable: true,
            //     cascaderItem: ['biddingTypeIdQuery'],
            //     search: true,
            //     dicFlag: false,
            //     overHidden: true,
            //     display:false,
            //     addDisplay: false,
            //     editDisplay: false,
            //     viewDisplay: false,
            // },
            // {//4
            //   label: "商品小类",
            //   prop: "biddingTypeIdQuery",
            //   type: "select",
            //   rules: [{
            //     required: true,
            //     message: "请选择商品小类",
            //     trigger: "blur"
            //   }],
            //     dicFlag: false,
            //     dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
            //     props: {
            //         label: "name",
            //         value: "id"
            //     },
            //     search: true,
            //     display:false,
            //     addDisplay: false,
            //     editDisplay: false,
            //     viewDisplay: false,
            // },
            //  {
            //       label: "商品类型",
            //       prop: "type",
            //       type: "select",
            //       rules: [{
            //           required: true,
            //           message: "请选择商品类型",
            //           trigger: "blur"
            //       }],
            //       dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
            //       props: {
            //           label: "name",
            //           value: "id"
            //       },
            //       filterable: true,
            //       searchFilterable: true,
            //       cascaderItem: ['bidding'],
            //       editDisabled:true,
            //       hide:true,
            //       addDisplay: false,
            //       editDisplay: false,
            //       viewDisplay: false,
            //   },
            // {
            //   label: "商品大类",
            //   prop: "bidding",
            //   type: "select",
            //   rules: [{
            //     required: true,
            //     message: "请选择商品大类",
            //     trigger: "blur"
            //   }],
            //     dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall/{{key}}",
            //     props: {
            //         label: "name",
            //         value: "id"
            //     },
            //     filterable: true,
            //     searchFilterable: true,
            //     cascaderItem: ['biddingTypeId'],
            //     dicFlag: false,
            //     overHidden: true,
            //     hide:true,
            //     addDisplay: false,
            //     editDisplay: false,
            //     viewDisplay: false,
            // },
            // {//4
            //   label: "商品小类",
            //   prop: "biddingTypeId",
            //   type: "select",
            //   rules: [{
            //     required: true,
            //     message: "请选择商品小类",
            //     trigger: "blur"
            //   }],
            //     dicFlag: false,
            //     dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
            //     props: {
            //         label: "name",
            //         value: "id"
            //     },
            //     hide:true,
            //     addDisplay: false,
            //     editDisplay: false,
            //     viewDisplay: false,
            // },
            {//6
                label: "品牌",
                prop: "brand",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },

              // {//7
              //     label: "每一计量单位折合净重为",
              //     labelWidth:180,
              //     prop: "netWeight",
              //     type: "input",
              //     rules: [{
              //         required: false,
              //         message: "请输入净含量",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
              // {//8
              //     label: "保质期",
              //     prop: "shelfLife",
              //     type: "input",
              //     rules: [{
              //         required: false,
              //         message: "请输入保质期",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              //     hide:true,
              // },
              // {//9
              //     label: "生产许可",
              //     prop: "production",
              //     type: "input",
              //     rules: [{
              //         required: false,
              //         message: "请输入生产许可",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              //     hide:true,
              // },
              // {//10
              //     label: "产地",
              //     prop: "origin",
              //     type: "input",
              //     rules: [{
              //         required: false,
              //         message: "请输入产地",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              //     hide:true,
              // },
              // {//11
              //     label: "质量等级",
              //     prop: "qualityLevel",
              //     type: "input",
              //     rules: [{
              //         required: false,
              //         message: "请输入质量等级",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              //     hide:true,
              // },
              // {//12
              //     label: "质量标准",
              //     prop: "qualityStandard",
              //     type: "input",
              //     rules: [{
              //         required: false,
              //         message: "请输入质量标准",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              //     hide:true,
              // },
              // {
              //     label: "生产厂家",
              //     prop: "manufacturer",
              //     type: "input",
              //     rules: [{
              //         required: true,
              //         message: "请输入生产厂家",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
              // {
              //     label: "状态",
              //     prop: "status",
              //     type: "radio",
              //     dicData:[
              //       {
              //         label:'正常',
              //         value:"1"
              //       },
              //       {
              //         label:'停用',
              //         value:"0"
              //       },
              //     ],
              //     value:"1",
              //     rules: [{
              //         required: true,
              //         message: "请选择状态",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
              // {
              //     label: "储存方法",
              //     prop: "storage",
              //     type: "input",
              //     hide:true,
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
              // {
              //     label: "备注",
              //     prop: "remark",
              //     type: "input",
              //     hide:true,
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
              // {
              //     label: "商品图片",
              //     prop: "imgUrl",
              //     type: 'upload',
              //     listType: 'picture-img',
              //     propsHttp: {
              //         res: 'data',
              //         url: 'link'
              //     },
              //     tip: '只能上传jpg/png文件，大小不超过 500KB',
              //     action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              //     rules: [{
              //         required: false,
              //         message: '请上传图片',
              //         trigger: 'click'
              //     }],
              //     slot: true,
              //     span: 24,
              //     hide: true,
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
              // {
              //   label: '供应商',
              //   prop: 'supplierList',
              //   type: 'dynamic',
              //   span:24,
              //   hide: true,
              //   children: {
              //     index:false,
              //     align: 'center',
              //     type:'form',
              //     headerAlign: 'center',
              //     rowAdd:(done)=>{
              //       // console.log(this.allSupplierDataDic)
              //       // this.$message.success('新增回调');
              //         done({
              //           input:'默认值'
              //         });
              //     },
              //     rowDel:(row,done)=>{
              //       this.$message.success('删除回调'+JSON.stringify(row));
              //       done();
              //     },
              //     column: [{
              //       label: '供应商名称',
              //       prop: "supplierId",
              //       type: 'select',
              //       props: {
              //         label: 'name',
              //         value: 'id'
              //       },
              //       dicUrl: `/api/rabbit-liancan/schoolGoods/allSupplier`,
              //     },{
              //       label: '合同截止日期',
              //       prop: "supplierEndDate",
              //       type: 'date',
              //     },{
              //       label: '合同文件',
              //       prop: "supplierFile",
              //       type: 'upload',
              //       listType: 'picture-img',
              //       propsHttp: {
              //           res: 'data',
              //           url: 'link'
              //       },
              //       tip: '只能上传jpg/png文件，大小不超过 500KB',
              //       action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              //     }]
              //   }
              // },
              {
                label: "上架供应量",
                prop: "supplyQty",
                type:'input',
                rules: [{
                  required: true,
                  message: "请输入上架供应量",
                  trigger: "blur"
                }]
              },
              {
                label: "销售单价",
                prop: "price",
                type:'input',
                append:'元',
                rules: [{
                  required: true,
                  message: "请输入销售单价",
                  trigger: "blur"
                }]
              },
              // {
              //     label: "状态",
              //     prop: "supplyStatus",
              //     type: "radio",
              //     dicData:[
              //       {
              //         label:'上架中',
              //         value:"1"
              //       },
              //       {
              //         label:'已下架',
              //         value:"0"
              //       },
              //     ],
              //     value:"1",
              //     rules: [{
              //         required: true,
              //         message: "请选择状态",
              //         trigger: "blur"
              //     }],
              //     addDisplay: false,
              //     editDisplay: false,
              //     viewDisplay: false,
              // },
          ]
        },
        viewFlag: false,
        dataNum: 1,
        arrayData: [{
					viewId: 1,
					viewName: 'view1'
        }],
        allSupplierData:[],
        uploadDataList:[],
        imageId:'',
        fileVOList: [],
        // 上传文件列表
				fileList: [],
        supplierTableLoading: false,
        supplierForm:{},
        dialogSupplier:false,
        data: [],
        goodsId:'',
        isShow2: false,
        goodsListLoading: true,
        goodsListData: [],
        goodsListPage: {
            pageSize: 10,
            currentPage: 1,
            total: 0
        },
        goodsListForm: {},
        goodsListOption: {
          height:'300px',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          addBtn:false,
          editBtn:false,
          delBtn:false,
          viewBtn: false,
          cellBtn:true,
          selection: true,
          align: "center",
          column: [
            {
              label: '',
              prop: 'bind',
              span:24,
              hide:true,
              formslot:true
            },
            {
              label: "供应商",
              prop: "supplierCompanyId",
              type: "select",
              dicUrl: "/api/service/rabbit-supplier/supplierCompany/all/list",
              props: {
                label: "name",
                value:"id"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
            },
            {
              label: "商品名称",
              prop: "name",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              rules: [{
                required: true,
                message: "请输入商品名称",
                trigger: "blur"
              }],
            },
            {//6
                label: "品牌",
                prop: "brand",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
              {
                label: "上架供应量",
                prop: "supplyQty",
                type:'input',
                rules: [{
                  required: true,
                  message: "请输入上架供应量",
                  trigger: "blur"
                }]
              },
              {
                label: "销售单价",
                prop: "price",
                type:'input',
                append:'元',
                rules: [{
                  required: true,
                  message: "请输入销售单价",
                  trigger: "blur"
                }]
              },
              {
                label: "采购数量",
                prop: "purchaseQty",
                type:'input',
                rules: [{
                  required: true,
                  message: "请输入采购数量",
                  trigger: "blur"
                }],
                cell: true
              },
              {
                label: "采购金额(元)",
                prop: "purchaseAmount",
                type:'input',
                rules: [{
                  required: true,
                  message: "请输入采购金额(元)",
                  trigger: "blur"
                }],
              },
          ]
        },
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      // permissionList() {
      //   return {
      //     addBtn: this.vaildData(this.permission.schoolGoods_add, false),
      //     viewBtn: this.vaildData(this.permission.schoolGoods_view, false),
      //     delBtn: this.vaildData(this.permission.schoolGoods_delete, false),
      //     editBtn: this.vaildData(this.permission.schoolGoods_edit, false)
      //   };
      // },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      if (this.userInfo.userType === 'canteen'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
      }
      // 设置上传接口头部信息
      const access_token = JSON.parse(localStorage.getItem("rabbit-liancan-userInfo")).content.access_token
      this.uploadHeaders = {
          'Authorization':'Bearer ' + access_token,
      };
    },
      watch: {
          'form.type'() {
              let type = this.form.type;
              //食材
              if(type == "1314471996665397249" || type == "1366636175794667521"){
              // if(type == "1315498192049037313"){
                this.option.column.forEach(then=>{
                  // then.prop == 'biddingTypeId'
                  if(then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
                      then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
                    then.addDisplay = true
                  }
                })
              }
              //燃料
              if(type == "1316561056074649602" || type == "1366636571036516354"){
              // if(type == "1315613115907416066"){
                this.option.column.forEach(then=>{
                  if(then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
                      then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
                    then.addDisplay = false;
                  }
                })
              }
          },
          'publicForm.publicName'(val){
            console.log(val)
            if(!val){
              //初始化
              this.publicRow = {}
              this.publicForm = {}
              this.publicForm.publicName = ''
            } else if (val != '') {
              console.log(this.publicRow)
              //新增时候publicRow.publicId没有值，更新时候有值
              if(this.publicRow.addOrEdit == 'add'){
                this.publicForm.publicId = this.publicRow.id
                this.publicForm.typePublic = this.publicRow.type
                this.publicForm.typeNamePublic = this.publicRow.$type
                this.publicForm.publicName = this.publicRow.name
                this.publicForm.biddingPublic = this.publicRow.bidding
                this.publicForm.biddingNamePublic = this.publicRow.$bidding
                this.publicForm.biddingTypeIdPublic = this.publicRow.biddingTypeId
                this.publicForm.biddingTypeIdNamePublic = this.publicRow.$biddingTypeId

              }
              if(this.publicRow.addOrEdit == 'edit'){
                this.publicForm.publicId = this.publicRow.publicId
                this.publicForm.typePublic = this.publicRow.type
                this.publicForm.typeNamePublic = this.publicRow.$typeQuery
                this.publicForm.publicName = this.publicRow.name
                this.publicForm.biddingPublic = this.publicRow.bidding
                this.publicForm.biddingNamePublic = this.publicRow.$biddingQuery
                this.publicForm.biddingTypeIdPublic = this.publicRow.biddingTypeId
                this.publicForm.biddingTypeIdNamePublic = this.publicRow.$biddingTypeIdQuery
              }
              this.publicForm.unitPublic = this.publicRow.unitName
              this.publicForm.netWeightPublic = this.publicRow.netWeight
              this.publicForm.brandPublic = this.publicRow.brand
              this.publicForm.foodSpecPublic = this.publicRow.foodSpec
              this.publicForm.shelfLifePublic = this.publicRow.shelfLife
              this.publicForm.manufacturerPublic = this.publicRow.manufacturer
              this.publicForm.productionPublic = this.publicRow.production
              this.publicForm.originPublic = this.publicRow.origin
              this.publicForm.qualityLevelPublic = this.publicRow.qualityLevel
              this.publicForm.qualityStandardPublic = this.publicRow.qualityStandard
              this.publicForm.storagePublic = this.publicRow.storage
              this.publicForm.remarkPublic = this.publicRow.remark
              this.publicForm.imgUrlPublic = this.publicRow.imgUrl
            }
          }
      },
    methods: {
      rowSave(row, loading, done) {
        row.publicId = this.publicRow.id
        row.name = this.publicRow.name
        row.type = this.publicRow.type
        row.typeName = this.publicRow.$type
        row.bidding = this.publicRow.bidding
        row.biddingName = this.publicRow.$bidding
        row.biddingTypeId = this.publicRow.biddingTypeId
        row.biddingTypeIdName = this.publicRow.$biddingTypeId
        row.unit = this.publicRow.unit
        row.brand = this.publicRow.brand
        row.manufacturer = this.publicRow.manufacturer

        // row.netWeight = this.publicRow.netWeight
        // row.foodSpec = this.publicRow.foodSpec
        // row.shelfLife = this.publicRow.shelfLife
        // row.production = this.publicRow.production
        // row.origin = this.publicRow.origin
        // row.qualityLevel = this.publicRow.qualityLevel
        // row.qualityStandard = this.publicRow.qualityStandard
        // row.storage = this.publicRow.storage
        // row.remark = this.publicRow.remark
        // row.imgUrl = this.publicRow.imgUrl
        console.log(row)
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$router.go(0)
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        row.publicId = this.publicRow.publicId
        row.name = this.publicRow.name
        row.type = this.publicRow.type
        row.typeName = this.publicRow.$typeQuery
        row.bidding = this.publicRow.bidding
        row.biddingName = this.publicRow.$biddingQuery
        row.biddingTypeId = this.publicRow.biddingTypeId
        row.biddingTypeIdName = this.publicRow.$biddingTypeIdQuery
        row.unit = this.publicRow.unit
        row.netWeight = this.publicRow.netWeight
        row.brand = this.publicRow.brand
        row.foodSpec = this.publicRow.foodSpec
        row.shelfLife = this.publicRow.shelfLife
        row.manufacturer = this.publicRow.manufacturer
        row.production = this.publicRow.production
        row.origin = this.publicRow.origin
        row.qualityLevel = this.publicRow.qualityLevel
        row.qualityStandard = this.publicRow.qualityStandard
        row.storage = this.publicRow.storage
        row.remark = this.publicRow.remark
        row.imgUrl = this.publicRow.imgUrl
        console.log(row)
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      // handleSave(){
      //  var params = [];
      //  var supplierIds = [];
      //  this.arrayData.forEach(ele=>{
      //    // 移除文件
      //    ele.agreement = undefined;
      //    this.uploadDataList.forEach(img=>{
      //      if(ele.viewId != '' && ele.viewId != null && ele.viewId != undefined){
      //         // 新增view的关联
      //         if(ele.viewId == img.id){
      //           if(img.url != ''){
      //               ele.agreement = img.url;
      //           }
      //         }
      //      }else{
      //       //页面加载初始化已存在的view
      //         if(ele.id == img.id){
      //           if(img.url != ''){
      //               ele.agreement = img.url;
      //           }
      //         }
      //      }
      //     })
      //   if(ele.source != 0){
      //     ele.goodsId = this.goodsId;
      //     params.push(ele);
      //   }
      //   supplierIds.push(ele.supplier);
      //  });

      //  //重设图片格式
      //  params.forEach(ele=>{
      //    if(ele.agreement != undefined && ele.agreement != null && ele.agreement.length != 0 && ele.agreement != ''){
      //       if(ele.agreement.constructor === Array){
      //         var image = '';
      //         ele.agreement.forEach(img=>{
      //           image = image + img.link + ",";
      //         })
      //         if(image != ''){
      //           ele.agreement = image.substring(0,image.length - 1);
      //         }
      //       }
      //    } else {
      //      ele.agreement = '';
      //    }
      //  })

      //  //检查是否重复提交了供应商
      //   var hash = {}
      //   for (var i in supplierIds) {
      //     if (hash[supplierIds[i]]) {
      //       this.$message({
      //         type: "error",
      //         message: "同一供应商无法重复提交，请检查后重试!"
      //       });
      //       return;
      //     }
      //     hash[supplierIds[i]] = true
      //   }

      //  addSupplier(params).then(res =>{
      //     this.onLoad(this.page);
      //     this.dialogSupplier = false;
      //     this.$message({
      //       type: "success",
      //       message: "操作成功!"
      //     });
      //   }, error => {
      //     window.console.log(error);
      //   });
      // },
      // supplierBtn(row){
      //   this.dialogSupplier = true
      //   this.allSupplierReq();
      //   this.loadSupplier(row.id);
      // },
      // allSupplierReq:function(){
      //   allSupplier().then(res => {
      //       this.allSupplierData = res.data.data;
      //   })
      // },
      // /**
			//  * 加载详情项列表
			//  */
			// loadSupplier: function(id) {
			//   this.uploadDataList = [];
      //   this.goodsId = id;
			// 	loadSupplierData(id).then(res => {
      //     this.arrayData = res.data.data;
      //     this.arrayData.forEach(ele=>{
      //       if(ele.agreement && ele.agreement != null && ele.agreement != ''){
      //         this.uploadDataList.push({'id':ele.id,'url': ele.agreement});
      //         var images = ele.agreement.split(",");
      //         var imageList = [];
      //         for(var i = 0 ; i < images.length ; i++){
      //           imageList.push({'id':ele.id,'name':'img_'+i+1,'link':images[i]})
      //         }
      //         ele.agreement = imageList;
      //       }
      //     })
			// 	});
			// 	this.supplierTableLoading = false
      // },
      // addView: function() {
			// 	this.dataNum += 1;
			// 	this.arrayData.push({
			// 		viewId: this.dataNum,
			// 		viewName: 'view' + this.dataNum
			// 	})
      // },
      // delView(item, index) {
			// 	if (item.id == undefined) {
			// 		this.viewDel(index);
			// 		return;
			// 	}
			// 	this.$confirm('是否确认删除?', '提示', {
			// 		confirmButtonText: '确定',
			// 		cancelButtonText: '取消',
			// 		type: 'warning'
			// 	}).then(function() {
			// 		return removeSupplier(item.id);
			// 	}).then(res => {
			// 		if (res.data.success) {
			// 			this.$message({
			// 				showClose: true,
			// 				message: "删除成功",
			// 				type: 'success'
			// 			})
			// 			this.loadSupplier(this.goodsId);
      //     }
			// 	})
      // },

			viewDel(index) {
				//处理图片数据
				this.fileList.forEach(element => {
					if (element.view == index) {
						this.fileList.splice(element, 1);
					}
				})
				//处理页面
				if (this.arrayData.length <= 1) {
					this.$message({
						showClose: true,
						message: "最少要填写一个供应商",
						type: 'error'
					})
					return false
				}
				this.arrayData.splice(index, 1) //删除了数组中对应的数据也就将这个位置的输入框删除
			},
      beforeOpen(done, type) {
          if(this.form.type == "1314471996665397249" || type == "1366636175794667521" ){
              // if(type == "1315498192049037313"){
              // this.option.column.forEach(then=>{
              //   if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
              //       then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
              //     then.addDisplay = true
              //   }
              // })
          }
          //燃料
          if(this.form.type == "1316561056074649602" || type == "1366636571036516354"){
              // if(type == "1315613115907416066"){
              // this.option.column.forEach(then=>{
              //   if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
              //       then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
              //     then.addDisplay = false
              //   }
              // })
          }
          // 新增初始化
          if(type == 'add'){
            this.publicRow = {}
            this.publicForm = {}
            this.publicForm.publicName = ''
          }
          done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        params.evaluationBiddingFlag = 1//获取中标的供应商
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      //图片相关
      //上传前回调
			beforeUpload: function(file) {
				var str = file.name;
				str = str.substring(str.lastIndexOf("\.") + 1, str.length);
				var reStr = this.selectType(str);
				if (reStr == "NO") {
					this.$message.error('文件格式错误');
					return false;
				} else {
					this.fileType = reStr;
					return true;
				}
			},
			//判断文件类型
			selectType(type) {
				var imageList = ["jpg", "jpeg", "png", "JPG", "PNG"];
				for (var item in imageList) {
					if (imageList[item] == type) {
						return "IMAGE";
					}
				}
				return "NO";
			},
			//上传成功回调
			handleUploadSuccess: function(response, file, fileVOList) {
        var imageUrl = file.response.data.link;

        var isAdd = true;
        this.uploadDataList.forEach(ele => {
          if(ele.id == this.imageId){
            ele.url = ele.url+","+imageUrl;
            isAdd = false;
          }
        });
        if(isAdd){
          var imageData = {'id':this.imageId,'url':imageUrl};
          this.uploadDataList.push(imageData);
        }
			},
			//删除文件回调
			handleUploadRemove: function(file, fileVOList) {
        var url = '';
        //初始化view的删除操作
        if(file.response == undefined){
          url = file.link;
        }else{
          url = file.response.data.link;
        }

        this.uploadDataList.forEach(ele=>{
          var imageList = ele.url.split(",");
          imageList = imageList.filter(img => img != url);
          ele.url = imageList.toString();
        })
      },
      uploadData:function(data){
        if(data.viewId != null && data.viewId != '' && data.viewId != undefined){
           //页面新增的view
           this.imageId = data.viewId;
        }else{
          //页面初始化已存在的view
          //检查当前view是否存在图片
          if(data.agreement != ''){
            var linkStr = '';
             data.agreement.forEach(ele=>{
                linkStr = linkStr + ele.link + ",";
             })
            linkStr = linkStr.substring(0,linkStr.length - 1);
            var isNewGroup = false;
            this.uploadDataList.forEach(v => {
              if (v.id == data.id) {
                isNewGroup = true;
                v.url = v.url + "," + linkStr;
              }
            });
            if (isNewGroup) {
              this.uploadDataList.push({'id':data.id,'url':linkStr});
            }
          }
          this.imageId = data.id;
        }
      },
      choice(){
        this.isShow2 = true
      },
      searchChange2(params, done) {
          this.query = params;
          this.goodsListPage.currentPage = 1
          this.goodsListOnLoad(this.goodsListPage, params);
          done();
      },
      searchReset2() {
          this.query = {};
          this.goodsListOnLoad(this.goodsListPage);
      },
      selectionChange2(list) {
          this.selectionList = list;
      },
      currentChange2(currentPage){
          this.goodsListPage.currentPage = currentPage;
      },
      sizeChange2(pageSize){
          this.goodsListPage.pageSize = pageSize;
      },
      goodsListOnLoad(page, params = {}) {
          this.goodsListLoading = true;
          params.pcUser = 1
          getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
              const data = res.data.data;
              this.goodsListPage.total = data.total;
              this.goodsListData = data.records;
              this.goodsListLoading = false;
              this.selectionClear();
          });
      },
      addGoods(row){
        console.log(row)
        row.addOrEdit = 'add'//新增
        this.publicRow = row
        this.publicForm.publicId = row.id
        this.publicForm.publicName = row.name
        // this.publicForm.typePublic = row.type
        // this.publicForm.typeNamePublic = row.$type
        // this.publicForm.biddingPublic = row.bidding
        // this.publicForm.biddingNamePublic = row.$bidding
        // this.publicForm.biddingTypeIdPublic = row.biddingTypeId
        // this.publicForm.biddingTypeIdNamePublic = row.$biddingTypeId
        // this.publicForm.netWeightPublic = row.netWeight
        // this.publicForm.brandPublic = row.brand
        // this.publicForm.foodSpecPublic = row.foodSpec
        // this.publicForm.shelfLifePublic = row.shelfLife
        // this.publicForm.manufacturerPublic = row.manufacturer
        // this.publicForm.productionPublic = row.production
        // this.publicForm.originPublic = row.origin
        // this.publicForm.qualityLevelPublic = row.qualityLevel
        // this.publicForm.qualityStandardPublic = row.qualityStandard
        // this.publicForm.storagePublic = row.storage
        // this.publicForm.remarkPublic = row.remark
        // this.publicForm.imgUrlPublic = row.imgUrl
        this.isShow2 = false
        // //食材
        // if(row.type == "1314471996665397249" || row.type == "1366636175794667521" ){
        //       this.publicOption.column.forEach(then=>{
        //         if(then.prop == 'biddingTypeIdNamePublic' || then.prop == 'netWeightPublic' || then.prop == 'shelfLifePublic' || then.prop == 'productionPublic' ||
        //             then.prop == 'originPublic' || then.prop == 'qualityLevelPublic' || then.prop == 'qualityStandardPublic'){
        //           then.display = true
        //         }
        //       })
        //   }
        //   //燃料
        //   if(row.type == "1316561056074649602" || row.type == "1366636571036516354"){
        //     this.publicOption.column.forEach(then=>{
        //         if(then.prop == 'biddingTypeIdNamePublic' || then.prop == 'netWeightPublic' || then.prop == 'shelfLifePublic' || then.prop == 'productionPublic' ||
        //             then.prop == 'originPublic' || then.prop == 'qualityLevelPublic' || then.prop == 'qualityStandardPublic'){
        //           then.display = false
        //         }
        //       })
        //   }
      },
      updateOpen(row, index){
        console.log(row)
        row.addOrEdit = 'edit'//更新
        this.publicRow = row
        //食材
        if(row.type == "1314471996665397249" || row.type == "1366636175794667521" ){
            this.publicOption.column.forEach(then=>{
              if(then.prop == 'biddingTypeIdNamePublic' || then.prop == 'netWeightPublic' || then.prop == 'shelfLifePublic' || then.prop == 'productionPublic' ||
                  then.prop == 'originPublic' || then.prop == 'qualityLevelPublic' || then.prop == 'qualityStandardPublic'){
                then.display = true
              }
            })
        }
        //燃料
        if(row.type == "1316561056074649602" || row.type == "1366636571036516354"){
          this.publicOption.column.forEach(then=>{
              if(then.prop == 'biddingTypeIdNamePublic' || then.prop == 'netWeightPublic' || then.prop == 'shelfLifePublic' || then.prop == 'productionPublic' ||
                  then.prop == 'originPublic' || then.prop == 'qualityLevelPublic' || then.prop == 'qualityStandardPublic'){
                then.display = false
              }
            })
        }

        // this.publicForm.publicId = row.publicId
        this.publicForm.publicName = row.name
        //调用编辑表单
        this.$refs.crud.rowEdit(row,row.$index);
      },
      upper(row) {
        this.upperShow = true
        this.upperRow = row
      },
      upperClose(){
        this.upperForm = {}
        this.upperRow = {}
        this.upperShow = false
      },
      upperSubmit(){
        this.upperRow.supplyQty = this.upperForm.supplyQty
        this.upperRow.price = this.upperForm.price
        this.upperRow.supplyStatus = '1'
        updateForStatus(this.upperRow).then(() => {
          this.upperShow = false
          this.upperForm = {}
          this.upperRow = {}
        });
      },
      addCart(row){
        row.supplierGoodsId = row.id
        row.supplierCompanyId = row.deptId
        addCart(row).then(() => {
          this.onLoad(this.page);
          this.goodsListOnLoad(this.goodsListPage);
          this.$message({
            type: "success",
            message: "添加购物车成功!"
          });
          this.$refs.crud.toggleSelection();
        });
      },
      viewShoppingCart(){
        var params = {}
        getInvoiceTitle(1, 1000, Object.assign(params, this.query)).then(res => {
              const data = res.data.data;
              console.log(data)
              this.goodsForm.deliveryDate = new Date()
              this.idNumberList = data.records
              if(data.records.length > 0){
                this.goodsForm.idNumber = data.records[0].number
                this.goodsForm.receiptUser = this.userInfo.nickname
                this.goodsForm.receiptPhone = this.userInfo.account
                this.goodsForm.receiptAddress = data.records[0].content
              }
          });
        this.isShow2 = true
      },
      removeGoodsCartRow(row){
        console.log(row)
        this.$confirm("确定移除该购物车商品?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return removeCart(row.id);
          })
          .then(() => {
            this.goodsListOnLoad(this.goodsListPage, {});
            this.$message({
              type: "success",
              message: "移除成功!"
            });
          });
      },
      updateGoodsCartRow(row,index,done,loading){
        loading()
        row.purchaseAmount = parseInt(row.purchaseQty)*row.price
        console.log(row)
        updateCart(row).then(() => {
          // this.goodsListOnLoad(this.goodsListPage);
          this.$message({
            type: "success",
            message: "保存成功!"
          });
          done()
        });
      },
      saveOrderForConfirm(){
        console.log(this.goodsForm)
        saveOrderForConfirm(this.goodsForm)
        .then(() => {
          // this.goodsListOnLoad(this.goodsListPage);
          this.$message({
            type: "success",
            message: "保存成功!"
          });
        })
        .then(() => {
          this.isShow2 = false
        })
      },
      saveOrderForDirectly(){
        console.log(this.goodsForm)
        //下单采购
        saveOrder(Object.assign({
            // planGoodsIds : this.planGoodsList,
            directlyOrderFlag : 1, //后台解析 1直接下单
            sendTime : this.goodsForm.deliveryDate,
            invoiceId : this.goodsForm.idNumber,
            userName : this.goodsForm.receiptUser,
            phone : this.goodsForm.receiptPhone,
            site : this.goodsForm.receiptAddress
        })).then(res => {
            if (res.data.success) {
                this.$message({
                    showClose: true,
                    message: res.data.message,
                    type: 'success'
                })
            } else {
                this.$message({
                    showClose: true,
                    message: res.data.message,
                    type: 'error'
                })
            }
        });
        this.isShow2 = false
      },
    }
  };
</script>

<style>
</style>
