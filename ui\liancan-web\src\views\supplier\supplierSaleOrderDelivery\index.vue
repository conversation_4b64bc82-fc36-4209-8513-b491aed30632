<template>
  <basic-container>
    <div>
      <div class="all-mess">
        <div class="mess-header">
          <div
            :class="{acitve:activeIdx==index}"
            v-for="(item,index) in messList"
            :key="index"
            @click="menuClick(index)"
          >
            {{item}}
          </div>
        </div>
        <span class="mess-content" v-if="this.activeIdx == 0">
          <avue-crud :option="option"
                    :table-loading="loading"
                    :data="data"
                    :page="page"
                    :permission="permissionList"
                    :before-open="beforeOpen"
                    v-model="form"
                    ref="crud"
                    @row-update="rowUpdate"
                    @row-save="rowSave"
                    @row-del="rowDel"
                    @search-change="searchChange"
                    @search-reset="searchReset"
                    @selection-change="selectionChange"
                    @current-change="currentChange"
                    @size-change="sizeChange"
                    @on-load="onLoad">
            <template slot="menuLeft">
              <el-button class="filter-item" size="small" type="primary"  @click="refresh">刷新</el-button>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button @click="viewDetail(row,index)" type="text" size="mini">查看明细</el-button>
            </template>
          </avue-crud>
        </span>
        <span class="mess-content" v-if="this.activeIdx == 1">
          <avue-crud :option="canteenOption"
                    :table-loading="canteenLoading"
                    :data="canteenData"
                    :page="canteenPage"
                    :permission="permissionList"
                    :before-open="beforeOpen"
                    v-model="form"
                    ref="crud"
                    @row-update="rowUpdate"
                    @row-save="rowSave"
                    @row-del="rowDel"
                    @search-change="searchCanteenChange"
                    @search-reset="searchCanteenReset"
                    @selection-change="selectionChange"
                    @current-change="canteenCurrentChange"
                    @size-change="canteenSizeChange"
                    @on-load="canteenOnLoad">
            <template slot="menuLeft">
              <el-button class="filter-item" size="small" type="primary"  @click="canteenRefresh">刷新</el-button>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button @click="canteenViewDetail(row,index)" type="text" size="mini">查看明细</el-button>
            </template>
          </avue-crud>
        </span>
      </div>
    </div>

    <!-- 查看明细 开始 -->
    <el-dialog :title="goodsTitle" :visible.sync="goodsShow" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="goodsListOption"
                 :table-loading="goodsListLoading"
                 :data="goodsListData"
                 :page="goodsListPage"
                 ref="crud"
                 @current-change="currentChange2"
                 @size-change="sizeChange2"
                  >
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="viewOrderDetail(scope.row)">查看订单
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 查看明细 结束 -->

    <!-- 查看订单 开始 -->
    <el-dialog title="订单" :visible.sync="orderShow" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="orderOption"
                 :table-loading="orderLoading"
                 :data="orderData"
                 :page="orderPage"
                 ref="crud"
                 @current-change="currentChange3"
                 @size-change="sizeChange3"
                  >
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="viewOrderDetailDialog(scope.row)">查看订单详情
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 查看订单 结束 -->

    <!-- 查看订单明细 开始 -->
    <el-dialog :title="orderDetailTitle" :visible.sync="orderDetailShow" :append-to-body="true" @close="orderDetailClose" width="70%">
      <detailVue :orderId="pageParams.id"></detailVue>
    </el-dialog>
    <!-- 查看订单明细 结束 -->

    <!-- 按食堂统计-查看明细 开始 -->
    <el-dialog :title="canteenGoodsTitle" :visible.sync="canteenGoodsShow" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="canteenGoodsListOption"
                 :table-loading="canteenGoodsListLoading"
                 :data="canteenGoodsListData"
                 :page="canteenGoodsListPage"
                 ref="crud"
                 @current-change="canteenCurrentChange2"
                 @size-change="canteenSizeChange2"
                  >
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="canteenViewOrderDetail(scope.row)">查看订单
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 查看食堂统计-查看明细 结束 -->
    <!-- 查看食堂统计 查看订单明细 开始 -->
    <el-dialog title="订单" :visible.sync="canteenOrderShow" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="canteenOrderOption"
                 :table-loading="canteenOrderLoading"
                 :data="canteenOrderData"
                 :page="canteenOrderPage"
                 ref="crud"
                 @current-change="canteenCurrentChange3"
                 @size-change="canteenSizeChange3"
                  >
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="canteenViewOrderDetailDialog(scope.row)">查看订单详情
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 查看食堂统计 查看订单明细 结束 -->
  </basic-container>


</template>

<script>
  import {getWaitingDeliveryList, getWaitingDeliveryListForDetail, getWaitingDeliveryListForOrder,
          getCanteenWaitingDeliveryList, getCanteenWaitingDeliveryForDetail} from "@/api/liancan/supplierGoods";
  import detailVue from "@/views/reportStatistics/waitingDeliveryOrder/orderDetail";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        activeIdx: 0,
        messList: [ '按食材统计', '按食堂统计'],
        form: {},
        query: {},
        queryCanteen: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        searchFrom:{},
        searchCanteenFrom:{},
        dialogVisible:false,
        dialogImageUrl:undefined,
        option: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn: false,
          menu: true,
          // printBtn:true,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "商品名称",
              prop: "name",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              rules: [{
                required: true,
                message: "请输入商品名称",
                trigger: "blur"
              }],
              // search: true,
            },
             {
                  label: "商品类型",
                  prop: "type",
                  type: "select",
                  rules: [{
                      required: true,
                      message: "请选择商品类型",
                      trigger: "blur"
                  }],
                  dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  filterable: true,
                  searchFilterable: true,
                  cascaderItem: ['bidding'],
                  editDisabled:true,
                  // hide:true,
                  addDisplay: false,
                  editDisplay: false,
                  viewDisplay: false,
              },
            {
              label: "商品大类",
              prop: "bidding",
              type: "select",
              rules: [{
                required: true,
                message: "请选择商品大类",
                trigger: "blur"
              }],
                dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                filterable: true,
                searchFilterable: true,
                cascaderItem: ['biddingTypeId'],
                dicFlag: false,
                overHidden: true,
                // hide:true,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//4
              label: "商品小类",
              prop: "biddingTypeId",
              type: "select",
              rules: [{
                required: true,
                message: "请选择商品小类",
                trigger: "blur"
              }],
                dicFlag: false,
                dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                // hide:true,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
                label: "品牌",
                prop: "brand",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
                label: "生产厂家",
                prop: "manufacturer",
                type: "input",
                rules: [{
                    required: true,
                    message: "请输入生产厂家",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
              label: "待送货总量",
                prop: "quantity",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: '接单开始日期',
              prop: 'startDate',
              type:'date',
              // searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
              searchLabelWidth:120,
            },
            {
              label: '接单结束日期',
              prop: 'endDate',
              type:'date',
              // searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
              searchLabelWidth:120,
            },
          ]
        },
        goodsListOption:{
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn: false,
          menu: true,
          // printBtn:true,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "廉餐单位",
              prop: "parentDeptId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              // search: true,
            },
            {
              label: "食堂名称",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              // search: true,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
              label: "下单总量",
                prop: "quantity",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
          ]
        },
        data: [],
        goodsListLoading: true,
        goodsTitle: '',
        orderDetailTitle: '',
        goodsName: '',
        goodsShow:false,
        goodsListData: [],
        goodsListPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        orderOption:{
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn: false,
          menu: true,
          // printBtn:true,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
                label: "下单日期",
                prop: "createTime",
                type: "date",
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd',
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "单号",
                prop: "orderId",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "联系人",
                prop: "createUserName",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "联系电话",
                prop: "phone",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
              label: "下单总量",
                prop: "quantity",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "接单人",
              prop: "supplierName",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "接单时间",
              prop: "updateTime",
              type: "date",
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
          ]
        },
        orderLoading: true,
        orderShow:false,
        orderData: [],
        orderPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        pageParams: {},
        canteenData: [],
        orderDetailShow: false,
        canteenLoading: true,
        canteenPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        canteenOption: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn: false,
          menu: true,
          // printBtn:true,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
                label: "廉餐单位",
                prop: "unitName",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
                label: "食堂名称",
                prop: "deptName",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
              label: "待送货食材种类",
                prop: "quantity",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: '接单开始日期',
              prop: 'startDate',
              type:'date',
              // searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
              searchLabelWidth:120,
            },
            {
              label: '接单结束日期',
              prop: 'endDate',
              type:'date',
              // searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
              searchLabelWidth:120,
            },
          ]
        },
        canteenGoodsListLoading: true,
        canteenGoodsTitle: '',
        canteenGoodsShow:false,
        canteenGoodsListData: [],
        canteenGoodsListPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        canteenGoodsListOption: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn: false,
          menu: true,
          // printBtn:true,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "商品名称",
              prop: "name",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
              rules: [{
                required: true,
                message: "请输入商品名称",
                trigger: "blur"
              }],
              search: true,
            },
             {
                  label: "商品类型",
                  prop: "type",
                  type: "select",
                  rules: [{
                      required: true,
                      message: "请选择商品类型",
                      trigger: "blur"
                  }],
                  dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  filterable: true,
                  searchFilterable: true,
                  cascaderItem: ['bidding'],
                  editDisabled:true,
                  // hide:true,
                  addDisplay: false,
                  editDisplay: false,
                  viewDisplay: false,
              },
            {
              label: "商品大类",
              prop: "bidding",
              type: "select",
              rules: [{
                required: true,
                message: "请选择商品大类",
                trigger: "blur"
              }],
                dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                filterable: true,
                searchFilterable: true,
                cascaderItem: ['biddingTypeId'],
                dicFlag: false,
                overHidden: true,
                // hide:true,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//4
              label: "商品小类",
              prop: "biddingTypeId",
              type: "select",
              rules: [{
                required: true,
                message: "请选择商品小类",
                trigger: "blur"
              }],
                dicFlag: false,
                dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                // hide:true,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
                label: "品牌",
                prop: "brand",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
                label: "生产厂家",
                prop: "manufacturer",
                type: "input",
                rules: [{
                    required: true,
                    message: "请输入生产厂家",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
              label: "待送货总量",
                prop: "quantity",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
          ]
        },
        canteenOrderOption:{
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn: false,
          menu: true,
          // printBtn:true,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
                label: "下单日期",
                prop: "createTime",
                type: "date",
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd',
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "单号",
                prop: "orderId",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "联系人",
                prop: "createUserName",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {
              label: "联系电话",
                prop: "phone",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//6
              label: "下单总量",
                prop: "quantity",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
            {//5
                label: "计量单位",
                prop: "unit",
                type: "select",
                dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                props: {
                    label: "dictValue",
                    value: "dictKey"
                },
                rules: [{
                    required: true,
                    message: "请选择计量单位",
                    trigger: "blur"
                }],
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
            },
          ]
        },
        canteenOrderLoading: true,
        canteenOrderShow:false,
        canteenOrderData: [],
        canteenOrderPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        canteenId: '',
      };
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          /*        addBtn: this.vaildData(this.permission.work_personnel_add, false),
                  viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                  delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                  editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      const { messName } = this.$route.query
      if(messName){
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log( this.activeIdx )
      }
    },
    components: {
      'detailVue': detailVue  //将别名demo 变成 组件 Demo
    },
    methods: {
      menuClick(idx) {
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.query = {};
          this.searchForm ={};
          this.page.currentPage = 1;
          this.onLoad(this.page);
        }
        if (idx == 1){
          this.queryCanteen = {};
          this.searchCanteenForm ={};
          this.canteenPage.currentPage = 1;
          this.canteenOnLoad(this.canteenPage);
        }
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchFrom = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getWaitingDeliveryList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      refresh(){
        this.onLoad(this.page);
      },
      viewDetail(row, index){
        console.log(this.searchFrom)
        this.goodsName = row.name
        this.goodsTitle = '商品名称: '+row.name
        this.goodsShow = true
        var params = { name: row.name }
        this.goodsListOnLoad(this.goodsListPage, params)
      },
      currentChange2(currentPage){
        this.goodsListPage.currentPage = currentPage;
      },
      sizeChange2(pageSize){
        this.goodsListPage.pageSize = pageSize;
      },
      goodsListOnLoad(page, params = {}) {
        this.goodsListLoading = true;
        getWaitingDeliveryListForDetail(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.goodsListPage.total = data.total;
          this.goodsListData = data.records;
          this.goodsListLoading = false;
        });
      },
      viewOrderDetail(row, index){
        this.orderShow = true
        var params = { name: this.goodsName, canteenId: row.deptId }
        this.orderOnLoad(this.orderPage, params)
      },
      currentChange3(currentPage){
        this.orderPage.currentPage = currentPage;
      },
      sizeChange3(pageSize){
        this.orderPage.pageSize = pageSize;
      },
      orderOnLoad(page, params = {}) {
        this.orderLoading = true;
        getWaitingDeliveryListForOrder(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.orderPage.total = data.total;
          this.orderData = data.records;
          this.orderLoading = false;
        });
      },
      viewOrderDetailDialog (row) {
        this.orderDetailTitle = '订单: '+row.orderId
        this.pageParams.id = row.orderId;
        this.orderDetailShow = true;
      },
      orderDetailClose(){
        this.pageParams = {}
        this.orderDetailShow = false;
      },
      searchCanteenReset() {
        this.queryCanteen = {};
        this.canteenOnLoad(this.canteenPage);
      },
      searchCanteenChange(params, done) {
        this.queryCanteen = params
        this.searchCanteenFrom = params;
        this.canteenPage.currentPage = 1
        this.canteenOnLoad(this.canteenPage, params);
        done();
      },
      canteenCurrentChange(currentPage){
        this.canteenPage.currentPage = currentPage;
      },
      canteenSizeChange(pageSize){
        this.canteenPage.pageSize = pageSize;
      },
      canteenOnLoad(canteenPage, params = {}) {
        this.canteenLoading = true;
        getCanteenWaitingDeliveryList(canteenPage.currentPage, canteenPage.pageSize, Object.assign(params, this.queryCanteen)).then(res => {
          const data = res.data.data;
          this.canteenPage.total = data.total;
          this.canteenData = data.records;
          this.canteenLoading = false;
          // this.selectionClear();
        });
      },
      canteenRefresh(){
        this.canteenOnLoad(this.canteenPage);
      },
      canteenViewDetail(row, index){
        this.canteenGoodsTitle = '食堂名称: '+row.deptName
        this.canteenGoodsShow = true
        var params = { canteenId: row.deptId }
        this.canteenId = row.deptId
        this.canteenGoodsListOnLoad(this.canteenGoodsListPage, params)
      },
      canteenGoodsListOnLoad(page, params = {}) {
        this.canteenGoodsListLoading = true;
        getCanteenWaitingDeliveryForDetail(page.currentPage, page.pageSize, Object.assign(params, this.queryCanteen)).then(res => {
          const data = res.data.data;
          this.canteenGoodsListPage.total = data.total;
          this.canteenGoodsListData = data.records;
          this.canteenGoodsListLoading = false;
        });
      },
      canteenViewOrderDetail(row, index){
        console.log(row)
        this.orderShow = true
        var params = { name: row.name, canteenId: this.canteenId, startDate: this.queryCanteen.startDate ,endDate: this.queryCanteen.endDate  }
        this.orderOnLoad(this.orderPage, params)
      },
      canteenCurrentChange3(currentPage){
        this.canteenOrderPage.currentPage = currentPage;
      },
      canteenSizeChange3(pageSize){
        this.canteenOrderPage.pageSize = pageSize;
      },
    }
  };
</script>

<style lang="scss" scoped>
  .all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      /deep/ div {
        width: 200px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      height: 640px;
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20px;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
