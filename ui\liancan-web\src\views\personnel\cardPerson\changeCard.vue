<template>
  <basicContainer>
    <avue-crud ref="form" v-model="form" :option="tableOption" :data="data" @on-load="onLoad" :page.sync="page"
               @row-update="handleUpdate" :table-loading="tableLoading" @search-change="handleSearch"
               @refresh-change="handleRefresh" @search-reset="handleRefresh" :before-open="beforeOpen"
               @size-change="sizeChange" @selection-change="selectionChange">
      <template slot="menu" slot-scope="scope">
        <el-button type="text" size="mini" icon="el-icon-setting" @click="showInfoHandle(scope.row)">补（换）卡</el-button>
      </template>
      <template slot="menuLeft">
        <el-button size="small" type="success" plain icon="el-icon-coin" @click="openCardMachine">开启卡务机</el-button>
        <el-button size="small" type="primary" plain icon="el-icon-coin" @click="closeCardMachine">关闭卡务机</el-button>
        <!-- <el-button size="small" type="warning" plain icon="el-icon-coin" @click="openCardBatchMachine">批量发卡</el-button> -->
      </template>
      <template slot="avatar" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
      </template>
    </avue-crud>

    <el-dialog title="查看详情" append-to-body
               :visible.sync="detailBox" width="80%" top="2vh" :before-close="onClose">
      <p v-if="isNotReady || errorMsg" style="color: crimson;font-size: x-large">{{errorMsg}}</p>
      <span class="food-title">基本信息</span>
      <div class="food-view">
        <div  class="row-table" style="">
          <div class="row-table-first" style="width: 10%;">人员照片</div>
          <div class="row-table-second" style="width: 89.5%;" >
            <el-image :src="detailInfo.avatar" fit="cover" style="height: 150px;" @click="handleClickPreview(row.avatar)"></el-image>
          </div>
        </div>

        <div  class="row-table">
          <div class="row-table-first">姓名</div><div class="row-table-second">{{detailInfo.userName || ''}}</div>
          <div class="row-table-first">性别</div>
          <div class="row-table-second">
            <span v-if="detailInfo.sex == '1'">男</span>
            <span v-else-if="detailInfo.sex == '2'">女</span>
            <span v-else>未知</span>
          </div>
          <div class="row-table-first">民族</div><div class="row-table-second">{{detailInfo.nation || ''}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">部门</div><div class="row-table-second">{{ detailInfo.deptName || '' }}</div>
          <div class="row-table-first">人员类别</div><div class="row-table-second">{{ detailInfo.$personnelType || '' }}</div>
          <div class="row-table-first">学龄段</div><div class="row-table-second">{{ '' }}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">人员属性</div><div class="row-table-second">{{ detailInfo.attributeName || '' }}</div>
          <div class="row-table-first">用餐类别</div><div class="row-table-second">{{ detailInfo.mealsName || '' }}</div>
          <div class="row-table-first">登录帐号</div><div class="row-table-second">{{ detailInfo.account || '' }}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">编号</div><div class="row-table-second">{{ detailInfo.studentJobNo || '' }}</div>
          <div class="row-table-first">状态</div>
          <div class="row-table-second" style="width: 56.2%;">
            <span v-if="detailInfo.cardStatus == '1'">正常</span>
            <span v-else-if="detailInfo.cardStatus == '2'">已挂失</span>
            <span v-else-if="detailInfo.cardStatus == '3'">异常</span>
            <span v-else-if="detailInfo.cardStatus == '4'">过期</span>
            <span v-else>未开卡</span>
          </div>
        </div>
      </div>
      <span class="food-title">ic卡信息</span>
      <el-form ref="cardForm" :model="cardForm" :rules="rules" :inline="true" label-width="80px" style="margin-top: 20px;width: 700px">
        <el-form-item label="IC卡设备" prop="deviceId">
          <el-select ref="detailInfoRef" v-model="cardForm.deviceId" placeholder="请选择IC卡设备" style="width: 220px;">
            <el-option
              v-for="item in this.detailInfo.icDeviceList"
              :key="item.id"
              :label="item.name+' ('+item.equipmentCode+')'"
              :value="item.id"
            />
          </el-select>
          <el-button @click="chooseSerial" type="primary" size="small" round>选择</el-button>
        </el-form-item>
        <br/>
        <el-form-item label="卡序号(新)" prop="cardNum">
          <el-input v-model="cardForm.cardNum" placeholder="请输入卡序号(新)" style="width: 220px;" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="卡号(新)" prop="cardId">
          <el-input v-model="cardForm.cardId" placeholder="请输入卡号(新)" style="width: 220px;"></el-input>
        </el-form-item>
        <br/>
        <el-form-item label="生效日期">
          <el-date-picker type="date" placeholder="请选择生效日期" v-model="cardForm.startDate"></el-date-picker>
        </el-form-item>
        <el-form-item label="终止日期">
          <el-date-picker type="date" placeholder="请选择终止日期" v-model="cardForm.endDate"></el-date-picker>
        </el-form-item>
        <br/>
        <el-form-item label="补(换)卡费用">
          <el-input v-model="cardForm.fee" placeholder="请输入补(换)卡费用" style="width: 220px;"></el-input>
        </el-form-item>
        <el-form-item label="补(换)卡原因">
          <el-input v-model="cardForm.reason" placeholder="请输入补(换)卡原因" style="width: 220px;"></el-input>
        </el-form-item>
        <br/>
        <el-form-item>
          <el-button type="primary" @click="onSubmit('cardForm')">保存</el-button>
          <el-button @click="onClose">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </basicContainer>
</template>

<script>
import {
  sendCardCmd,
  sendCard,
  pares,
  readHeartBeat,
  heartBeatCmd,
  covert,
  resetStatusCmd,
  setMachineTimeCmd,
  optCardCmd,
  readCard,
  readCardCmd,
  formatDateTime,
  handShakeCmd,
  createCard,
} from "@/api/waterCard/cardSerialPort";
import {getList,
  getCardNum
} from "@/api/personnel/systemPersonnel";
import {getList as getDeviceList} from "@/api/businessOutlets/window";

import {
  mapGetters
} from 'vuex';
import func from "@/util/func";

const DIC = {
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
  VAILD: [{
    label: '人员(1)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '1'
  },{
    label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '2'
  },
  ],
  VAILD2: [{
    label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
    value: '2'
  },
  ]
}
export default {
  data() {
    return {
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      categoryStatus:undefined,
      searchForm: {},
      query: {},
      form: {},
      data: [],
      detailInfo: {},
      detailBox: false,
      size: 'small',
      tableLoading: false,
      selectionList: [],
      batchIndex: -1,
      tableOption: {
        height: 'auto',
        calcHeight: 80,
        searchShow: true,
        searchMenuSpan: 6,
        border: true,
        addBtn: false,
        delBtn: false,
        editBtn: false,
        viewBtn: false,
        selection: true,
        align: 'left',
        column: [
            {
                label: "主键",
                prop: "id",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
                hide: true,
            },
            {
                label: "姓名",
                prop: "userName",
                type: "input",
                sortable:true,
                search:true,
                rules: [{
                  required: true,
                  message: "请输入姓名",
                  trigger: "blur"
                }],

              },
              {
                label: '性别',
                prop: 'sex',
                type: "radio",
                //slot: true,
                dicData: DIC.sex,
                search:true,
                sortable:true,
                rules: [{
                  required: true,
                  message: '请选择性别',
                  trigger: 'blur'
                }],
              },
              {
                label: "编号",
                prop: "studentJobNo",
                type: "input",
                search:true,
                sortable:true,
                rules: [{
                  required: true,
                  message: "请输入编号",
                  trigger: "blur"
                }],
              },
              {
                label: "部门",
                prop: "deptName",
                type: "input",
                overHidden: true,
                sortable:true,
                minWidth: 120,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
              },
              {
                label: "部门",
                prop: "deptId",
                type: "tree",
                dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
                props: {
                  label: "title",
                  value: "id"
                },
                editDisplay: false,
                viewDisplay: false,
                multiple:true,
                /*slot:true,*/
                search:true,
                hide:true,
                rules: [{
                  required: true,
                  message: "请输入部门",
                  trigger: "click"
                }]
              },
              {
                label: "用餐类别",
                sortable:true,
                prop: "mealsName",
                type: "input",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: false,
              },
              {
                label: "用餐类别",
                prop: "mealsType",
                type: "select",
                search:true,
                hide:true,
                editDisplay: false,
                viewDisplay: false,
                dicUrl: "/api/service/rabbit-liancan/diningType/dict",
                props: {
                  res: "data",
                  label: "name",
                  value: "id"
                },
              },
              {
                label: '人员类别',
                prop: 'personnelType',
                type: "radio",
                //slot: true,
                dicData: DIC.VAILD,
                search:true,
                rules: [{
                  required: true,
                  message: '请选择人员类别',
                  trigger: 'blur'
                }],
                width: 90,
              },
              {
                label: '人员类别',
                prop: 'personnelType2',
                type: "radio",
                //slot: true,
                dicData: DIC.VAILD2,
                value:'2',
                search:true,
                hide:true,
                rules: [{
                  required: true,
                  message: '请选择人员类别',
                  trigger: 'blur'
                }],
                width: 90,
              },
              {
                label: '人员类别',
                prop: 'personnelTypeName',
                type: "input",
                addDisplay:false,
                hide:true,
                rules: [{
                  required: true,
                  message: '请选择人员类别',
                  trigger: 'blur'
                }],
                width: 90,
              },
              {
                label: "人员照片",
                prop: "avatar",
                type: 'upload',
                listType: 'picture-img',
                action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                propsHttp: {
                  res: 'data',
                  url: 'link',
                },
                span:24,
                slot: true,
                width: 90,
              },
              {
                label: "人员状态",
                prop: "status",
                type: "radio",
                dicData: [{
                  label: "启用",
                  value: '1'
                },
                ],
                value:'1',
                search:true,
                hide:true,
                rules: [{
                  required: true,
                  message: "请选择人员状态",
                  trigger: "blur"
                }],
              },
              // {
              //   label: "卡号",
              //   prop: "cardId",
              //   type: "input",
              //   editDisplay: false
              // },
              // {
              //   label: "卡序号",
              //   prop: "cardNum",
              //   type: "input",
              //   editDisplay: false
              // },
              // {
              //   label: "卡状态",
              //   prop: "cardStatus",
              //   type: "select",
              //   dicData: [
              //     {
              //       value: '0',
              //       label: '未开卡'
              //     },
              //     {
              //       value: '1',
              //       label: '正常'
              //     },
              //     {
              //       value: '2',
              //       label: '已挂失'
              //     },
              //     {
              //       value: '3',
              //       label: '异常'
              //     },
              //     {
              //       value: '4',
              //       label: '过期'
              //     }
              //   ],
              //   // search: true,
              //   editDisplay: false
              // },
              // {
              //   label: "生效日期",
              //   prop: "startDate",
              //   type: "date",
              //   format: "yyyy-MM-dd",
              //   valueFormat: "yyyy-MM-dd",
              //   rules: [{
              //     required: true,
              //     message: "请选择生效日期",
              //     trigger: "blur"
              //   }],
              // },
              // {
              //   label: "终止日期",
              //   prop: "endDate",
              //   type: "date",
              //   format: "yyyy-MM-dd",
              //   valueFormat: "yyyy-MM-dd",
              //   rules: [{
              //     required: true,
              //     message: "请选择终止日期",
              //     trigger: "blur"
              //   }],
              // },
        ]
      },
      syncBox: false,
      schoolList: [],
      // 提交表单
      // deviceId: 0,
      //记录当前卡务机
      deviceInfo: {},
      cardForm: {
        // fee: 0,
        // waterTypeList: []
      },
      rules: {
        deviceId: [
          { required: true, message: '请选择IC卡设备', trigger: 'blur' },
        ],
        waterTypeList: [
          { required: true, message: '请选择开通水务', trigger: 'blur' },
        ],
        cardNum: [
          { required: true, message: '请输入卡序号', trigger: 'blur' },
        ],
        startDate: [
          { required: true, message: '请选择生效日期', trigger: 'blur' },
        ],
        endDate: [
          { required: true, message: '请选择终止日期', trigger: 'blur' },
        ],
        identifyCode: [
          { required: true, message: '请输入学生帐号', trigger: 'blur' },
        ],
        cardId: [
          { required: true, message: '请输入卡序列号', trigger: 'blur' },
        ],
      },

      //串口相关参数=======================
      // 串口配置
      isNotReady: true,
      isSendCard: false,
      unitCode: 0,
      port: {},
      reader: {},
      messageRecive: {},
      messageReciveStr: '',
      reciveStr: '',
      tipStr: '卡务机未准备就绪,请稍后,若长时间未反应请手动开启',
      errorMsg: '',
      connected: {},
      serialOptions: {
        baudRate: 115200, // 一个正的、非零的值，表示串口通信应该建立的波特率
        dataBits: 8, // 7或8的整数值，表示每帧的数据位数。默认值为8
        stopBits: 1, // 1或2的整数值，表示帧结束时的停止位数。默认值为1。
        parity: 'none', // 奇偶校验模式为“none”、“偶”或“奇”。默认值为none。
        bufferSize: 2048, // 一个无符号长整数，指示要建立的读和写缓冲区的大小。如果未通过，默认值为255。
        flowControl: 'none', // 流控制类型，“none”或“hardware”。默认值为none。
      },
      //串口相关参数=======================
      // 过滤设备与Arduino Uno USB供应商/产品id。
      portFilters: [
        { usbVendorId: 0x2341, usbProductId: 0x0043 },
        { usbVendorId: 0x2341, usbProductId: 0x0001 }
      ],
    }
  },
  async mounted() {
    //加载页面完毕开启串口
    // this.prepareOpen();
  },
  created() {
    this.connected.value = false;
    this.categoryStatus = "0";
  },
  async destroyed() {
    //关闭页面关闭串口
    this.prepareClose();
  },
  computed: {
    ...mapGetters(['tenant', 'userInfo']),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  watch: {
    'form.saleType'() {
      let saleType = this.form.saleType;
      let groupIds = this.findObject(this.tableOption.column, 'groupIds');
      if (saleType == '2') {
        groupIds.display = true;
      } else {
        groupIds.display = false;
      }
    }
  },
  methods: {
    openCardBatchMachine() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选中的数据批量发卡?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.batchIndex = 0;
          this.showInfo(this.selectionList[this.batchIndex]);
        })
        .then(() => {
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          // this.onLoad(this.page);
        });
    },
    openCardMachine() {
      //加载页面完毕开启串口
      this.prepareOpen();
    },
    closeCardMachine() {
      //关闭页面关闭串口
      this.prepareClose();
    },
    beforeOpen(done, type) {
      if (["edit"].includes(type)) {
        // 所属学校
        const schoolId = this.findObject(this.tableOption.column, 'schoolId');
        schoolId.disabled = true;
      }
      done();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      console.log(this.query)
      this.query.status = "1";
      params.categoryStatus = this.categoryStatus;
      params.cardStatus = '2';//查询状态为2已挂失的人员, 卡状态(0:未开卡,1:正常,2:已挂失,3:异常,4过期)
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
        if (data.records.length > 0) {
          const code = func.toInt(data.records[0].cloudSchoolCode)
          if (code !== this.unitCode) {
            this.unitCode = code
          }
        }
      });
      //加载脸卡消费机
      this.getDeviceList()
    },
    //多选
    selectionChange(list) {
      this.batchIndex = -1;
      this.selectionList = list;
    },
    selectionClear() {
      this.batchIndex = -1;
      this.selectionList = [];
      try{
        this.$refs.crud.toggleSelection();
        // eslint-disable-next-line no-empty
      }catch (error){}
    },
    sizeChange(pageSize) {
      this.page.pageSize = pageSize;
      this.onLoad(this.page)
    },
    handleSearch: function(params, done) {
      this.query = params;
      this.page.currentPage = 1;
      this.onLoad(this.page, params);
      done();
    },
    handleRefresh: function() {
      this.query = {};
      this.page.currentPage = 1;
      this.onLoad(this.page)
    },
    handleUpdate(row, index, done, loading) {
      update(row).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        window.console.log(error);
        loading();
      });
    },
    getDeviceList(){
      //加载脸卡消费机
      const params = {equipmentType: '4'}//4:脸卡消费机
      getDeviceList(1, -1, Object.assign(params, this.query)).then(res => {
        console.log(res)
        const data = res.data.data;
        this.detailInfo.icDeviceList = data.records
      });
    },
    async showInfoHandle(row) {
        await this.showInfo(row);
        this.$set(this.cardForm,"cardId", '');
    },
    async showInfo(row) {
      //加载页面完毕开启串口
      // this.unitCode = func.toInt(row.schoolCode);
      // this.prepareOpen();
      if (!this.port.value){
        //打开卡务机
        this.prepareOpen();
      }

      this.detailInfo = row;
      //加载脸卡消费机
      this.getDeviceList()

      //请求后台获取卡序号
      // this.$set(this.cardForm,"identifyCode",res.data.data.identifyCode);
      // await getCardNum(row.id).then(res => {
      //   console.log(res)
      //   // this.$set(this.cardForm,"cardId", res.data.data);
      //   this.$set(this.cardForm,"cardNum", res.data.data);
      // });
      this.isSendCard = false;
      this.detailBox = true;

      //
      // //获取用户信息
      // getDetails(row.id).then(res => {
      //   if (res.data.code == 200) {

      //   }
      // }, error => {
      //   window.console.log(error);
      // })
    },
    onClose() {
      //关闭页面关闭串口
      // this.prepareClose();
      try{
        this.setResetStatusHandler();
      }catch (error) {
        console.log(error)
      }
      // this.cardForm = {fee: 0,waterTypeList: []}
      this.detailBox = false
    },
    // 开卡提交
    async onSubmit(formName) {
      console.log(formName)
      //判断是否选择了卡务机
      if(this.cardForm.deviceId == NaN || this.cardForm.deviceId == undefined || this.cardForm.deviceId == ''){
        this.$message.error("请选择IC卡设备");
        return;
      }
      //判断是否录入了新卡号
      if(this.cardForm.cardId == NaN || this.cardForm.cardId == undefined || this.cardForm.cardId == ''){
          this.$message.error("请输入卡号");
          return;
      }
      //获取新的卡号
      await getCardNum(this.detailInfo.id).then(res => {
        console.log(res)
        // this.$set(this.cardForm,"cardId", res.data.data);
        this.$set(this.cardForm,"cardNum", res.data.data);
      });
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //先发卡成功才能保存
          if (this.isSendCard){
            //已经发卡，可以直接保存
            this.applyForm();
          }else{
            // this.$confirm('是否现在发卡?', '提示', {
            //   confirmButtonText: '确定',
            //   cancelButtonText: '取消',
            //   type: 'warning'
            // }).then(() => {
              this.sendCard();
            // });
          }
        } else {
          console.log('error submit!!');
          return false;
        }
      });
    },
    applyForm() {
      // 1开卡，2补卡
      this.$set(this.cardForm,"applyType",'2');
      this.$set(this.cardForm,"id",this.detailInfo.id);

      createCard(this.cardForm).then(res => {
        if (res.data.code == 200) {
          //关闭页面关闭串口
          //批量发卡
          let isNext = false;
          if (this.selectionList.length>0){
            this.batchIndex+=1;
            if (this.batchIndex<this.selectionList.length){
              isNext = true;
              this.showInfo(this.selectionList[this.batchIndex]);
              setTimeout(() => {
                this.sendCard();
              }, 1000);
            }
          }
          if (!isNext){
            this.detailBox = false;
            this.$message.success('保存成功！');
            this.onLoad(this.page);
          }
        }
      }, error => {
        window.console.log(error);
      })
    },
    prepareClose(){
      console.log("============prepareClose")
      try {
        try{
          this.setResetStatusHandler();
        }catch (error) {
          console.log(error)
        }
        try {
          setTimeout(()=>this.closeSerial() ,1000);
        }catch (error){
          console.log(error);
        }
      }catch (error){
        console.log(error);
      }finally {
        this.detailBox = false;
        this.isSendCard = false;
        this.isNotReady = true;
        this.errorMsg = this.tipStr;
        this.connected.value = false;
      }
    },

    // 测试串口==========================================================================
    //打开串口前判断
    async prepareOpen(){
      if (!this.unitCode || this.unitCode==0) {
        this.errorMsg = '请选择所属学校并搜索';
        this.ElNotification({title: '发卡失败',message: '请选择所属学校并搜索',type: 'warning',duration: 2000})
        return;
      }
      console.log("============prepareOpen")
      this.isSendCard = false;
      this.isNotReady = true;
      this.errorMsg = this.tipStr;
      if ("serial" in navigator) {
        console.log("支持串口功能")
        // 获取用户之前授予该网站访问权限的所有串口
        try {
          const ports = await navigator.serial.getPorts();
          navigator.serial.addEventListener('connect', e => {
            // Add |e.target| to the UI or automatically connect.
            console.log(e);
          });

          navigator.serial.addEventListener('disconnect', e => {
            // Remove |e.target| from the UI. If the device was open the
            // disconnection can also be observed as a stream error.
            console.log(e);
          });

          if (ports!=undefined){
            console.log(ports);
            if (ports.length > 1) {//如果有多个，则全部删除
              for (var i=0;i<ports.length;i++) {
                var itPort = ports[i];
                itPort.forget();
              }
              this.errorMsg = '打开串口失败,请确保卡务机正确连接电脑后手动选择打开1';
              this.ElNotification({title: '打开串口失败',message: '打开串口失败,请确保卡务机正确连接电脑后手动选择打开1',type: 'warning',duration: 2000})
            }else if (ports.length == 1){
              this.port.value = ports[0];
              this.openSerial();
            }else{
              this.errorMsg = '打开串口失败,请确保卡务机正确连接电脑后手动选择打开2';
              this.ElNotification({title: '打开串口失败',message: '打开串口失败,请确保卡务机正确连接电脑后手动选择打开2',type: 'warning',duration: 2000})
              this.chooseSerial();
            }
          }else{
            this.errorMsg = '打开串口失败,请确保卡务机正确连接电脑后手动选择打开3';
            this.ElNotification({title: '打开串口失败',message: '打开串口失败,请确保卡务机正确连接电脑后手动选择打开3',type: 'warning',duration: 2000})
          }
        }catch (error){
          console.error(error);
        }
      } else {
        this.errorMsg = '您的浏览器版本太低，可能不支持浏览器串口的使用，开发模式请使用localhost，正式环境请使用https';
        this.$message.success('您的浏览器版本太低，可能不支持浏览器串口的使用，开发模式请使用localhost，正式环境请使用https');
      }
    },
    // 1.选择串口
    async chooseSerial(){
      // 提示用户选择一个串口
      try {
        // 提示用户选择一个串口
        this.port.value = await navigator.serial.requestPort();

        // 获取用户之前授予该网站访问权限的所有串口
        // const ports = await navigator.serial.getPorts();

        console.log(this.port.value);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '选择串口失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
      this.errorMsg = '打开串口接收信息';
      this.ElNotification({title: '选择串口成功',message: '打开串口接收信息',type: 'success',duration: 2000})
      this.openSerial();
    },

    // 2.打开串口
    async openSerial(){
      // try {
      //   this.closeSerial();
      // } catch (error) {
      //   console.log(error);
      // }
      try {
        await this.port.value.open(this.serialOptions)
        //发送握手指令
        let replyMessage = handShakeCmd();
        console.log("发送握手请求：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        console.log(error);
        if (error.toString().indexOf("The port is already open")>=0) {
          console.log("The port is already open");
          this.closeSerial();
          await this.port.value.open(this.serialOptions)
        }else{
          return;
        }
      }
      try {
        if (this.port != undefined && this.port.value!=undefined && this.port.value.readable!=undefined) {
          this.reader.value = this.port.value.readable.getReader()
          this.connected.value = true
          this.readLoop()
        }
      }catch (error) {
        console.log(error);
      }
    },
    // 3.关闭串口
    async closeSerial(){
      try {
        if (this.reader != undefined && this.reader.value!=undefined) {
          try {
            this.reader.value.releaseLock();
          }catch (error) {
            console.log(error);
          }
          // try{
          //   await this.reader.value.cancel();
          // }catch (error){
          //   console.log(error);
          // }
        }
        if (this.port != undefined && this.port.value!=undefined) {
          try{
            await this.port.value.close();
          }catch (error){
            console.log(error);
          }
        }
      } catch (error) {
        console.log(error);
      } finally {
        this.connected.value = false
        this.isNotReady = true;
        this.errorMsg = this.tipStr;
      }
    },
    ///////////////////卡务操作///////////////////////////////////////
    // 4.发卡
    async sendCard(){
      try {
        if (!this.unitCode || this.unitCode==0) {
          this.errorMsg = '未设置单位码';
          this.ElNotification({title: '发卡失败',message: '未设置单位码',type: 'warning',duration: 2000})
          return;
        }
        // 判断串口是否已经打开
        if (!this.port.value){
          this.errorMsg = '请先选择卡务机串口并打开';
          this.ElNotification({title: '发卡失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
          return;
        }
        //判断是否选择了对应的卡务机设备
        if (!this.port.sn){
          this.errorMsg = '卡务机尚未准备就绪,请稍后再试~~~';
          this.ElNotification({title: '发卡失败',message: '卡务机尚未准备就绪,请稍后再试~~~',type: 'warning',duration: 2000})
          return;
        }
        //判断当前卡务机是否为所选择的卡务机
        // let detailInfoId = this.$refs.detailInfoRef.selected.value;
        let isFind = this.findMachine();
        if (!isFind){
          this.errorMsg = '当前卡务机未注册';
          this.ElNotification({title: '发卡失败',message: '当前卡务机未注册',type: 'warning',duration: 2000})
          return;
        }

        let accountNo = this.detailInfo.studentJobNo;
        // accountNo = "100264";
        if (accountNo==undefined || accountNo==""){
          this.errorMsg = '学号未填写';
          this.ElNotification({title: '发卡失败',message: '学号未填写',type: 'warning',duration: 2000})
          return;
        }
        let cardNo = this.cardForm.cardNum;
        if (!cardNo){
          this.errorMsg = '卡号未填写';
          this.ElNotification({title: '发卡失败',message: '卡号未填写',type: 'warning',duration: 2000})
          return;
        }
        let replyMessage = sendCardCmd(accountNo,cardNo);//学号跟用户帐号
        console.log("发卡请求：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '发卡失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
    },

    // 5.挂失
    async lossCard(){
      try {
        // 判断串口是否已经打开
        if (!this.port.value){
          this.errorMsg = '请先选择卡务机串口并打开';
          this.ElNotification({title: '挂失失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
          return;
        }
        let accountNo = this.detailInfo.account;
        // accountNo = "100264";
        if (!accountNo){
          this.errorMsg = '学号未填写';
          this.ElNotification({title: '挂失失败',message: '学号未填写',type: 'warning',duration: 2000})
          return;
        }
        let cardNo = this.cardForm.cardNum;
        if (!cardNo){
          this.errorMsg = '卡号未填写';
          this.ElNotification({title: '挂失失败',message: '卡号未填写',type: 'warning',duration: 2000})
          return;
        }
        let cardStatus = 1;
        let replyMessage = optCardCmd(accountNo,cardNo,cardStatus);
        console.log("挂失请求：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '挂失失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
    },

    // 6.解挂
    async unlossCard(){
      try {
        // 判断串口是否已经打开
        if (!this.port.value){
          this.errorMsg = '请先选择卡务机串口并打开';
          this.ElNotification({title: '解挂失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
          return;
        }
        let accountNo = this.detailInfo.account;
        // accountNo = "100264";
        if (!accountNo){
          this.errorMsg = '学号未填写';
          this.ElNotification({title: '解挂失败',message: '学号未填写',type: 'warning',duration: 2000})
          return;
        }
        let cardNo = this.cardForm.cardNum;
        if (!cardNo){
          this.errorMsg = '卡号未填写';
          this.ElNotification({title: '解挂失败',message: '卡号未填写',type: 'warning',duration: 2000})
          return;
        }
        let cardStatus = 0;
        let replyMessage = optCardCmd(accountNo,cardNo,cardStatus);
        console.log("解挂请求：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '解挂失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
    },
    // 7.设置时间
    async setMachineTimeHandler(){
      try {
        //检测是否已经打开串口
        if (!this.port.value){
          this.errorMsg = '请先选择卡务机串口并打开';
          this.ElNotification({title: '设置时间失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
          return;
        }
        let replyMessage = setMachineTimeCmd();
        console.log("设置时间：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '设置时间失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
    },
    // 8.复位
    async setResetStatusHandler(){
      try {
        //检测是否已经打开串口
        if (!this.port.value){
          this.errorMsg = '请先选择卡务机串口并打开';
          this.ElNotification({title: '复位失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
          return;
        }
        let replyMessage = resetStatusCmd();
        console.log("复位：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '复位失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
    },
    // 9.读卡
    async readCardHandler(){
      try {
        //检测是否已经打开串口
        if (!this.port.value){
          this.errorMsg = '请先选择卡务机串口并打开';
          this.ElNotification({title: '读卡失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
          return;
        }
        let replyMessage = readCardCmd();
        console.log("读卡：" + replyMessage);
        this.sendData(replyMessage);
      } catch (error) {
        // 错误提示弹窗
        this.errorMsg = '错误信息：' + error;
        this.ElNotification({title: '读卡失败',message: '错误信息：' + error,type: 'warning',duration: 2000})
        return;
      }
    },
    ///////////////////卡务操作///////////////////////////////////////
    async readLoop() {
      try {
        while (this.connected.value) {
          try {
            // eslint-disable-next-line no-constant-condition
            while (this.port.value.readable) {
              try{
                const { value, done } = await this.reader.value.read()
                if (done) {
                  this.reader.value.releaseLock();
                  break;
                }
                this.messageRecive.value = new TextDecoder().decode(value)
                console.log(this.messageRecive.value);
                this.messageReciveStr+=this.messageRecive.value;
                this.reciveStr+=this.messageRecive.value;
                //若收到以'\r\n'结尾，说明接收完毕
                if (this.reciveStr.endsWith("\r\n")) {
                  try {
                    //判断接收到的是什么指令
                    let cardCmd = {};
                    cardCmd = pares(this.reciveStr);
                    console.log(cardCmd.cmd)
                    if (cardCmd.cmd==='XT') {//心跳
                      let heartBeatEntity = {};
                      heartBeatEntity = readHeartBeat(this.reciveStr)
                      this.port.sn = heartBeatEntity.sn;
                      this.isNotReady = false;
                      this.errorMsg = "";
                      var bRet = this.findMachine();
                      if (!bRet) {
                        this.errorMsg = '未注册的卡务机';
                        console.log("初始化失败,未注册的卡务机");
                        this.ElNotification({title: '初始化失败',message: '未注册的卡务机',type: 'error',duration: 2000});
                      }

                      //如果窗口号为0则初始化卡务机
                      if (!this.deviceInfo) {
                        this.errorMsg = '未注册的卡务机1';
                        console.log("初始化失败,未注册的卡务机1");
                        this.ElNotification({title: '初始化失败',message: '未注册的卡务机1',type: 'error',duration: 2000});
                      }

                      if (this.deviceInfo && "0"==heartBeatEntity.windowNo) {
                        console.log("this.deviceInfo.unitCode="+this.deviceInfo.unitCode+",this.unitCode="+this.unitCode);
                        if (!this.deviceInfo.unitCode==this.unitCode){
                          this.errorMsg = '未注册的卡务机2';
                          console.log("初始化失败,未注册的卡务机2");
                          this.ElNotification({title: '初始化失败',message: '未注册的卡务机2',type: 'error',duration: 2000});
                        }else{
                          const date = new Date();
                          const currentDateTime = formatDateTime(date, 'yyyy-MM-dd HH:mm:ss');
                          this.sendData("\r\n#SJ="+currentDateTime+"$\r\n");

                          const currentDate = formatDateTime(date, 'yyMMdd');
                          console.log("date:" + currentDate);
                          const lastDate = currentDate;
                          console.log("lastDate:" + lastDate);
                          console.log("company unitCode:" + this.deviceInfo.unitCode);
                          const s = parseInt(this.deviceInfo.unitCode) + parseInt(lastDate);
                          console.log("s:" + s);
                          const scmd = "\r\n#CSH=1," + heartBeatEntity.machineType + ","+s+",太空风测试$\r\n";
                          //初始化卡务机
                          this.sendData(scmd);
                          console.log("scmd:" + scmd);
                        }
                      }

                      //回复系统
                      let random = heartBeatEntity.randomLock;
                      let coverRandom = covert(random);
                      console.log("coverRandom: " + coverRandom);
                      console.log("unitCode: " + this.unitCode);
                      let randomCode = coverRandom * 2 + this.unitCode;
                      let replyMessage = heartBeatCmd(randomCode);
                      console.log("XT回复：" + replyMessage);
                      this.sendData(replyMessage);
                    }else if (cardCmd.cmd==='SJHQ') {//卡务机请求设置时间
                      let replyMessage = setMachineTimeCmd();
                      console.log("设置时间：" + replyMessage);
                      this.sendData(replyMessage);
                    }else if (cardCmd.cmd==='SJFH') {//设置时间返回
                      console.log("设置时间返回："+ cardCmd.body)
                    }else if (cardCmd.cmd==='DKFH') {//读卡返回
                      let cardEntity = {};
                      cardEntity = readCard(this.reciveStr);
                      this.ElNotification({title: '读卡成功',message: '帐号：'+cardEntity.accountNo + '\n'
                          +'卡号：'+cardEntity.cardNo + '\n'
                          +'卡状态：'+cardEntity.cardStatus + '\n'
                          +'挂失状态：'+cardEntity.lossStatus
                        ,type: 'success',duration: 2000})
                    }else if (cardCmd.cmd==='ZTFWFH') {//复位返回
                      console.log("复位返回："+ cardCmd.body)
                    }else if (cardCmd.cmd==='FKFH') {//发卡返回
                      let cardEntity = {};
                      cardEntity = sendCard(this.reciveStr);
                      //状态(0正常,2无效卡),操作(0成功,1失败),账号,卡号
                      if ("0"==cardEntity.cardStatus && "0"==cardEntity.optStatus) {
                        this.isSendCard = true;
                        // this.ElNotification({title: '发卡成功',message: '发卡成功',type: 'success',duration: 2000})
                        //提示是否保存记录
                        // this.$confirm('发卡成功,是否保存?', '提示', {
                        //   confirmButtonText: '确定',
                        //   cancelButtonText: '取消',
                        //   type: 'warning'
                        // }).then(() => {
                          this.applyForm();
                        // });
                      }
                    }
                  }catch (error){
                    console.error(error);
                  }finally {
                    //处理完毕清空已接收的字符串
                    this.reciveStr = '';
                  }
                }
              }catch (error){
                console.error(error);
              }
            }
          } catch (error) {
            console.error(error);
          } finally {
            this.reader.releaseLock();
          }
        }
      } catch (error) {
        this.errorMsg = `串口读取信息失败：${error}`;
        this.ElNotification({title: '读取串口失败',message: `串口读取信息失败：${error}`,type: 'error',duration: 2000});
      }
    },
    async sendData(replyMessage) {
      console.log("发送数据:"+replyMessage);
      if (this.port.value) {
        if (replyMessage) {
          if (this.port==undefined || this.port.value==undefined || this.port.value.writable==undefined){
            this.errorMsg = '请先选择卡务机串口并打开';
            this.ElNotification({title: '发送失败',message: '请先选择卡务机串口并打开',type: 'warning',duration: 2000})
            return;
          }
          const writer = this.port.value.writable.getWriter();
          try{
            console.log("即将发送数据:"+replyMessage)
            await writer.write(new TextEncoder().encode(replyMessage));
          }catch (error){
            console.log(error);
          }finally {
            // await writer.releaseLock();
            await writer.close();
          }
          console.log("发送成功");
        } else {
          return this.ElNotification({
            title: '发送数据失败',
            message: "请输入需要发送的数据内容",
            type: "warning",
            duration: 2000,
          });
        }
      } else {
        this.ElNotification({
          title: '发送数据失败',
          message: "串口未连接或未打开！",
          type: "warning",
          duration: 2000,
        });
        console.error("串口未连接或未打开！");
      }
    },
  findMachine(){
    if (this.detailInfo==undefined || this.detailInfo.icDeviceList==undefined) {
      return true;
    }
    if (this.deviceInfo.unitCode!=undefined && this.deviceInfo.unitCode!='') {
      return true;
    }
    let isFind = false;
    for (var i = 0 ;i < this.detailInfo.icDeviceList.length;i++){
      var item = this.detailInfo.icDeviceList[i];
      let deviceSn = item.equipmentCode;
      if (deviceSn === this.port.sn) {
        //找到与当前相同的卡务机
        this.deviceInfo = item;
        // this.deviceId = item.id;
        this.$set(this.cardForm,"deviceId",item.id);
        isFind = true;
        break;
      }
    }
    return isFind;
  },
// 将数据存进两个数组中
    ElNotification(vparam) {
      this.$message.success(vparam.message);
    },
  }
}
</script>

<style scoped>
.margin-top {
  margin-top: 15px;
}
.my-label {
  width: 200px;
}
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}
</style>
