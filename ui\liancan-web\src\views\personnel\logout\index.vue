<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="{row}">
        <el-button size="small" type="text" @click="mealAccountDetails(row)">账户明细</el-button>
        <el-button size="small" type="text" @click="archiveRefund(row)">存档退款</el-button>
        <el-button v-if="appliactionType == '1'" size="mini" icon="el-icon-edit" type="text" @click="recoveryOpen(row)">恢复</el-button>
        <el-button size="small" type="text" @click="dataMaintenanceDetails(row)">资料维护历史</el-button>
      </template>
      <template slot="menuLeft">
        <el-button v-if="permission.system_personnel_logout_export && this.modeType != '1'" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportPersonnelData">导出</el-button>
<!--        <el-button v-if="permission.system_personnel_logout_batch_delete && this.modeType != '1'" class="filter-item" size="small" type="danger" icon="el-icon-delete" @click="batchDelPersonnel">批量删除</el-button>-->
      </template>
      <template slot="avatar" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.avatar" fit="cover" @click="handleClickPreview(row.avatar)"></el-image>
      </template>
    </avue-crud>
    <el-dialog title="批量编辑" :visible.sync="batchEditVisible" :append-to-body="true" @close="batchEditClose" width="60%">
      <avue-form ref="editForm" :option="editOption" v-model="editForm" @submit="batchUpdatePersonnel">
        <template slot-scope="scope" slot="urlList">
          <div><span style="color: red;">重要提示：批量编辑操作会同时更改所选人员的相关信息，风险较高，请务必认真仔细操作，避免影响人员用餐。</span></div>
        </template>
      </avue-form>
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
    <el-dialog title="批量删除" :visible.sync="batchDelVisible" :append-to-body="true" @close="batchCashRechargeClose" width="60%">
      <div style="padding-bottom: 20px;">已选择人员 （{{this.personnelNumber}} 人）</div>
      <div style="padding-bottom: 30px;font-size: 17px;">{{this.personnelName}}</div>
      <div style="padding-bottom: 30px;font-size: 17px;">确认删除所选人员？</div>
      <span style="font-size: 16px;color: red;">重要提示：一旦确认删除，系统将彻底删除该人员相关的各类操作明细，且不可恢复，请务必谨慎操作!</span>
      <div style="text-align: center;padding-top: 20px;">
        <el-button type="primary" icon="el-icon-check" size="small" @click="handleDelete()">确定</el-button>
        <el-button type="primary" icon="el-icon-circle-close" size="small" plain @click="batchCashRechargeClose()">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="恢复" :visible.sync="recoveryVisible" :append-to-body="true" @close="recoveryClose" width="60%">
      <avue-form ref="recoveryForm" :option="recoveryOption" v-model="recoveryForm" @submit="editPersonnelDate">

      </avue-form>
      <div style="text-align: center;padding-top: 20px;">
        <el-button v-if="this.isShow == '0'" type="primary" icon="el-icon-check" size="small" @click="handleRecovery()">确定</el-button>
        <el-button v-if="this.isShow == '1'" disabled type="primary" icon="el-icon-check" size="small">确定</el-button>
      </div>
    </el-dialog>

    <el-dialog title="账户明细" :visible.sync="consumAmountVisible" :append-to-body="true" @close="consumAmountVisible=false" width="80%">
      <div>
        <div class="all-mess">
          <div class="mess-header">
            <div
              :class="{acitve:activeIdx==index}"
              v-for="(item,index) in messList"
              :key="index"
              @click="menuClick(index)"
            >
              {{item}}
            </div>
          </div>
          <div class="mess-content" v-if="activeIdx == 0">
            <avue-crud :option="consumAmountOption"
                       :table-loading="consumAmountLoading"
                       :data="consumAmountData"
                       :page="consumAmountPage"
                       :permission="permissionList"
                       :before-open="beforeOpen"
                       v-model="consumAmountForm"
                       ref="consumAmountForm"
                       @search-change="consumAmountSearchChange"
                       @search-reset="consumAmountSearchReset"
                       @selection-change="consumAmountSelectionChange"
                       @current-change="consumAmountCurrentChange"
                       @size-change="consumAmountSizeChange"
                       @on-load="consumAmountOnLoad">
              <template slot="menu" slot-scope="{row}">
                <el-button size="mini" type="text" @click="lookDetails(row)">查看详情</el-button>
              </template>
              <template slot="menuLeft">
                <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportConsumAmount">导出</el-button>
              </template>
              <template slot="balance1" slot-scope="scope">
                <span style="color:red;" v-if="scope.row.type == '0'" size="medium" type="primary">{{scope.row.balance1}}</span>
                <span v-if="scope.row.type != '0'" size="medium" type="primary">{{scope.row.balance1}}</span>
              </template>
            </avue-crud>
          </div>
          <div class="mess-content" v-if="activeIdx == 1">
            <avue-crud :option="consumAmountOption"
                       :table-loading="consumAmountLoading"
                       :data="consumAmountData"
                       :page="consumAmountPage"
                       :permission="permissionList"
                       :before-open="beforeOpen"
                       v-model="consumAmountForm"
                       ref="consumAmountForm"
                       @search-change="consumAmountSearchChange"
                       @search-reset="consumAmountSearchReset"
                       @selection-change="consumAmountSelectionChange"
                       @current-change="consumAmountCurrentChange"
                       @size-change="consumAmountSizeChange"
                       @on-load="consumAmountOnLoad">
              <template slot="menu" slot-scope="{row}">
                <el-button size="mini" type="text" @click="lookDetails(row)">查看详情</el-button>
              </template>
              <template slot="menuLeft">
                <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportConsumAmount">导出</el-button>
              </template>
              <template slot="balance1" slot-scope="scope">
                <span style="color:red;" v-if="scope.row.type == '0'" size="medium" type="primary">{{scope.row.balance1}}</span>
                <span v-if="scope.row.type != '0'" size="medium" type="primary">{{scope.row.balance1}}</span>
              </template>
            </avue-crud>
          </div>
        </div>
      </div>
    </el-dialog>

    <el-dialog title="详情" :visible.sync="detailsVisible" width="60%" left :append-to-body="true" @close="detailsVisible = false">
      <el-row v-if="this.operationType == '0' || this.operationType == '1'">
        <div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              姓名：{{this.detailsForm.userName}}
            </div>
            <!--            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
                          <span v-if="obj.consumType == '0'">下单类型：统缴餐</span>
                          <span v-if="obj.consumType == '1'">下单类型：自选餐(自由消费)</span>
                          <span v-if="obj.consumType == '2'">下单类型：自选餐(预订菜品)</span>
                          <span v-if="obj.consumType == '3'">下单类型：自选餐(预订餐次)</span>
                        </div>-->
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              性别：
              <span v-if="this.detailsForm.sex == '1'">男</span>
              <span v-if="this.detailsForm.sex == '2'">女</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              编号：{{this.detailsForm.studentJobNo}}
            </div>
            <!--        <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
                      部门：{{this.rechargeForm.deptName}}
                    </div>-->
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.detailsForm.mealsType == '0'">充值账户：统缴餐钱包</span>
              <span v-if="this.detailsForm.mealsType == '1'">充值账户：自选餐钱包</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.detailsForm.topUpType == 'WXPAY'">充值方式：微信支付</span>
              <span v-if="this.detailsForm.topUpType == 'CASH'">充值方式：现金支付</span>
            </div>
            <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;">
              充值金额：{{this.detailsForm.balance}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              手续费费率：{{this.detailsForm.serviceChargeRate}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              手续费：{{this.detailsForm.serviceCharge}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              实收金额：{{this.detailsForm.actualAmount}}
            </div>
            <div style="width:15%;margin-bottom: 20px;margin-top:20px;float: left;">
              充值操作人：{{this.detailsForm.topUpUser}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              操作人身份：{{this.detailsForm.identity}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              充值时间：{{this.detailsForm.createTime}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.detailsForm.payStatus == 'WAITPAY'">支付状态：等待回调</span>
              <span v-if="this.detailsForm.payStatus == 'PAID'">支付状态：支付成功</span>
              <span v-if="this.detailsForm.payStatus == 'PAYFAIL'">支付状态：支付失败</span>
              <span v-if="this.detailsForm.payStatus == 'CANCELPAY'">支付状态：支付取消</span>
            </div>
            <div style="width:38%;margin-bottom: 20px;margin-top:20px;float: left;">
              备注：{{this.detailsForm.remark}}
            </div>
          </div>
        </div>
      </el-row>

      <el-row v-if="this.operationType != '0' && this.operationType != '1'">
        <div>
          <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">用餐人</div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              姓名：{{this.detailsForm.userName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              性别：{{this.detailsForm.sex}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              编号：{{this.detailsForm.studentJobNo}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              部门：{{this.detailsForm.deptName}}
            </div>
          </div>
        </div>
      </el-row>
      <el-row v-if="this.operationType == '3' || this.operationType == '4' || this.operationType == '5' || this.operationType == '7' || this.operationType == '8' ||
      this.operationType == '9' || this.operationType == '11' || this.operationType == '12' || this.operationType == '15' || this.operationType == '16' || this.operationType == '10' || this.operationType == '14'">
        <div>
          <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">消费</div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              营业网点：{{this.detailsForm.outletsName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.detailsForm.consumType == '0'">用餐类型：统缴餐</span>
              <span v-if="this.detailsForm.consumType == '1'">用餐类型：自选餐(自由消费)</span>
              <span v-if="this.detailsForm.consumType == '2'">用餐类型：自选餐(预订菜品)</span>
              <span v-if="this.detailsForm.consumType == '3'">用餐类型：自选餐(预订餐次)</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              下单金额：{{this.detailsForm.consumMoney}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              实收金额：{{this.detailsForm.actualConsumMoney}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              用餐时间：{{this.detailsForm.consumTime}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              付款时间：{{this.detailsForm.paySuccessTime}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              下单单号：{{this.detailsForm.consumBillNo}}
            </div>
            <!--            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
                          付款方式：{{obj.payMethod }}
                        </div>-->
            <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="this.detailsForm.consumType == '0'">
              扣费方式：{{this.detailsForm.deductionMethod}}
            </div>
            <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="this.detailsForm.consumType == '1'">
              扣费方式：{{this.detailsForm.deductionMethod}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              下单操作人：{{this.detailsForm.placeOrderName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.detailsForm.operatorIdentity == 'TEACHER'">操作人身份：老师</span>
              <span v-if="this.detailsForm.operatorIdentity == 'STUDENT'">操作人身份：学生</span>
              <span v-if="this.detailsForm.operatorIdentity == 'PARENT'">操作人身份：家长</span>
              <span v-if="this.detailsForm.operatorIdentity == 'SYSTEM'">操作人身份：系统</span>
              <span v-if="this.detailsForm.operatorIdentity == 'UNIT'">操作人身份：食堂职工</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              窗口组：{{this.detailsForm.windowGroupName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              窗口号：{{this.detailsForm.windowName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="this.detailsForm.consumType == '0'">
              餐次：{{this.detailsForm.mealType}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="this.detailsForm.consumType == '1'">
              餐次：{{this.detailsForm.mealType}}
            </div>
            <div style="width:15%;margin-bottom: 20px;margin-top:20px;float: left;">
              状态：{{this.detailsForm.payStatus}}
            </div>
            <div v-if="this.detailsForm.consumType == '0'" style="width:15%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="this.detailsForm.diningSigns == '0'">用餐标识：计划内用餐</span>
              <span v-if="this.detailsForm.diningSigns == '1'">用餐标识：计划外用餐</span>
            </div>
          </div>
        </div>
      </el-row>
      <el-row v-if="this.operationType == '2'">
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">取现钱包：</span>{{this.detailsForm.refundType}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">取现金额：</span>{{this.detailsForm.money}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">取现操作人：</span>{{this.detailsForm.refundUser}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">取现操作时间：</span>{{this.detailsForm.createTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">备注：</span>{{this.detailsForm.remark}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.operationType == '6'">
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款分类：</span>{{this.detailsForm.refundType}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款金额：</span>{{this.detailsForm.money}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款操作人：</span>{{this.detailsForm.refundUser}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款操作时间：</span>{{this.detailsForm.createTime}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">备注：</span>{{this.detailsForm.remark}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.operationType == '9'">
        <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">附加信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错单号：</span>{{this.detailsForm.errorOrderId}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错后实收金额：</span>{{this.detailsForm.errorActBalance}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错时间：</span>{{this.detailsForm.errorDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">错误类型：</span>{{this.detailsForm.errorType}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错人：</span>{{this.detailsForm.errorUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错备注：</span>{{this.detailsForm.errorRemark}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.operationType == '13' || this.operationType == '17'">
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">关联消费单号：</span>{{this.detailsForm.consumDetailsId}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款分类：</span>{{this.detailsForm.refundType}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款金额：</span>{{this.detailsForm.money}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款操作人：</span>{{this.detailsForm.refundUser}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款时间：</span>{{this.detailsForm.createTime}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款单号：</span>{{this.detailsForm.id}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.operationType == '10' || this.operationType == '14'">
        <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">订餐信息</div>
        <avue-crud :option="orderOption"
                   :table-loading="orderLoading"
                   :data="orderData"
                   :page="orderPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="orderForm"
                   ref="orderForm"
                   @search-change="orderSearchChange"
                   @search-reset="orderSearchReset"
                   @selection-change="orderSelectionChange"
                   @current-change="orderCurrentChange"
                   @size-change="orderSizeChange"
                   @on-load="orderOnLoad">

        </avue-crud>
      </el-row>

      <el-row v-if="this.operationType == '19'">
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">钱包：</span>{{this.detailsForm.balanceType}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">存档退款金额：</span>{{this.detailsForm.money}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">退款操作人：</span>{{this.detailsForm.createUserName}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">操作时间：</span>{{this.detailsForm.createTime}}</div>
        </el-col>
        <el-col style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" >
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">备注：</span>{{this.detailsForm.remark}}</div>
        </el-col>
      </el-row>
    </el-dialog>

    <el-dialog title="结算退款" :visible.sync="archiveRefundVisible" :append-to-body="true" @close="this.archiveRefundVisible = false" width="40%">
      <avue-form ref="archiveRefundForm" :option="archiveRefundOption" v-model="archiveRefundForm" @submit="saveArchiveRefund">
      </avue-form>
      <span style="font-size: 15px;color:red">请注意:</span><br>
      <span style="font-size: 15px;color:red">(1) 结算退款金额只能小于或等于钱包余额。</span><br>
      <span style="font-size: 15px;color:red">(2) 操作后,相应钱包的余额会减少,请谨慎操作。</span>
    </el-dialog>


    <el-dialog title="资料维护历史" :visible.sync="DataMaintenanceVisible" :append-to-body="true" @close="DataMaintenanceVisible=false" width="80%">
      <avue-crud :option="dataMaintenanceOption"
                 :table-loading="dataMaintenanceLoading"
                 :data="dataMaintenanceData"
                 :page="dataMaintenancePage"
                 :permission="permissionList"
                 :before-open="beforeOpen"
                 v-model="dataMaintenanceForm"
                 ref="dataMaintenanceForm"
                 @search-change="dataMaintenanceSearchChange"
                 @search-reset="dataMaintenanceSearchReset"
                 @selection-change="dataMaintenanceSelectionChange"
                 @current-change="dataMaintenanceCurrentChange"
                 @size-change="dataMaintenanceSizeChange"
                 @on-load="dataMaintenanceOnLoad">
        <template slot="menu" slot-scope="{row}">
          <el-button v-if="row.action == '2' || row.action == '6'" size="mini" type="text" @click="viewDetails(row.id)">查看详情</el-button>
        </template>
      </avue-crud>
    </el-dialog>

    <el-dialog title="详情" :visible.sync="editDetailsVisible" :append-to-body="true" @close="editDetailsVisible=false" width="80%">
      <div>
        <p style="font-size: 25px;">更改前:</p>
        <p v-for="item in beforeList" style="font-size: 20px;margin-top: 20px;">{{item}}</p>
        <hr/>
        <p style="font-size: 25px;margin-top: 20px;">更改后:</p>
        <p v-for="item in afterList" style="font-size: 20px;margin-top: 20px;">{{item}}</p>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList,
    getFileDetail,
    remove,
    exportPersonnelData,
    recovery,
    update
  } from "@/api/personnel/systemPersonnel";
  import {getConsumAmountList,getConsumAmountDetail,exportAmountDetailsData}
  from "@/api/personnel/consumAmount/personnelConsumAmount";
  import {
    getDeptTree
  } from "@/api/setting/dept/systemDeptSetting";
  import {refund} from "@/api/liancan/archive/refundDetails/archiveRefundDetails";
  import {
    getDeptAgencyType,
  } from "@/api/system/dept";
  import {getOperationLogList,getOperationLogDetail} from "@/api/foodWallet/operationLog";
  import {mapGetters} from "vuex";
  import website from '@/config/website';
  const DIC = {
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
    MEALTYPE1:[{
      label: '统缴餐钱包',
      value: "1"
    },{
      label: '自选餐钱包',
      value: "2"
    }],
    VAILD: [{
      label: '人员(1)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
      value: '1'
    }, {
      label: '人员(2)',//2023-02-03需求更改人员类别，原来1为学生,2为教职工
      value: '2'
    },
    ],
    SAGE: [{
      label: '小学生',
      value: '0'
    },{
      label: '初中生',
      value: '1'
    },{
      label: '高中生',
      value: '2'
    }],
    PERSONNEL:[{
      label: '停用',
      value: '0'
    },{
      label: '启用',
      value: '1'
    }, {
      label: '注销',
      value: '2'
    },{
      label: '锁定',
      value: '3'
    }],
    OPEN:[{
      label: '是',
      value: '1'
    }],
    logoutType:[{
      label: '删除注销',
      value: '1'
    },
    {
      label: '毕业注销',
      value: '2'
    }]
  }
  export default {
    data() {
      return {
        activeIdx: 0,
        messList: [ '统缴餐明细', '自选餐明细'],
        form: {},
        query: {},
        importForm:{},
        searchForm: {},
        editForm:{},
        editPersonnelForm:{},
        editPersonnelForm2:{},
        resetForm:{},
        downForm:{},
        recoveryForm:{},
        searchForm2:{},
        consumAmountForm:{},
        detailsForm:{},
        orderForm:{},
        archiveRefundForm:{},
        dataMaintenanceForm:{},
        loading: true,
        consumAmountLoading:true,
        dataMaintenanceLoading:true,
        orderLoading:true,
        importDialogVisible:false,
        DataMaintenanceVisible:false,
        batchEditVisible:false,
        dialogVisible:false,
        schoolVisibleb:false,
        resetVisible:false,
        batchDelVisible:false,
        recoveryVisible:false,
        archiveRefundVisible:false,
        editDetailsVisible:false,
        id:undefined,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        consumAmountPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        orderPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        dataMaintenancePage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        agencyType:undefined,
        modeType:undefined,
        appliactionType:undefined,
        selectionList: [],
        uploadFilesList:[],
        consumAmountData:[],
        orderData:[],
        dataMaintenanceData:[],
        beforeList:[],
        afterList:[],
        dialogImageUrl:undefined,
        fileName:undefined,
        userId:undefined,
        personnelNumber:undefined,
        personnelName:undefined,
        userIds:undefined,
        mealsType:undefined,
        personnelId:undefined,
        operationType:undefined,
        orderId:undefined,
        mealType:undefined,
        userId2:undefined,
        isShow:'0',
        editditVisible:false,
        editditVisible2:false,
        consumAmountVisible:false,
        detailsVisible:false,
        option: {
      /*    height:'auto',
          calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          editBtn:false,
          labelWidth: 190,
          searchSpan:100,
          dialogWidth: "70%",
          addBtn:false,
          delBtn:false,
          printBtn:true,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              span: 24,
              sortable:true,
              search:true,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 100,
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
              search:true,
              sortable:true,
              rules: [{
                required: true,
                message: '请选择性别',
                trigger: 'blur'
              }],
              width: 100,
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
              search:true,
              sortable:true,
              rules: [{
                required: true,
                message: "请输入编号",
                trigger: "blur"
              }],
            },
            {
              label: "原部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
              sortable:true,
              minWidth: 120,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "原部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                label: "title",
                value: "id"
              },
              editDisplay: false,
              viewDisplay: false,
              multiple:true,
              /*slot:true,*/
              search:true,
              hide:true,
              rules: [{
                required: true,
                message: "请输入部门",
                trigger: "click"
              }]
            },
            {
              label: "原用餐类别",
              prop: "mealsName",
              type: "input",
              sortable:true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "原用餐类别",
              prop: "mealsType",
              type: "select",
              search:true,
              hide:true,
              editDisplay: false,
              viewDisplay: false,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: '人员类别',
              prop: 'personnelType',
              type: "radio",
              //slot: true,
              dicData: DIC.VAILD,
              search:true,
              rules: [{
                required: true,
                message: '请选择人员类别',
                trigger: 'blur'
              }],
              width: 90,
            },
            {
              label: '学龄段',
              prop: 'schoolAge',
              type: "select",
              rules: [{
                required: true,
                message: '请选择学龄段',
                trigger: 'blur'
              }],
              width: 90,
              dicData: DIC.SAGE,
            },
/*            {
              label: "人员属性",
              prop: "attribute",
              type: "select",
              dicUrl: '/api/service/rabbit-liancan/userRole/dict',
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },*/
            {
              label: "人员照片",
              prop: "avatar",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              rules: [{
                required: true,
                message: "请上传人员照片",
                trigger: "blur"
              }],
              width: 90,
            },
            {
              label: "一卡通卡号",
              prop: "cardId",
              type: "input",
            },
            {
              label: "有效期",
              prop: "effectiveTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              hide:true,
              viewDisplay:false,
              addDisplay:false,
              pickerOptions: {
                disabledDate(time) {
                  return time.getTime() < Date.now();
                },
              },
              width: 90,
            },
            {
              label: "存档日期",
              prop: "logoutTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
            },
            {
              label: "统缴餐钱包余额(元)",
              prop: "money1",
              type: "input",
              width:150,
              /*          mock:{
                          type:'number',
                          max:1,
                          min:2,
                          precision:2
                        },
                        precision:2,*/
            },
            {
              label: "自选餐钱包余额(元)",
              prop: "money2",
              type: "input",
              width:150,
            },
          ]
        },
        recoveryOption: {
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              span: 24,
              search:true,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
              search:true,
              rules: [{
                required: true,
                message: '请选择性别',
                trigger: 'blur'
              }]
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
              search:true,
              rules: [{
                required: true,
                message: "请输入编号",
                trigger: "blur"
              }],
            },
            {
              label: "用餐类别",
              prop: "mealsType1",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              display:false,
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              search:true,
              hide:true,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
              /*            rules: [{
                            required: true,
                            message: "请输入用餐类别",
                            trigger: "blur"
                          }],*/
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              props: {
                label: "title",
                value: "id"
              },
              multiple:true,
              slot:true,
              rules: [{
                required: true,
                message: "请输入部门",
                trigger: "click"
              }]
            },
            {
              label: '人员类别',
              prop: 'personnelType',
              type: "radio",
              //slot: true,
              dicData: DIC.VAILD,
              value:'1',
              search:true,
              rules: [{
                required: true,
                message: '请选择人员类别',
                trigger: 'blur'
              }],
              width: 90,
            },
            {
              label: "人员属性",
              prop: "attribute",
              type: "select",
              dicUrl: '/api/service/rabbit-liancan/userRole/dict',
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: "人员照片",
              prop: "avatar",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span:24,
              slot: true,
              rules: [{
                required: true,
                message: "请上传人员照片",
                trigger: "blur"
              }],
            },
            {
              label: "一卡通卡号",
              prop: "cardId",
              type: "input",
              /*      rules: [{
                      required: true,
                      message: "请输入一卡通卡号",
                      trigger: "blur"
                    }],*/
            },
            {
              label: "有效期",
              prop: "effectiveTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              /*       rules: [{
                       required: true,
                       message: "请选择有效期",
                       trigger: "blur"
                     }],*/
              display:false,
              pickerOptions: {
                disabledDate(time) {
                  return time.getTime() < Date.now();
                },
              }
            },
            {
              label: "民族",
              prop: "nation",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              /*     rules: [{
                     required: true,
                     message: "请选择民族",
                     trigger: "blur"
                   }],*/
            },
            /*          {
                        label: "开通伙食费钱包",
                        prop: "boardOpenStatuss",
                        type: "radio",
                        dicData: DIC.OPEN,
                        hide:true,
                        value:'1',
                        mock:{
                          type:'dic'
                        },
                      },*/
            /*           {
                         label: "伙食费钱包初始余额(元)",
                         prop: "balance",
                         type: "input",
                         disabled:true,*/
            /*      disabled:true,*/
            /*              rules: [{
                            required: true,
                            message: "请输入伙食费钱包初始余额(元)",
                            trigger: "blur"
                          }],*/
            /* },*/
            /*{
              label: "自选餐每餐次消费限额(元)",
              prop: "consumQuota",
              type: "input",
            },*/
            {
              label:'',
              prop: 'urlList',
              labelWidth: 0,
              span: 24,
              formslot: true,
            },
          ]
         /* column: [
            {
              label: "原部门",
              prop: "deptName",
              type: "input",
              disabled:true,
            },
            {
              label: "恢复后所在部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
              labelWidth:140,
              props: {
                label: "title",
                value: "id"
              },
            },
            {
              label: "原用餐类别",
              prop: "mealsName",
              type: "input",
              disabled:true,
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              search:true,
              hide:true,
              editDisplay: false,
              viewDisplay: false,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
          ]*/
        },
        consumAmountOption:{
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn:false,
          selection: false,
          align: 'center',
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "单号",
              prop: "orderId",
              type: "input",
              width:160,
            },
            {
              label: "分类",
              prop: "type",
              type: "select",
              search:true,
              dicData: [
                {
                  label: "收入",
                  value: "0"
                },
                {
                  label: "支出",
                  value: "1"
                },
                {
                  label: "其他",
                  value: "2"
                }
              ],
            },
            {
              label: "操作类型",
              prop: "operationType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=consum_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search:true,
            },
            {
              label: "操作前账户余额",
              prop: "beforeBalance1",
              type: "input",
            },
            {
              label: "发生金额",
              prop: "balance1",
              type: "input",
              slot:true,
            },
            {
              label: "操作后账户余额",
              prop: "afterBalance1",
              type: "input",
            },
            {
              label: "备注",
              prop: "remarks",
              type: "input",
              overHidden: true,
            },
            {
              label: '操作时间',
              prop: 'createTime',
              type: 'datetime',
              format: 'yyyy-MM-dd HH:mm:ss',
              width:160,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
          ]
        },
        orderOption:{
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn:false,
          header: false,
          /*selection: true,*/
          menu:false,
          column: [
            {
              label: '姓名',
              prop: 'studentName',
              type: 'input',
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
            },
            {
              label: "日期",
              prop: "month",
              type: "input",

            },
            {
              label: "星期",
              prop: "week",
              type: "select",
              dicData: [
                {
                  label: "星期一",
                  value: "1"
                },
                {
                  label: "星期二",
                  value: "2"
                },
                {
                  label: "星期三",
                  value: "3"
                },
                {
                  label: "星期四",
                  value: "4"
                },
                {
                  label: "星期五",
                  value: "5"
                },
                {
                  label: "星期六",
                  value: "6"
                },
                {
                  label: "星期日",
                  value: "7"
                }
              ],
            },
            {
              label: "餐次",
              prop: "mealName",
              type: "input",
            },
            {
              label: "价格",
              prop: "money",
              type: "input",
            },
            {
              label: '状态',
              prop: 'isRefund',
              type: 'select',
              dicData: [
                {
                  label: "已订",
                  value: "0"
                },
                {
                  label: "已退订",
                  value: "1"
                },
                {
                  label: "不可退订",
                  value: "2"
                },
                {
                  label: "结束",
                  value: "3"
                }
              ],
            },
            {
              label: '取餐/退订时间',
              prop: 'takeMealTime',
              type: "date",
              format: 'yyyy-MM-dd HH:mm',
              valueFormat: 'yyyy-MM-dd HH:mm',
            },
            {
              label: '退订操作人',
              prop: 'unsubscribeUserName',
              type: 'input',
              span: 20,
            },
          ]
        },
        archiveRefundOption: {
          labelWidth:120,
          column: [
            {
              label: "人员姓名",
              prop: "userName",
              type: "input",
              disabled: true,
              span: 24,
            },
            {
              label: "统缴餐钱包余额(元)",
              prop: "money1",
              type: "number",
              labelWidth:150,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              precision:2,
              disabled: true,
              span: 12,
            },
            {
              label: "自选餐钱包余额(元)",
              prop: "money2",
              type: "number",
              labelWidth:150,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              precision:2,
              disabled: true,
              span: 12,
            },
            {
              label: "选择钱包",
              prop: "balanceType",
              type: "radio",
              labelWidth:150,
              dicData: DIC.MEALTYPE1,
              rules: [{
                required: true,
                message: "请选择要结算退款钱包",
                trigger: "blur"
              }],
              span: 24
            },
            {
              label: "结算退款金额",
              prop: "balance",
              type: "number",
              rules: [{
                required: true,
                message: "请输入金额,小数点后两位",
                trigger: "blur"
              }],
              precision:2,
              mock:{
                type:'number',
                max:1,
                min:2,
                precision:2
              },
              minRows: 0,
              //maxRows: 3,
            },
            {
              label: "结算退款备注",
              prop: "remark",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入结算退款备注",
                trigger: "blur"
              }],
            }
          ]
        },
        dataMaintenanceOption:{
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          editBtn:false,
          delBtn:false,
          selection: false,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "流水号",
              prop: "serialNumber",
              type: "input",
            },
            {
              label: "动作",
              prop: "action",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=operation_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search:true,
            },
            {
              label: "操作人",
              prop: "operatorName",
              type: "input",
            },
            {
              label: "操作人来源",
              prop: "operatorSource",
              type: "input",
            },
            {
              label: "操作(同步)时间",
              prop: "createTime",
              type: "input",
            },
            {
              label: "ip地址",
              prop: "ipAddress",
              type: "input",
            },
            {
              label: "开始时间",
              prop: "startDateTime",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDateTime",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission", 'userInfo']),
      permissionList() {
        return {
          viewBtn: this.vaildData(this.permission.system_personnel_logout_view, false),
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      names() {
        let names = [];
        this.selectionList.forEach(ele => {
          names.push(ele.userName);
        });
        return names.join(" ");
      }
    },
    created(){
      getDeptAgencyType().then(res =>{
        this.agencyType = res.data.data.agencyType;
        if (res.data.data.applicationMode === '1'){
          this.appliactionType = '1';
          this.modeType = '2';
        }else if (res.data.data.applicationMode === '2'){
          this.modeType = '2';
          this.appliactionType = '2';
        }else if (res.data.data.applicationMode === '3'){
          this.modeType = '1';
          this.appliactionType = '2';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }else if (res.data.data.applicationMode === '0'){
          this.modeType = '1';
          this.appliactionType = '2';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }
      })
/*      getDeptAgencyType().then(res =>{
        this.agencyType = res.data.data.agencyType;
        if (res.data.data.applicationMode === '3'){
          this.modeType = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }else if (res.data.data.applicationMode === '0'){
          this.modeType = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
        }else {
          this.modeType = '2';
        }
      })*/
  /*    getDeptTree().then(res => {
        const index = this.$refs.crud.findColumnIndex("deptId");
        this.option.column[index].dicData = res.data.data;
      });*/
    },
    methods: {
      menuClick(idx) {
        this.query = [];
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.mealsType = '0';
          this.searchForm2.mealsType = '0';
          this.searchForm2.userId = this.personnelId;
          this.consumAmountPage.currentPage = 1;
          this.consumAmountOnLoad(this.consumAmountPage);
        }
        if (idx == 1){
          this.mealsType = '1';
          this.searchForm2.mealsType = '1';
          this.searchForm2.userId = this.personnelId;
          this.consumAmountPage.currentPage = 1;
          this.consumAmountOnLoad(this.consumAmountPage);
        }
      },
      beforeOpen(done, type) {
        done();
        // 转成数组
        if (this.form.attribute != null) {
          let attribute = (this.form.attribute).split(',')
          this.form.attribute = attribute
        }
        console.log(">>>>>>>>>>>>1",JSON.stringify(this.form.attribute))
        // 转成数组
        if (this.form.boardOpenStatus != null) {
          let boardOpenStatus = (this.form.boardOpenStatus).split(',')
          this.form.boardOpenStatus = boardOpenStatus
        }
        if (["edit", "view"].includes(type)) {
          getFileDetail(this.form.id).then(res => {
            const balanceColumn = this.option.column[13];
            if (this.form.boardOpenStatus.map(parseInt) == 1){
              balanceColumn.disabled = false;
            }
            if (this.form.boardOpenStatus.map(parseInt) == 0){
              balanceColumn.disabled = true;
            }
            this.form = res.data.data;
          });
        }
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.searchForm = params;
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
        this.$refs.crud.refreshTable();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        this.query.status = "2";
        this.query.categoryStatus = "0";
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      handleClickPreview: function(url) {
        console.log(">>>>>>>>>>>>>")
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      exportPersonnelData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出以下数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.searchForm.status = "2";
        this.searchForm.categoryStatus = "0";
        exportPersonnelData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '导出注销人员信息报表.xls';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      batchDelPersonnel(){
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.userIds = this.ids;
        this.personnelName = this.names;
        this.personnelNumber = this.selectionList.length;
        this.batchDelVisible = true;
      },
      delPersonnel(row){
        this.userIds = row.id;
        this.personnelName = row.userName;
        this.personnelNumber = 1;
        this.batchDelVisible = true;
      },
      batchCashRechargeClose(){
        this.batchDelVisible = false;
      },
      recoveryOpen(row){
        this.recoveryForm = row;
        this.recoveryVisible = true;
      },
      recoveryClose(){
        this.recoveryVisible = false;
      },
      handleRecovery(){
        this.$confirm("确定将选择数据恢复?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            this.isShow = '1';
            this.recoveryForm.status = "1";
            update(this.recoveryForm).then(() => {
              this.page.currentPage = 1;
              this.onLoad(this.page);
              this.isShow = '0';
              this.recoveryVisible = false;
              this.$message({
                type: "success",
                message: "操作成功!"
              });
            }, error => {
              this.isShow = '0';
              window.console.log(error);
            });
          });
      },
      handleDelete() {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.userIds);
          })
          .then(() => {
            this.page.currentPage = 1;
            this.onLoad(this.page);
            this.batchDelVisible = false;
            this.userIds = "";
            this.$message({
              type: "success",
              message: "删除成功"
            });
          });
      },
      mealAccountDetails(row){
        this.activeIdx = 0;
        this.mealsType = '0';
        this.personnelId = row.id;
        this.searchForm2.mealsType = '0';
        this.searchForm2.userId = row.id;
        this.consumAmountPage.currentPage = 1;
        this.consumAmountOnLoad(this.consumAmountPage);
        /*        this.accountTitle = '统缴餐账户明细'
                this.mealsType = '0';
                this.personnelId = row.id;
                this.searchForm2.mealsType = '0';
                this.searchForm2.userId = row.id;
                this.consumAmountPage.currentPage = 1;
                this.consumAmountOnLoad(this.consumAmountPage);*/
        this.consumAmountVisible = true;
      },
      consumAmountSearchReset() {
        this.query = {};
        this.consumAmountOnLoad(this.consumAmountPage);
      },
      consumAmountSearchChange(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm2 = params;
        this.query = params;
        this.consumAmountPage.currentPage = 1
        this.consumAmountOnLoad(this.consumAmountPage, params);
        done();
      },
      consumAmountSelectionChange(list) {
        this.selectionList = list;
      },
      consumAmountSelectionClear() {
        this.selectionList = [];
        this.$refs.consumAmountForm.toggleSelection();
      },
      consumAmountCurrentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      consumAmountSizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      consumAmountOnLoad(page, params = {}) {
        this.consumAmountLoading = true;
        params.mealsType = this.mealsType;
        params.userId = this.personnelId;
        getConsumAmountList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.consumAmountPage.total = data.total;
          this.consumAmountData = data.records;
          this.consumAmountLoading = false;
          this.consumAmountSelectionClear();
        });
      },
      exportConsumAmount(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportAmountDetailsData(this.searchForm2).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '账户明细报表.xls';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      lookDetails(row){
        this.operationType = row.operationType;
        this.orderId = row.orderId;
        this.mealType = row.operationType;
        getConsumAmountDetail(row.id).then(res => {
          this.detailsForm = res.data.data;
        });
        if (row.operationType == '10' || row.operationType == '14'){
          this.orderPage.currentPage = 1;
          this.orderOnLoad(this.orderPage);
        }
        this.detailsVisible = true;
      },
      orderSearchReset() {
        this.query = {};
        this.orderOnLoad(this.orderPage);
      },
      orderSearchChange(params, done) {
        this.query = params;
        this.orderPage.currentPage = 1
        this.orderOnLoad(this.orderPage, params);
        done();
      },
      orderSelectionChange(list) {
        this.selectionList = list;
      },
      orderSelectionClear() {
        this.selectionList = [];
        this.$refs.orderForm.toggleSelection();
      },
      orderCurrentChange(currentPage){
        this.orderPage.currentPage = currentPage;
      },
      orderSizeChange(pageSize){
        this.orderPage.pageSize = pageSize;
      },
      orderOnLoad(page, params = {}) {
        this.orderLoading = true;
        params.orderId = this.orderId;
        params.mealType = this.mealType;
        getOrderMelasList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.orderPage.total = data.total;
          this.orderData = data.records;
          this.orderLoading = false;
          this.orderSelectionClear();
        });
      },
      editPersonnelDate(row,loading){
        /*        row.deptId = row.deptId.join(",");*/
        const loading1 = this.$loading({
          lock: true,
          text: '正在修改信息，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        row.status = "1";
        update(row).then(res => {
          loading();
          loading1.close();
          this.onLoad(this.page);
          this.recoveryVisible = false;
          this.$message({
            type: "success",
            message: res.data.data,
            duration: 7000
          });
        }, error => {
          loading();
          loading1.close();
          window.console.log(error);
        });
      },
      archiveRefund(row){
        if (row.personnelType != null && row.personnelType == "2"){
          this.archiveRefundForm.refundType = "5"
        }else {
          this.archiveRefundForm.refundType = null
        }
        this.archiveRefundVisible = true;
        this.archiveRefundForm.money1 = row.balance;
        this.archiveRefundForm.money2 = row.optionalBalance;
        this.archiveRefundForm.userName = row.userName;
        this.archiveRefundForm.personnelId = row.id;
      },
      saveArchiveRefund(row, loading, done) {
        if (this.archiveRefundForm.balance <= 0) {
          this.$message.error('退款金额要大于0');
          loading();
        }else if (this.archiveRefundForm.balanceType == "1"){
          if (this.archiveRefundForm.balance > this.archiveRefundForm.money1){
            this.$message.error('退款金额不得大于统缴餐钱包金额!');
            loading();
          }else {
            this.$confirm("确定要给"+row.userName+"退款金额:"+row.balance+"元?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                refund(this.archiveRefundForm).then(()=>{
                  this.onLoad(this.page);
                  this.archiveRefundForm.balanceType = null;
                  this.archiveRefundForm.balance = null;
                  this.archiveRefundForm.remark = "";
                  this.archiveRefundVisible = false;
                  loading();
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  });
                }, error => {
                  this.archiveRefundForm.balanceType = null;
                  this.archiveRefundForm.balance = null;
                  this.archiveRefundForm.remark = "";
                  this.archiveRefundVisible = false;
                  loading();
                  window.console.log(error);
                });
              })
              .catch(() => {
                loading();
              });
          }
        }else if (this.archiveRefundForm.balanceType == "2"){
          if (this.archiveRefundForm.balance > this.archiveRefundForm.money2){
            this.$message.error('退款金额不得大于自选餐钱包金额!');
            loading();
          }else {
            this.$confirm("确定要给"+row.userName+"退款金额:"+row.balance+"元?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                refund(this.archiveRefundForm).then(()=>{
                  this.onLoad(this.page);
                  this.archiveRefundForm.balanceType = null;
                  this.archiveRefundForm.balance = null;
                  this.archiveRefundForm.remark = "";
                  this.archiveRefundVisible = false;
                  loading();
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  });
                }, error => {
                  this.archiveRefundForm.balanceType = null;
                  this.archiveRefundForm.balance = null;
                  this.archiveRefundForm.remark = "";
                  this.archiveRefundVisible = false;
                  loading();
                  window.console.log(error);
                });
              })
              .catch(() => {
                loading();
              });
          }
        }
      },
      dataMaintenanceDetails(row){
        this.dataMaintenancePage.currentPage = 1;
        this.userId2 = row.id;
        this.dataMaintenanceOnLoad(this.dataMaintenancePage);
        this.DataMaintenanceVisible = true;
      },
      dataMaintenanceSearchReset() {
        this.dataMaintenanceQuery = {};
        this.dataMaintenanceOnLoad(this.dataMaintenancePage);
      },
      dataMaintenanceSearchChange(params, done) {
        if (params.startDateTime != '' && params.startDateTime != null && params.startDateTime != undefined){
          if (params.endDateTime == '' || params.endDateTime == null || params.endDateTime == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDateTime != '' && params.endDateTime != null && params.endDateTime != undefined){
          if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDateTime);
            var endDateTime = new Date(params.endDateTime);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.dataMaintenanceQuery = params;
        this.dataMaintenancePage.currentPage = 1
        this.dataMaintenanceOnLoad(this.dataMaintenancePage, params);
        done();
      },
      dataMaintenanceSelectionChange(list) {
        this.selectionList = list;
      },
      dataMaintenanceSelectionClear() {
        this.selectionList = [];
        this.$refs.dataMaintenanceFrom.toggleSelection();
      },
      dataMaintenanceCurrentChange(currentPage){
        this.dataMaintenancePage.currentPage = currentPage;
      },
      dataMaintenanceSizeChange(pageSize){
        this.dataMaintenancePage.pageSize = pageSize;
      },
      dataMaintenanceOnLoad(page, params = {}) {
        this.dataMaintenanceLoading = true;
        params.userId = this.userId2;
        getOperationLogList(page.currentPage, page.pageSize, Object.assign(params, this.dataMaintenanceQuery)).then(res => {
          const data = res.data.data;
          this.dataMaintenancePage.total = data.total;
          this.dataMaintenanceData = data.records;
          this.dataMaintenanceLoading = false;
          this.dataMaintenanceSelectionClear();
        });
      },
      viewDetails(id){
        getOperationLogDetail(id).then(res => {
          this.beforeList = res.data.data.beforeStrList;
          this.afterList = res.data.data.afterStrList;
        });
        this.editDetailsVisible = true;
      }
    }
  };
</script>

<style lang="scss">
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    margin-bottom: 10px;
    ::v-deep div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20px;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>
