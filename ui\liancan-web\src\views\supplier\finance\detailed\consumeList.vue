<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
<!--        <el-button v-if="permission.consum_details_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportOrderDetailsData">导出</el-button>-->
<!--        <el-button v-if="permission.consum_details_consumer_repair && !this.unitSelect && this.modeType2 != '1' && this.isContractor == '0'" size="small" type="primary" @click="openConsumPatching">消费补扣</el-button>-->
<!--        <el-button v-if="permission.consum_details_batch_refund && !this.unitSelect && this.modeType2 != '1'" size="small" type="primary" @click="openBatchRefund">统缴餐批量退款</el-button>-->
<!--        <el-button v-if="permission.consum_details_batch_refund && !this.unitSelect && this.modeType2 != '1'" size="small" type="primary" @click="openBatchErrorCorrection">统缴餐批量纠错</el-button>-->
      </template>
      <template slot="menuRight">
        <el-button class="filter-item" size="small" type="primary" icon="el-icon-finished" @click="genVoucherBatch">一键生成凭证</el-button>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.consumType == '0'"
                   @click.stop="opentMealsConsumDetails(row)">详情
        </el-button>
<!--        <el-button type="text"-->
<!--                   icon="el-icon-setting"-->
<!--                   size="small"-->
<!--                   plain-->
<!--                   style="border: 0;background-color: transparent !important;"-->
<!--                   v-if="permission.consum_details_refund && !unitSelect && row.consumType == '0' && row.isRefund == '0' && row.dinnerStatus == '1' && row.modeType != '1'"-->
<!--                   @click.stop="openRufundSave(row)">退款-->
<!--        </el-button>-->
        <el-button type="text"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="!unitSelect && row.consumType == '0' && row.isRefund == '1' && row.dinnerStatus == '1'">已退款
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.consumType == '1' && row.isErrorCorrection == '1'"
                   @click.stop="opentMealsConsumDetails(row)">详情
        </el-button>
        <el-button type="text"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="!unitSelect && row.consumType == '1' && row.isErrorCorrection == '1'">已纠错
        </el-button>
        <el-button type="text"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="!unitSelect && row.consumType == '0' && row.isErrorCorrection == '1'">已纠错
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.consumType == '1' && row.isErrorCorrection == '0'"
                   @click.stop="opentMealsConsumDetails(row)">详情
        </el-button>
<!--        <el-button type="text"-->
<!--                   icon="el-icon-setting"-->
<!--                   size="small"-->
<!--                   plain-->
<!--                   style="border: 0;background-color: transparent !important;"-->
<!--                   v-if="permission.consum_details_error_correction && !unitSelect && row.consumType == '1' && row.isErrorCorrection == '0' && row.modeType != '1'"-->
<!--                   @click.stop="openErrorCorrectionSave(row)">纠错-->
<!--        </el-button>-->
<!--        <el-button type="text"-->
<!--                   icon="el-icon-setting"-->
<!--                   size="small"-->
<!--                   plain-->
<!--                   style="border: 0;background-color: transparent !important;"-->
<!--                   v-if="permission.consum_details_error_correction && !unitSelect && row.consumType == '0' && row.isErrorCorrection == '0' && row.modeType != '1'"-->
<!--                   @click.stop="mealErrorCorrectionSave(row)">纠错-->
<!--        </el-button>-->
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.consumType == '2'"
                   @click.stop="opentMealsConsumDetails(row)">详情
        </el-button>
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.consumType == '3'"
                   @click.stop="opentMealsConsumDetails(row)">详情
        </el-button>
        <el-button type="text"
                   icon="el-icon-setting"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.ifVoucher == 0 && row.status == 'PAID'"
                   @click.stop="createVoucher(row)">生成凭证
        </el-button>
      </template>
      <template slot="menuLeft">
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >统计时间：{{this.startDate}} - {{this.endDate}}
        </a>
      </template>
    </avue-crud>
    <!-- 新版弹出窗 -->
    <el-dialog title="消费详情" :visible.sync="consumDetailsVisible" width="70%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div>
          <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">用餐人</div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              姓名：{{obj.userName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              性别：{{obj.sex}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              学号/工号：{{obj.studentJobNo}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              部门：{{obj.deptName}}
            </div>
          </div>
        </div>
      </el-row>
      <el-row>
        <div>
          <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">消费</div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              营业网点：{{obj.outletsName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="obj.consumType == '0'">用餐类型：统缴餐</span>
              <span v-if="obj.consumType == '1'">用餐类型：自选餐(自由消费)</span>
              <span v-if="obj.consumType == '2'">用餐类型：自选餐(预订菜品)</span>
              <span v-if="obj.consumType == '3'">用餐类型：自选餐(预订餐次)</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              下单金额：{{obj.consumMoney}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              实收金额：{{obj.actualConsumMoney}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              用餐时间：{{obj.consumTime}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              付款时间：{{obj.paySuccessTime}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              下单单号：{{obj.consumBillNo}}
            </div>
            <!--            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
                          付款方式：{{obj.payMethod }}
                        </div>-->
            <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="obj.consumType == '0'">
              扣费方式：{{obj.deductionMethod}}
            </div>
            <div style="width: 20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="obj.consumType == '1'">
              扣费方式：{{obj.deductionMethod}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              下单操作人：{{obj.placeOrderName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="obj.operatorIdentity == 'TEACHER'">操作人身份：老师</span>
              <span v-if="obj.operatorIdentity == 'STUDENT'">操作人身份：学生</span>
              <span v-if="obj.operatorIdentity == 'PARENT'">操作人身份：家长</span>
              <span v-if="obj.operatorIdentity == 'SYSTEM'">操作人身份：系统</span>
              <span v-if="obj.operatorIdentity == 'UNIT'">操作人身份：食堂职工</span>
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="obj.consumType == '0'">
              餐次：{{obj.mealType}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;" v-if="obj.consumType == '1'">
              餐次：{{obj.mealType}}
            </div>
            <div style="width:15%;margin-bottom: 20px;margin-top:20px;float: left;">
              状态：{{obj.payStatus}}
            </div>
            <div v-if="obj.consumType == '0'" style="width:15%;margin-bottom: 20px;margin-top:20px;float: left;">
              <span v-if="obj.diningSigns == '0'">用餐标识：计划内用餐</span>
              <span v-if="obj.diningSigns == '1'">用餐标识：计划外用餐</span>
            </div>
          </div>
        </div>
      </el-row>
      <el-row v-if="obj.consumType == '0' && obj.mealsStatus == '1' && obj.diningSigns == '0' && obj.status == '1'">
        <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">附加信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">统缴餐计划名称：</span>{{obj.mealPayName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">缴费日期：</span>{{obj.paymentDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">供餐日期：</span>{{obj.servingDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">用餐类别：</span>{{obj.mealCategory}}</div>
        </el-col>
      </el-row>
      <el-row v-if="obj.consumType == '1' && obj.errorStatus == '1'">
        <div class="flex_title" style="margin:10px 0;font-size: 16px;font-weight: bold;">附加信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错单号：</span>{{obj.errorCorrectionNo}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错后实收金额：</span>{{obj.paidAmount}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错时间：</span>{{obj.errorDateTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;" v-if="obj.errorType == '1'"><span class="title_style">错误类型：</span>少收钱</div>
          <div style="margin-bottom: 2px;margin-top:10px;" v-if="obj.errorType == '2'"><span class="title_style">错误类型：</span>多收钱</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错人：</span>{{obj.errorUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top:10px;"><span class="title_style">纠错备注：</span>{{obj.errorRemark}}</div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog title="菜品详情" :visible.sync="dishesDetailsVisible" width="17%" left :append-to-body="true" @close="dishesDetailsClose">
      <div class="flex" v-for="(item,index) in orderDishesList"
           :key="index">
        <div>
          <div style="float: left;"><img :src="item.url" style="width: 50px"></div>
          <div style="margin-bottom: 20px;margin-top:20px;  width: 275px;padding-bottom:30px;border-bottom-style: inset;"><span style="margin-left: 20px;">{{item.name}}</span>
            <!--            <span style="margin-left: 20px;" v-if="item.week == '1'">周一</span>
                        <span style="margin-left: 20px;" v-if="item.week == '2'">周二</span>
                        <span style="margin-left: 20px;" v-if="item.week == '3'">周三</span>
                        <span style="margin-left: 20px;" v-if="item.week == '4'">周四</span>
                        <span style="margin-left: 20px;" v-if="item.week == '5'">周五</span>
                        <span style="margin-left: 20px;" v-if="item.week == '6'">周六</span>
                        <span style="margin-left: 20px;" v-if="item.week == '7'">周日</span>
                        <span style="margin-left: 20px;">{{item.mealName}}</span>-->
            <span style="float: right;">￥  {{item.subtotal.toFixed(2)}}</span>
            <!--            <span style="margin-left: 20px;" v-if="item.isRefund == '0'">退订</span>
                        <span style="margin-left: 20px;" v-if="item.isRefund == '1'">已退订</span>
                        <span style="margin-left: 20px;" v-if="item.isRefund == '2'">不可退订</span>
                        <span style="margin-left: 20px;" v-if="item.isRefund == '3'">结束</span>-->
            <div style="margin-left: 70px;margin-top: 15px;">x   {{item.quantity}}</div>
          </div>
        </div>
      </div>
      <div style="margin-left: 194px;" v-if="orderDishesList.length > 0 ">合计 ￥ {{this.dishesForm.dishesTotal}}</div>
    </el-dialog>
    <el-dialog title="自由消费纠错" :visible.sync="consumErrorCorrectionVisible" :append-to-body="true" @close="consumErrorCorrectionClose" width="50%">
      <avue-form ref="errorCorrectionForm" :option="errorCorrectionOption" v-model="errorCorrectionForm" @submit="submitErrorCorrection">
      </avue-form>
    </el-dialog>

    <el-dialog title="统缴餐纠错" :visible.sync="mealConsumErrorCorrectionVisible" :append-to-body="true" @close="mealConsumErrorCorrectionClose" width="50%">
      <avue-form ref="mealErrorCorrectionForm" :option="mealErrorCorrectionOption" v-model="mealErrorCorrectionForm" @submit="mealSubmitErrorCorrection">
      </avue-form>
    </el-dialog>
    <el-dialog title="统缴餐消费纠错" :visible.sync="batchErrorCorrectionVisible" :append-to-body="true" @close="batchErrorCorrectionClose" width="50%">
      <avue-form ref="batchErrorCorrectionForm" :option="batchErrorCorrectionOption" v-model="batchErrorCorrectionForm" @submit="batchSubmitErrorCorrection">
        <template slot-scope="scope" slot="urlList">
          <div>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:40px; text-decoration:none;letter-spacing:5px">【重要说明：】</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:40px; text-decoration:none;letter-spacing:5px">1、每一笔消费单，只能进行一次消费纠错，请务必谨慎操作！</span></p>
            <p style="line-height:0; margin-bottom:5px;"><span class="--mb--rich-text" data-boldtype="0" style="font-family:SourceHanSansSC; font-weight:400; font-size:14px; color:rgb(251, 4, 4); font-style:normal; letter-spacing:0px; line-height:40px; text-decoration:none;letter-spacing:5px">2、统缴餐消费单批量纠错时，系统将会自动忽略掉哪些之前已经进行过消费纠错的单据。</span></p>
          </div>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="消费补扣" :visible.sync="consumPatchingVisible" :append-to-body="true" @close="consumPatchingClose" width="60%">
      <avue-form ref="consumPatchingForm" :option="consumPatchingOption" v-model="consumPatchingForm" @submit="consumPatching">
        <template slot="chooseUsers" slot-scope="scope">
          <patching-user v-model="consumPatchingForm.chooseUsers" multiple></patching-user>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="批量退款" :visible.sync="batchRefundVisible" :append-to-body="true" @close="batchRefundClose" width="70%">
      <div style="padding-bottom: 20px;color: red">第一步：请选择消费日期餐次及部门，查询消费记录</div>
      <avue-crud :option="batchRefundOption"
                 :table-loading="batchRefundLoading"
                 :data="batchRefundData"
                 :page="batchRefundPage"
                 v-model="batchRefundForm"
                 ref="batchRefundForm"
                 @search-change="batchRefundSearchChange"
                 @search-reset="batchRefundSearchReset"
                 @selection-change="batchRefundSelectionChange"
                 @current-change="batchRefundCurrentChange"
                 @size-change="batchRefundSizeChange"
                 @on-load="onLoadBatchRefund">
        <template slot="menuLeft">
          <span class="filter-item" size="small" type="warning" icon="el-icon-upload" style="color: red;">第二步：请勾选将要批量退款的人员后，点击立即退款</span>
        </template>
      </avue-crud>
      <div style="text-align: center;padding-top: 20px;">
        <el-button type="primary" icon="el-icon-check" size="small" @click="handleImmediateRefund">立即退款</el-button>
      </div>
    </el-dialog>

    <el-dialog title="批量退款" :visible.sync="submitBatchRefundVisible" :append-to-body="true" @close="submitBatchRefundClose" width="50%">
      <div style="padding-bottom: 20px;font-size: 17px;font-weight: bold;">已选批量退款人员 （{{this.personnelNumber}} 人）</div>
      <div style="padding-bottom: 35px;font-size: 15px;">{{this.personnelName}}</div>
      <div style="padding-bottom: 35px;font-size: 17px;font-weight: bold;">退款总金额&nbsp;&nbsp;&nbsp;{{this.amountTotal}}元</div>
      <div style="color: red;">重要提示：</div>
      <div style="padding-bottom: 35px;color: red;">此操作不可撤销，不可逆转，发布后将退款金额退还到用餐客户的统缴餐钱包中，请务必认真检查，谨慎操作!</div>
      <div style="text-align: center;padding-top: 20px;">
        <el-button type="primary" icon="el-icon-check" size="small" @click="subMitBatchRefund">确定</el-button>
        <el-button type="primary" icon="el-icon-circle-close" size="small" plain @click="submitBatchRefundClose">取消</el-button>
      </div>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getDetail,
  add,
  update,
  remove,
  exportOrderDetailsData,
  getOrderMelasList,
  getOrderDishesList,
  refund,
  patchingUserStatus,
  batchPatching,
  getMealsList,
  batchRefund
} from "@/api//businessManage/consumDetails/consumDetails";
import {add as addErrorCorrection,batchAdd,errorCorrectStatus,singleAdd} from "@/api/businessManage/errorCorrectionDetails";
import {createConsumeVoucher} from "@/api/supplier/finance/financialVoucher";
import {
  getDeptAgencyType,
} from "@/api/system/dept";
import {mapGetters} from "vuex";
import {getSubjectTree,getDeptTree} from "@/api/supplier/finance/financialUtils";
import {getConsumDetailsList} from "@/api/supplier/finance/financialBill";
const DIC = {
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
  IDENTITY: [
    {
      label: '学生',
      value: "STUDENT"
    },
    {
      label: '家长',
      value: "PARENT"
    },
    {
      label: '老师',
      value: "TEACHER"
    },
    {
      label: '系统',
      value: "SYSTEM"
    },
    {
      label: '食堂职工',
      value: "UNIT"
    }
  ],
  ERRORSTATUS:[
    {
      label: '少收钱',
      value: "1"
    },
    {
      label: '多收钱',
      value: "2"
    }
  ],
  MEALTYPE: [
    {
      label: '统缴餐',
      value: "0"
    },
    {
      label: '自选餐(自由消费)',
      value: "1"
    },
    {
      label: '自选餐(预订菜品)',
      value: "2"
    },
    {
      label: '自选餐(预订餐次)',
      value: "3"
    },
  ],
  PAYSTATUS:[{
    label: '待支付',
    value: "WAITPAY"
  },{
    label: '成功',
    value: "PAID"
  },
    {
      label: '已退款',
      value: "REFUNDED"
    },{
      label: '支付失败',
      value: "PAYFAIL"
    }],
  IFVOUCHER: [
    {
      label: '是',
      value: "1"
    },
    {
      label: '否',
      value: "0"
    }
  ],
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      deptTree:[],
      form: {},
      query: {},
      obj:{},
      freeObj:{},
      orderMealsObj:{},
      errorCorrectionForm:{},
      mealErrorCorrectionForm:{},
      batchErrorCorrectionForm:{},
      orderMealsForm:{},
      orderDishesForm:{},
      dishesForm:{},
      batchRefundForm:{},
      consumPatchingForm:{
        chooseUsers: [],
      },
      loading: true,
      batchRefundLoading:true,
      orderMealsLoading:true,
      orderDishesLoading:true,
      batchRefundVisible:false,
      batchErrorCorrectionVisible:false,
      submitBatchRefundVisible:false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      batchRefundPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      orderMealsPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      orderDishesPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      orderMealsData:[],
      orderDishesData:[],
      orderDishesList:[],
      batchRefundData:[],
      searchForm: {},
      consumDetailsVisible:false,
      freeConsumDetailsVisible:false,
      orderMealsConsumDetailsVisible:false,
      consumErrorCorrectionVisible:false,
      mealConsumErrorCorrectionVisible:false,
      dishesDetailsVisible:false,
      consumPatchingVisible:false,
      consumOrderId:undefined,
      userId:undefined,
      orderIds:undefined,
      orderMealsId:undefined,
      dishesTotal2:undefined,
      startDate:undefined,
      endDate:undefined,
      personnelNumber:undefined,
      personnelName:undefined,
      amountTotal:undefined,
      isContractor:undefined,
      selectionList: [],
      selectionNameList:[],
      selectionMmountList:[],
      orderDishesOption:{
        /*          height:'auto',
                  calcHeight: 30,
                  searchShow: true,
                  searchMenuSpan: 6,*/
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        header: false,
        /*selection: true,*/
        menu:true,
        column: [
          {
            label: "日期",
            prop: "month",
            type: "input",

          },
          {
            label: "星期",
            prop: "week",
            type: "select",
            dicData: [
              {
                label: "周一",
                value: "1"
              },
              {
                label: "周二",
                value: "2"
              },
              {
                label: "周三",
                value: "3"
              },
              {
                label: "周四",
                value: "4"
              },
              {
                label: "周五",
                value: "5"
              },
              {
                label: "周六",
                value: "6"
              },
              {
                label: "周日",
                value: "7"
              }
            ],
          },
          {
            label: "餐次",
            prop: "mealName",
            type: "input",
          },
          {
            label: "价格",
            prop: "money",
            type: "input",
          },
          {
            label: '状态',
            prop: 'isRefund',
            type: 'select',
            dicData: [
              {
                label: "已订",
                value: "0"
              },
              {
                label: "已退订",
                value: "1"
              },
              {
                label: "不可退订",
                value: "2"
              },
              {
                label: "结束",
                value: "3"
              }
            ],
          }
        ]
      },
      orderMealsOption:{
        /*          height:'auto',
                  calcHeight: 30,
                  searchShow: true,
                  searchMenuSpan: 6,*/
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        header: false,
        /*selection: true,*/
        menu:false,
        column: [
          {
            label: "日期",
            prop: "month",
            type: "input",

          },
          {
            label: "星期",
            prop: "week",
            type: "select",
            dicData: [
              {
                label: "周一",
                value: "1"
              },
              {
                label: "周二",
                value: "2"
              },
              {
                label: "周三",
                value: "3"
              },
              {
                label: "周四",
                value: "4"
              },
              {
                label: "周五",
                value: "5"
              },
              {
                label: "周六",
                value: "6"
              },
              {
                label: "周日",
                value: "7"
              }
            ],
          },
          {
            label: "餐次",
            prop: "mealName",
            type: "input",
          },
          {
            label: "价格",
            prop: "money",
            type: "input",
          },
          {
            label: '状态',
            prop: 'isRefund',
            type: 'select',
            dicData: [
              {
                label: "已订",
                value: "0"
              },
              {
                label: "已退订",
                value: "1"
              },
              {
                label: "不可退订",
                value: "2"
              },
              {
                label: "结束",
                value: "3"
              }
            ],
          }
        ]
      },
      option: {
        /*   height:'auto',
           calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: true,
        searchSpan:100,
        menuWidth: 150,
        /*showSummary: true,
        sumColumnList: [
          {
            name: 'actualConsumMoney',
            type: 'sum'
          },
          ],*/
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "单位",
            prop: "unitId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search:true,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }]
          },
          {
            label: "学校",
            prop: "schoolId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: true,
            search:true,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }]
          },
          // {
          //   label: "姓名",
          //   prop: "userId",
          //   type: 'select',
          //   remote: true,
          //   dicUrl: "/api/service/rabbit-personnel/systemPersonnel/dict?name={{key}}",
          //   search:true,
          //   sortable:true,
          // },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search:true,
          },
          {
            label: '性别',
            prop: 'gender',
            type: "radio",
            //slot: true,
            dicData: DIC.sex,
            hide:true,
          },
          {
            label: "性别",
            prop: "sex",
            type: "input",
            sortable:true,
          },
          {
            label: "编码",
            prop: "financialCode",
            type: "input",
            sortable:true,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            search:false,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            slot: true,
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          {
            label: "食堂",
            prop: "canteenId",
            type: "tree",
            search:true,
            hide: true,
            // dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=0",
            dicData:[],
            props: {
              label: "deptName",
              value:"id"
            },
          },
          {
            label: "所属食堂",
            prop: "canteenName",
            type: "input",
            hide: false,
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            sortable:true,
          },
          {
            label: '用餐类型',
            prop: 'consumType',
            type:'select',
            dicData: DIC.MEALTYPE,
            search:false,
            sortable:true,
          },
          {
            label: '餐次',
            prop: 'dinnerType',
            type: 'select',
            span: 24,
            search: false,
            sortable:true,
            dicData: [
              {
                value: '-1',
                label: '其他'
              },
              {
                value: '0',
                label: '早餐'
              },
              {
                value: '1',
                label: '中餐'
              },
              {
                value: '2',
                label: '晚餐'
              },
              {
                value: '3',
                label: '夜餐'
              }
            ]
          },
          {
            label: "下单金额",
            prop: "money",
            type: "number",
            precision:2,
            sortable:true,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            width: 100,
          },
          {
            label: "实收金额",
            prop: "actualConsumMoney",
            type: "number",
            precision:2,
            sortable:true,
            search: false,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            width: 90,
          },
          {
            label: "开始时间",
            prop: "startDateTime",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "结束时间",
            prop: "endDateTime",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          /*          {
                      label: '用餐时间',
                      prop: 'orderDate',
                      type:'datetime',
                      searchSpan:6,
                      searchRange:true,
                      search:true,
                      format: 'yyyy-MM-dd',
                      valueFormat: 'yyyy-MM-dd',
                      hide:true,
                    },*/
          /*           {
                       label: "下单金额",
                       prop: "orderAmount",
                       type: "input",
                       search:true,
                       hide:true,
                     },*/
          {
            label: '用餐时间',
            prop: 'consumTime',
            type: 'date',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            disabled:true,
            sortable:true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            dicData: DIC.PAYSTATUS,
            search:true,
            sortable:true,
          },
          {
            label: "下单操作人",
            prop: "placeOrderName",
            type: "input",
            search:true,
            sortable:true,
          },
          /*     {
                 label: "操作人身份",
                 prop: "operatorIdentity",
                 type: "select",
                 dicData: DIC.IDENTITY,
                 search:true,
               },*/
          /*    {
                label: "下单单号",
                prop: "consumBillNo",
                type: "input",
                width: 160,
              },*/
          /*          {
                      label: "营业网点",
                      prop: "outletsId",
                      type: "select",
                      span: 24,
                      search:true,
                      rules: [{
                        required: true,
                        message: "请输入营业网点",
                        trigger: "blur"
                      }],
                      dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/select",
                      props: {
                        label: "name",
                        value: "id"
                      },
                    },*/
          {
            label: "用餐类别",
            prop: "dinnerTypeId",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/diningType/allDict',
            props: {
              label: "name",
              value: "id"
            },
            search:false,
          },
          {
            label: "营业网点",
            prop: "outletsId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            search:false,
            sortable:true,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowGroup'],
            cascaderChange: true
          },
          {
            label: "窗口组",
            prop: "windowGroupId",
            type: "select",
            span: 24,
            search:false,
            sortable:true,
            rules: [{
              required: true,
              message: "请输入窗口组",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowId'],
            cascaderChange: true
          },
          {
            label: "窗口号",
            prop: "windowId",
            type: "select",
            span: 24,
            search:false,
            sortable:true,
            rules: [{
              required: true,
              message: "请输入窗口号",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
            props: {
              label: "number",
              value: "id"
            },
          },
          {
            label: "是否已生成",
            prop: "ifVoucher",
            type: "select",
            sortable:true,
            hide: true,
            search: true,
            dicData: DIC.IFVOUCHER,
            /*hide:true,*/
          },
        ]
      },
      option1: {
        detail:false,
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 150,
        group: [
          {
            label: '用餐人',
            prop: 'yhxx',
            column: [
              {
                label: '姓名',
                prop: 'userName',
                detail:true,
                span: 6,
                placeholder:'',
                disabled:true
              },
              {
                label: '性别',
                prop: 'sex',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '学号/工号',
                prop: 'studentJobNo',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '部门',
                prop: 'deptName',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          } ,{
            label: '消费',
            prop: 'tksq',
            column: [
              {
                label: '营业网点',
                prop: 'outletsName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '下单类型',
                prop: 'consumType',
                detail:true,
                type:'select',
                span: 6,
                dicData: DIC.MEALTYPE,
                disabled:true
              },
              {
                label: '下单金额',
                prop: 'consumMoney',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '用餐时间',
                prop: 'consumTime',
                type: 'date',
                format: 'yyyy-MM-dd HH:mm:ss',
                valueFormat: 'yyyy-MM-dd HH:mm:ss',
                span: 6,
                disabled:true
              },
              {
                label: '下单单号',
                prop: 'consumBillNo',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '付款方式',
                prop: 'payMethod',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '下单操作人',
                prop: 'operatorName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: "操作人身份",
                prop: "operatorIdentity",
                type: "select",
                dicData: DIC.IDENTITY,
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '状态',
                prop: 'payStatus',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          }
          , {
            label: '附加信息',
            prop: 'yhxx',
            column: [
              {
                label: '统缴餐计划名称',
                prop: 'mealPayName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '缴费日期',
                prop: 'paymentDate',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '供餐日期',
                prop: 'servingDate',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '用餐类别',
                prop: 'mealCategory',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          }
        ]
      },
      option2: {
        detail:false,
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 150,
        group: [
          {
            label: '用餐人',
            prop: 'yhxx',
            column: [
              {
                label: '姓名',
                prop: 'userName',
                detail:true,
                span: 6,
                placeholder:'',
                disabled:true
              },
              {
                label: '性别',
                prop: 'sex',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '学号/工号',
                prop: 'studentJobNo',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '部门',
                prop: 'deptName',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          } ,{
            label: '消费',
            prop: 'tksq',
            column: [
              {
                label: '营业网点',
                prop: 'outletsName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '消费类型',
                prop: 'consumType',
                detail:true,
                type:'select',
                span: 6,
                dicData: DIC.MEALTYPE,
                disabled:true
              },
              {
                label: '消费金额',
                prop: 'consumMoney',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '消费时间',
                prop: 'consumTime',
                type: 'date',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd',
                span: 6,
                disabled:true
              },
              {
                label: '消费单号',
                prop: 'consumBillNo',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '付款方式',
                prop: 'payMethod',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '下单操作人',
                prop: 'operatorName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: "操作人身份",
                prop: "operatorIdentity",
                type: "select",
                dicData: DIC.IDENTITY,
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '状态',
                prop: 'payStatus',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          }
        ]
      },
      option3: {
        detail:false,
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 150,
        group: [
          {
            label: '用餐人',
            prop: 'yhxx',
            column: [
              {
                label: '姓名',
                prop: 'userName',
                detail:true,
                span: 6,
                placeholder:'',
                disabled:true
              },
              {
                label: '性别',
                prop: 'sex',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '学号/工号',
                prop: 'studentJobNo',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '部门',
                prop: 'deptName',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          } ,{
            label: '消费',
            prop: 'tksq',
            column: [
              {
                label: '营业网点',
                prop: 'outletsName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '下单类型',
                prop: 'consumType',
                detail:true,
                type:'select',
                span: 6,
                dicData: DIC.MEALTYPE,
                disabled:true
              },
              {
                label: '下单金额',
                prop: 'consumMoney',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '实际消费金额',
                prop: 'actualConsumMoney',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '消费时间',
                prop: 'consumTime',
                type: 'date',
                format: 'yyyy-MM-dd',
                valueFormat: 'yyyy-MM-dd',
                span: 6,
                disabled:true
              },
              {
                label: '下单单号',
                prop: 'consumBillNo',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '付款方式',
                prop: 'payMethod',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '下单操作人',
                prop: 'operatorName',
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: "操作人身份",
                prop: "operatorIdentity",
                type: "select",
                dicData: DIC.IDENTITY,
                detail:true,
                span: 6,
                disabled:true
              },
              {
                label: '状态',
                prop: 'payStatus',
                detail:true,
                span: 6,
                disabled:true
              }
            ]
          }
        ]
      },
      errorCorrectionOption: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: true,
        labelWidth: 150,
        dialogWidth: 900,
        dialogHeight: 800,
        emptyBtn:false,
        column: [
          {
            label: "纠错前下单金额",
            prop: "orderAmount",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            disabled:true,
            rules: [{
              required: true,
              message: "请选择采购类型",
              trigger: "blur"
            }],
          },
          {
            label: "纠错后实收金额",
            prop: "paidAmount",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            change:({value,column})=>{
              if(value !== '' && value !== undefined && value != null) {
                var orderAmount = this.errorCorrectionForm.orderAmount;
                var differenceAmount = value - orderAmount;
                this.errorCorrectionForm.differenceAmount = differenceAmount.toFixed(2);
                if (this.errorCorrectionForm.differenceAmount > 0){
                  this.errorCorrectionForm.type = '少收钱'
                  this.errorCorrectionForm.status = 1;
                } else {
                  this.errorCorrectionForm.type = '多收钱'
                  this.errorCorrectionForm.status = 2;
                }
              }else {
                this.errorCorrectionForm.differenceAmount = '0.00'
              }
            },
            rules: [{
              required: true,
              message: "请输入纠错金额",
              trigger: "blur"
            }],
          },
          {
            label: "纠错差额",
            prop: "differenceAmount",
            type: "input",
            disabled:true,
          },
          {
            label: "错误类型",
            prop: "type",
            type: "input",
            disabled:true,
          },
          {
            label: "错误类型",
            prop: "status",
            type: "input",
            display:false,
          },
          {
            label: "纠错备注",
            prop: "errorRemark",
            type: "textarea",
            span:24,
            showWordLimit:true,
            maxlength:30,
            rules: [{
              required: true,
              message: "请输入纠错原因",
              trigger: "blur"
            }],
          },
        ]
      },
      mealErrorCorrectionOption: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: true,
        labelWidth: 150,
        dialogWidth: 900,
        dialogHeight: 800,
        emptyBtn:false,
        column: [
          {
            label: "纠错前下单金额",
            prop: "orderAmount",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            disabled:true,
            rules: [{
              required: true,
              message: "请选择采购类型",
              trigger: "blur"
            }],
          },
          {
            label: "纠错后实收金额",
            prop: "paidAmount",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            change:({value,column})=>{
              if(value !== '' && value !== undefined && value != null) {
                var orderAmount = this.mealErrorCorrectionForm.orderAmount;
                var differenceAmount = value - orderAmount;
                this.mealErrorCorrectionForm.differenceAmount = differenceAmount.toFixed(2);
                if (this.mealErrorCorrectionForm.differenceAmount > 0){
                  this.mealErrorCorrectionForm.type = '少收钱'
                  this.mealErrorCorrectionForm.status = 1;
                } else {
                  this.mealErrorCorrectionForm.type = '多收钱'
                  this.mealErrorCorrectionForm.status = 2;
                }
              }else {
                this.mealErrorCorrectionForm.differenceAmount = '0.00'
              }
            },
            rules: [{
              required: true,
              message: "请输入纠错金额",
              trigger: "blur"
            }],
          },
          {
            label: "纠错差额",
            prop: "differenceAmount",
            type: "input",
            disabled:true,
          },
          {
            label: "错误类型",
            prop: "type",
            type: "input",
            disabled:true,
          },
          {
            label: "错误类型",
            prop: "status",
            type: "input",
            display:false,
          },
          {
            label: "纠错备注",
            prop: "errorRemark",
            type: "textarea",
            span:24,
            showWordLimit:true,
            maxlength:30,
            rules: [{
              required: true,
              message: "请输入纠错原因",
              trigger: "blur"
            }],
          },
        ]
      },
      batchErrorCorrectionOption: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: true,
        labelWidth: 150,
        dialogWidth: 900,
        dialogHeight: 800,
        emptyBtn:false,
        column: [
          {
            label: '下单日期',
            prop: 'orderDate',
            type:'datetime',
            searchSpan:6,
            searchRange:true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
          },
          {
            label: '餐次',
            prop: 'dinnerType',
            type: 'select',
            dicData: [
              {
                value: '0',
                label: '早餐'
              },
              {
                value: '1',
                label: '中餐'
              },
              {
                value: '2',
                label: '晚餐'
              },
              {
                value: '3',
                label: '夜餐'
              }
            ]
          },
          {
            label: "纠错前下单金额",
            prop: "orderAmount",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            rules: [{
              required: true,
              message: "请选择采购类型",
              trigger: "blur"
            }],
          },
          {
            label: "纠错后实收金额",
            prop: "paidAmount",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            change:({value,column})=>{
              if(value !== '' && value !== undefined && value != null) {
                var orderAmount = this.batchErrorCorrectionForm.orderAmount;
                var differenceAmount = value - orderAmount;
                this.batchErrorCorrectionForm.differenceAmount = differenceAmount.toFixed(2);
                if (this.batchErrorCorrectionForm.differenceAmount > 0){
                  this.batchErrorCorrectionForm.type = '少收钱'
                  this.batchErrorCorrectionForm.status = 1;
                } else {
                  this.batchErrorCorrectionForm.type = '多收钱'
                  this.batchErrorCorrectionForm.status = 2;
                }
              }else {
                this.batchErrorCorrectionForm.differenceAmount = '0.00'
              }
            },
            rules: [{
              required: true,
              message: "请输入纠错金额",
              trigger: "blur"
            }],
          },
          {
            label: "纠错差额",
            prop: "differenceAmount",
            type: "input",
            disabled:true,
          },
          {
            label: "错误类型",
            prop: "type",
            type: "input",
            disabled:true,
          },
          {
            label: "错误类型",
            prop: "status",
            type: "input",
            display:false,
          },
          {
            label: "纠错备注",
            prop: "errorRemark",
            type: "textarea",
            span:24,
            showWordLimit:true,
            maxlength:30,
            rules: [{
              required: true,
              message: "请输入纠错原因",
              trigger: "blur"
            }],
          },
          {
            label:'',
            prop: 'urlList',
            labelWidth: 0,
            span: 24,
            formslot: true,
          },
        ]
      },
      consumPatchingOption:{

        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            display: false
          },
          {
            label: "营业网点",
            prop: "businessOutletsId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            search:true,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowGroup'],
            cascaderChange: true
          },
          {
            label: "窗口组",
            prop: "windowGroup",
            type: "select",
            span: 24,
            search:true,
            rules: [{
              required: true,
              message: "请输入窗口组",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-windowGroup/windowGroup/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            cascaderItem: ['windowNumber'],
            cascaderChange: true
          },
          {
            label: "窗口号",
            prop: "windowNumber",
            type: "select",
            span: 24,
            search:true,
            rules: [{
              required: true,
              message: "请输入窗口号",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-window/window/dict/{{key}}",
            props: {
              label: "number",
              value: "id"
            },
          },
          {
            label: '用餐人',
            prop: 'chooseUsers',
            span: 24,
            hide: true,
            formslot: true, //自定义表单
            rules: [{
              required: true,
              message: '请选择用餐人',
              trigger: 'blur'
            }],
          },
          {
            label: "消费模式",
            prop: "consumMode",
            type: "select",
            dicData: [
              {
                label: "统缴餐",
                value: "0"
              },
              {
                label: "自选餐(自由消费)",
                value: "1"
              }
            ],
            value:"0",
            rules: [{
              required: true,
              message: "请选择消费模式",
              trigger: "blur"
            }],
          },
          {
            label: "消费日期",
            prop: "mealsDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            pickerOptions: {
              disabledDate(time) {
                return time.getTime() >= Date.now()-86400000;
              },
            },
            rules: [{
              required: true,
              message: "请选择消费日期",
              trigger: "blur"
            }],
          },
          {
            label: '消费餐次',
            prop: 'mealTimesType',
            type: 'select',
            dicData: [{
              value: '-1',
              label: '其他'
            },{
              value: '0',
              label: '早餐'
            },
              {
                value: '1',
                label: '中餐'
              },
              {
                value: '2',
                label: '晚餐'
              },
              {
                value: '3',
                label: '夜餐'
              }
            ],
            rules: [{
              required: true,
              message: "请选择消费餐次",
              trigger: "blur"
            }],
          },
          {
            label: "消费金额",
            prop: "consumAmount",
            type: "number",
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            minRows: 0,
            precision:2,
            rules: [{
              required: true,
              message: "请输入大于0的金额",
              trigger: "blur"
            }],
          },
        ]
      },
      batchRefundOption:{
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: true,
        searchSpan:100,
        menuWidth: 150,
        menu:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            width: 100,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            width: 150,
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            overHidden: true,
            width: 150,
          },
          {
            label: "下单金额",
            prop: "money",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            width: 100,
          },
          {
            label: "实收金额",
            prop: "actualConsumMoney",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            width: 90,
          },
          {
            label: "用餐类别",
            prop: "dinnerTypeId",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/diningType/allDict',
            props: {
              label: "name",
              value: "id"
            },
            search:true,
          },
          {
            label: '用餐日期',
            prop: 'orderDate',
            type:'datetime',
            searchSpan:7,
            searchRange:true,
            search:true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            hide:true,
          },
          {
            label: '餐次',
            prop: 'mealType',
            type: 'select',
            span: 24,
            searchSpan:5,
            search: false,
            hide:true,
            dicData: [{
              value: '0',
              label: '早餐'
            },
              {
                value: '1',
                label: '中餐'
              },
              {
                value: '2',
                label: '晚餐'
              },
              {
                value: '3',
                label: '夜餐'
              }
            ]
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree",
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          {
            label: '用餐时间',
            prop: 'consumTime',
            type: 'date',
            format: 'yyyy-MM-dd HH:mm:ss',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
          },
          {
            label: "营业网点",
            prop: "outletsId",
            type: "select",
            span: 24,
            rules: [{
              required: true,
              message: "请输入营业网点",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
            props: {
              label: "name",
              value: "id"
            },
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.consum_details_add, false),
        viewBtn: this.vaildData(this.permission.consum_details_view, false),
        delBtn: this.vaildData(this.permission.consum_details_delete, false),
        editBtn: this.vaildData(this.permission.consum_details_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    orderId() {
      let orderId = [];
      this.selectionList.forEach(ele => {
        orderId.push(ele.id);
      });
      return orderId.join(",");
    },
    names() {
      let names = [];
      this.selectionNameList.forEach(ele => {
        names.push(ele.userName);
      });
      return names.join(" ");
    },
    amount() {
      var amount = 0;
      this.selectionMmountList.forEach(ele => {
        amount = amount+ele.actualConsumBalance;
      });
      return amount.toFixed(2)
    }
  },
  created(){
    getSubjectTree().then(res=>{
      self.deptTree = res.data.data;
      const column9 = this.option.column[9];
      column9.dicData = self.deptTree;
    });
    getDeptTree(this.schoolId,null).then(res => {
      const index = this.$refs.crud.findColumnIndex("canteenId");
      this.option.column[index].dicData = res.data.data;
    });
    getDeptAgencyType().then(res =>{
      this.isContractor = res.data.data.isContractor;
      if (res.data.data.applicationMode === '1'){
        this.modeType = '1';
        this.modeType2 = '2';
      }else if (res.data.data.applicationMode === '2'){
        this.modeType = '2';
        this.modeType2 = '2';
      }else if (res.data.data.applicationMode === '3'){
        this.modeType = '1';
        this.modeType2 = '1';
        this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        /* this.option.menu = false;*/
      }else if (res.data.data.applicationMode === '0'){
        this.modeType = '1';
        this.modeType2 = '1';
        this.option.addBtn = false;
        this.option.delBtn = false;
        this.option.viewBtn = false;
        this.option.editBtn = false;
        /* this.option.menu = false;*/
      }
    })
    /*      getDeptTree().then(res => {
            const index = this.$refs.crud.findColumnIndex("deptId");
            this.option.column[index].dicData = res.data.data;
          });*/
    this.getMonthStartEnd();
    // 单位列表是否显示
    console.log(JSON.stringify(this.userInfo))
    this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    if (this.userInfo.userType === 'school'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.printBtn = true;
    }else if (this.userInfo.userType === 'education'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;

      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.column[8].search = false;
    }else if (this.userInfo.userType === 'jiWei'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;

      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.column[9].search = false;
    }else if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[3].search = false;
      this.option.column[4].hide = true;
      this.option.column[1].hide = true;
      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.printBtn = true;
    }
    /*if (this.userInfo.userType === 'school'){
      this.option.column[1].hide = true
    }
    this.option.column[1].search = this.unitSelect;*/
  },
  methods: {
    genVoucherBatch() {
      this.$message({
        type: "warning",
        message: "功能开发中",
      });
    },
    getMonthStartEnd(){
      var startDate = new Date().getFullYear()+' 01-01'
      var endDate = new Date().getFullYear()+' 12-31'
      this.startDate = startDate;
      this.endDate = endDate;
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.searchForm = {};
      this.searchForm.orderType = "1";
      this.query = {};
      this.getMonthStartEnd();
      this.onLoad(this.page);
    },
    batchRefundSearchReset() {
      this.query = {};
      this.onLoadBatchRefund(this.batchRefundPage);
    },
    orderMealsSearchReset() {
      this.searchForm = {};
      this.query = {};
      this.orderMealsOnLoad(this.orderMealsPage);
    },
    orderDishesSearchReset() {
      this.searchForm = {};
      this.query = {};
      this.orderDishesOnLoad(this.orderDishesPage);
    },
    searchChange(params, done) {
      if (params.startDateTime != '' && params.startDateTime != null && params.startDateTime != undefined){
        if (params.endDateTime == '' || params.endDateTime == null || params.endDateTime == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDateTime != '' && params.endDateTime != null && params.endDateTime != undefined){
        if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDateTime);
          var endDateTime = new Date(params.endDateTime);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
        this.getMonthStartEnd()
      }else {
        this.startDate = params.startDateTime;
        this.endDate = params.endDateTime;
      }
      this.searchForm = params;
      this.searchForm.orderType = "1";
      this.query = params;
      this.page.currentPage = 1
      /*      if (params.orderDate != '' && params.orderDate != null && params.orderDate != undefined) {
              params.startDateTime = params.orderDate[0];
              params.endDateTime = params.orderDate[1];
              this.startDate = params.orderDate[0];
              this.endDate = params.orderDate[1];
            }*/
      this.onLoad(this.page, params);
      done();
    },
    orderMealsSearchChange(params, done) {
      this.searchForm = params;
      this.query = params;
      this.orderMealsPage.currentPage = 1
      this.orderMealsOnLoad(this.orderMealsPage, params);
      done();
    },
    batchRefundSearchChange(params, done) {
      this.query = params;
      if (params.orderDate != '' && params.orderDate != null && params.orderDate != undefined) {
        params.startDateTime = params.orderDate[0];
        params.endDateTime = params.orderDate[1];
      }
      this.batchRefundPage.currentPage = 1
      this.onLoadBatchRefund(this.batchRefundPage, params);
      done();
    },
    orderDishesSearchChange(params, done) {
      this.searchForm = params;
      this.query = params;
      this.orderDishesPage.currentPage = 1
      this.orderDishesOnLoad(this.orderDishesPage, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    batchRefundSelectionChange(list) {
      this.selectionList = list;
      this.selectionNameList = list;
      this.selectionMmountList = list;
    },
    orderMealsSelectionChange(list) {
      this.selectionList = list;
    },
    orderDishesSelectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    batchRefundSelectionClear() {
      this.selectionList = [];
      this.selectionNameList = [];
      this.selectionMmountList = [];
      this.$refs.batchRefundForm.toggleSelection();
    },
    orderMealsSelectionClear() {
      this.selectionList = [];
      this.$refs.orderMealsForm.toggleSelection();
    },
    orderDishesSelectionClear() {
      this.selectionList = [];
      this.$refs.orderDishesForm.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    batchRefundCurrentChange(currentPage){
      this.batchRefundPage.currentPage = currentPage;
    },
    orderMealsCurrentChange(currentPage){
      this.orderMealsPage.currentPage = currentPage;
    },
    orderDishesCurrentChange(currentPage){
      this.orderDishesPage.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    batchRefundSizeChange(pageSize){
      this.batchRefundPage.pageSize = pageSize;
    },
    orderMealsSizeChange(pageSize){
      this.orderMealsPage.pageSize = pageSize;
    },
    orderDishesSizeChange(pageSize){
      this.orderDishesPage.pageSize = pageSize;
    },
    orderMealsOnLoad(page, params = {}) {
      this.orderMealsLoading = true;
      getOrderMelasList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.orderMealsId).then(res => {
        const data = res.data.data;
        this.orderMealsPage.total = data.total;
        this.orderMealsData = data.records;
        this.orderMealsLoading = false;
        this.orderMealsSelectionClear();
      });
    },
    orderDishesOnLoad(page, params = {}) {
      this.orderMealsLoading = true;
      getOrderDishesList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.orderMealsId).then(res => {
        const data = res.data.data;
        this.orderDishesPage.total = data.total;
        this.orderDishesData = data.records;
        this.orderDishesLoading = false;
        this.orderDishesSelectionClear();
      });
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(params.deptId == null && params.deptId){
        if(this.schoolId != null && this.schoolId != ''){
          params.unitId = this.schoolId;
        }
      }
      params.orderType = "1";
      getConsumDetailsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    onLoadBatchRefund(page, params = {}) {
      this.batchRefundLoading = true;
      getMealsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.batchRefundPage.total = data.total;
        this.batchRefundData = data.records;
        this.batchRefundLoading = false;
        this.batchRefundSelectionClear();
      });
    },
    opentMealsConsumDetails(row){
      this.orderMealsId = row.id;
      this.orderMealsPage.currentPage = 1;
      getDetail(row.id).then(res => {
        this.obj = res.data.data;
        this.obj.operatorName = row.operatorName
      });
      this.consumDetailsVisible = true;
    },
    opentFreeConsumDetails(row){
      getDetail(row.id).then(res => {
        this.freeObj.userName = res.data.data.userName
        this.freeObj.sex = res.data.data.sex
        this.freeObj.studentJobNo = res.data.data.studentJobNo
        this.freeObj.deptName = res.data.data.deptName
        this.freeObj.outletsName = res.data.data.outletsName
        this.freeObj.consumType = res.data.data.consumType
        this.freeObj.consumMoney = res.data.data.consumMoney
        this.freeObj.consumTime = res.data.data.consumTime
        this.freeObj.consumBillNo = res.data.data.consumBillNo
        this.freeObj.payMethod = res.data.data.payMethod
        this.freeObj.operatorName = row.operatorName
        this.freeObj.operatorIdentity = res.data.data.operatorIdentity
        this.freeObj.payStatus = res.data.data.payStatus
        this.freeObj.mealPayName = res.data.data.mealPayName
        this.freeObj.paymentDate = res.data.data.paymentDate
        this.freeObj.servingDate = res.data.data.servingDate
        this.freeObj.mealCategory = res.data.data.mealCategory
      });
      this.freeConsumDetailsVisible = true;
    },
    opentOrderMealsConsumDetails(row){
      getDetail(row.id).then(res => {
        this.orderMealsObj.userName = res.data.data.userName
        this.orderMealsObj.sex = res.data.data.sex
        this.orderMealsObj.studentJobNo = res.data.data.studentJobNo
        this.orderMealsObj.deptName = res.data.data.deptName
        this.orderMealsObj.outletsName = res.data.data.outletsName
        this.orderMealsObj.consumType = res.data.data.consumType
        this.orderMealsObj.consumMoney = res.data.data.consumMoney
        this.orderMealsObj.consumTime = res.data.data.consumTime
        this.orderMealsObj.consumBillNo = res.data.data.consumBillNo
        this.orderMealsObj.payMethod = res.data.data.payMethod
        this.orderMealsObj.operatorName = row.operatorName
        this.orderMealsObj.operatorIdentity = res.data.data.operatorIdentity
        this.orderMealsObj.payStatus = res.data.data.payStatus
        this.orderMealsObj.actualConsumMoney = res.data.data.actualConsumMoney
        this.orderMealsObj.mealPayName = res.data.data.mealPayName
        this.orderMealsObj.paymentDate = res.data.data.paymentDate
        this.orderMealsObj.servingDate = res.data.data.servingDate
        this.orderMealsObj.mealCategory = res.data.data.mealCategory
      });
      this.orderMealsConsumDetailsVisible = true;
    },
    exportOrderDetailsData(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出下方明细数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      this.searchForm.orderType ="1";
      exportOrderDetailsData(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '支付消费明细报表.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    tongjiaocan(){
      this.obj.userName = "";
      this.obj.sex = "";
      this.obj.studentJobNo = "";
      this.obj.deptName = "";
      this.obj.outletsName = "";
      this.obj.consumType = "";
      this.obj.consumMoney = "";
      this.obj.consumTime = "";
      this.obj.consumBillNo = "";
      this.obj.payMethod = "";
      this.obj.operatorName = "";
      this.obj.operatorIdentity = "";
      this.obj.payStatus = "";
      this.obj.mealPayName = "";
      this.obj.paymentDate = "";
      this.obj.servingDate = "";
      this.obj.mealCategory = "";
    },
    openErrorCorrectionSave(row){
      this.consumOrderId = row.id;
      this.userId = row.userId;
      this.errorCorrectionForm.orderAmount = row.money;
      this.consumErrorCorrectionVisible = true;
    },
    mealErrorCorrectionSave(row){
      this.orderIds = row.id;
      this.mealErrorCorrectionForm.orderAmount = row.money;
      this.mealConsumErrorCorrectionVisible = true;
    },
    consumErrorCorrectionClose(row){
      this.errorCorrectionForm.orderAmount = "";
      this.errorCorrectionForm.paidAmount = "";
      this.errorCorrectionForm.differenceAmount = "";
      this.errorCorrectionForm.errorRemark = "";
      this.errorCorrectionForm.status = "";
      this.consumErrorCorrectionVisible = false;
    },
    mealConsumErrorCorrectionClose(row){
      this.mealErrorCorrectionForm.orderAmount = "";
      this.mealErrorCorrectionForm.paidAmount = "";
      this.mealErrorCorrectionForm.differenceAmount = "";
      this.mealErrorCorrectionForm.errorRemark = "";
      this.mealErrorCorrectionForm.status = "";
      this.mealConsumErrorCorrectionVisible = false;
    },
    batchErrorCorrectionClose(){
      this.batchErrorCorrectionForm.orderDate = "";
      this.batchErrorCorrectionForm.dinnerType = "";
      this.batchErrorCorrectionForm.orderAmount = "";
      this.batchErrorCorrectionForm.paidAmount = "";
      this.batchErrorCorrectionForm.differenceAmount = "";
      this.batchErrorCorrectionForm.type = "";
      this.batchErrorCorrectionForm.errorRemark = "";
      this.batchErrorCorrectionVisible=false;
    },
    submitErrorCorrection(row, loading, done) {
      if (row.paidAmount <= 0) {
        this.$message.warning('金额必须大于0');
        loading();
      } else {
        row.consumOrderId = this.consumOrderId;
        row.userId = this.userId;
        addErrorCorrection(row).then(() => {
          loading();
          this.errorCorrectionForm.orderAmount = "";
          this.errorCorrectionForm.paidAmount = "";
          this.errorCorrectionForm.differenceAmount = "";
          this.errorCorrectionForm.errorRemark = "";
          this.errorCorrectionForm.status = "";
          this.consumErrorCorrectionVisible = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          loading();
          window.console.log(error);
        });
      }
    },
    mealSubmitErrorCorrection(row, loading, done) {
      if (row.paidAmount <= 0) {
        this.$message.warning('金额必须大于0');
        loading();
      } else {
        row.orderIds = this.orderIds;
        singleAdd(row).then(() => {
          loading();
          this.mealErrorCorrectionForm.orderAmount = "";
          this.mealErrorCorrectionForm.paidAmount = "";
          this.mealErrorCorrectionForm.differenceAmount = "";
          this.mealErrorCorrectionForm.errorRemark = "";
          this.mealErrorCorrectionForm.status = "";
          this.mealConsumErrorCorrectionVisible = false;
          this.page.currentPage = 1;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          loading();
          window.console.log(error);
        });
      }
    },
    batchSubmitErrorCorrection(row, loading, done) {
      if (row.paidAmount <= 0) {
        this.$message.warning('纠错后实收金额必须大于0');
        loading();
      } else if (row.orderAmount <= 0 ){
        this.$message.warning('纠错前下单金额必须大于0');
        loading();
      }else {
        row.orderIds = this.orderId;
        errorCorrectStatus(row).then(res => {
          if (res.data.data === 'error') {
            this.$alert(res.data.message, '提示', {
              confirmButtonText: '确定',
            });
            loading();
          } else {
            this.$confirm("确定要批量纠错？", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                batchAdd(row).then(() => {
                  loading();
                  this.batchErrorCorrectionForm.orderDate = "";
                  this.batchErrorCorrectionForm.dinnerType = "";
                  this.batchErrorCorrectionForm.orderAmount = "";
                  this.batchErrorCorrectionForm.paidAmount = "";
                  this.batchErrorCorrectionForm.differenceAmount = "";
                  this.batchErrorCorrectionForm.type = "";
                  this.batchErrorCorrectionForm.errorRemark = "";
                  this.batchErrorCorrectionVisible=false;
                  this.page.currentPage =  1;
                  this.onLoad(this.page);
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  });
                }, error => {
                  loading();
                  window.console.log(error);
                });
              })
              .catch(() => {
                loading();
              });
          }
        }, error => {
          loading();
          window.console.log(error);
        });
      }
    },
    dishesDetailsClose(){
      this.dishesDetailsVisible = false;
    },
    orderDishesDetails(row){
      this.orderDishesList = row.optionalDishes;
      this.dishesForm.dishesTotal = row.money;
      this.dishesDetailsVisible = true;
    },
    openRufundSave(row){
      this.$confirm('重要提示:此操作不可撤销，不可逆转。发布后将退款金额退还到用餐客户的伙食费钱包中，请务必认真检查，谨慎操作!', '是否确认对该消费单据进行退款操作?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        center: true
      }).then(() => {
        refund(row.id).then(
          res => {
            this.$message({
              type: 'success',
              message: res.data.data
            });
            this.page.currentPage = 1;
            this.onLoad(this.page);
          },
          error => {
            window.console.log(error);
          }
        );
      }).catch(() => {
      });
    },
    consumPatchingClose(){
      this.$refs.consumPatchingForm.resetForm()
      this.consumPatchingForm.chooseUsers = [];
      this.consumPatchingVisible = false;
    },
    openBatchRefund(){
      this.batchRefundVisible = true;
    },
    openBatchErrorCorrection(row){
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.batchErrorCorrectionVisible = true;
    },
    batchRefundClose(){
      this.batchRefundPage.currentPage = 1
      this.onLoadBatchRefund(this.batchRefundPage);
      this.batchRefundVisible = false;
    },
    openConsumPatching(){
      this.consumPatchingVisible = true;
    },
    consumPatching(row,loading,done){
      if (row.consumAmount <= 0){
        this.$message.warning('金额必须大于0');
        loading();
      }else {
        patchingUserStatus(row).then(res => {
          if (res.data.data === 'yes') {
            this.$alert(res.data.message, '提示', {
              confirmButtonText: '确定',
            });
            loading();
          } else {
            this.$confirm("确定要批量补扣:" + row.consumAmount.toFixed(2) + "元?", "提示", {
              confirmButtonText: "确定",
              cancelButtonText: "取消",
              type: "warning"
            })
              .then(() => {
                batchPatching(row).then(() => {
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  });
                  this.$refs.consumPatchingForm.resetForm();
                  this.consumPatchingForm.chooseUsers = [];
                  this.consumPatchingVisible = false;
                  this.onLoad(this.page);
                  loading();
                }, error => {
                  loading();
                  window.console.log(error);
                });
              })
              .catch(() => {
                loading();
              });
          }
        }, error => {
          loading();
          window.console.log(error);
        });
      }
    },
    handleImmediateRefund(){
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.personnelName = this.names;
      this.personnelNumber = this.selectionList.length;
      this.amountTotal = this.amount;
      this.submitBatchRefundVisible = true;
    },
    submitBatchRefundClose(){
      this.submitBatchRefundVisible = false;
    },
    subMitBatchRefund(){
      this.$confirm("确定将选择的数据批量退款?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return refund(this.ids);
        })
        .then(() => {
          this.batchRefundPage.currentPage = 1
          this.onLoadBatchRefund(this.batchRefundPage);
          this.page.currentPage = 1;
          this.onLoad(this.page);
          this.submitBatchRefundVisible = false;
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    createVoucher(row) {
      this.$confirm("确定生成凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        let param = {
          billCode: row.financialCode,
          billNo: row.id,
          canteenId: row.canteenId
        }
        return createConsumeVoucher(param);
      }).then(
        () => {
          this.loading = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        },
        error => {
          this.loading = false;
          this.$message.error(error);
        }
      );
    }
  }
};
</script>

<style>
</style>
