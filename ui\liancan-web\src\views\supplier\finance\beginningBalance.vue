<template>
  <basic-container>
    <div style="margin-left: 15px;">
      <el-form ref="formAccount" :model="formAccount" label-width="80px" >
        <el-form-item label="当前帐套:">
          <el-select v-model="accountSetsId"
                     :change="changeAccountSet"
                     placeholder="请选择帐套"
                     style="width:450px;">
            <el-option
              v-for="item in accountSetsList"
              :key="item.id"
              :label="item.accountName"
              :value="item.id">
            </el-option>
          </el-select>
          <el-tag type="danger" style="margin-left: 20px;">帐套正式启用前，请先设置各会计科目的期初金额</el-tag>
        </el-form-item>
        <el-divider></el-divider>
      </el-form>
    </div>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="dataCompute"
               :page="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crudEx"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuRight">
        <el-button type="primary"
                   size="small"
                   @click="trialBalanceHandler">试算平衡</el-button>
      </template>
      <template slot="menuLeft">
        <el-button v-if="this.accountSets.isUse==0" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="importData">导入</el-button>
        <el-button class="filter-item" size="small" type="success" icon="el-icon-upload" @click="exportRecord">导出</el-button>
      </template>
      <template slot-scope="{row,index}" slot="menu">
        <el-button
          type="text"
          size="small"
          v-if="row.isLastLayer == 0 && !row.$cellEdit"
          @click="rowCell(row,index)"
        >修改</el-button>
        <el-button
          type="text"
          size="small"
          v-if="row.isLastLayer == 0  && row.$cellEdit"
          @click="rowCell(row,index)"
        >保存</el-button>
        <el-button
          type="text"
          size="small"
          v-if="row.isLastLayer == 0  && row.$cellEdit"
          @click="rowCancelEdit(row,index)"
        >取消</el-button>

        <el-button type="text"
                   icon="el-icon-turn-off"
                   size="small"
                   v-if="row.isLastLayer == 0"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="resetAccount(row, 0)">重置
        </el-button>
      </template>
      <template slot="subjectCode" slot-scope="{row}" >
        <div v-if="row.layer==undefined">{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==1">{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==2">&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==3">&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==4">&emsp;&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==5">&emsp;&emsp;&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
        <div v-if="row.layer==6">&emsp;&emsp;&emsp;&emsp;&emsp;{{getDistplayStr(row.layer,row.subjectCode)}}</div>
      </template>
    </avue-crud>
<!--    //试算平衡弹出框==========================================-->
    <el-dialog title="试算平衡检查"
               append-to-body
               :visible.sync="tipDialogShow"
               width="30%"
               :before-close="handleClose">
      <el-button class="el-icon-check" type="success" circle size="mini" v-if="isBalance==0" @click="handleClose"></el-button>
      <el-button class="el-icon-close" type="danger" circle size="mini" v-if="isBalance==1" @click="handleClose"></el-button>
      <el-tag type="info" v-if="isBalance==0" closable @click="handleClose">{{tipInfo}}</el-tag>
      <el-tag type="danger" v-if="isBalance==1" closable @click="handleClose">{{tipInfo}}</el-tag>
      <el-table
        :data="tipData"
        border
        style="width: 100%">
        <el-table-column
          prop="subjectName"
          label="项目"
          width="180">
        </el-table-column>
        <el-table-column
          prop="debitAmount"
          label="借方金额">
        </el-table-column>
        <el-table-column
          prop="lenderAmount"
          label="贷方金额">
        </el-table-column>
        <el-table-column
          prop="difAmount"
          label="差额">
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog title="导入" :visible.sync="importDialogVisible" width="70%" @close="closeDow"
               :append-to-body="true">
      <avue-form ref="downForm" :option="option1" v-model="downForm" :upload-preview="uploadPreview"
                 :upload-error="uploadError" :upload-delete="uploadDelete" :upload-before="uploadBefore" :upload-after="uploadAfter" @submit="uploadFile">
        <template slot="attachmentsd" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">表头：
            科目编码、科目名称、方向、期初金额、借方累计、贷方累计、年初余额
          </el-button>
        </template>
        <template slot="attachments" slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-view" @click="handleExcel()" style="font-size:15px;">点击下载基础信息模板
          </el-button>
        </template>
        <template slot="excleXls" slot-scope="scope">
          <el-button size="mini" type="text" style="font-size:10px;color:#000000">文件后缀名必须为xls或xlsx （即Excel格式），文件大小不得大于1MB
          </el-button>
        </template>
      </avue-form>
    </el-dialog>

  </basic-container>
</template>

<script>
import {
  getList,
  getDetail,
  add,
  update,
  trialBalance,
  resetAccount
} from "@/api/supplier/finance/beginningBalance";
import {mapGetters} from "vuex";
import {amtFilters,amtFiltersEx} from "@/api/supplier/finance/financialUtils";
import {getAccountSetsList,IsNumber} from "@/api/supplier/finance/voucher";
import {exportBeginningBalanceRecord} from "@/api/supplier/finance/exportExcel";
import {selectType,downBeinningBalanceTemplate, uploadFileBeginningBalance} from "@/api/supplier/finance/importExcel";
var DIC = {
  // 余额方向
  balanceDirection: [{
    label: '借',
    value: 0
  },{
    label: '贷',
    value: 1
  }],
  // 类型
  systemDefault: [{
    label: '固定科目',
    value: 0
  },{
    label: '自定义科目',
    value: 1
  }],
  // 是否允许选择
  isSelect: [{
    label: '否',
    value: 0
  },{
    label: '是',
    value: 1
  }],
}
export default {
  data() {
    return {
      uploadFilesList: [], //导入列表
      importDialogVisible:false,//导入
      isBalance: 1,
      tipInfo: '',
      tipData: [],
      tipDialogShow: false,
      formAccount: {},
      accountSetsId: '',
      accountSets:{},
      accountSetsList:[],//当前食堂所有的帐套
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        addBtn: false,
        menu:true,
        viewBtn: false,
        editBtn: false,
        delBtn: false,
        refreshBtn: false,
        searchBtn: false,
        columnBtn: false,
        addRowBtn: false,
        cellBtn: false,
        cancelBtn: false,
        selection: true,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "科目编码",
            prop: "subjectCode",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            labelWidth: 150,
            maxlength:30,
            showWordLimit:true,
            align: "left",
            slot: true,
            rules: [{
              required: true,
              message: "科目编码",
              trigger: "blur"
            }],
          },
          {
            label: "科目名称",
            prop: "subjectName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            maxlength:30,
            showWordLimit:true,
            rules: [{
              required: true,
              message: "科目名称",
              trigger: "blur"
            }],
            labelWidth: 150,
          },
          {
            label: "资产类别",
            prop: "subjectTypeId",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true,
            labelWidth: 150,
            dicUrl: "/api/service/rabbit-supplier/finance/financialSubjectType/subjectType",
            props: {
              label: "typeName",
              value:"id"
            },
            rules: [{
              required: true,
              message: "资产类别",
              trigger: "blur"
            }],
          },
          {
            label: "余额方向",
            prop: "balanceDirection",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.balanceDirection,
            rules: [{
              required: true,
              message: "余额方向",
              trigger: "blur"
            }],
          },
          {
            label: "类型",
            prop: "systemDefault",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.systemDefault,
            rules: [{
              required: true,
              message: "类型",
              trigger: "blur"
            }],
          },
          {
            label: "是否允许选择使用",
            prop: "isSelect",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.isSelect,
            rules: [{
              required: true,
              message: "是否允许选择使用",
              trigger: "blur"
            }],
          },
          {
            label: "关联供应商",
            prop: "supplierId",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
            search: false,
            dicUrl: "/api/service/rabbit-signUp/supplierSignUp/schoolSupplier",
            props: {
              label: "name",
              value:"id"
            },
            rules: [{
              required: true,
              message: "关联供应商",
              trigger: "blur"
            }],
            formatter:(val)=>{
              if(val.supplierId==0) {
                return '';
              }
              return val.supplierId;
            },
          },
          {
            label: "期初金额",
            prop: "beginningAmount",
            type: "input",
            cell: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
            formatter:(val)=>{
              if(val.beginningAmount==0) {
                return '';
              }
              return amtFilters(val.beginningAmount);
            },
          },
          {
            label: "借方累计",
            prop: "debitAmount",
            type: "input",
            cell: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
            formatter:(val)=>{
              if(val.debitAmount==0) {
                return '';
              }
              return amtFilters(val.debitAmount);
            },
          },
          {
            label: "贷方累计",
            prop: "lenderAmount",
            type: "input",
            cell: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
            formatter:(val)=>{
              if(val.lenderAmount==0) {
                return '';
              }
              return amtFilters(val.lenderAmount);
            },
          },
          {
            label: "年初余额",
            prop: "yearBginningAmount",
            type: "input",
            cell: false,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 150,
            formatter:(val)=>{
              if(val.yearBginningAmount==0) {
                return '';
              }
              return amtFilters(val.yearBginningAmount);
            },
          },
        ]
      },
      data: [],
      option1: {
        labelWidth: 250,
        card: true,
        /*submitBtn: false,
        emptyBtn: false,*/
        group: [{
          label: '下载财务初始余额表',
          prop: 'group1',
          column: [{
            prop: 'attachmentsd',
            hide: true,
            formslot: true
          },
            {
              prop: 'attachments',
              span: 24,
              hide: true,
              formslot: true
            },
          ]
        },
          {
            label: '上传财务初始余额表',
            prop: 'group2',
            column: [{
              prop: "excleXls",
              hide: true,
              formslot: true
            },
              {
                label: '上传',
                prop: 'fileList',
                type: 'upload',
                loadText: '附件上传中，请稍等',
                span: 24,
                dataType: 'array',
                hide:true,
                limit:1,
                propsHttp: {
                  res: 'data',
                  url:'originalName'
                },
                rules: [{
                  required: true,
                  message: "请上传文件在提交",
                  trigger: "blur"
                }],
                action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              },
            ]
          },
        ]
      },
      downForm:{},
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    },
    dataCompute() {
      var list = [];
      if(this.data == undefined || this.data.length == 0) {
        return list;
      }
      for(var i=0;i<this.data.length;i++) {
        var item = this.data[i];
        var obj = {};
        obj.layer = item.layer;
        obj.isLastLayer = item.isLastLayer;
        obj.parentSubjectId = item.parentSubjectId;
        obj.accountSetsId = item.accountSetsId;
        obj.subjectId = item.subjectId;
        obj.subjectCode = item.subjectCode;
        obj.subjectName = item.subjectName;
        obj.subjectTypeId = item.subjectTypeId;
        obj.subjectTypeName = item.subjectTypeName;
        obj.balanceDirection = item.balanceDirection;
        obj.systemDefault = item.systemDefault;
        obj.isSelect = item.isSelect;
        obj.supplierId = item.supplierId;
        obj.beginningAmount = item.beginningAmount;
        obj.debitAmount = item.debitAmount;
        obj.lenderAmount = item.lenderAmount;
        obj.yearBginningAmount = item.yearBginningAmount;
        obj.billId = item.billId;
        list.push(obj);
      }
      return list;
    },
  },
  created(){
    this.loading = true;
    const self = this;
    //获取食堂帐套
    getAccountSetsList().then(res => {
        self.accountSetsList = res.data.data.records;
        if(self.accountSetsList!=null && self.accountSetsList.length == 1) {
          self.accountSets = self.accountSetsList[0];
          self.accountSetsId = self.accountSetsList[0].id;
          //获取数据列表
        }else {
          self.$message.warning("请先创建食堂账套，有了会计科目之后，才能设置期初金额");
        }
        self.loading = false;
      });

  },
  mounted() {

  },
  methods: {
    handleClose(done) {
      this.tipDialogShow = false;
    },
    trialBalanceHandler(){
      trialBalance(this.accountSetsId).then(res => {
          if(res.data.code=='200') {
            let data = JSON.parse(res.data.data);
            if(data.isBeginAmountBalance=='false'||
              data.isYearAmountBalance=='false'||
              data.isYearBeginBalance=='false') {
              this.isBalance = 1;
              this.tipInfo = "财务初始余额试算不平衡，请核对录入的财务初始余额是否正确！";
            }else {
              this.isBalance = 0;
              this.tipInfo = "恭喜您，您录入的初始余额平衡！";
            }
            this.tipData = [{subjectName:"期初余额(综合本位币)",debitAmount:amtFilters(data.beginAmount_1) ,lenderAmount:amtFilters(data.beginAmount_2) ,difAmount:amtFiltersEx(data.beginAmount_1-data.beginAmount_2,0)},
              {subjectName:"累计发生额(综合本位币)",debitAmount:amtFilters(data.debitAmount) ,lenderAmount:amtFilters(data.lenderAmount) ,difAmount:amtFiltersEx(data.debitAmount-data.lenderAmount,0)}]
            this.tipDialogShow = true;
          }else{
            this.$notify({
              title: '失败',
              message: '检查失败,无法连接服务器',
              type: 'warning',
              duration: 2000
            });
          }
        });
    },
    changeAccountSet(row) {
      this.accountSets = row;
    },
    getDistplayStr(layer,subjectCode) {
      return subjectCode;
    },
    rowCell(row, index) {
      if(this.accountSets.isUse==1) {
        this.$message.error("帐套已经启用，不能修改");
        return;
      }
      this.$refs.crudEx.rowCell(row, index)
    },
    rowSave(row, loading, done) {
      if(this.accountSets.isUse==1) {
        this.$message.error("帐套已经启用，不能修改");
        return;
      }
      add(row).then(() => {
        loading();
        this.onLoad(this.page,this.query);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowCancelEdit(row, index) {
      // this.$refs.crudEx.rowCancel(row, index);
      this.onLoad(this.page,this.query);
    },
    rowUpdate(row, index, loading, done) {
      if(this.accountSets.isUse==1) {
        this.$message.error("帐套已经启用，不能修改");
        loading();
        done();
        this.onLoad(this.page,this.query);
        return;
      }
      else if(!IsNumber(row.beginningAmount)) {//检查数字是否为合法数字
        this.$message.error("【期初金额】输入不正确,请输入数字");
        done();
        return;
      }else if(!IsNumber(row.debitAmount)) {//检查数字是否为合法数字
        this.$message.error("【借方累计】金额输入不正确,请输入数字");
        done();
        return;
      }else if(!IsNumber(row.lenderAmount)) {//检查数字是否为合法数字
        this.$message.error("【贷方累计】金额输入不正确,请输入数字");
        done();
        return;
      }

      update(row).then(() => {
        loading();
        this.onLoad(this.page,this.query);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        done();
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
    },
    handleDelete() {
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page,this.query);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crudEx.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.accountSetsId = this.accountSetsId;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.$nextTick(()=> {
          this.data = data.records;
        });
        this.loading = false;
        this.selectionClear();
      });
    },

    resetAccount(row){
      if(this.accountSets.isUse==1) {
        this.$message.error("帐套已经启用，不能重置");
        return;
      }
      this.checkVisible = true;
      getDetail(row.id).then(res => {
        let data = res.data.data;
        this.$confirm("您确定要重置科目【"+row.subjectName+"】期初金额么？", {
          confirmButtonText: "我确认重置",
          cancelButtonText: "我再检查一下",
          type: "warning"
        })
          .then(() => {
            return resetAccount(row);
          })
          .then(() => {
            this.onLoad(this.page,this.query);
            this.$message({
              type: "success",
              message: "重置成功!"
            });
          });
      });
    },
    exportRecord(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportBeginningBalanceRecord(this.query).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '财务初始余额表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    importData(){
      if(this.accountSets.isUse==1) {
        this.$message.error("帐套已经启用，不能修改");
        return;
      }
      this.importDialogVisible = true;
    },
    closeDow(){
      this.downForm.fileList = "";
      this.uploadFilesList = [];
      this.importDialogVisible = false;
    },
    uploadFile(row,loading){
      if(this.accountSets.isUse==1) {
        this.$message.error("帐套已经启用，不能修改");
        return;
      }
      console.log(">>>>>>>>>>>>>>",JSON.stringify(this.uploadFilesList[0].value))
      const loading1 = this.$loading({
        lock: true,
        text: '正在导入表格数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      uploadFileBeginningBalance(this.uploadFilesList[0].value).then(res => {
        if (res.data.data === 'error'){
          loading1.close()
          loading();
          this.downForm.fileList = "";
          this.uploadFilesList = [];
          this.$alert(res.data.message, '信息', {
            confirmButtonText: '确定'
          })
        }else {
          loading1.close()
          loading();
          this.downForm.fileList = "";
          this.uploadFilesList = [];
          this.$message({
            type: "success",
            message: "导入成功"
          });
          this.page.currentPage = 1;
          this.onLoad(this.page);
          this.importDialogVisible = false;
        }
      });
    },
    handleExcel(){
      downBeinningBalanceTemplate().then(res => {
        const blob = new Blob([res.data]);
        const fileName = '财务初始余额表.xlsx';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    uploadBefore(file, done, loading,column) {
      /*        console.log(">>>>>>>>>上传前的方法",file)*/
      var str = file.name;
      str = str.substring(str.lastIndexOf("\.") + 1, str.length);

      var reStr = selectType(str);
      if (reStr == "NO") {
        loading();
        this.$message.error('文件格式错误,只能上传"[xls,xlsx]"格式');
        return false;
      } else {
        /*    var newFile = new File([file], '1234', { type: file.type });*/
        this.fileName = file.name
        done()
        return true;
      }
    },
    uploadError(error, column) {
    },
    uploadAfter(res, done, loading,column) {
      console.log("文件路径",JSON.stringify(res))
      let proof = {
        label: this.fileName,
        value: res.link
      }
      this.uploadFilesList.push(proof);
      done()
    },
    uploadPreview(file,column,done){
      done()//默认执行打开方法
    },
    uploadDelete(column,file) {
      this.uploadFilesList.splice(file.uid,1);
    },



  }
};
</script>
