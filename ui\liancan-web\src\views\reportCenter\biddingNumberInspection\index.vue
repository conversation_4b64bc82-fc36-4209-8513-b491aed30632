<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportData">导出</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, exportData} from "@/api/reportCenter/biddingNumberInspection";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        searchFrom:{},
        option: {
          //height:'auto',
          showSummary: true,
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          menu: false,
          addBtn: false,
          delBtn: false,
          selection: false,
          column: [
            {
              label: '省份',
              prop: 'province',
              type: 'select',
              props: {
                label: 'regionName',
                value: 'id'
              },
              cascaderItem: ['city', 'area'],
              search:true,
              hide: true,
              viewDisplay: false,
              dicUrl: `/api/rabbit-system/region/getProvince`,
              rules: [
                {
                  required: true,
                  message: '请选择省份',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '城市',
              prop: 'city',
              type: 'select',
              props: {
                label: 'regionName',
                value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
              rules: [
                {
                  required: true,
                  message: '请选择城市',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: '地区',
              prop: 'area',
              type: 'select',
              props: {
                label: 'regionName',
                value: 'id'
              },
              search:true,
              hide: true,
              viewDisplay: false,
              dicFlag: false,
              dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
              rules: [
                {
                  required: true,
                  message: '请选择地区',
                  trigger: 'blur'
                }
              ]
            },
            {
              label: "单位名称",
              prop: "name",
              type: "input",
            },
            {
              label: "单位类型",
              prop: "type",
              type: "select",
              dicUrl: '/api/service/rabbit-system/dict/dictionary?code=agency_type',
              props: {
                label: 'dictValue',
                value: 'dictKey'
              },
            },
            {
              label: "食堂名称",
              prop: "canteenName",
              type: "input",
            },
            {
              label: "招标类别",
              prop: "purchaseTypeName",
              type: "input",
            },
            {
              label: "招标类型",
              prop: "purchaseType",
              type: "select",
              search: true,
              dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict",
              props: {
                label: "name",
                value: "id"
              },
              hide: true
            },
            {
              label: "未开始",
              prop: "notStartCount"
            },
            {
              label: "报名中",
              prop: "signingCount"
            },
            {
              label: "评标中",
              prop: "evaluatingCount"
            },
            {
              label: "已结束",
              prop: "finishCount"
            },
            {
              label: "数量合计",
              prop: "amount"
            },
          ],
          sumColumnList: [
            {
              name: 'notStartCount',
              type: 'sum',
              //decimals:1
            },
            {
              name: 'signingCount',
              type: 'sum',
            },
            {
              name: 'evaluatingCount',
              type: 'sum',
            },
            {
              name: 'finishCount',
              type: 'sum',
            },
            {
              name: 'amount',
              type: 'sum',
            },
          ],
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.schoolAuthInfo_add, false),
          viewBtn: this.vaildData(this.permission.schoolAuthInfo_view, false),
          delBtn: this.vaildData(this.permission.schoolAuthInfo_delete, false),
          editBtn: this.vaildData(this.permission.schoolAuthInfo_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.searchFrom = params;
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      exportData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportData(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '招标数量统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
