<template>
    <basicContainer>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
<!--                <el-button type="primary"  icon="el-icon-download" size="small" @click="printOrderHandler" >导出</el-button>-->
<!--                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>-->
            </template>
        </avue-crud>
    </basicContainer>
</template>
<script>
import {supplierSaleOrderTrack} from "@/api/liancan/supplierGoods";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";
import {getOrderList} from "@/api/liancan/order";
var DIC = {
    orderStatus: [{
        label: '未接单',
        value: "0"
    },{
        label: '食堂已收',
        value: "1"
    },{
        label: '取消/拒单',
        value: "2"
    },{
        label: '已送达',
        value: "3"
    },{
        label: '配送中',
        value: "4"
    }],
    goodStatus: [
        {
            value: '0',
            label: "仓库拣货",
        },
        {
            value: '1',
            label: "已出库",
        },
        {
            value: '2',
            label: "配送中",
        },
        {
            value: '3',
            label: "已送达",
        },
        {
            value: '4',
            label: "客户已收",
        },
        {
            value: '5',
            label: "待下单",
        },
        {
            value: '6',
            label: "订单待审核",
        },
        {
            value: '7',
            label: "订单未通过",
        },
        {
            value: '8',
            label: "待供应商接单",
        },
        {
            value: '9',
            label: "供应商拒单",
        },
        {
            value: '10',
            label: "订单取消",
        },
        {
            value: '11',
            label: "供应商已接单",
        },
        {
            value: '12',
            label: "供应商已出库",
        },
        {
            value: '13',
            label: "供应商配送中",
        },
        {
            value: '14',
            label: "供应商已送达",
        },
        {
            value: '15',
            label: "客户已收",
        },
        {
            value: '16',
            label: "供应商已退货",
        },
    ]
}
export default {
    data() {
        return {
            form: {},
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                menu: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "收单时间",
                        prop: "orderTime",
                        type: "date",
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        width: 150,
                    },
                    {
                        label: "开始日期",
                        prop: "startDate",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                    {
                        label: "结束日期",
                        prop: "endDate",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                    },
                    {
                        label: "订单号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "下单客户",
                        prop: "deptName",
                        type: "input",
                        width: 150,
                    },
                    //订单进展
                    {
                        label: '订单进展',
                        prop: 'orderStatus',
                        type: 'select',
                        search: true,
                        dicData: [
                            {
                                label: '未接单',
                                value: '0'
                            },
                            {
                                label: '食堂已收',
                                value: '1'
                            },
                            {
                                label: '取消/拒单',
                                value: '2'
                            },
                            {
                                label: '已送达',
                                value: '3'
                            },
                            {
                                label: '配送中',
                                value: '4'
                            },
                            {
                                label: '已出库',
                                value: '5'
                            },
                            {
                                label: '接单备货',
                                value: '6'
                            },
                            {
                                label: '已退货',
                                value: '7'
                            },
                            {
                                label: '收货结束',
                                value: '8'
                            }
                        ],
                        rules: [{
                            required: false,
                            message: '请选择订单进展',
                            trigger: 'blur'
                        }],
                        width: 100
                    },
                    {
                        label: "指定收货方",
                        prop: "unitName",
                        type: "input",
                        width: 150,
                    },
                    {
                        label: "收货方食堂",
                        prop: "deptId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                        props: {
                            label: "deptName",
                            value:"id"
                        },
                        width: 150,
                    },
                    {
                        label: "收货方人员",
                        prop: "contactUserName",
                        type: "input",
                        width: 150,
                    },
                    {
                        label: "收货方电话",
                        prop: "contactPhone",
                        type: "input",
                        width: 150,
                    },
                    {
                        label: "送货日期",
                        prop: "deliveryTime",
                        type: "datetime",
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd",
                    },
                    {
                        label: "送货地址",
                        prop: "deliveryAddress",
                        type: "input",
                        width: 250,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                        search: false,
                        width: 150,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        width: 150,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "下单数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "下单金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                    {
                        label: "商品备货方式",
                        prop: "supplyMethod",
                        type: "select",
                        dicData: [
                        {
                            label: "自营仓库出货配送",
                            value: 0
                        },
                        {
                            label: "自营仓库出货配送",
                            value: 1
                        },
                        {
                            label: "转供应商直发客户",
                            value: 2
                        }
                        ],
                        rules: [{
                        required: true,
                        message: "请选择备货方式",
                        trigger: "blur"
                        }],
                        width: 150,
                    },
                    //订单状态
                    {
                        label: '商品状态',
                        prop: 'goodStatus',
                        type: 'select',
                        search: true,
                        dicData: DIC.goodStatus,
                        width: 120,
                    },
                    {
                        label: "关联单据",
                        prop: "currentRelatedBillTypeName",
                        type: "select",
                        search: true,
                        dicData: DIC.sourceBillTypes,
                        width: 150

                    },
                    {
                        label: "关联单号",
                        prop: "currentRelatedBillCode",
                        type: "input",
                        search: true,
                        width: 150
                    },
                    {
                        label: "单据日期",
                        prop: "sourceBillDate",
                        type: "date",
                        search: true,
                        format: "yyyy-MM-dd",
                        valueFormat: "yyyy-MM-dd"
                    },
                    {
                        label: "供应商",
                        prop: "sourceSupplierName",
                        type: "input",
                        search: true,
                        width: 150
                    },
                    {
                        label: "登记人",
                        prop: "sourceCreateBy",
                        type: "input"
                    },
                    {
                        label: "登记时间",
                        prop: "sourceCreateTime",
                        type: "datetime",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        width: 150,
                    },
                    {
                        label: "异常提醒",
                        prop: "abnormalRemind",
                        type: "input"
                    },
                    {
                        label: "异常处理建议",
                        prop: "abnormalSuggestion",
                        type: "input",
                        width: 150,
                    },
                    {
                        label: "实售数量",
                        prop: "actualQty",
                        type: "input",
                        dataType: "number",
                        formatter:(val)=>{
                            if(val.actualQty===0) {
                                return '';
                            }
                            return amtFilters(val.actualQty);
                        }
                    },
                    {
                        label: "实售金额",
                        prop: "actualAmt",
                        type: "input",
                        dataType: "number",
                        formatter:(val)=>{
                            if(val.actualAmt===0) {
                                return '';
                            }
                            return amtFilters(val.actualAmt);
                        }
                    }
                ]
            },
            data: [],

            //新增退货单弹窗参数 start
            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },
        };
    },
    computed: {
    },
    mounted(){
        this.getPrevious()
    },
    watch: {
    },
    created(){
    },
    components: {
    },
    methods: {
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage){
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize){
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            supplierSaleOrderTrack(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    }
};
</script>

<style>
</style>
