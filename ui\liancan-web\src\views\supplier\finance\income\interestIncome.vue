<template>
  <basic-container>
    <div class="table-form">
      <avue-crud :option="option"
                :table-loading="loading"
                :data="data"
                :page="page"
                :permission="permissionList"
                :before-open="beforeOpen"
                v-model="form"
                ref="crud"
                @row-update="rowUpdate"
                @row-save="rowSave"
                @row-del="rowDel"
                @search-change="searchChange"
                @search-reset="searchReset"
                @selection-change="selectionChange"
                @current-change="currentChange"
                @size-change="sizeChange"
                @on-load="onLoad">
        <template slot="menu" slot-scope="scope">
        <el-button v-if="scope.row.status === '1'" size="mini" type="text" @click="toVoid(scope.row)">
          <i class="el-icon-delete"></i>
          <span>作废</span>
        </el-button>
          <el-button type="text"
                    icon="el-icon-setting"
                    size="small"
                    plain
                    style="border: 0;background-color: transparent !important;"
                    v-if="scope.row.status == '1' && scope.row.ifVoucher == 0"
                    @click.stop="createVoucher(scope.row)">生成凭证
          </el-button>
        </template>
      </avue-crud>
    </div>
  </basic-container>
</template>

<script>
import {dateFormat} from "@/util/date";
import {getList, getDetail, add, update, remove, getSupplier, getPayee} from "@/api/supplier/finance/income/foodIncome";
import {createIncomeVoucher} from "@/api/supplier/finance/financialVoucher";
import {reversalHandle} from "@/api/supplier/finance/voucher";
import {mapGetters} from "vuex";
var DIC = {
  status: [{
    label: '已作废',
    value: "0"
  }, {
    label: '正常',
    value: "1"
  }],
  payType: [{
    label: '食堂残值收入(杂项收入)',
    value: 3
  }, {
    label: '其他',
    value: 4
  }],
}
export default {
  data() {
    return {
      form: {},
      orderForm: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      selectionLists: [],
      orderData: [],
      orderVisible: false,
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: true,
        editBtn: false,
        selection: false,
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: true,
            searchSpan: 3,
          },
          {
            label: "所属食堂",
            prop: "deptName",
            type: "input",
            search: false,
            addDisplay: true,
            editDisplay: false,
            viewDisplay: false,
            disabled: true,
            hide: true
          },
          {
            label: "单号",
            prop: "payNumber",
            type: "input",
            rules: [{
              required: true,
              message: "请输入单号",
              trigger: "blur"
            }],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: false,
            overHidden:true,
            hide: true
          },
          // {
          //   label: "分类",
          //   prop: "payType",
          //   type: "select",
          //   search: false,
          //   rules: [{
          //     required: true,
          //     message: "请选择",
          //     trigger: "blur"
          //   }],
          //   dicData: DIC.payType,
          // },
          // {
          //   label: "入帐账户",
          //   prop: "accountNo",
          //   type: "select",
          //   dicUrl: "/api/service/rabbit-financial/income/getBankAccountOnlyTo?purpose=1",
          //   props: {
          //     required: true,
          //     label: "accountName",
          //     value:"accountNo"
          //   },
          //   rules: [{
          //     required: true,
          //     message: "请选择入帐账户",
          //     trigger: "blur"
          //   }],
          //   addDisplay: true,
          //   editDisplay: false,
          //   viewDisplay: true,
          //   search: true,
          //   overHidden:true
          // },
          {
            label: "入帐账户",
            prop: 'accountNo',
            type: "tree",
            search: true,
            viewDisplay: true,
            addDisplay: true,
            editDisplay: false,
            filters: true,
            overHidden:true,
            dicUrl: '/api/service/rabbit-supplier/finance/financialVoucher/tree/scope/finance',
            props: {
              label: "subjectName",
              value: "id"
            },
            rules: [{
              required: true,
              message: "科目",
              trigger: "blur"
            }],
            searchSpan: 3,
          },
          {
            label: "入帐金额(元)",
            prop: "amount",
            type: "input",
            rules: [{
              required: true,
              message: "请输入入帐金额",
              trigger: "blur"
            }],
            search: false,
          },
          {
            label: "登记人员",
            prop: "createName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
          },
          {
            label: "登记时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            value: dateFormat(new Date(), "yyyy-MM-dd HH:mm:ss"),
            rules: [{
              required: true,
              message: "请选择登记时间",
              trigger: "blur"
            }],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            overHidden: true
          },
          {
            label: "登记时间",
            prop: "createTimeRange",
            type: "daterange",
            startPlaceholder: '开始日期',
            endPlaceholder: '结束日期',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd HH:mm:ss',
            search: true,
            searchRange: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            searchSpan: 5,
          },
          {
            label: "支付方",
            prop: "payAccountNo",
            type: "input",
            search: true,
            rules: [{
              required: true,
              message: "请输入支付方",
              trigger: "blur"
            }],
            overHidden: true,
            maxlength: 200,
            showWordLimit: true,
            searchSpan: 3,
          },
          {
            label: "备注",
            prop: "remarks",
            type: "textarea",
            search: false,
            overHidden: true,
            maxlength: 200,
            showWordLimit: true,
          },
          {
            label: "状态",
            prop: "status",
            type: "select",
            search: true,
            rules: [{
              required: true,
              message: "请选择",
              trigger: "blur"
            }],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            dicData: DIC.status,
            searchSpan: 3,
          },
          {
            label: "发票",
            prop: "invoiceImg",
            type: 'upload',
            listType: 'picture-img',
            propsHttp: {
              res: 'data',
              url: 'link'
            },
            tip: '只能上传jpg/png文件，大小不超过 500KB',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file',
            rules: [{
              required: false,
              message: '请上传图片',
              trigger: 'click'
            }],
            slot: true,
            span: 24,
            hide: true,
          },
          {
            label: "入账凭证",
            prop: "voucherImg",
            type: 'upload',
            listType: 'picture-img',
            propsHttp: {
              res: 'data',
              url: 'link'
            },
            tip: '只能上传jpg/png文件，大小不超过 500KB',
            action: '/api/service/rabbit-resource/oss/endpoint/put-file',
            rules: [{
              required: false,
              message: '请上传图片',
              trigger: 'click'
            }],
            slot: true,
            span: 24,
            hide: true,
          }
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.foodIncome_add, true),
        viewBtn: this.vaildData(this.permission.foodIncome_view, true),
        delBtn: this.vaildData(this.permission.foodIncome_delete, false),
        editBtn: this.vaildData(this.permission.foodIncome_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    rowSave(row, loading, done) {
      row.incomeType = 4;
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }else {
        getPayee().then(res => {
          this.form.deptName = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      if (params.createTimeRange != '' && params.createTimeRange != null && params.createTimeRange != undefined) {
        params.beginTime = params.createTimeRange[0];
        params.endTime = params.createTimeRange[1];
      }
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionChanges(list) {
      this.selectionLists = list;
    },
    selectPayer(){
      console.log(JSON.stringify("this.selectionLists:"+JSON.stringify(this.selectionLists)))
      this.form.payer = this.selectionLists[0].name;
      this.orderVisible = false;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.incomeType = 4
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    createVoucher(row) {
      this.$confirm("确定生成凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        // let param = {
        //   billNo: row.id
        // }
        return createIncomeVoucher(row.id);
      }).then(
        () => {
          this.loading = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        },
        error => {
          this.loading = false;
          this.$message.error(error);
        }
      );
    },
    toVoid(row) {
      console.log('toVoid ===== ', row)
      this.$confirm("作废后将不计入收入统计，且作废后将不能恢复，是否确定作废?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          console.log('确定')
          // 冲销凭证
          reversalHandle(row.voucherIds, 0).then(res => {
            console.log('reversalHandle ===== ', res)
            if (res.data.code == 200) {
              // 删除记录
              remove(row.id).then(res => {
                console.log('remove ===== ', res)
                if (res.data.code == 200) {
                  this.onLoad(this.page);
                  this.$message({
                    type: "success",
                    message: "操作成功!"
                  });
                } else {
                  this.$message.error(res.data.msg);
                }
              });
            } else {
              this.$message.error(res.data.msg);
            }
          });
        })
        .catch(() => {
          console.log('取消')
        });
    }
  }
};
</script>

<style  lang="less" scoped>
  /deep/ .table-form .el-form-item__label{
      display: none;
  }
  /deep/ .table-form .el-form-item__content{
    margin-left: 0 !important;
  }
</style>
