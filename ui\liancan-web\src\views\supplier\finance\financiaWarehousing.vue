<template>
  <basic-container>
    <div class="all-mess">
      <div class="mess-header">
        <div :class="{acitve:activeIdx==index}" v-for="(item,index) in messList" :key="index" @click="menuClick(index)">
          {{item}}
        </div>
      </div>
<!--      <div class="mess-content" v-if="activeIdx == 0">-->
<!--        <avue-crud :option="option"-->
<!--                   :table-loading="loading"-->
<!--                   :data="data"-->
<!--                   :page="page"-->
<!--                   :permission="permissionList"-->
<!--                   :before-open="beforeOpen"-->
<!--                   v-model="form"-->
<!--                   ref="crud"-->
<!--                   @search-change="searchChange"-->
<!--                   @search-reset="searchReset"-->
<!--                   @selection-change="selectionChange"-->
<!--                   @current-change="currentChange"-->
<!--                   @size-change="sizeChange"-->
<!--                   @on-load="onLoad">-->
<!--          <template slot="menu" slot-scope="scope">-->
<!--            <el-button-->
<!--                type="text"-->
<!--                size="small"-->
<!--                icon="el-icon-view"-->
<!--                @click="opentView(scope.row)">查看-->
<!--            </el-button>-->
<!--          </template>-->
<!--          <template slot="sendTime" slot-scope="scope">-->
<!--            <span>{{scope.row.sendTime}}之前</span>-->
<!--          </template>-->
<!--        </avue-crud>-->
<!--      </div>-->

      <div class="mess-content" v-if="activeIdx == 0">
        <avue-crud :option="goodsOption"
                   :table-loading="loading"
                   :data="goodsData"
                   :page="goodsPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="goodsForm"
                   ref="goodsForm"
                   @search-change="searchChangeGoods"
                   @search-reset="searchResetGoods"
                   @current-change="currentChangeGoods"
                   @size-change="sizeChangeGoods"
                   @on-load="onGoodsLoad">
          <!-- <template slot="menu" slot-scope="scope">
            <el-button
              type="text"
              size="small"
              icon="el-icon-setting"
              v-if="scope.row.ifVoucherIn == 0"
              @click="createVoucher(scope.row)">生成凭证
            </el-button>
          </template> -->
        </avue-crud>
      </div>

    </div>

    <el-dialog title="验收详情" :visible.sync="isShow" :append-to-body="true" @close="closeForm" width="60%">
      <div>
        <avue-form ref="orderForm" :option="orderOption" v-model="orderForm">
        </avue-form>
      </div>
      <div >
        <span class="fontsize">订单商品信息</span>
        <avue-crud ref="crud" v-model="orderDetailForm" :option="orderDetailOption" :data="orderDetailData" @on-load="orderDetailOnLoad" :page="orderDetailPage"
                   :table-loading="orderDetailLoading">
        </avue-crud>
      </div>
      <div>
        <span class="fontsize">进仓确认历史</span>
        <avue-crud ref="crud" v-model="orderDetailForm2" :option="orderDetailOption2" :data="orderDetailData2" @on-load="orderDetailOnLoad2" :page="orderDetailPage2"
                   :table-loading="orderDetailLoading2">
        </avue-crud>
      </div>
    </el-dialog>

  </basic-container>
</template>

<script>
import {getAccepterComfirmPage,orderDetailList,getAccepterLog,getAccepterLogging} from "@/api/liancan/order";
import {createWarehousingVoucher} from "@/api/supplier/finance/financialVoucher";
import {mapGetters} from "vuex";
var DIC = {
  isBuy: [{
    label: '未确认',
    value: "0"
  },{
    label: '确认通过',
    value: "1"
  },{
    label: '确认拒绝',
    value: "2"
  }],
  payStatus: [{
    label: '未付款',
    value: "0"
  },{
    label: '已付款',
    value: "1"
  }],
  orderStatus:[{
    label: '未接单',
    value: "0"
  },{
    label: '食堂已收',
    value: "1"
  },{
    label: '取消/拒单',
    value: "2"
  },{
    label: '已送达',
    value: "3"
  },{
    label: '配送中',
    value: "4"
  }],
  accepterStatus: [{
    label: '未确认',
    value: "1"
  },{
    label: '验收通过',
    value: "2"
  },{
    label: '验收不通过',
    value: "3"
  },
    {
      label: ' ',
      value: "0"
    },],
}
export default {
  data() {
    return {
      activeIdx:0,
      messList: [ '按商品查看' ],
      form: {},
      goodsForm:{},
      query: {},
      goodsQuery:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      goodsPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      isShow: false,
      orderForm: {},
      orderDetailForm: {},
      orderDetailForm2: {},
      orderDetailLoading: true,
      orderDetailLoading2: true,
      orderDetailData: [],
      orderDetailData2: [],
      orderDetailPage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      orderDetailPage2: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      resOrderId: null,
      confirmBat: false,
      orderOption:{
        emptyBtn: false,
        submitBtn: false,
        column: [
          {
            label: "订单号",
            prop: "id",
            type: "input",
            width: 150,
            disabled: true,
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
            props: {
              label: "name",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "采购方",
            prop: "deptId",
            type: "select",
            dicUrl: '/api/service/rabbit-system/dept/dict',
            props: {
              label: "deptName",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "采购人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "发票",
            prop: "invoiceId",
            hide: true,
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
            props: {
              label: "num",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "采购确认人",
            prop: "verifyUserId",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "确认状态",
            prop: "isBuy",
            type: "select",
            dicData: DIC.isBuy,
            disabled: true,
          },
          {
            label: "商品种类数",
            prop: "totalQuantity",
            type: "input",
            slot: true,
            disabled: true,
          },
          {
            label: "总价",
            prop: "totalPrices",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            display: false,
          },
          {
            label: "配送时间",
            prop: "sendTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            slot: true,
            disabled: true,
          },
          {
            label: "订单状态",
            prop: "orderStatus",
            type: "select",
            dicData: DIC.orderStatus,
            disabled: true,
          },
          {
            label: "送货地址",
            prop: "site",
            type: "input",
            disabled: true,
          },
          {
            label: "电话",
            prop: "phone",
            type: "input",
            hide: true,
            disabled: true,
          },
          {
            label: "联系人",
            prop: "userName",
            type: "input",
            hide: true,
            disabled: true,
          },
        ],
      },
      orderDetailOption:{
        align: "center",
        height: 250,
        calcHeight: 30,
        searchShow: false,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        selection: false,
        menu: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        column: [{
          label: 'id',
          prop: 'id',
          type: 'input',
          hide: true,
          display: false,
          showColumn: false,
        },
          {
            label: "商品",
            prop: "goodsId",
            type: "select",
            dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
            props: {
              label: "name",
              value: "id"
            },
          },
          {
            label: "价格",
            prop: "price",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            //minRows: 0,
          },
          {
            label: "计量单位",
            prop: "unit",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
          },
          {
            label: "采购数量",
            prop: "quantity",
            type: "number",
          },
          {
            label: "验收数量",
            prop: "accepterQuantity",
            type: "number",
          },
          {
            label: "小计",
            prop: "subtotal",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            //minRows: 0,
          },
        ],
      },
      orderDetailOption2:{
        align: "center",
        height: 200,
        calcHeight: 30,
        searchShow: false,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        selection: false,
        menu: false,
        viewBtn: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        column: [{
          label: 'id',
          prop: 'id',
          type: 'input',
          hide: true,
          display: false,
          showColumn: false,
        },
          {
            label: "进仓确认人",
            prop: "name",
            type: "input",
            width: 150,
          },
          {
            label: "进仓确认时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 170,
          },
          {
            label: "进仓确认结果",
            prop: "accepterStatus",
            type: "select",
            dicData: [
              {
                label: "验收通过",
                value: '0'
              },
              {
                label: "验收不通过",
                value: '1'
              },
            ],
            width: 120,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
          },
        ],
      },
      option: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: "采购单号",
            prop: "id",
            type: "input",
            display: false,
          },
          {
            label: "所属食堂",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            hide: true,
            addDisplay: false,
            search:true,
          },
          {
            label: "所属食堂",
            prop: "deptName",
            type: "input",
          },
          {
            label: "供应商",
            prop: "supplierName",
            type: "input",
            overHidden: true,
            width: 190,
          },
          {
            label: "采购人",
            prop: "userName",
            type: "input",
          },
          {
            label: "下单时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 160,
          },
          {
            label: "验收进仓人",
            prop: "accepterUserName",
            type: "input",
          },
          {
            label: "验收进仓时间",
            prop: "accepterTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            width: 160,
          },
          {
            label: "验收总价",
            prop: "totalPrices",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
          },
          {
            label: "进仓确认人",
            prop: "accepterUserNameb",
            type: "input",
          },
          {
            label: "确认状态",
            prop: "accepterStatus",
            type: "select",
            dicData: DIC.accepterStatus,
          },
          {
            label: "支付状态",
            prop: "payStatus",
            type: "select",
            dicData: DIC.payStatus,
            search: true,
          },
          {
            label: "采购人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            search: true,
            showColumn: false,
            hide: true,
            searchFilterable: true,
          },
          {
            label: "采购开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            searchLabelWidth: 100,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "采购结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            searchLabelWidth: 100,
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan2/purchaseExpenditure/schoolSupplier',
            props: {
              label: "name",
              value: "id"
            },
            search: true,
            showColumn: false,
            hide: true,
            searchFilterable: true,
          },
          {
            label: "确认状态",
            prop: "status",
            type: "select",
            dicData: [{
              label: '验收通过',
              value: "0"
            },{
              label: '未确认',
              value: "1"
            }],
            search: true,
            showColumn: false,
            hide: true,
          },
          {
            label: "验收进仓开始时间",
            prop: "checkoutStartDate",
            type: "datetime",
            hide: true,
            search: true,
            searchLabelWidth: 140,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "验收进仓结束时间",
            prop: "checkoutEndDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            searchLabelWidth: 140,
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
        ]
      },
      //index 2
      goodsOption: {
        align: "center",
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: true,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        column: [
          {
            label: "采购单号",
            prop: "id",
            type: "input",
            search:true,
          },
          {
            label: "所属食堂",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            hide: true,
            addDisplay: false,
            search:true,
          },
          {
            label: "所属食堂",
            prop: "deptName",
            viewDisplay:false,
            type: "input",
          },
          {
            label: "供应商",
            prop: "supplierId",
            type: "select",
            dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
            search:true,
            props: {
              label: "name",
              value: "id"
            },
            disabled: true,
          },
          {
            label: "商品名称",
            prop: "goodsName",
            search:true,
            type: "input",
          },
          {
            label: "单价",
            prop: "price",
            type: "input",
          },
          {
            label: "计量单位",
            prop: "unit",
            type: "input",
          },
          {
            label: "进仓数量",
            prop: "accepterQuantity",
            type: "input",
          },
          {
            label: "进仓金额",
            prop: "subtotal",
            type: "input",
          },
          {
            label: "进仓登记人",
            prop: "accepterUserName",
            type: "input",
          },
          {
            label: "进仓登记人",
            searchLabelWidth:110,
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            search: true,
            showColumn: false,
            hide: true,
            viewDisplay:false,
            searchFilterable: true,
          },
          {
            label: "验收进仓时间",
            width: 160,
            prop: "endTime",
            type: "input",
          },
          {
            label: "进仓确认人",
            prop: "accepterUserNameb",
            type: "input",
          },
          {
            label: "确认状态",
            prop: "accepterStatus",
            dicData:DIC.accepterStatus,
            search:true,
            type: "select",
          },
          {
            label:"确认备注",
            prop:"remark",
            hide:true,
          },
          {
            label: "登记开始时间",
            searchLabelWidth:110,
            prop: "startDate",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd 00:00:00",
            search: true,
            hide: true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
          {
            label: "登记结束时间",
            searchLabelWidth:110,
            prop: "endDate",
            type: "datetime",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd 23:59:59",
            search:true,
            hide:true,
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
        ]
      },
      data: [],
      goodsData:[]
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
    },
    ids() {
    }
  },
  created(){
    if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[2].hide = true;
      this.goodsOption.column[1].search = false;
      this.goodsOption.column[2].hide = true;
    }
  },
  methods: {
    menuClick(idx) {
      this.query=[];
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.query = {};
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
      if (idx == 1){
        this.query = {};
        this.goodsPage.currentPage = 1;
        this.onGoodsLoad(this.goodsPage);
      }
    },
    beforeOpen(done, type) {
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchResetGoods(){
      this.goodsQuery = {};
      this.onGoodsLoad(this.goodsPage);
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('采购结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('采购开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('采购开始时间不能大于结束时间');
          }
        }
      }
      this.query = params;
      this.page.currentPage = 1
      /*          if (params.timeRange != '' && params.timeRange != null && params.timeRange != undefined) {
                    params.startDate = params.timeRange[0];
                    params.endDate = params.timeRange[1];
                }*/
      params.timeRange = null;
      this.onLoad(this.page, params);
      done();
    },
    searchChangeGoods(params, done) {
      this.goodsQuery = params;
      this.goodsPage.currentPage = 1
      if(params.createUser != null && params.createUser != ''){
        params.accepterUserId = params.createUser;
      }
      this.onGoodsLoad(this.goodsPage, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    currentChangeGoods(currentPage){
      this.goodsPage.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    sizeChangeGoods(pageSize){
      this.goodsPage.pageSize = pageSize;
    },
    onGoodsLoad(page, params = {}) {
      this.loading = true;
      params.queryType = 1;
      getAccepterLogging(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.goodsPage.total = data.total;
        this.goodsData = data.records;
        this.loading = false;
        //this.selectionClear();
      });
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.queryType = 0;
      getAccepterLogging(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        // this.selectionClear();
      });
    },
    closeForm: function() {
      this.isShow = false
    },
    opentView(row){
      this.isShow = true;
      this.orderForm = row;
      if(this.orderForm.verifyUserId == 0){
        this.orderForm.verifyUserId = ' ';
      }
      this.resOrderId = row.id;
      this.orderDetailOnLoad(this.orderDetailPage);
      this.orderDetailOnLoad2(this.orderDetailPage);
    },
    orderDetailOnLoad(orderDetailPage) {
      this.orderDetailLoading = true;
      orderDetailList(orderDetailPage.currentPage, orderDetailPage.pageSize, Object.assign({
        orderId : this.resOrderId
      })).then(res => {
        const data = res.data.data;
        this.orderDetailPage.total = data.total;
        this.orderDetailData = data.records;
        this.orderDetailLoading = false;
      });
    },
    orderDetailOnLoad2(orderDetailPage) {
      this.orderDetailLoading2 = true;
      getAccepterLog(orderDetailPage.currentPage, orderDetailPage.pageSize, Object.assign({
        orderId : this.resOrderId
      })).then(res => {
        const data = res.data.data;
        this.orderDetailPage2.total = data.total;
        this.orderDetailData2 = data.records;
        this.orderDetailLoading2 = false;
      });
    },
    createVoucher(row) {
      console.log('111111111', row)
      this.$confirm("确定生成凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        let param = {
          billCode: row.financialCode,
          billNo: row.orderDetailId,
          canteenId: row.deptId,
        }
        return createWarehousingVoucher(param);
      }).then(
        () => {
          this.loading = false;
          this.onGoodsLoad(this.goodsPage);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        },
        error => {
          this.loading = false;
          this.$message.error(error);
        }
      );
    },
  }
};
</script>

<style lang="scss" scoped>
.fontsize {
  font-weight: 700;
}
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    /deep/ div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>
