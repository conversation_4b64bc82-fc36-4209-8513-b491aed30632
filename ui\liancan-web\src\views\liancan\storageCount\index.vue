<template>
  <basic-container>
    <div>
      <div class="all-mess">
        <div class="mess-header">
          <div :class="{acitve:activeIdx==index}" v-for="(item,index) in messList" :key="index" @click="menuClick(index)">
            {{item}}
          </div>
        </div>
        <div class="mess-content" v-if="activeIdx == 0">
          <avue-crud :option="option"
                     :table-loading="loading"
                     :data="data"
                     :page="page"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="form"
                     ref="crud"
                     @search-change="searchChange"
                     @search-reset="searchReset"
                     @selection-change="selectionChange"
                     @current-change="currentChange"
                     @size-change="sizeChange"
                     @on-load="onLoad">
            <template slot="menuLeft">
              <el-row type="flex" justify="center">
                <el-col :span="24">
                  <div style="margin: 0 auto; text-align: center">
                    <a style="color: red" type="danger" size="small" icon="el-icon-delete" plain>
                      一旦开始了库存盘点，到结束盘点之前，期间不能进行入库、出库等会导致商品库存数量发生变化的全部操作。因此请在适当时间内进行库存盘点操作！
                    </a>
                  </div>
                </el-col>
             </el-row>
             <el-row type="flex" justify="center" style="margin: 30px;">
                <el-col :span="24" style="margin: 0 auto; text-align: center">
                  <el-button v-if="data.length == 0" type="primary" plain @click.stop="startCheck(row)">开始盘点</el-button>
                </el-col>
             </el-row>
            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button v-if="row.schedule == 0" type="text" icon="el-icon-view" size="small" plain @click.stop="detailLoad(row)">继续盘点 </el-button>
               <!-- 后台配置了盘点审核角色的用户才可以操作审核 -->
              <el-button v-if="role == 'auditCheck' && row.schedule == 1" type="text" icon="el-icon-view" size="small" plain @click.stop="auditDetail(row)">盘点审核 </el-button>
              <el-button type="text" icon="el-icon-view" size="small" plain @click.stop="cancelCheck(row)">取消 </el-button>
            </template>
          </avue-crud>
        </div>
        <div class="mess-content" v-if="activeIdx == 1">
          <avue-crud :option="allOption"
                     :data="allData"
                     :page="allPage"
                     :permission="permissionList"
                     :before-open="beforeOpen"
                     v-model="monthForm"
                     ref="monthForm"
                     @search-change="searchChangeMonth"
                     @search-reset="searchResetMonth"
                     @selection-change="selectionChangeMonth"
                     @current-change="currentChangeMonth"
                     @size-change="sizeChangeMonth"
                     @on-load="onLoadAll">
            <template slot="menuLeft">

            </template>
            <template slot-scope="{row}" slot="menu">
              <el-button type="text" icon="el-icon-view" size="small" plain @click.stop="viewDetail(row)">查看 </el-button>
              <el-button type="text"
                        icon="el-icon-setting"
                        size="small"
                        plain
                        style="border: 0;background-color: transparent !important;"
                        v-if="row.ifVoucher == 0"
                        @click.stop="createVoucher(row)">生成凭证
              </el-button>
            </template>

          </avue-crud>
        </div>

      </div>
    </div>
    <el-dialog title='盘点详情' :visible.sync="dateDetailsVisible" width="70%" left :append-to-body="true" @close="tongjiaocan">
      <el-form ref="verificationDataForm" :inline="true" :model="verificationDataForm" label-width="80px" :rules="rules">
        <el-row style="padding-top: 30px;">
            <el-row :span="24">
                <el-form-item label="名称" prop="name">
								  <el-input v-model="verificationDataForm.name"  style="width:320px" :disabled="role == 'auditCheck' && aution == 'edit' && schedule == 1"></el-input>
							  </el-form-item>
              <el-col :span="24">
                  <a style="color: red" type="danger" size="small" icon="el-icon-delete" plain> 盘点情况 </a>
              </el-col>
              <el-col :span="24">
                  <p style="color: red" type="danger" size="small" icon="el-icon-delete" plain>
                    1、如果本次盘点实际数量少于盘点时库存数量，则务必更改“本次盘点实际数量”的具体数值。更改后 </p>
                    <p style="color: red" type="danger" size="small" icon="el-icon-delete" plain>
                    2、若产生报废数量，则必须要录入报废原因，并拍照证明（上传照片） </p>
              </el-col>
            </el-row>

          <el-table :data="verificationDataForm.detailList"  border style="width: 100%;" @cell-click="tabClick" :row-class-name="tableRowClassName">
          <el-table-column prop="businessName" label="商品名称" align="center" width="120" style="background: #fafafa;"> </el-table-column>
          <el-table-column prop="brand" label="品牌" align="center" width="120"> </el-table-column>
          <el-table-column prop="unit" align="center" label="计量单位" width="120"> </el-table-column>
          <el-table-column prop="inventoryNumber" align="center" label="盘点时库存数量" width="120"> </el-table-column>
          <el-table-column align="center" label="本次盘点实际数量" width="120">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.realityInventoryNumber" style="width:90px" :min="0" :controls="false" @input='changeNumber(scope.row)' placeholder="请输入" :disabled="role == 'auditCheck' && aution == 'edit'  && schedule == 1"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column align="center"  label="报废数量" width="120">
            <template slot-scope="scope">
              <el-input-number v-model="scope.row.scrapBusinessNumber" :min="0" style="width:90px" :controls="false" placeholder="请输入" :disabled="role == 'auditCheck' && aution == 'edit'  && schedule == 1"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column align="center"  label="报废原因" width="380">
            <template slot-scope="scope">
              <el-input v-model="scope.row.remark" placeholder="请输入" :disabled="role == 'auditCheck' && aution == 'edit'  && schedule == 1"></el-input>
            </template>
          </el-table-column>
          <el-table-column align="center"  label="拍照证明" width="200">
              <template slot-scope="scope">
                <div v-if="isAttachment" style="height:25%;width:100%;">
                  <el-upload class="upload-demo" action="/api/service/rabbit-resource/oss/endpoint/put-file" :headers="uploadHeaders"
                    :file-list="scrapImages" :on-success="handleUploadSuccess" :on-remove="handleUploadRemove"
                    :before-upload="beforeUpload" :on-preview="openPreview" v-if="!viewFlag">
                    <el-button size="mini" type="primary" @click="uploadData(scope)" >点击上传</el-button>
                  </el-upload>
				        </div>

                <div v-else v-for="(item, index) in scope.row.scrapImage" :key="index" style="width: 100%;margin-bottom:10px">
                  <el-link type="primary" @click="openPreviewByView(item)">{{item.name}}</el-link>
                </div>

			        </template>
             </el-table-column>
          </el-table>

          <!-- 盘点审核人或者查看详情时可以看到此项  -->
          <el-row :span="24" style="margin-top: 20px;" v-if="(role == 'auditCheck' || aution =='view') && schedule == 1 || schedule == 3">
              <el-form-item label="审核备注">
                <el-input v-model="verificationDataForm.auditRemark" style="width:320px" :disabled="aution =='view'"></el-input>
              </el-form-item>
          </el-row>

        </el-row>
      </el-form>
        <div style="text-align: center;margin-top: 20px;">
          <div v-if="aution =='edit'">
            <el-button type="primary" v-if="role == 'auditCheck' && schedule == 1" @click="auditData(3)">审核通过</el-button>
            <div v-else>
                <el-button type="primary" @click="addData(0)">保存</el-button>
                <el-button type="primary" @click="addData(1)">保存并提交审核</el-button>
            </div>
          </div>
          <div v-else>
            <el-button type="primary" @click="closeDetail()">关闭</el-button>
          </div>
				</div>
    </el-dialog>

    <el-dialog  append-to-body :visible.sync="imageDialog" @close="closeImage">
        <el-image  :src="fileUrl">
        </el-image>
    </el-dialog>

  </basic-container>
</template>
<script>
import {getList,startCheck,saveData,getDetail} from "@/api/liancan/storageCount";
import {createStorageVoucher} from "@/api/finance/financialVoucher";
import {mapGetters} from "vuex";
export default {
  data() {
    return {
      rules: {
        name:[{required: true, message: '请输入名称', trigger: 'blur'}]
      },
      role:'',
      aution:'',
      schedule:'',
      imageId:'',
      uploadDataList:[],
      verificationDataForm:{},
      activeIdx: 0,
      messList: [ '盘点中', '历史盘点记录'],
      form: {},
      query: {},
      // 上传文件列表
      uploadFilesList: [],
      // 上传接口头部信息
      uploadHeaders: {
        Authorization: ''
      },
      fileUrl:'',
      imageDialog:false,
      viewFlag: false,
      isAttachment:false,
      fileVOList: [],
      monthForm:{},
      loading: true,
      dateDetailsVisible:false,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      allPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      searchForm:{},
      searchForm1:{},
      selectionList: [],
      option: {
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        addBtn:false,
        delBtn:false,
        editBtn: false,
        searchBtn:false,
        emptyBtn:false,
        column: [
          {
            label: "盘点单号",
            prop: "id",
            type: "input",
          },
          {
            label: '名称',
            prop: 'name',
            type: "input",
          },
          {
            label: "盘点商品种类",
            prop: "businessNumber",
            type: "input",
          },
          {
            label: "报废商品种类",
            prop: "scrapBusinessNumber",
            type: "input",
          },
          {
            label: "报废总金额",
            prop: "scrapBusinessAmount",
             type: "input",
          },
          {
            label: "盘点人",
            prop: "operator",
             type: "input",
          },
          {
            label: "盘点时间",
            prop: "createTime",
             type: "input",
          },
          {
            label: "进度",
            prop: "schedule",
            type: "select",
            dicData: [
                      {
                          label: "盘点中",
                          value: 0
                      },
                      {
                          label: "盘点完成待审核",
                          value: 1
                      },
                      {
                          label: "盘点取消",
                          value: 2
                      },
                      {
                          label: "盘点完成",
                          value: 3
                      }
            ],
          },
        ]
      },
      allOption:{
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn:false,
        delBtn:false,
        editBtn:false,
        selection: false,
        column: [
          {
            label: "盘点单号",
            prop: "id",
            type: "input",
          },
          {
            label: '名称',
            prop: 'name',
            type:'input',
          },
          {
            label: "盘点商品种类",
            prop: "businessNumber",
            type: "input",
          },
          {
            label: "报废商品种类",
            prop: "scrapBusinessNumber",
            type: "input",
          },
          {
            label: "报废总金额",
            prop: "scrapBusinessAmount",
             type: "input",
          },
          {
            label: "盘点人",
            prop: "operator",
            type: "input",
            search:true,
          },
          {
            label: "盘点时间",
            prop: "createTime",
             type: "input",
          },
          {
            label: "进度",
            prop: "schedule",
            type: "select",
            dicData: [
                      {
                          label: "盘点中",
                          value: 0
                      },
                      {
                          label: "盘点完成待审核",
                          value: 1
                      },
                      {
                          label: "盘点取消",
                          value: 2
                      },
                      {
                          label: "盘点完成",
                          value: 3
                      }
            ],
          },
          {
              label: "盘点开始时间",
              prop: "startDate",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd 00:00:00",
              searchLabelWidth:110,
              search: true,
              hide: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "盘点结束时间",
              prop: "endDate",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd 23:59:59",
              searchLabelWidth:110,
              search:true,
              hide:true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
        ]
      },
      data: [],
      allData:[],
    }
  },
  computed: {
      ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.food_wallet_recharge_add, false),
        viewBtn: this.vaildData(this.permission.food_wallet_recharge_view, false),
        delBtn: this.vaildData(this.permission.food_wallet_recharge_delete, false),
        editBtn: this.vaildData(this.permission.food_wallet_recharge_edit, false)
      };
    },
  },
  created() {
    const { messName } = this.$route.query
    if (this.userInfo.role_name.indexOf("auditCheck") != -1) {
      this.role = 'auditCheck';
    }
  // 设置上传接口头部信息
  const access_token = JSON.parse(localStorage.getItem("rabbit-liancan-userInfo")).content.access_token
  this.uploadHeaders = {
      'Authorization':'Bearer ' + access_token,
  };
  },
  methods: {
    menuClick(idx) {
      this.query=[];
      if(this.activeIdx == idx) return
      this.activeIdx = idx
      if (idx == 0){
        this.query = {};
        this.page.currentPage = 1;
        this.onLoad(this.page);
      }
      if (idx == 1){
        this.query = {};
        this.allPage.currentPage = 1;
        this.onLoadAll(this.allPage);
      }

    },
    searchReset() {
      this.query = {};
      this.searchForm = {};
      this.onLoad(this.page);
    },

    searchResetMonth() {
      this.query = {};
      this.searchForm1 = {};
      this.onLoadAll(this.allPage);
    },

    searchChange(params, done) {
      this.searchForm = params;
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },

    searchChangeMonth(params, done) {
      this.searchForm1 = params;
      this.query = params;
      this.allPage.currentPage = 1
      this.onLoadAll(this.allPage, params);
      done();
    },

    selectionChange(list) {
      this.selectionList = list;
    },

    selectionChangeMonth(list) {
      this.selectionList = list;
    },

    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },

    selectionClearMonth() {
      this.selectionList = [];
      this.$refs.monthForm.toggleSelection();
    },

    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },


    currentChangeMonth(currentPage){
      this.allPage.currentPage = currentPage;
    },

    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },


    sizeChangeMonth(pageSize){
      this.allPage.pageSize = pageSize;
    },

    onLoad(page, params = {}) {
      this.loading = true;
      this.query.auditStatus = 0;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    onLoadAll(page, params = {}){
      this.loading = true;
      this.query.auditStatus = 1;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.allPage.total = data.total;
        this.allData = data.records;
        this.loading = false;
        this.selectionClearMonth();
      });
    },

    //取消盘点
    cancelCheck(row){
      this.$confirm("重要提示：取消本次库存盘点后，所录入的盘点数据都将丢失！同时系统可恢复进行入库、出库等操作。", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning"
					})
					.then(() => {
             row.schedule = 2;
              saveData(row).then(() => {
                this.onLoad(this.page);
                this.$message({
                  type: "success",
                  message: "操作成功!"
                });
                this.dateDetailsVisible = false;
                }, error => {
                  this.$message({
                    type: "error",
                    message: "操作失败!"+error
                  });
                });
          })
    },
    startCheck(row){
      	this.$confirm("重要提示：一旦开始了库存盘点，到结束盘点之前，期间不能进行入库、出库等会导致商品库存数量发生变化的全部操作。因此请在适当时间内进行库存盘点操作！", {
						confirmButtonText: "确定",
						cancelButtonText: "取消",
						type: "warning"
					})
					.then(() => {
            this.isAttachment = true;
            this.aution = 'edit';
            startCheck().then(res=>{
              this.verificationDataForm = res.data.data;
              var detailList = this.verificationDataForm.detailList;
              if(detailList != null && detailList.length > 0){
                  detailList.forEach(ele=>{
                    ele.realityInventoryNumber = ele.inventoryNumber;
                  })
                  this.verificationDataForm.detailList = detailList;
              }
              this.schedule = res.data.data.schedule;
            })
            this.dateDetailsVisible = true;
					});
    },
    viewDetail(row){
      this.detailCommon(row);
      this.aution = 'view';
      this.schedule = row.schedule;
      this.dateDetailsVisible = true;
    },
    auditDetail(row){
      this.detailCommon(row);
      this.aution = 'edit';
      this.isAttachment = false;
      this.schedule = row.schedule;
      this.dateDetailsVisible = true;
    },
    detailLoad(row){
      getDetail(row.id).then(res=>{
        this.verificationDataForm = res.data.data;
      })
      this.aution = 'edit';
      this.schedule = row.schedule;
      this.isAttachment = true;
      this.dateDetailsVisible = true;
    },
    detailCommon(row){
      getDetail(row.id).then(res=>{
        var detailList = res.data.data.detailList;
        detailList.forEach(ele=>{
          if(ele.scrapImage != null && ele.scrapImage != ''){
            var images = ele.scrapImage.split(",");
            var imageList = [];
            for(var i = 0 ; i < images.length ; i++){
              imageList.push({'name':'img_'+i+1,'link':images[i]})
            }
            ele.scrapImage = imageList;
          }
        })

        res.data.data.detailList = detailList;

        this.verificationDataForm = res.data.data;
      })
    },
    auditData(type){
      this.$confirm("重要提示：审核通过后，本次库存盘点将自动结束！若本次盘点中，有商品报废了，则其库存数量将会减少相应的报废数量。盘点结束后，可恢复进行入库、出库等操作。您确认要审核通过本次库存盘点么？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
        .then(() => {
          //如果是提交审核，直接将图片处理为字符串
          this.verificationDataForm.detailList.forEach(ele=>{
            var imgStr = ele.scrapImage.toString();
            ele.scrapImage = imgStr;
          });
          this.verificationDataForm.schedule = type;
          saveData(this.verificationDataForm).then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.dateDetailsVisible = false;
            }, error => {
              this.$message({
                type: "error",
                message: "操作失败!"+error
              });
            });
        });
    },
    addData(type){
      //处理图片
      this.verificationDataForm.detailList.forEach(ele=>{
        this.uploadDataList.forEach(img=>{
            if(ele.id == img.id){
                if(img.url != ''){
                    ele.scrapImage = img.url;
                }
            }
          })
      })

      if(this.verificationDataForm.name == "" || this.verificationDataForm.name == null){
        this.$message({type:'error',message:'盘点名称不能为空'});
        return;
      }

      this.verificationDataForm.schedule = type;
      saveData(this.verificationDataForm).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
        this.dateDetailsVisible = false;
        }, error => {
          window.console.log(error);
        });
    },

    closeDetail(){
      this.dateDetailsVisible = false;
    },
    //值改变事件，用于自动添加报废数量
    changeNumber(row){
        row.scrapBusinessNumber = row.inventoryNumber - row.realityInventoryNumber;
    },

    //上传前回调
			beforeUpload: function(file) {
				var str = file.name;
				str = str.substring(str.lastIndexOf("\.") + 1, str.length);
				var reStr = this.selectType(str);
				if (reStr == "NO") {
					this.$message.error('文件格式错误');
					return false;
				} else {
					this.fileType = reStr;
					return true;
				}
			},
			//判断文件类型
			selectType(type) {
				var imageList = ["jpg", "jpeg", "png", "JPG", "PNG"];
				for (var item in imageList) {
					if (imageList[item] == type) {
						return "IMAGE";
					}
				}
				return "NO";
			},
			//上传成功回调
			handleUploadSuccess: function(response, file, fileVOList) {
        var imageUrl = file.response.data.link;
				let proof = {
					fileId: file.uid,
					name: file.name,
					url: imageUrl,
					type: this.fileType
        }
        this.fileVOList.push(proof);

        var isAdd = true;
        this.uploadDataList.forEach(ele => {
          if(ele.id == this.imageId){
            ele.url = ele.url+","+imageUrl;
            isAdd = false;
          }
        });
        if(isAdd){
          var imageData = {'id':this.imageId,'url':imageUrl};
          this.uploadDataList.push(imageData);
        }
			},
			//删除文件回调
			handleUploadRemove: function(file, fileVOList) {
        var url = file.response.data.link;
        console.log("file",file.response.data.link);
				var fileId = file.fileId;
				for (var item in this.fileVOList) {
					if (this.fileVOList[item].fileId == fileId) {
						this.fileVOList.splice(item, 1);
					}
        }
        this.uploadDataList.forEach(ele=>{
          var imageList = ele.url.split(",");
          imageList.forEach(img=>{
            if(img == url){
              if(imageList.length > 1){
                for(var i = 0; i < imageList.length; i++) {
                  if(imageList[i] == url) {
                    imageList.splice(i, 1);
                    break;
                  }
                }
              }else{
                 img = '';
              }
           }
          })
          ele.url = imageList.toString();
        })
      },
      openPreview: function(item) {
				if (item.raw.type == "image/png" || item.raw.type == "image/jpg" || item.raw.type == "image/jpeg") {
          this.imageDialog = true;
          this.fileUrl = item.response.data.link;
				} else {
					//window.open('/admin/file/download/' + this.tenant + '/' + item.fileId, '_blank')
				}
      },
      openPreviewByView:function(item){
          this.imageDialog = true;
          this.fileUrl = item.link;
      },
      closeImage:function(){
          this.imageDialog = false;
      },
      uploadData:function(data){
        this.imageId = data.row.id;
      },
      createVoucher(row) {
        this.$confirm("确定生成凭证?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          this.loading = true;
          return createStorageVoucher(row.id);
        }).then(
          () => {
            this.loading = false;
            this.onLoadAll(this.allPage);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          },
          error => {
            this.loading = false;
            this.$message.error(error);
          }
        );
      },
  }
}
</script>

<style lang="scss" scoped>
.all-mess{
  background: #fff;
  overflow: hidden;
  .mess-header{
    display: flex;
    height: 50px;
    background: #F1F6FF;
    /deep/ div {
      width: 120px;
      height: 100%;
      font-size: 16px;
      color: #333;
      text-align: center;
      line-height: 50px;
      cursor: pointer;
      &:hover{
        color: #3775da;
      }
      &.acitve{
        color: #3775da;
        background: #fff;
      }
    }
  }
  .mess-content{
    padding: 0 20px;
    box-sizing: border-box;
    .mess-item{
      height: 48px;
      display: flex;
      align-items: center;
      font-size: 16px;
      color: #333;
      margin-top: 4px;
      .mess-state{
        margin-right: 10px;
      }
      .mess-name{
        width: 400px;
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
        margin-right: 20rpx;
      }
      .mess-detail{
        color: #3775da;
        text-decoration:underline;
        margin-left: 50px;
        cursor: pointer;
      }
    }
  }
  .mess-footer{
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 40px 0 40px 0;
    position: relative;
    .mess-but{
      position: absolute;
      left: 40px;
      bottom: -2px;
      font-size: 16px;
      height: 38px;
      line-height: 10px;
    }
  }
}
</style>
