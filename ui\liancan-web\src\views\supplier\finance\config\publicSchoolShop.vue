<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{size}" slot="manufacturerForm">
          <div>
            <el-input :disabled="true" :size="size" v-model="form.manufacturer" placeholder="请选择生产商"></el-input>
            <el-button type="primary" :size="size" icon="el-icon-team" plain @click.stop="choiceManufacturerDialog()">选择</el-button>
          </div>
      </template>
      <template slot-scope="{size}" slot="brandForm">
          <div>
            <el-input :disabled="true" :size="size" v-model="form.brand" placeholder="请选择品牌"></el-input>
            <el-button v-if="form.manufacturer" type="primary" :size="size" icon="el-icon-team" plain @click.stop="choiceBrand()">选择</el-button>
          </div>
      </template>
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.schoolGoods_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>

    <el-dialog  append-to-body :visible.sync="imageDialog" @close="closeImage">
        <el-image  :src="fileUrl">
        </el-image>
    </el-dialog>

    <!-- 生产商 开始 -->
    <el-dialog title="商品" :visible.sync="manufacturerShow" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="manufacturerOption"
                 :table-loading="manufacturerLoading"
                 :data="manufacturerData"
                 :page="manufacturerPage"
                 v-model="manufacturerForm"
                 ref="crud"
                 @row-save="saveManufacturer"
                 @search-change="manufacturerSearchChange"
                 @search-reset="manufacturerSearchReset"
                 @selection-change="manufacturerSelectionChange"
                 @current-change="manufacturerCurrentChange"
                 @size-change="manufacturerSizeChange"
                 >
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="choiceManufacturer(scope.row)">选择
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 生产商 结束 -->

    <!-- 生产商品牌 开始 -->
    <el-dialog title="品牌" :visible.sync="brandShow" :append-to-body="true" @close="closeForm2" width="90%">
      <avue-crud :option="manufacturerOption"
                 :data="brandData"
                 v-model="brandForm"
                 ref="crud">
        <template slot="menu" slot-scope="scope">
          <el-button type="text"
                     size="mini"
                     @click="returnBrand(scope.row)">选择
          </el-button>
        </template>
        <template slot-scope="scope" slot="menuLeft">
          <el-button v-if="brandShow"
            icon="el-icon-plus"
            size="small"
            @click="addBrand()">新增品牌</el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <!-- 生产商品牌 结束 -->

    <!-- 生产商品牌 开始 -->
    <el-dialog title="品牌" :visible.sync="addBrandShow" :append-to-body="true" @close="closeForm2" width="90%">
      <el-form ref="form" :model="addBrandForm" label-width="80px">
        <el-form-item label="生产商名称">
          <el-input v-model="addBrandForm.manufacturerName" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="生产商类别">
          <el-input v-model="addBrandForm.manufacturerCategory" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="营业执照/身份证号">
          <el-input v-model="addBrandForm.businessLicense" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="生产许可证号">
          <el-input v-model="addBrandForm.productionLicenseNum" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="联系电话">
          <el-input v-model="addBrandForm.telephone" :disabled="true"></el-input>
        </el-form-item>
        <el-form-item label="品牌名称">
          <el-input v-model="addBrandForm.brandName"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitBrand">提交</el-button>
        </el-form-item>
      </el-form>

    </el-dialog>
    <!-- 生产商品牌 结束 -->

  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/liancan/shopGoodsPublic";
  import {getList as getManufacturerList, add as addManufacturer, update as updateManufacturer } from "@/api/liancan/manufacturerManagement";
  import {mapGetters} from "vuex";
  const DIC = {
    manufacturerCategory: [
      {
        label: '企业',
        value: 0
      },
      {
        label: '个人',
        value: 1
      }
    ]
  }
  export default {
    data() {
      return {
        addBrandShow: false,
        brandShow: false,
        manufacturerShow: false,
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          // index: true,
          viewBtn: true,
          selection: true,
          align: "center",
          column: [
            // {
            //   label: "所属食堂",
            //   prop: "deptId",
            //   type: "select",
            //   dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            //   props: {
            //     label: "deptName",
            //     value:"id"
            //   },
            //   addDisplay: false,
            //   editDisplay: false,
            //   viewDisplay: false,
            //   search: true,
            // },
            // {//6
            //   label: "商品编码",
            //     prop: "id",
            //     type: "input",
            //     // display: false,
            //     // disabled: false,
            //     addDisplay: false,
            //     editDisplay: false,
            //     viewDisplay: true,
            //     search: true,
            // },
            {
                  label: "一类",
                  prop: "typeQuery",
                  type: "select",
                  rules: [{
                      required: true,
                      message: "请选择一类",
                      trigger: "blur"
                  }],
                  dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                  props: {
                      label: "name",
                      value: "id"
                  },
                  filterable: true,
                  searchFilterable: true,
                  cascaderItem: ['biddingQuery'],
                  search: true,
                  display:false,
              },
            {
              label: "二类",
              prop: "biddingQuery",
              type: "select",
              rules: [{
                required: true,
                message: "请选择二类",
                trigger: "blur"
              }],
                dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                filterable: true,
                searchFilterable: true,
                cascaderItem: ['biddingTypeIdQuery'],
                search: true,
                dicFlag: false,
                overHidden: true,
                display:false,
            },
            // {//4
            //   label: "商品小类",
            //   prop: "biddingTypeIdQuery",
            //   type: "select",
            //   rules: [{
            //     required: true,
            //     message: "请选择商品小类",
            //     trigger: "blur"
            //   }],
            //     dicFlag: false,
            //     dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
            //     props: {
            //         label: "name",
            //         value: "id"
            //     },
            //     search: true,
            //     display:false,
            // },
            {
                label: "一类",
                prop: "type",
                type: "select",
                rules: [{
                    required: true,
                    message: "请选择一类",
                    trigger: "blur"
                }],
                dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                props: {
                    label: "name",
                    value: "id"
                },
                filterable: true,
                searchFilterable: true,
                cascaderItem: ['bidding'],
                editDisabled:true,
                hide:true,
            },
            {
              label: "二类",
              prop: "bidding",
              type: "select",
              rules: [{
                required: true,
                message: "请选择二类",
                trigger: "blur"
              }],
                dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                filterable: true,
                searchFilterable: true,
                cascaderItem: ['biddingTypeId'],
                dicFlag: false,
                overHidden: true,
                hide:true,
            },
            {//4
              label: "商品名称",
              prop: "biddingTypeId",
              type: "select",
              rules: [{
                required: true,
                message: "请选择商品",
                trigger: "blur"
              }],
                dicFlag: false,
                dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall/{{key}}",
                props: {
                    label: "name",
                    value: "id"
                },
                hide:true,
            },
            {
              label: "商品子项名称",
              prop: "name",
              type: "input",
              rules: [{
                required: true,
                message: "请输入商品名称",
                trigger: "blur"
              }],
                search: true,
            },
              {//5
                  label: "计量单位",
                  prop: "unit",
                  type: "select",
                  dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                  props: {
                      label: "dictValue",
                      value: "dictKey"
                  },
                  rules: [{
                      required: true,
                      message: "请选择计量单位",
                      trigger: "blur"
                  }],
              },
              {//7
                label: "每一计量单位折合净重为(斤)",
                  labelWidth:180,
                  prop: "netWeight",
                  type: "input",
                  rules: [{
                      required: false,
                      message: "请输入净含量",
                      trigger: "blur"
                  }],
                  addDisplay: true,
              },
              {
                label: '生产商',
                prop: 'manufacturer',
                formslot:true,
                span:24,
                rules: [{
                  required: true,
                  message: "请选择生产商",
                  trigger: "blur"
                }]
            },
            {
              label: '生产商类别',
              prop: 'manufacturerCategory',
              type: "input",
              dicData: DIC.manufacturerCategory,
              span:24,
              hide: true,
              display: true,
              disabled: true,
            },
            {
              label: "营业执照/身份证号",
              prop: "businessLicense",
              type: "input",
              span:24,
              hide: true,
              display: true,
              disabled: true,
            },
            {
              label: "生产许可证号",
              prop: "productionLicenseNum",
              type: "input",
              span:24,
              hide: true,
              display: true,
              disabled: true,
            },
            {
              label: "联系电话",
              prop: "telephone",
              type: "input",
              span:24,
              hide: true,
              display: true,
              disabled: true,
            },
            {
              label: "联系地址",
              prop: "contactAddress",
              type: "input",
              span:24,
              hide: true,
              display: true,
              disabled: true,
            },
              {//6
                  label: "品牌",
                  prop: "brand",
                  formslot:true,
                  span:24,
                  rules: [{
                    required: true,
                    message: "请选择品牌",
                    trigger: "blur"
                  }]
              },
              {//6
                label: "规格",
                  prop: "foodSpec",
                  type: "input",
                  hide:true,
              },
              {//8
                  label: "保质期",
                  prop: "shelfLife",
                  type: "input",
                  rules: [{
                      required: false,
                      message: "请输入保质期",
                      trigger: "blur"
                  }],
                  addDisplay: true,
                  hide:true,
              },
              {//11
                label: "质量等级",
                  prop: "qualityLevel",
                  type: "input",
                  rules: [{
                      required: false,
                      message: "请输入质量等级",
                      trigger: "blur"
                  }],
                  addDisplay: true,
                  hide:true,
              },
              {//12
                  label: "质量标准",
                  prop: "qualityStandard",
                  type: "input",
                  rules: [{
                      required: false,
                      message: "请输入质量标准",
                      trigger: "blur"
                  }],
                  addDisplay: true,
                  hide:true,
              },
              {
                  label: "储存方法",
                  prop: "storage",
                  type: "input",
                  hide:true,
              },
              {
                label: "是否允许食堂选择使用",
                labelWidth:180,
                prop: "useFlag",
                type: "radio",
                dicData:[
                {
                  label:'是',
                  value:1
                },{
                  label:'否',
                  value:0
                }],
                rules: [{
                  required: true,
                  message: "请选择是否允许",
                  trigger: "blur"
                }],
                search: true,
              },
              {
                  label: "备注",
                  prop: "remark",
                  type: "input",
                  hide:true,
              },
              {
                  label: "商品图片",
                  prop: "imgUrl",
                  type: 'upload',
                  listType: 'picture-img',
                  propsHttp: {
                      res: 'data',
                      url: 'link'
                  },
                  tip: '只能上传jpg/png文件，大小不超过 500KB',
                  action: '/api/service/rabbit-resource/oss/endpoint/put-file',
                  rules: [{
                      required: false,
                      message: '请上传图片',
                      trigger: 'click'
                  }],
                  slot: true,
                  span: 24,
                  hide: true,
              }
          ]
        },
        loading: true,
        viewFlag: false,
        dataNum: 1,
        arrayData: [{
					viewId: 1,
					viewName: 'view1'
        }],
        uploadDataList:[],
        imageId:'',
        imageDialog:false,
        fileVOList: [],
        fileUrl:'',
        // 上传文件列表
				fileList: [],
        supplierForm:{},
        data: [],
        goodsId:'',
        manufacturerOption: {
          // menu:false,
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          // addBtn: false,
          viewBtn: false,
          editBtn: false,
          delBtn: false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '生产商类别',
              prop: 'manufacturerCategory',
              type: "radio",
              dicData: DIC.manufacturerCategory,
              span:24,
              rules: [{
                required: true,
                message: "请选择生产商类别",
                trigger: "blur"
              }],
            },
            {
              label: "生产商名称",
              prop: "manufacturerName",
              type: "input",
              span:24,
              rules: [{
                required: true,
                message: "请输入名称",
                trigger: "blur"
              }],
            },
            {
              label: "营业执照/身份证号",
              prop: "businessLicense",
              type: "input",
              span:24,
              rules: [{
                required: true,
                message: "请输入名称",
                trigger: "blur"
              }],
            },
            {
              label: "生产许可证号",
              prop: "productionLicenseNum",
              type: "input",
              span:24,
              rules: [{
                required: true,
                message: "请输入生产许可证号",
                trigger: "blur"
              }],
            },
            {
              label: "联系电话",
              prop: "telephone",
              type: "input",
              span:24,
              rules: [{
                required: true,
                message: "请输入联系电话",
                trigger: "blur"
              }],
            },
            {
              label: "联系地址",
              prop: "contactAddress",
              type: "input",
              span:24,
              rules: [{
                required: true,
                message: "请输入联系地址",
                trigger: "blur"
              }],
            },
            {
              label: '产品品牌',
              prop: 'productBrandList',
              type: 'dynamic',
              span:24,
              children: {
                align: 'center',
                headerAlign: 'center',
                rowAdd:(done)=>{
                  // this.$message.success('新增回调');
                    done({
                      input:'',
                    });
                },
                rowDel:(row,done)=>{
                  // this.$message.success('删除回调'+JSON.stringify(row));
                  done();
                },
                column: [
                  {
                    label: '品牌名称',
                    prop: "brandName",
                    type:'input',
                  },
                  // {
                  //   label: "图片",
                  //   prop: "imgUrl",
                  //   type: 'upload',
                  //   dataType: 'string',
                  //   listType: 'picture-card',
                  //   action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                  //   propsHttp: {
                  //     res: 'data',
                  //     url: 'link',
                  //   },
                  //   // span:24,
                  //   rules: [{
                  //     required: true,
                  //     message: "请上传图片",
                  //     trigger: "blur"
                  //   }],
                  //   // hide: true,
                  // }
                ]
              }
            },
            {
              label: "品牌",
              prop: "brandName",
              type: "input",
              span:24,
              rules: [{
                required: true,
                message: "请输入名称",
                trigger: "blur"
              }],
              hide: true,
              display: false,
              // disabled: true,
            },
          ]
        },
        manufacturerLoading: true,
        manufacturerData: [],
        manufacturerPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        manufacturerForm: {},
        manufacturerRow: {},
        brandData: [],
        brandForm: {},
        addBrandForm: {},
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.schoolGoods_add, false),
          viewBtn: this.vaildData(this.permission.schoolGoods_view, false),
          delBtn: this.vaildData(this.permission.schoolGoods_delete, false),
          editBtn: this.vaildData(this.permission.schoolGoods_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      if (this.userInfo.userType === 'canteen'){
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
      }
      // 设置上传接口头部信息
      const access_token = JSON.parse(localStorage.getItem("rabbit-liancan-userInfo")).content.access_token
      this.uploadHeaders = {
          'Authorization':'Bearer ' + access_token,
      };
    },
      watch: {
          // 'form.type'() {
          //     let type = this.form.type;
          //     //食材
          //     if(type == "1314471996665397249" || type == "1366636175794667521"){
          //     // if(type == "1315498192049037313"){
          //       this.option.column.forEach(then=>{
          //         // then.prop == 'shelfLife' || then.prop == 'production' || then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'
          //         if(then.prop == 'netWeight' ){
          //           then.addDisplay = true
          //         }
          //       })
          //     }
          //     //燃料
          //     if(type == "1316561056074649602" || type == "1366636571036516354"){
          //     // if(type == "1315613115907416066"){
          //       this.option.column.forEach(then=>{
          //         //|| then.prop == 'shelfLife' || then.prop == 'production' ||  then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'
          //         if(then.prop == 'netWeight' ){
          //           then.addDisplay = false;
          //         }
          //       })
          //     }
          // },
      },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        if (row.useFlag === 1) {
          this.$message.warning("已被选用不可删除");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      addView: function() {
				this.dataNum += 1;
				this.arrayData.push({
					viewId: this.dataNum,
					viewName: 'view' + this.dataNum
				})
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        if (this.selectionList[0].useFlag === 1) {
          this.$message.warning("已被选用不可删除");
          return;
        }
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
          // if(this.form.type == "1314471996665397249" || type == "1366636175794667521" ){
          //     // if(type == "1315498192049037313"){
          //     this.option.column.forEach(then=>{
          //       if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
          //           then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
          //         then.addDisplay = true
          //       }
          //     })
          // }
          // //燃料
          // if(this.form.type == "1316561056074649602" || type == "1366636571036516354"){
          //     // if(type == "1315613115907416066"){
          //     this.option.column.forEach(then=>{
          //       if(then.prop == 'biddingTypeId' || then.prop == 'netWeight' || then.prop == 'shelfLife' || then.prop == 'production' ||
          //           then.prop == 'origin' || then.prop == 'qualityLevel' || then.prop == 'qualityStandard'){
          //         then.addDisplay = false
          //       }
          //     })
          // }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      //图片相关
      //上传前回调
			beforeUpload: function(file) {
				var str = file.name;
				str = str.substring(str.lastIndexOf("\.") + 1, str.length);
				var reStr = this.selectType(str);
				if (reStr == "NO") {
					this.$message.error('文件格式错误');
					return false;
				} else {
					this.fileType = reStr;
					return true;
				}
			},
			//判断文件类型
			selectType(type) {
				var imageList = ["jpg", "jpeg", "png", "JPG", "PNG"];
				for (var item in imageList) {
					if (imageList[item] == type) {
						return "IMAGE";
					}
				}
				return "NO";
			},
			//上传成功回调
			handleUploadSuccess: function(response, file, fileVOList) {
        var imageUrl = file.response.data.link;

        var isAdd = true;
        this.uploadDataList.forEach(ele => {
          if(ele.id == this.imageId){
            ele.url = ele.url+","+imageUrl;
            isAdd = false;
          }
        });
        if(isAdd){
          var imageData = {'id':this.imageId,'url':imageUrl};
          this.uploadDataList.push(imageData);
        }
			},
			//删除文件回调
			handleUploadRemove: function(file, fileVOList) {
        var url = '';
        //初始化view的删除操作
        if(file.response == undefined){
          url = file.link;
        }else{
          url = file.response.data.link;
        }

        this.uploadDataList.forEach(ele=>{
          var imageList = ele.url.split(",");
          imageList = imageList.filter(img => img != url);
          ele.url = imageList.toString();
        })
      },
      openPreview: function(item) {
        if(item.link != null && item.link != '' && item.link != undefined){
            this.imageDialog = true;
            this.fileUrl = item.link;
        }else{
          if (item.raw.type == "image/png" || item.raw.type == "image/jpg" || item.raw.type == "image/jpeg") {
            this.imageDialog = true;
            this.fileUrl = item.response.data.link;
          } else {
            //window.open('/admin/file/download/' + this.tenant + '/' + item.fileId, '_blank')
          }
        }

      },
      openPreviewByView:function(item){
          this.imageDialog = true;
          this.fileUrl = item.link;
      },
      closeImage:function(){
          this.imageDialog = false;
      },
      uploadData:function(data){
        if(data.viewId != null && data.viewId != '' && data.viewId != undefined){
           //页面新增的view
           this.imageId = data.viewId;
        }else{
          //页面初始化已存在的view
          //检查当前view是否存在图片
          if(data.agreement != ''){
            var linkStr = '';
             data.agreement.forEach(ele=>{
                linkStr = linkStr + ele.link + ",";
             })
            linkStr = linkStr.substring(0,linkStr.length - 1);
            var isNewGroup = false;
            this.uploadDataList.forEach(v => {
              if (v.id == data.id) {
                isNewGroup = true;
                v.url = v.url + "," + linkStr;
              }
            });
            if (isNewGroup) {
              this.uploadDataList.push({'id':data.id,'url':linkStr});
            }
          }
          this.imageId = data.id;
        }
      },
      choiceManufacturerDialog(){
        this.manufacturerShow = true
        this.manufacturerOption.column[8].display = false
        this.manufacturerOnLoad(this.manufacturerPage);
      },
      manufacturerOnLoad(manufacturerPage, params = {}) {
        this.manufacturerLoading = true;
        getManufacturerList(manufacturerPage.currentPage, manufacturerPage.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          console.log(data)
          this.manufacturerPage.total = data.total;
          this.manufacturerData = data.records;
          this.manufacturerLoading = false;
          // this.selectionClear();
        });
      },
      manufacturerSearchReset() {
        this.query = {};
        this.manufacturerOnLoad(this.manufacturerPage);
      },
      manufacturerSearchChange(params, done) {
        this.query = params;
        this.manufacturerPage.currentPage = 1
        this.manufacturerOnLoad(this.manufacturerPage, params);
        done();
      },
      // manufacturerSelectionChange(list) {
      //   this.selectionList = list;
      // },
      // manufacturerSelectionClear() {
      //   this.selectionList = [];
      //   this.$refs.crud.toggleSelection();
      // },
      manufacturerCurrentChange(currentPage){
        this.manufacturerPage.currentPage = currentPage;
      },
      manufacturerSizeChange(pageSize){
        this.manufacturerPage.pageSize = pageSize;
      },
      saveManufacturer(row, loading, done) {
        addManufacturer(row).then(res => {
          loading();
          this.manufacturerOnLoad(this.manufacturerPage);
          console.log(">>>>>>>>>>>>>>>>>",res.data.success)
    /*      this.$message({
            type: "success",
            message: "操作成功!"
          });*/
          if (res.data.success) {
            this.$message({
              type: "success",
              message: res.data.message
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.message
            });
          }
        }, error => {
          done();
          window.console.log(error);
        });
      },
      choiceManufacturer(row){
        console.log(row)
        this.manufacturerRow = row
        this.manufacturerShow = false
        this.form.manufacturer = row.manufacturerName
        this.form.businessLicense = row.businessLicense
        this.form.productionLicenseNum = row.productionLicenseNum
        this.form.telephone = row.telephone
        this.form.contactAddress = row.contactAddress
      },
      choiceBrand(){
        this.brandShow = true
        this.manufacturerOption.addBtn = false
        this.manufacturerOption.column[3].hide = true
        this.manufacturerOption.column[4].hide = true
        this.manufacturerOption.column[5].hide = true
        this.manufacturerOption.column[6].hide = true

        this.manufacturerOption.column[8].hide = false
        this.manufacturerOption.column[8].display = true
        const tmp = this.manufacturerRow

        this.brandData = this.manufacturerRow.productBrandList.map(ele=>{
          const tmpObj = { ...tmp, brandName: ele.brandName };
          console.log(tmpObj);
          return tmpObj;
        })
        console.log(this.brandData )
        // this.manufacturerOnLoad(this.manufacturerPage);
      },
      returnBrand(row){
        this.brandShow = false
        this.manufacturerOption.addBtn = true
        this.manufacturerOption.column[3].hide = false
        this.manufacturerOption.column[4].hide = false
        this.manufacturerOption.column[5].hide = false
        this.manufacturerOption.column[6].hide = false

        this.manufacturerOption.column[8].hide = true
        this.manufacturerOption.column[8].display = false
        this.form.brand = row.brandName
      },
      addBrand(){
        this.addBrandForm.manufacturerName = this.manufacturerRow.manufacturerName
        //从DIC.manufacturerCategory数组里面获取this.manufacturerRow.manufacturerCategory value的值
        for (let i = 0; i < DIC.manufacturerCategory.length; i++) {
          if (DIC.manufacturerCategory[i].value == this.manufacturerRow.manufacturerCategory) {
            this.addBrandForm.manufacturerCategory = DIC.manufacturerCategory[i].label;
          }
        }
        this.addBrandForm.businessLicense = this.manufacturerRow.businessLicense
        this.addBrandForm.productionLicenseNum = this.manufacturerRow.productionLicenseNum
        this.addBrandForm.telephone = this.manufacturerRow.telephone
        this.addBrandShow = true
      },
      submitBrand(){
        //添加到数据库
        this.manufacturerRow.productBrandList.push({"brandName": this.addBrandForm.brandName})
        console.log(this.manufacturerRow)
        updateManufacturer(this.manufacturerRow).then(res => {
          if (res.data.success) {
            this.addBrandShow = false
            //添加到页面
            const tmpObj = { ...this.manufacturerRow, brandName: this.addBrandForm.brandName };
            this.brandData.push(tmpObj)
            this.$message({
              type: "success",
              message: res.data.message
            });
          } else {
            this.$message({
              type: "error",
              message: res.data.message
            });
          }
        }, error => {
          done();
          window.console.log(error);
        });
      }
    }
  };
</script>

<style>
</style>
