<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
        </avue-crud>
    </basic-container>
</template>

<script>
import {mapGetters} from "vuex";
import {getList,getActualStock,getPurchasePrice,getPurchaseDetail,getSaleDetail,getSalePrice} from "@/api/supplier/supplierQueryInfo";

export default {
    props: {
        // 声明接收的参数
        rowData: {
            type: Object,
            default: () => ({}) // 默认值
        }
    },
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 5,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: '300px',
                searchShow: true,
                refreshBtn: true,
                columnBtn: true,
                searchBtn: true,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                menu: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                        searchLabelWidth: 45,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                        searchLabelWidth: 45,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        search: true,
                        width: 100,
                        searchLabelWidth: 100,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                        search: true,
                        width: 100,
                        searchLabelWidth: 100,
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                    },
                    {
                        label: "库存数量",
                        prop: "stockQty",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                    },
                    {
                        label: "生产商",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "售价1",
                        prop: "salePrice1",
                        type: "input",
                    },
                    {
                        label: "售价2",
                        prop: "salePrice2",
                        type: "input",
                    },
                    {
                        label: "售价3",
                        prop: "salePrice3",
                        type: "input",
                    },
                    {
                        label: "售价4",
                        prop: "salePrice4",
                        type: "input",
                    },
                    {
                        label: "售价5",
                        prop: "salePrice5",
                        type: "input",
                    },
                ]
            },
            data: [],
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
    },
    watch: {
    },
    mounted() {
    },
    methods: {
        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getSalePrice(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
