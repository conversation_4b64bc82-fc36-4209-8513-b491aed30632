<template>
  <basic-container>
    <avue-crud :option="monthOption"
               :data="monthData"
               :page="monthPage"
               :table-loading="monthLoading"
               :before-open="beforeOpen"
               v-model="monthForm"
               ref="monthForm"
               @search-change="searchChangeMonth"
               @search-reset="searchResetMonth"
               @selection-change="selectionChangeMonth"
               @current-change="currentChangeMonth"
               @size-change="sizeChangeMonth"
               @on-load="onLoadMonth">
<!--      <template slot="menuLeft">-->
<!--        <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>-->
<!--      </template>-->
      <el-date-picker
        v-model="month"
        type="month"
        placeholder="选择月">
      </el-date-picker>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getMonthListByCanteen} from "@/api/queryStatistics/foodWalletRecharge";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      monthForm:{},
      monthPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      monthOption:{
        /*          height:'auto',*/
        /*    calcHeight: 30,*/
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        addBtn:false,
        menu:false,
        selection: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'stuRechargeBalance1',
            type: 'sum'
          },
          {
            name: 'parRechargeBalance1',
            type: 'sum'
          },
          {
            name: 'teaRechargeBalance1',
            type: 'sum'
          },
          {
            name: 'cashRechargeBalance1',
            type: 'sum'
          },
          {
            name: 'facultyRechargeBalance1',
            type: 'sum'
          },
          {
            name:'wxRechargeBalance1',
            type: 'sum'
          },
          {
            name:'serviceChargeBalance1',
            type: 'sum'
          },
          {
            name:'actuallyReceivedBalance1',
            type: 'sum'
          },
          {
            name:'rechargeTotalBalance1',
            type: 'sum'
          },
          {
            name:'actuallyTotalBalance1',
            type: 'sum'
          }
        ],
        column: [
          {
            label: "",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: '月份',
            prop: 'monthDate',
            type:'month',
            searchSpan:6,
            searchRange:true,
            search:true,
            format: 'yyyy-MM',
            valueFormat: 'yyyy-MM',
            hide:true,
          },
          /*        {
                    label: "月",
                    prop: "monthDate",
                    type: "month",
                    mock:{
                      type:'datetime',
                      format:'yyyy-MM'
                    },
                    format:'yyyy-MM',
                    valueFormat:'yyyy-MM',
                    search:true,
                    hide:true,
                  },*/
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          {
            label: "月份",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM",
            valueFormat: "yyyy-MM",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            sortable:true,
          },
          /*       {
                   label: "单位",
                   prop: "unitId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search: true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },
                 {
                   label: "学校",
                   prop: "schoolId",
                   type: "select",
                   dicUrl: "/api/service/rabbit-system/dept/select",
                   props: {
                     label: "deptName",
                     value:"id"
                   },
                   hide: false,
                   search: true,
                   rules: [{
                     required: true,
                     message: "请选择部门",
                     trigger: "click"
                   }]
                 },*/
          {
            label: "充值账户",
            prop: "rechargeType",
            type: "select",
            align: 'center',
            search:true,
            sortable:true,
            dicData: [
              {
                label: "统缴餐钱包",
                value: "0"
              },
              {
                label: "自选餐钱包",
                value: "1"
              }
            ],
          },
          {
            label: "现金充值(实收)",
            prop: "cashRechargeBalance1",
            align: 'center',
            sortable:true,
          },
          {
            label: '微信充值',
            align: 'center',
            children: [{
              label: '学生',
              prop: 'stuRechargeBalance1',
              align: 'center',
              sortable:true,
            }, {
              label: '家长代充',
              prop: 'parRechargeBalance1',
              align: 'center',
              sortable:true,
            },{
              label: '老师代充',
              prop: 'teaRechargeBalance1',
              align: 'center',
              sortable:true,
            },
              {
                label: '教职工',
                prop: 'facultyRechargeBalance1',
                align: 'center',
                sortable:true,
              }]
          },
          {
            label: "微信充值金额",
            prop: "wxRechargeBalance1",
            align: 'center',
            sortable:true,
          },
          {
            label: "微信充值手续费",
            prop: "serviceChargeBalance1",
            align: 'center',
            sortable:true,
          },
          {
            label: "微信充值(实收)",
            prop: "actuallyReceivedBalance1",
            align: 'center',
            sortable:true,
          },
          {
            label: "充值金额合计",
            prop: "rechargeTotalBalance1",
            sortable:true,
          },
          {
            label: "充值实收金额合计",
            prop: "actuallyTotalBalance1",
            sortable:true,
            width:160,
          }
        ]
      },
      monthData:[],
      searchForm1:{},
      selectionList: [],
      monthLoading: true,
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchChangeMonth(params, done) {
      this.searchForm1 = params;
      this.query = params;
      this.monthPage.currentPage = 1
      this.onLoadMonth(this.monthPage, params);
      done();
    },
    searchResetMonth() {
      this.query = {};
      this.searchForm1 = {};
      this.onLoadMonth(this.monthPage);
    },
    selectionChangeMonth(list) {
      this.selectionList = list;
    },
    currentChangeMonth(currentPage){
      this.monthPage.currentPage = currentPage;
    },
    sizeChangeMonth(pageSize){
      this.monthPage.pageSize = pageSize;
    },
    selectionClearMonth() {
      this.selectionList = [];
      this.$refs.monthForm.toggleSelection();
    },
    onLoadMonth(page, params = {}) {
      this.monthLoading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getMonthListByCanteen(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.monthPage.total = data.total;
        this.monthData = data.records;
        this.monthLoading = false;
        this.selectionClearMonth();
      });
    },




  }
}
</script>

<style scoped>

</style>
