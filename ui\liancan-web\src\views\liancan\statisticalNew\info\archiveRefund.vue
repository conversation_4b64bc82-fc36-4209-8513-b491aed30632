<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
<!--      <template slot="menuLeft">-->
<!--        <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportArchiveRefund">导出</el-button>-->
<!--      </template>-->
    </avue-crud>
  </basic-container>
</template>

<script>
import {getListByCanteenId, getDetail} from "@/api/liancan/archive/refundDetails/archiveRefundDetails";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      searchForm:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      option: {
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        selection: false,
        menu: false,
        showSummary: true,
        sumColumnList: [
          {
            name: 'money',
            type: 'sum',
            decimals:1
          },
        ],
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search:true,
            sortable:true,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            sortable: true,
          },
          {
            label: "原部门",
            prop: "deptName",
            type: "input",
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              label: "title",
              value: "id"
            },
            editDisplay: false,
            viewDisplay: false,
            multiple:true,
            /*slot:true,*/
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请输入部门",
              trigger: "click"
            }]
          },
          {
            label: "钱包",
            prop: "balanceType",
            type: 'select',
            search:true,
            sortable:true,
            dicData: [{
              value: "1",
              label: "统缴餐钱包"
            },
              {
                value: "2",
                label: "自选餐钱包"
              }
            ],
          },
          {
            label: "存档退款金额",
            prop: "money",
            sortable:true,
          },
          {
            label: "操作人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            sortable:true,
          },
          {
            label: "操作时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            sortable:true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
            overHidden: true,
          },
          {
            label: "开始时间",
            prop: "startDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide: true,
            search: true,
            display: false,
            showColumn: false,
          },
          {
            label: "结束时间",
            prop: "endDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            type: "date",
            hide: true,
            search: true,
            display: false,
            showColumn: false,
          },
        ]
      },
      data: []
    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      this.searchForm = params;
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getListByCanteenId(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },





  }
}
</script>

<style scoped>

</style>
