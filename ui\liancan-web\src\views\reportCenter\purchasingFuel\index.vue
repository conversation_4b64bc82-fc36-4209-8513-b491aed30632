<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportData">导出</el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {purchasingFuel,purchasingFuelExport} from "@/api/liancan/order";
  import {setOption} from "@/api/liancan2/common";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        searchFrom:{},
        option: {
          //height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          menu: false,
          addBtn: false,
          delBtn: false,
          selection: false,
          column: [
            {
            label: '省份',
            prop: 'province',
            type: 'select',
            props: {
            label: 'regionName',
            value: 'id'
            },
            cascaderItem: ['city', 'area'],
            search:true,
            hide: true,
            viewDisplay: false,
            dicUrl: `/api/rabbit-system/region/getProvince`,
            rules: [
            {
              required: true,
              message: '请选择省份',
              trigger: 'blur'
            }
            ]
          },
          {
            label: '城市',
            prop: 'city',
            type: 'select',
            props: {
            label: 'regionName',
            value: 'id'
            },
            search:true,
            hide: true,
            viewDisplay: false,
            dicFlag: false,
            dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
            rules: [
            {
              required: true,
              message: '请选择城市',
              trigger: 'blur'
            }
            ]
          },
          {
            label: '地区',
            prop: 'area',
            type: 'select',
            props: {
            label: 'regionName',
            value: 'id'
            },
            search:true,
            hide: true,
            viewDisplay: false,
            dicFlag: false,
            dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
            rules: [
            {
              required: true,
              message: '请选择地区',
              trigger: 'blur'
            }
            ]
          },
            {
              label: "单位名称",
              prop: "parentDeptName",
              type: "input",
            },
            {
              label: "单位类型",
              prop: "agencyType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              search: true,
            },
            {
              label: "学校类型",
              prop: "schoolType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=school_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              search: true,
            },
            {
              label: "食堂名称",
              prop: "deptName",
              type: "input",
            },
            {
              label: "单位名称",
              prop: "deptParentId",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan2/user/getSchoolList",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: true,
              addDisplay: false,
              search:true,
              cascaderItem: ['deptId'],
              cascaderChange: true,
            },
            {
              label: "食堂名称",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan2/user/getCanteenList?schoolId={{key}}",
              props: {
                label: "deptName",
                value:"id"
              },
              dicFlag: false,
              hide: true,
              addDisplay: false,
              search: true,
            },
            {
              label: "煤气",
              prop: "coalgas",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "天然气",
              prop: "naturalGas",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "生物燃料",
              prop: "biofuels",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "煤炭",
              prop: "coal",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "木材",
              prop: "wood",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "其他燃料",
              prop: "other",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
            {
              label: "总计",
              prop: "sum",
              type: "input",
              precision:2,
              mock:{
                type:'number',
                min:0,
                precision:2
              },
              minRows: 0,
            },
          ],
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          /*addBtn: this.vaildData(this.permission.schoolAuthInfo_add, false),
          viewBtn: this.vaildData(this.permission.schoolAuthInfo_view, false),
          delBtn: this.vaildData(this.permission.schoolAuthInfo_delete, false),
          editBtn: this.vaildData(this.permission.schoolAuthInfo_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created(){
      // 单位列表是否显示
      setOption(this.option.column, this.userInfo.userType);
    },
    methods: {
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.searchFrom = params;
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        purchasingFuel(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      exportData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.searchFrom.status = "2";
        purchasingFuelExport(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '采购燃料统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
