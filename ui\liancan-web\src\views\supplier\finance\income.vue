<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="incomeAmount" slot-scope="scope">
        {{scope.row.money}}
      </template>
    </avue-crud>
    <el-dialog title="" :visible.sync="orderVisible" :append-to-body="true" @close="orderFormClose" width="60%">
      <avue-crud ref="crud" v-model="orderForm" :option="orderOption" :data="orderData" @on-load="loadMenus" :page="page"
                 :table-loading="menuTableLoading" @selection-change="selectionChanges">
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectPayer">确定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, getSupplier, getPayee} from "@/api/supplier/finance/income";
  import {mapGetters} from "vuex";
  var DIC = {
    payType: [{
      label: '供应商',
      value: "0"
    },
    /*{
      label: '学生',
      value: "1"
    },{
      label: '教职工',
      value: "2"
    }*/
    ]
  }
  export default {
    data() {
      return {
        form: {},
        orderForm: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        selectionLists: [],
        orderData: [],
        orderVisible: false,
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          editBtn: false,
          selection: true,
          column: [
            {
              label: "科目类型",
              prop: "course",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=income_course",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              span: 24,
              search: true,
              rules: [{
                required: true,
                message: "请输入科目类型",
                trigger: "blur"
              }],
            },
            {
              label: "入账时间",
              prop: "incomeTime",
              type: "datetime",
              format: 'yyyy-MM-dd HH:mm:ss',
              valueFormat: 'yyyy-MM-dd HH:mm:ss',
              addDisplay: false,
              editDisplay: false,
              search: true,
            },
            {
              label: "交易内容",
              prop: "transactionContent",
              type: "textarea",
              span: 24,
            },
            {
              label: "收入单号",
              prop: "incomeNumber",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              search: true,
            },
            {
              label: "入账方式",
              prop: "entryType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=entry_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              addDisplay: false,
              editDisplay: false,
            },
            {
              label: "支付方类型",
              prop: "payerType",
              type: "select",
              span: 24,
              rules: [{
                required: true,
                message: "请输入支付方",
                trigger: "blur"
              }],
              dicData: DIC.payType,
              hide: true,
              viewDisplay: false
            },
            {
              label: "支付方",
              prop: "payer",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入支付方",
                trigger: "blur"
              }],
            },
            {
              label: "收款方",
              prop: "payee",
              type: "input",
              span: 24,
              hide: true,
              rules: [{
                //required: true,
                message: "请输入收款方",
                trigger: "blur"
              }],
              disabled: true
            },
            {
              label: "发票",
              prop: "invoices",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "入账凭证",
              prop: "entryVoucherFile",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "收入金额",
              prop: "money",
              type: "input",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入收入金额",
                trigger: "blur"
              }],
              slot: true
            },
            {
              label: "操作人",
              prop: "userId",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
            },
          ]
        },
        orderOption: {
          height:'auto',
          calcHeight: 30,
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: true,
          menu: false,
          column: [{
            label: "企业名称",
            prop: "name",
            type: "input",
          },
            {
              label: "企业性质",
              prop: "companyNatures",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
              props: {
                label: "dictValue",
                value: "dictKey"
              },

            },
            {
              label: "联系人",
              prop: "contactsName",
            },
            {
              label: "联系人手机",
              prop: "mobile",
            },
            {
              label: "地址",
              prop: "address",
            },
            {
              label: "中标供应商商品或服务",
              prop: "commodityType",
            }
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission", "userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.income_add, false),
          viewBtn: this.vaildData(this.permission.income_view, false),
          delBtn: this.vaildData(this.permission.income_delete, false),
          editBtn: this.vaildData(this.permission.income_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    watch: {
      'form.payerType'() {
        if (this.form.payerType !== '' && this.form.payerType !== undefined) {
          getSupplier().then(res =>{
            this.orderData = res.data.data;
          })
          console.log("-----"+JSON.stringify(this.userInfo))
          this.orderVisible = true;
        }
      },
    },
    methods: {
      rowSave(row, loading, done) {
        row.entryType = "1";
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }else {
          getPayee().then(res => {
            this.form.payee = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChanges(list) {
        this.selectionLists = list;
      },
      selectPayer(){
        console.log(JSON.stringify("this.selectionLists:"+JSON.stringify(this.selectionLists)))
        this.form.payer = this.selectionLists[0].name;
        this.orderVisible = false;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
