<template>
  <basic-container>
    <div>
      <div class="all-mess">
        <div class="mess-header">
          <div
            :class="{acitve:activeIdx==index}"
            v-for="(item,index) in messList"
            :key="index"
            @click="menuClick(index)"
          >
            {{item}}
          </div>
        </div>
        <span class="mess-content" v-if="this.activeIdx == 0">
          <avue-crud :option="illegalProgressOption"
                     :table-loading="illegalProgressLoading"
                     :data="illegalProgressData"
                     :page="illegalProgressPage"
                     v-model="illegalProgressForm"
                     ref="illegalProgressForm"
                     @search-change="searchChange"
                     @search-reset="searchReset"
                     @selection-change="selectionChange"
                     @current-change="currentChange"
                     @size-change="sizeChange"
                     @on-load="onLoad">
                <template slot="menuLeft">
            <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportIllegalProgressData">导出</el-button>
          </template>
          </avue-crud>
        </span>
        <span class="mess-content" v-if="this.activeIdx == 1">
          <avue-crud :option="violationsTypeOption"
                     :table-loading="violationsTypeLoading"
                     :data="violationsTypeData"
                     :page="violationsTypePage"
                     v-model="violationsTypeForm"
                     ref="violationsTypeForm"
                     @search-change="searchChange1"
                     @search-reset="searchReset1"
                     @selection-change="selectionChange1"
                     @current-change="currentChange1"
                     @size-change="sizeChange1"
                     @on-load="violationsTypeOnLoad">
                     <template slot="menuLeft">
            <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportViolationsTypeData">导出</el-button>
          </template>
          </avue-crud>
        </span>
      </div>
    </div>
  </basic-container>
</template>

<script>
  import {getWarningStatisticsList,getViolationsTypeList,exportIllegalProgressData,exportViolationsTypeData} from "@/api/liancan/illegalWarnLog";
  export default {
    data() {
      return {
        form:{},
        warningForm:{},
        managementForm:{},
        fileForm:{},
        allWarningListForm:{},
        illegalProgressForm:{},
        violationsTypeForm:{},
        data:[],
        illegalProgressData:[],
        violationsTypeData:[],
        illegalProgressPage: {
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 10, // 每页显示多少条,
          isAsc: false //是否倒序
        },
        violationsTypePage:{
          total: 0, // 总页数
          currentPage: 1, // 当前页数
          pageSize: 10, // 每页显示多少条,
          isAsc: false //是否倒序
        },
        loading: true,
        allWarningListloading:true,
        illegalProgressLoading:true,
        violationsTypeLoading:true,
        shopInformationsData:[],
        allWarningListData:[],
        approvalVisible:false,
        menuTableLoading:true,
        biddingsForm:{},
        biddingForm:{},
        warningRecordVisible:false,
        warningRecordVisible1:false,
        warningRecordVisible2:false,
        otherWarningVisible3:false,
        otherWarningSuccessVisible:false,
        otherWarningVisible:false,
        warningRecordForm:{},
        warningRecordForm1:{},
        warningRecordForm2:{},
        replyForm:{},
        searchForm:{},
        illegalProgressOption:{
       /*   height:'auto',
          calcHeight: 50,*/
          calcHeight: 50,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          editBtn: false,
          delBtn: false,
          addBtn: false,
          viewBtn: false,
          menu: false,
          selection: false,
          showSummary: true,
          printBtn:true,
          sumColumnList: [
            {
              name: 'inquiredNumber',
              type: 'sum'
            },
            {
              name: 'canteenReplyNumber',
              type: 'sum'
            },
            {
              name: 'handleNumber',
              type: 'sum'
            },
            {
              name: 'fileNumber',
              type: 'sum'
            },
            {
              name: 'filedNumber',
              type: 'sum'
            },
            {
              name: 'totalQuantity',
              type: 'sum'
            },
          ],
          column: [
            {
              label: '统计时间',
              prop: 'date',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
            {
              label: "单位名称",
              prop: "unitName",
              type: "input",
              search:true,
            },
            {
              label: "单位类型",
              prop: "unitType",
              type: "select",
              search:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入单位类型",
                trigger: "blur"
              }],
            },
            {
              label: "待函询数量",
              prop: "inquiredNumber",
            },
            {
              label: "待食堂回复数量",
              prop: "canteenReplyNumber",
            },
            {
              label: "待处置数量",
              prop: "handleNumber",
            },
            {
              label: "待归档数量",
              prop: "fileNumber",
            },
            {
              label: "已归档数量",
              prop: "filedNumber",
            },
            {
              label: "数量合计",
              prop: "totalQuantity",
            },
          ]
        },
        violationsTypeOption: {
        /*  height:'auto',*/
          calcHeight: 50,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menuWidth: 120,
          showSummary: true,
          menu:false,
          printBtn:true,
          sumColumnList: [
            {
              name: 'biddingNumber',
              type: 'sum'
            },
            {
              name: 'procurementNumber',
              type: 'sum'
            },
            {
              name: 'revenueNumber',
              type: 'sum'
            },
            {
              name: 'expenditureNumber',
              type: 'sum'
            },
            {
              name: 'financialNumber',
              type: 'sum'
            },
            {
              name: 'totalQuantity',
              type: 'sum'
            },
          ],
          column: [
            {
              label: '统计时间',
              prop: 'date',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
            {
              label: "单位名称",
              prop: "unitName",
              type: "input",
              search:true,
            },
            {
              label: "单位类型",
              prop: "unitType",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              search:true,
              rules: [{
                required: true,
                message: "请输入单位类型",
                trigger: "blur"
              }],
            },
            {
              label: "招标违规数量",
              prop: "biddingNumber",
            },
            {
              label: "采购违规数量",
              prop: "procurementNumber",
            },
            {
              label: "收入违规数量",
              prop: "revenueNumber",
            },
            {
              label: "支出违规数量",
              prop: "expenditureNumber",
            },
            {
              label: "财务违规数量",
              prop: "financialNumber",
            },
            {
              label: "数量合计",
              prop: "totalQuantity",
            },
          ],
          disabledFlag:true,
        },
        orderForm:{},
        activeIdx: 0,
        messList: [ '按进度汇总', '按分类汇总']
      }
    },
    created() {
      const { messName } = this.$route.query
      if(messName){
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log( this.activeIdx )
      }
    },
    methods: {
      menuClick(idx) {
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.query = {};
          this.searchForm ={};
          this.illegalProgressPage.currentPage = 1;
          this.onLoad(this.illegalProgressPage);
        }
        if (idx == 1){
          this.query = {};
          this.searchForm ={};
          this.violationsTypePage.currentPage = 1;
          this.violationsTypeOnLoad(this.violationsTypePage);
        }
      },
      searchReset() {
        this.query = {};
        this.searchForm ={};
        this.onLoad(this.illegalProgressPage);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchForm = params;
        if (params.date != '' && params.date != null && params.date != undefined) {
          params.startDateTime = params.date[0];
          params.endDateTime = params.date[1];
          this.searchForm.startDateTime = params.date[0];
          this.searchForm.endDateTime = params.date[1];
        }
        this.illegalProgressPage.currentPage = 1
        this.onLoad(this.illegalProgressPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.illegalProgressForm.toggleSelection();
      },
      currentChange(currentPage){
        this.illegalProgressPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.illegalProgressPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.illegalProgressLoading = true;
        getWarningStatisticsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.illegalProgressPage.total = data.total;
          this.illegalProgressData = data.records;
          this.illegalProgressLoading = false;
          this.selectionClear();
        });
      },
      searchReset1() {
        this.query = {};
        this.searchForm ={};
        this.violationsTypeOnLoad(this.violationsTypePage);
      },
      searchChange1(params, done) {
        this.query = params;
        this.searchForm = params;
        if (params.date != '' && params.date != null && params.date != undefined) {
          params.startDateTime = params.date[0];
          params.endDateTime = params.date[1];
          this.searchForm.startDateTime = params.date[0];
          this.searchForm.endDateTime = params.date[1];
        }
        this.violationsTypePage.currentPage = 1
        this.violationsTypeOnLoad(this.violationsTypePage, params);
        done();
      },
      selectionChange1(list) {
        this.selectionList = list;
      },
      selectionClear1() {
        this.selectionList = [];
        this.$refs.violationsTypeForm.toggleSelection();
      },
      currentChange1(currentPage){
        this.violationsTypePage.currentPage = currentPage;
      },
      sizeChange1(pageSize){
        this.violationsTypePage.pageSize = pageSize;
      },
      violationsTypeOnLoad(page, params = {}) {
        this.violationsTypeLoading = true;
        getViolationsTypeList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.violationsTypePage.total = data.total;
          this.violationsTypeData = data.records;
          this.violationsTypeLoading = false;
          this.selectionClear1();
        });
      },
      exportIllegalProgressData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportIllegalProgressData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '违规处理进度汇总统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportViolationsTypeData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportViolationsTypeData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '违规分类汇总统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  }
</script>

<style lang="scss" scoped>
  .all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      ::v-deep div {
        width: 200px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      height: 640px;
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20px;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
