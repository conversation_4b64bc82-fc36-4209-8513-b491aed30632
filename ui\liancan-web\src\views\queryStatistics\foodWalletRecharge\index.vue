<template>
  <basic-container>
  <div>
    <div class="all-mess">
      <div class="mess-header">
        <div
          :class="{acitve:activeIdx==index}"
          v-for="(item,index) in messList"
          :key="index"
          @click="menuClick(index)"
        >
          {{item}}
        </div>
      </div>
      <div class="mess-content" v-if="activeIdx == 0">
        <avue-crud :option="option"
                   :data="data"
                   :page="page"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
          <template slot="menuLeft">
            <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData">导出</el-button>
          </template>
        </avue-crud>
      </div>
      <div class="mess-content" v-if="activeIdx == 1">
        <avue-crud :option="monthOption"
                   :data="monthData"
                   :page="monthPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="monthForm"
                   ref="monthForm"
                   @search-change="searchChangeMonth"
                   @search-reset="searchResetMonth"
                   @selection-change="selectionChangeMonth"
                   @current-change="currentChangeMonth"
                   @size-change="sizeChangeMonth"
                   @on-load="onLoadMonth">
          <template slot="menuLeft">
            <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData2">导出</el-button>
          </template>
          <el-date-picker
            v-model="month"
            type="month"
            placeholder="选择月">
          </el-date-picker>
        </avue-crud>
      </div>

      <div class="mess-content" v-if="activeIdx == 2">
        <avue-crud :option="yearOption"
                   :data="yearData"
                   :page="yearPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="yearForm"
                   ref="yearForm"
                   @search-change="searchChangeYear"
                   @search-reset="searchResetYear"
                   @selection-change="selectionChangeYear"
                   @current-change="currentChangeYear"
                   @size-change="sizeChangeYear"
                   @on-load="onLoadYear">
          <template slot="menuLeft">
            <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeDetailData3">导出</el-button>
          </template>
        </avue-crud>
      </div>
      <div class="mess-content" v-if="activeIdx == 3">
        <avue-crud :option="deptOption"
                   :data="deptData"
                   :page="deptPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="deptForm"
                   ref="deptForm"
                   @search-change="searchChangeDept"
                   @search-reset="searchResetDept"
                   @selection-change="selectionChangeDept"
                   @current-change="currentChangeDept"
                   @size-change="sizeChangeDept"
                   @on-load="onLoadDept">
          <template slot="menuLeft">
            <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect "class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportDeptFoodData">导出</el-button>
          </template>
          <template slot="menu" slot-scope="scope">
            <el-button type="text" size="small" icon="el-icon-view" @click="openDeptUser(scope.row)">查看
            </el-button>
          </template>
        </avue-crud>
      </div>
      <div class="mess-content" v-if="activeIdx == 4">
        <avue-crud :option="userOption"
                   :data="userData"
                   :page="userPage"
                   :permission="permissionList"
                   v-model="userForm"
                   ref="userForm"
                   @search-change="searchChangeUser"
                   @search-reset="searchResetUser"
                   @selection-change="selectionChangeUser"
                   @current-change="currentChangeUser"
                   @size-change="sizeChangeUser"
                   @on-load="onLoadUser">
          <template slot="menuLeft">
            <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect "class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportUserFoodData">导出</el-button>
          </template>
        </avue-crud>
      </div>
      <div class="mess-content" v-if="activeIdx == 5">
        <avue-crud :option="mealsUserOption"
                   :data="mealsUserData"
                   :page="mealsUserPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="mealsUserForm"
                   ref="mealsUserForm"
                   @search-change="searchChangeMealsUser"
                   @search-reset="searchResetMealsUser"
                   @selection-change="selectionChangeMealsUser"
                   @current-change="currentChangeMealsUser"
                   @size-change="sizeChangeMealsUser"
                   @on-load="onLoadMealsUser">
          <template slot="menuLeft">
            <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect "class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportMealsUserFoodData">导出</el-button>
          </template>
        </avue-crud>
      </div>
    </div>
  </div>
  <el-dialog title="部门充值人员统计" :visible.sync="deptUserVisible" width="70%" @close="this.deptUserVisible = false" :append-to-body="true">
    <avue-crud :option="deptUserOption"
               :data="deptUserData"
               :page="deptUserPage"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="deptUserForm"
               ref="deptUserForm"
               @search-change="searchChangeDeptUser"
               @search-reset="searchResetDeptUser"
               @selection-change="selectionChangeDeptUser"
               @current-change="currentChangeDeptUser"
               @size-change="sizeChangeDeptUser"
               @on-load="onLoadDeptUser">
      <template slot="menuLeft">
        <el-button v-if="permission.food_wallet_recharge_export && !this.unitSelect "class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportDeptUserFoodData">导出</el-button>
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >充值时间：{{this.timeSlot}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;部门：{{this.deptName}}
        </a>
      </template>
    </avue-crud>
  </el-dialog>
  </basic-container>
</template>

<script>
  import {getList,
    getDetail,
    add,
    update,
    remove,
    getMonthList,
    getYearList,
    getDeptList,
    getUserList,
    getDeptUserList,
    getMealsUserList,
    exportDateFoodData,
    exportMonthFoodData,
    exportYearFoodData,
    exportDeptFoodData,
    exportUserFoodData,
    exportDeptUserFoodData,
    exportMealsUserFoodData} from "@/api/queryStatistics/foodWalletRecharge";
  import {mapGetters} from "vuex";
  export default {
    props: {
        schoolId: String,
    },
    data() {
      return {
        activeIdx: 0,
        messList: [ '按日期统计', '按月份统计', '按年份统计','按部门统计','按充值人统计','按用餐人统计' ],
        form: {},
        query: {},
        monthForm:{},
        yearForm:{},
        deptForm:{},
        userForm:{},
        deptUserForm:{},
        mealsUserForm:{},
        loading: true,
        monthLoading:true,
        userLoading:true,
        deptUserVisible:false,
        timeSlot:undefined,
        deptName:undefined,
        startDate:undefined,
        endDate:undefined,
        mealsType:undefined,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        monthPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        yearPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        userPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptUserPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        mealsUserPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        searchForm:{},
        searchForm1:{},
        searchForm2:{},
        searchForm3:{},
        searchForm4:{},
        searchForm5:{},
        searchForm6:{},
        selectionList: [],
        deptData:[],
        userData:[],
        deptUserData:[],
        mealsUserData:[],
        option: {
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          /*  searchShow: true,*/
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name:'wxRechargeBalance1',
              type: 'sum'
            },
            {
              name:'serviceChargeBalance1',
              type: 'sum'
            },
            {
              name:'actuallyReceivedBalance1',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance',
              type: 'sum'
            },
            {
              name:'payAmount',
              type: 'sum'
            },
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
    /*        {
              label: '统计日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "日期",
              prop: "dateTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              align: 'center',
              sortable:true,
            },
        /*    {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              search:true,
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '伙食费微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
                {
                  label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                  prop: 'facultyRechargeBalance1',
                  align: 'center',
                  sortable:true,
                }]
            },
            {
              label: "伙食费微信充值金额",
              prop: "wxRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance",
              sortable:true,
              width:160,
            },
            {
              label: "客户实付金额合计",
              prop: "payAmount",
              sortable:true,
              width:160,
            }
          ]
        },
        monthOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name:'wxRechargeBalance1',
              type: 'sum'
            },
            {
              name:'serviceChargeBalance1',
              type: 'sum'
            },
            {
              name:'actuallyReceivedBalance1',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance1',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance1',
              type: 'sum'
            },
            {
              name:'payAmount1',
              type: 'sum'
            }

          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '月份',
              prop: 'monthDate',
              type:'month',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM',
              valueFormat: 'yyyy-MM',
              hide:true,
            },
    /*        {
              label: "月",
              prop: "monthDate",
              type: "month",
              mock:{
                type:'datetime',
                format:'yyyy-MM'
              },
              format:'yyyy-MM',
              valueFormat:'yyyy-MM',
              search:true,
              hide:true,
            },*/
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "月份",
              prop: "createTime",
              type: "datetime",
              format: "yyyy-MM",
              valueFormat: "yyyy-MM",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              sortable:true,
            },
     /*       {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              search:true,
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
              {
                label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'facultyRechargeBalance1',
                align: 'center',
                sortable:true,
              },
              {
                label: "微信充值总金额",
                prop: "wxRechargeBalance1",
                align: 'center',
                sortable:true,
              }]
            },

            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance1",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance1",
              sortable:true,
              width:160,
            },
            {
                label: "客户实付金额合计",
                prop: "payAmount1",
                sortable:true,
                width:160,
            }
          ]
        },
        yearOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name:'wxRechargeBalance1',
              type: 'sum'
            },
            {
              name:'serviceChargeBalance1',
              type: 'sum'
            },
            {
              name:'actuallyReceivedBalance1',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance1',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance1',
              type: 'sum'
            },
            {
              name:'payAmount1',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '年份',
              prop: 'yearDate',
              type:'year',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM',
              valueFormat: 'yyyy-MM',
              hide:true,
            },
/*            {
              label: "年",
              prop: "yearDate",
              type: "year",
              mock:{
                type:'datetime',
                format:'yyyy'
              },
              format:'yyyy',
              valueFormat:'yyyy',
              search:true,
              hide:true,
            },*/
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              hide:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "年份",
              prop: "dateTime",
              type: "datetime",
              format: "yyyy",
              valueFormat: "yyyy",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              sortable:true,
            },
/*            {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              search:true,
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '伙食费微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
                {
                  label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                  prop: 'facultyRechargeBalance1',
                  align: 'center',
                  sortable:true,
                }]
            },
            {
              label: "伙食费微信充值金额",
              prop: "wxRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance1",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance1",
              sortable:true,
              width:160,
            },
            {
              label: "客户实付金额合计",
              prop: "payAmount1",
              sortable:true,
              width:160,
            }
          ]
        },
        deptOption: {
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          /*  searchShow: true,*/
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          delBtn:false,
          editBtn:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name:'wxRechargeBalance1',
              type: 'sum'
            },
            {
              name:'serviceChargeBalance1',
              type: 'sum'
            },
            {
              name:'actuallyReceivedBalance1',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance1',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance1',
              type: 'sum'
            },
            {
              name:'payAmount1',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
   /*         {
              label: '统计日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "时间段",
              prop: "timeSlot",
              type: "input",
              width:200,
              sortable:true,
            },
    /*        {
              label: "单位",
              prop: "unitId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "学校",
              prop: "schoolId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },*/
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              hide:true,
              search:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              sortable:true,
            },
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              search:true,
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
                {
                  label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                  prop: 'facultyRechargeBalance1',
                  align: 'center',
                  sortable:true,
                }]
            },
            {
              label: "伙食费微信充值金额",
              prop: "wxRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance1",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance1",
              sortable:true,
            },
            {
              label: "客户实付金额合计",
              prop: "payAmount1",
              sortable:true,
              width:160,
            }
          ]
        },
        userOption: {
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          /*  searchShow: true,*/
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          searchSpan:100,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name:'wxRechargeBalance1',
              type: 'sum'
            },
            {
              name:'serviceChargeBalance1',
              type: 'sum'
            },
            {
              name:'actuallyReceivedBalance1',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance',
              type: 'sum'
            },
            {
              name:'payAmount',
              type: 'sum'
            },
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
    /*        {
              label: '统计日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "时间段",
              prop: "startAndEndDate",
              type: "input",
              width:200,
              sortable:true,
            },
            {
              label: "充值操作人",
              prop: "rechargeOperationName",
              type: "input",
              search: true,
              sortable:true,
            },
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              search:true,
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
                {
                  label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                  prop: 'facultyRechargeBalance1',
                  align: 'center',
                  sortable:true,
                }]
            },
            {
              label: "伙食费微信充值金额",
              prop: "wxRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance",
              sortable:true,
            },
            {
              label: "客户实付金额合计",
              prop: "payAmount",
              sortable:true,
              width:160,
            }
          ]
        },
        deptUserOption: {
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          delBtn:false,
          editBtn:false,
          selection: false,
          menu:false,
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              sortable:true,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 100,
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              sortable:true,
              //slot: true,
              dicData:[
                {
                  label: '男',
                  value: "1"
                },
                {
                  label: '女',
                  value: "2"
                }
              ],
              rules: [{
                required: true,
                message: '请选择性别',
                trigger: 'blur'
              }],
              width: 50,
            },
            {
              label: "学号/工号",
              prop: "studentJobNo",
              type: "input",
              sortable:true,
              rules: [{
                required: true,
                message: "请输入学号/工号",
                trigger: "blur"
              }],
            },
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
                {
                  label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                  prop: 'facultyRechargeBalance1',
                  align: 'center',
                  sortable:true,
                }]
            },
            {
              label: "伙食费微信充值金额",
              prop: "wxRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance1",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance1",
              sortable:true,
            },
            {
              label: "客户实付金额合计",
              prop: "payAmount1",
              sortable:true,
              width:160,
            }
          ]
        },
        mealsUserOption: {
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn:false,
          delBtn:false,
          editBtn:false,
          selection: false,
          menu:false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name:'wxRechargeBalance1',
              type: 'sum'
            },
            {
              name:'serviceChargeBalance1',
              type: 'sum'
            },
            {
              name:'actuallyReceivedBalance1',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance',
              type: 'sum'
            },
            {
              name:'rechargeTotalBalance1',
              type: 'sum'
            },
            {
              name:'actuallyTotalBalance1',
              type: 'sum'
            },
            {
              name:'payAmount1',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "开始时间",
              prop: "startDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },

            },
            {
              label: "结束时间",
              prop: "endDate",
              type: "datetime",
              hide: true,
              search: true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              mock: {
                type: 'datetime',
                format: 'yyyy-MM-dd',
                now: true,
              },
            },
     /*       {
              label: '统计日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },*/
            {
              label: "充值时间",
              prop: "timeSlot",
              type: "input",
              width:200,
              sortable:true,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              hide:true,
              search:true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
            },
            {
              label: "用餐人员",
              prop: "userName",
              type: "input",
              search:true,
              sortable:true,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: "学号/工号",
              prop: "studentJobNo",
              type: "input",
              sortable:true,
              width: 120,
              rules: [{
                required: true,
                message: "请输入学号/工号",
                trigger: "blur"
              }],
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
              sortable:true,
            },
            {
              label: "充值账户",
              prop: "rechargeType",
              type: "select",
              align: 'center',
              search:true,
              sortable:true,
              dicData: [
                {
                  label: "统缴餐钱包",
                  value: "0"
                },
                {
                  label: "自选餐钱包",
                  value: "1"
                }
              ],
            },
            {
              label: "伙食费现金充值金额",
              prop: "cashRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: '伙食费微信充值',
              align: 'center',
              children: [{
                label: '人员(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'stuRechargeBalance1',
                align: 'center',
                sortable:true,
              }, {
                label: '代充(1)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'parRechargeBalance1',
                align: 'center',
                sortable:true,
              },{
                label: '代充(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                prop: 'teaRechargeBalance1',
                align: 'center',
                sortable:true,
              },
                {
                  label: '人员(2)',//2023-02-03 需求更改人员类别，原来学生改为人员(1),教职工改为人员(2),家长代充改为代充(1),老师代充改为代充(2)
                  prop: 'facultyRechargeBalance1',
                  align: 'center',
                  sortable:true,
                }]
            },
            {
              label: "伙食费微信充值金额",
              prop: "wxRechargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信支付手续费",
              prop: "serviceChargeBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "微信充值单位实收",
              prop: "actuallyReceivedBalance1",
              align: 'center',
              sortable:true,
            },
            {
              label: "伙食费充值金额合计",
              prop: "rechargeTotalBalance1",
              sortable:true,
            },
            {
              label: "单位实收金额合计",
              prop: "actuallyTotalBalance1",
              sortable:true,
            },
            {
              label: "客户实付金额合计",
              prop: "payAmount1",
              sortable:true,
              width:160,
            }
          ]
        },
        data: [],
        monthData:[],
        yearData:[],
      }
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.food_wallet_recharge_add, false),
          viewBtn: this.vaildData(this.permission.food_wallet_recharge_view, false),
          delBtn: this.vaildData(this.permission.food_wallet_recharge_delete, false),
          editBtn: this.vaildData(this.permission.food_wallet_recharge_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      const { messName } = this.$route.query
      if(messName){
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log( this.activeIdx )
      }
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
   /*   if (this.userInfo.userType === 'school'){
        this.option.printBtn = true;
        this.monthOption.printBtn = true;
        this.yearOption.printBtn = true;
        this.deptOption.printBtn = true;
      }*/
 /*     if (this.userInfo.userType === 'school'){
        this.option.column[4].hide = true;
        this.monthOption.column[4].hide = true;
        this.yearOption.column[4].hide = true;
        this.option.column[5].hide = true;
        this.monthOption.column[5].hide = true;
        this.yearOption.column[5].hide = true;

        this.option.column[4].search = false;
        this.monthOption.column[4].search = false;
        this.yearOption.column[4].search = false;
        this.option.column[5].search = false;
        this.monthOption.column[5].search = false;
        this.yearOption.column[5].search = false;

        this.deptOption.column[3].hide = true;
        this.deptOption.column[3].hide = true;
        this.deptOption.column[3].hide = true;
        this.deptOption.column[4].hide = true;
        this.deptOption.column[4].hide = true;
        this.deptOption.column[4].hide = true;
        this.deptOption.column[3].search = false;
        this.deptOption.column[3].search = false;
        this.deptOption.column[3].search = false;

        this.deptOption.column[4].search = false;
        this.deptOption.column[4].search = false;
        this.deptOption.column[4].search = false;
      }else if (this.userInfo.userType === 'education'){
        this.deptOption.column[3].hide = true;
        this.deptOption.column[3].search = false;
        this.option.column[5].search = this.unitSelect;
        this.monthOption.column[5].search = this.unitSelect;
        this.yearOption.column[5].search = this.unitSelect;
        this.deptOption.column[5].search = false;
        this.option.column[4].hide = true;
        this.monthOption.column[4].hide = true;
        this.yearOption.column[4].hide = true;
        this.option.column[4].search = false;
        this.monthOption.column[4].search = false;
        this.yearOption.column[4].search = false;
      }else if (this.userInfo.userType === 'jiWei'){
        this.deptOption.column[4].hide = true;
        this.deptOption.column[4].search = false;
        this.deptOption.column[5].search =false;

        this.option.column[5].hide = true;
        this.monthOption.column[5].hide = true;
        this.yearOption.column[5].hide = true;

        this.option.column[5].search = false;
        this.monthOption.column[5].search = false;
        this.yearOption.column[5].search = false;
      }*/
    },
    methods: {
      menuClick(idx) {
        this.query = [];
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.page.currentPage = 1;
          this.onLoad(this.page);
        }
        if (idx == 1){
          this.monthPage.currentPage = 1;
          this.onLoadMonth(this.monthPage);
        }
        if (idx == 2){
          this.yearPage.currentPage = 1;
          this.onLoadYear(this.yearPage);
        }
        if (idx == 3){
          this.deptPage.currentPage = 1;
          this.onLoadDept(this.deptPage);
        }
        if (idx == 4){
          this.userPage.currentPage = 1;
          this.onLoadUser(this.userPage);
        }
        if (idx == 5){
          this.mealsUserPage.currentPage = 1;
          this.onLoadMealsUser(this.mealsUserPage);
        }
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoad(this.page);
      },
      searchResetMonth() {
        this.query = {};
        this.searchForm1 = {};
        this.onLoadMonth(this.monthPage);
      },
      searchResetYear() {
        this.query = {};
        this.searchForm2 = {};
        this.onLoadYear(this.yearPage);
      },
      searchResetDept() {
        this.query = {};
        this.searchForm3 = {};
        this.onLoadDept(this.deptPage);
      },
      searchResetUser() {
        this.query = {};
        this.searchForm4 = {};
        this.onLoadUser(this.userPage);
      },
      searchResetMealsUser() {
        this.query = {};
        this.searchForm6 = {};
        this.onLoadMealsUser(this.mealsUserPage);
      },
      searchResetDeptUser() {
        this.query = {};
        this.searchForm5 = {};
        this.onLoadDeptUser(this.deptUserPage);
      },
      searchChange(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm = params;
        this.query = params;
        this.page.currentPage = 1
        params.beginDate = params.startDate;
        params.endDate = params.endDate;
        this.searchForm.beginDate = params.startDate;
        this.searchForm.endDate = params.endDate;
   /*     if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.beginDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
          this.searchForm.beginDate = params.queryDate[0];
          this.searchForm.endDate = params.queryDate[1];
        }*/
        this.onLoad(this.page, params);
        done();
      },
      searchChangeMonth(params, done) {
        this.searchForm1 = params;
        this.query = params;
        this.monthPage.currentPage = 1
        this.onLoadMonth(this.monthPage, params);
        done();
      },
      searchChangeYear(params, done) {
        this.searchForm2 = params;
        this.query = params;
        this.yearPage.currentPage = 1
        this.onLoadYear(this.yearPage, params);
        done();
      },
      searchChangeDept(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm3.startDate = params.startDate;
        this.searchForm3.endDate = params.endDate;
        this.searchForm3 = params;
        this.query = params;
    /*    if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.startDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
          this.searchForm3.startDate = params.queryDate[0];
          this.searchForm3.endDate = params.queryDate[1];
        }*/
        this.deptPage.currentPage = 1
        this.onLoadDept(this.deptPage, params);
        done();
      },
      searchChangeMealsUser(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm6.startDate = params.startDate;
        this.searchForm6.endDate = params.endDate;
        this.searchForm6 = params;
        this.query = params;
     /*   if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.startDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
          this.searchForm6.startDate = params.queryDate[0];
          this.searchForm6.endDate = params.queryDate[1];
        }*/
        this.mealsUserPage.currentPage = 1
        this.onLoadMealsUser(this.mealsUserPage, params);
        done();
      },
      searchChangeDeptUser(params, done) {
        this.searchForm5 = params;
        this.query = params;
        this.deptUserPage.currentPage = 1
        this.onLoadDeptUser(this.deptUserPage, params);
        done();
      },
      searchChangeUser(params, done) {
        if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
          if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
            done();
            return this.$message.error('结束时间不能为空');
          }
        }
        if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
          if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
            done();
            return this.$message.error('开始时间不能为空');
          }else {
            var startDateTime = new Date(params.startDate);
            var endDateTime = new Date(params.endDate);
            if (startDateTime.getTime() > endDateTime.getTime()){
              done();
              return this.$message.error('开始时间不能大于结束时间');
            }
          }
        }
        this.searchForm4.startDate = params.startDate;
        this.searchForm4.endDate = params.endDate;
        this.searchForm4 = params;
        this.query = params;
/*        if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.startDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
          this.searchForm4.startDate = params.queryDate[0];
          this.searchForm4.endDate = params.queryDate[1];
        }*/
        this.userPage.currentPage = 1
        this.onLoadUser(this.userPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChangeMonth(list) {
        this.selectionList = list;
      },
      selectionChangeYear(list) {
        this.selectionList = list;
      },
      selectionChangeDept(list) {
        this.selectionList = list;
      },
      selectionChangeDeptUser(list) {
        this.selectionList = list;
      },
      selectionChangeMealsUser(list) {
        this.selectionList = list;
      },
      selectionChangeUser(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      selectionClearMonth() {
        this.selectionList = [];
        this.$refs.monthForm.toggleSelection();
      },
      selectionClearYear() {
        this.selectionList = [];
        this.$refs.yearForm.toggleSelection();
      },
      selectionClearDept() {
        this.selectionList = [];
        this.$refs.deptForm.toggleSelection();
      },
      selectionClearDeptUser() {
        this.selectionList = [];
        this.$refs.deptUserForm.toggleSelection();
      },
      selectionClearMealsUser() {
        this.selectionList = [];
        this.$refs.mealsUserForm.toggleSelection();
      },
      selectionClearUser() {
        this.selectionList = [];
        this.$refs.userForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeMonth(currentPage){
        this.monthPage.currentPage = currentPage;
      },
      currentChangeYear(currentPage){
        this.yearPage.currentPage = currentPage;
      },
      currentChangeDept(currentPage){
        this.deptPage.currentPage = currentPage;
      },
      currentChangeDeptUser(currentPage){
        this.deptUserPage.currentPage = currentPage;
      },
      currentChangeMealsUser(currentPage){
        this.mealsUserPage.currentPage = currentPage;
      },
      currentChangeUser(currentPage){
        this.userPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeMonth(pageSize){
        this.monthPage.pageSize = pageSize;
      },
      sizeChangeYear(pageSize){
        this.yearPage.pageSize = pageSize;
      },
      sizeChangeDept(pageSize){
        this.deptPage.pageSize = pageSize;
      },
      sizeChangeDeptUser(pageSize){
        this.deptUserPage.pageSize = pageSize;
      },
      sizeChangeMealsUser(pageSize){
        this.mealsUserPage.pageSize = pageSize;
      },
      sizeChangeUser(pageSize){
        this.userPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.canteenId = this.schoolId;
        }
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoadMonth(page, params = {}) {
        this.monthLoading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.unitId = this.schoolId;
        }
        getMonthList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.monthPage.total = data.total;
          this.monthData = data.records;
          this.monthLoading = false;
          this.selectionClearMonth();
        });
      },
      onLoadYear(page, params = {}) {
        this.yearLoading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.unitId = this.schoolId;
        }
        getYearList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.yearPage.total = data.total;
          this.yearData = data.records;
          this.yearLoading = false;
          this.selectionClearYear();
        });
      },
      onLoadDept(page, params = {}) {
        this.deptLoading = true;
        if(this.schoolId != null && this.schoolId != ''){
            params.unitId = this.schoolId;
        }
        getDeptList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptPage.total = data.total;
          this.deptData = data.records;
          this.deptLoading = false;
          this.selectionClearDept();
        });
      },
      onLoadDeptUser(page, params = {}) {
        params.startDate = this.startDate;
        params.endDate = this.endDate;
        params.deptId = this.deptId;
        params.mealsType = this.mealsType;
        getDeptUserList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptUserPage.total = data.total;
          this.deptUserData = data.records;
          this.selectionClearDeptUser();
        });
      },
      onLoadUser(page, params = {}) {
        this.userLoading = true;
        if(this.schoolId != null && this.schoolId != ''){
          params.unitId = this.schoolId;
        }
        getUserList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.userPage.total = data.total;
          this.userData = data.records;
          this.userLoading = false;
          this.selectionClearUser();
        });
      },
      onLoadMealsUser(page, params = {}) {
        if(this.schoolId != null && this.schoolId != ''){
          params.unitId = this.schoolId;
        }
        getMealsUserList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.mealsUserPage.total = data.total;
          this.mealsUserData = data.records;
          this.selectionClearMealsUser();
        });
      },
      exportRechargeDetailData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDateFoodData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportDeptFoodData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDeptFoodData(this.searchForm3).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportUserFoodData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportUserFoodData(this.searchForm4).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportRechargeDetailData2(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportMonthFoodData(this.searchForm1).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportRechargeDetailData3(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportYearFoodData(this.searchForm2).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportDeptUserFoodData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDeptUserFoodData(this.searchForm5).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportMealsUserFoodData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportMealsUserFoodData(this.searchForm6).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      openDeptUser(row){
        var arr = row.timeSlot.split(" - ");
        this.startDate = arr[0];
        this.endDate = arr[1];
        this.mealsType = row.mealsType;
        this.deptId = row.deptId;
        this.timeSlot = row.timeSlot;
        this.deptName = row.deptName;
        this.searchForm5.startDate = arr[0];
        this.searchForm5.endDate = arr[1];
        this.searchForm5.mealsType = row.mealsType;
        this.searchForm5.deptId = row.deptId;
        this.searchForm5.timeSlot = row.timeSlot;
        this.deptUserPage.currentPage = 1;
        this.onLoadDeptUser(this.deptUserPage);
        this.deptUserVisible = true;
      }
    }
  }
</script>

<style lang="scss" scoped>
  .all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      ::v-deep div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20rpx;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
