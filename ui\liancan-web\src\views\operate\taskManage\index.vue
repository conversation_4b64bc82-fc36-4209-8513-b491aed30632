<template>
  <basic-container>
    <avue-crud
               :defaults.sync="defaults"
               :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menu" slot-scope="scope">
        <el-button size="mini" type="text" icon="el-icon-check" @click="checkTask(scope.row)">查看任务进度
        </el-button>
        <el-button size="mini" type="text" icon="el-icon-delete" @click="delTask(scope.row)">删除任务
        </el-button>
      </template>
    </avue-crud>

    <el-dialog title="任务进度" :visible.sync="scheduleVisible" :append-to-body="true" @close="scheduleVisible=false" width="70%">
      <avue-crud ref="scheduleForm"
                 :option="scheduleOption"
                 :table-loading="scheduleLoading"
                 :data="scheduleData"
                 :page="schedulePage"
                 v-model="scheduleForm"
                 @selection-change="scheduleSelectionChange"
                 @current-change="scheduleCurrentChange"
                 @size-change="scheduleSizeChange">
        <template slot="menu" slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-delete" @click="delScheduleTask(scope.row)">删除任务
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, getScheduleList, removeSchedule} from "@/api/operate/taskManage";
import {mapGetters} from "vuex";
var DIC = {
  TASK_TYPE: [
    {
      label: "批量升级",
      value: 0
    },
    {
      label: "按设备升级",
      value: 1
    },
  ]
}
export default {
  data() {
    return {
      defaults: {},
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: true,
        delBtn: false,
        selection: false,
        labelWidth: 150,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "版本名称",
            prop: "versionTitle",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "版本号",
            prop: "versionNo",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "APP版本号",
            prop: "versionId",
            type: "select",
            rules: [{
              required: true,
              message: "请选择APP版本号",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-system/version/all",
            props: {
              label: "versionNo",
              value: "id"
            },
            width: 100,
            hide: true,
          },
          {
            label: "任务类型",
            prop: "type",
            type: "select",
            dicData: DIC.TASK_TYPE,
            rules: [{
              required: true,
              message: "请选择任务类型",
              trigger: "blur"
            }],
            // change: ({value,column})=>{
            //   this.selectType = value
            //   console.log('change =============== ', this.selectType)
            // }
          },
          // {
          //   label: "升级单位",
          //   prop: "deptId",
          //   type: "tree",
          //   dicUrl: "/api/service/rabbit-system/dept/school/tree",
          //   props: {
          //     label: "deptName",
          //     value: "id"
          //   },
          //   hide: true,
          //   addDisplay: false,
          //   editDisplay: false,
          //   viewDisplay: false,
          //   rules: [{
          //     required: true,
          //     message: "请选择升级单位",
          //     trigger: "click"
          //   }],
          // },
          {
            label: "限定升级的食堂",
            prop: "canteenIdList",
            type: "tree",
            //dicData: [],
            multiple: true,
            hide: true,
            dicUrl: "/api/service/rabbit-system/dept/canteen/selectTree",
            props: {
              //res: "data",
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            checkStrictly: true,
            overHidden: true,
            search: true,
            rules: [{
              required: true,
              message: "请选择限定升级的食堂",
              trigger: "click"
            }],
          },
          {
            label: "设备编号",
            prop: "deviceCodes",
            type: "textarea",
            placeholder: "请输入设备编号，如果有多个设备编号，用【,】间隔",
            slot: true,
            rules: [{
              required: true,
              message: "请输入取现原因",
              trigger: "blur"
            }],
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "升级设备数",
            prop: "count",
            type: "input",
            addDisplay: false,
            editDisplay: false,
          },
          {
            label: '发布时间',
            prop: 'createTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
        ]
      },
      data: [],
      checkTaskId: '',
      scheduleVisible: false,
      scheduleForm: {},
      scheduleLoading: true,
      schedulePage: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      scheduleSelectionList: [],
      scheduleData: [],
      scheduleOption: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        editBtn: false,
        addBtn: false,
        delBtn: false,
        selection: false,
        labelWidth: 150,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "设备编号",
            prop: "equipmentCode",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "窗口名称",
            prop: "windowName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "所属单位",
            prop: "deptName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "所属食堂",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: "当前APP版本",
            prop: "appVersion",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            overHidden: true,
          },
          {
            label: '升级完成时间',
            prop: 'completeTime',
            type: 'datetime',
            format: 'yyyy-MM-dd HH:mm:ss',
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
          },
        ]
      }
    }
  },
  computed: {
    ...mapGetters(["permission"]),
  },
  watch: {
    'form.type'() {
      if (this.form.type !== '' && this.form.type !== undefined) {
        // const index1 = this.$refs.crud.findColumnIndex("deptId");
        const index1 = this.$refs.crud.findColumnIndex("canteenIdList");
        const index2 = this.$refs.crud.findColumnIndex("deviceCodes");
        if (this.form.type == 0) {
          this.option.column[index1].addDisplay = true;
          this.option.column[index2].addDisplay = false;
        } else if (this.form.type == 1) {
          this.option.column[index1].addDisplay = false;
          this.option.column[index2].addDisplay = true;
        }
      }
    }
  },
  methods: {
    rowUpdate(row, index, loading, done) {

    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log("getList ======== ", res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      })
    },
    scheduleSelectionChange(list) {
      this.scheduleSelectionList = list;
    },
    scheduleCurrentChange(currentPage) {
      this.schedulePage.currentPage = currentPage;
    },
    scheduleSizeChange(pageSize) {
      this.schedulePage.pageSize = pageSize;
    },
    scheduleOnLoad(page, params = {}) {
      this.scheduleLoading = true;
      params.taskId = this.checkTaskId;
      getScheduleList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getScheduleList ======== ', res)
        const data = res.data.data;
        this.schedulePage.total = data.total;
        this.scheduleData = data.records;
        this.scheduleLoading = false;
      })
    },
    checkTask(row) {
      this.checkTaskId = row.id;
      this.schedulePage.currentPage = 1;
      this.scheduleOnLoad(this.schedulePage);
      this.scheduleVisible = true;
    },
    delTask(row) {
      this.$confirm("确定删除该任务?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return remove(row.id);
      }).then(() => {
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
    },
    delScheduleTask(row) {
      this.$confirm("确定删除该任务?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return removeSchedule(row.id, this.checkTaskId);
      }).then(() => {
        this.scheduleOnLoad(this.schedulePage);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      });
    }
  }
}
</script>

<style lang="scss">

</style>
