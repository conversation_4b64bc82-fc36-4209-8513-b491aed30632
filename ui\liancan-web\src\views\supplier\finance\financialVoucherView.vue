<template>
  <div class="charge-container" id="chargeContainer">
    <div class="charge">
      <el-row type="flex" class="row-bg" justify="center">
        <h1 class="charge-title">记账凭证</h1>
      </el-row>
      <div class="charge-header">
        <div>
          凭证字
          <el-select style="width: 80px;" v-model="voucher.word" disabled="true">
            <el-option
              v-for="item in wordList"
              :key="item.name"
              :label="item.name"
              :value="item.name">
            </el-option>
          </el-select>
        </div>
        <div>
          凭证号
          <el-input type="input" readonly="true" style="width: 80px;" min="1" v-model="voucher.no" disabled="true"></el-input>
        </div>
        <div>
          日期
          <el-date-picker type="date" readonly="true" style="width: 145px;" :picker-options="expireTimeOption" v-model="voucher.date" disabled="true"></el-date-picker>
        </div>
        <div>
<!--          显示第几期-->
          <h4 class="charge-title" style="margin-left: 10px;">{{voucherDate}}</h4>
        </div>
        <div>
          <!--          显示审核状态-->
          <h2 class="charge-title" style="margin-left: 10px;color: red">{{voucherAuditStatus}}</h2>
        </div>
        <div style="float: right;position: relative;">
          附单据
              <el-input style="width: 80px;" disabled="false" readonly="readonly" v-model="voucher.bill"></el-input>
          张
          <el-button type="primary" @click="showAttachmentDialog" size="small" plain>管理</el-button>
          <i class="el-icon-info"></i>
          <div class="tip-box">
            <table class="tip-box-table" border="1">
              <tr style="background-color: #f5f4f4;">
                <td>操作</td>
                <td>快捷键</td>
              </tr>
              <tr>
                <td>选择列表</td>
                <td>F7</td>
              </tr>
              <tr>
                <td>自动平衡</td>
                <td>=</td>
              </tr>
              <tr>
                <td>新增</td>
                <td>F4</td>
              </tr>
              <tr>
                <td>保存</td>
                <td>Ctrl+S</td>
              </tr>
              <tr>
                <td>保存并审核</td>
                <td>F10</td>
              </tr>
              <tr>
                <td>保存并新增</td>
                <td>F11</td>
              </tr>
              <tr>
                <td>复制上一行</td>
                <td>//</td>
              </tr>
              <tr>
                <td>复制上一行摘要</td>
                <td>..</td>
              </tr>
              <tr>
                <td>金额自动切换借贷方</td>
                <td>空格键</td>
              </tr>
              <tr>
                <td>单据头与分录快速切换</td>
                <td>Tab</td>
              </tr>
            </table>
          </div>
        </div>
      </div>
      <table class="charge-table" border="1">
        <tr>
          <td width="6%">操作</td>
          <td :width="isAuxiliary?'14%':'22%'">摘要</td>
          <td :width="isAuxiliary?'15%':'22%'">会计科目</td>
          <td width="14%" v-if="isAuxiliary">辅助核算</td>
          <td width="50%">
            <table style="height: 50px;">
              <tr style="border-bottom: 1px solid #bab9b9;">
                <td width="50%" style="border-right: 1px solid #bab9b9;">借方金额</td>
                <td width="50%">贷方金额</td>
              </tr>
              <tr>
                <td style="border-right: 1px solid #bab9b9;">
                  <table class="debtor-lender-table" style="height: 100%;">
                    <tr>
                      <td>亿</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>万</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>元</td>
                      <td>角</td>
                      <td>分</td>
                    </tr>
                  </table>
                </td>
                <td>
                  <table class="debtor-lender-table" style="height: 100%;">
                    <tr>
                      <td>亿</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>万</td>
                      <td>千</td>
                      <td>百</td>
                      <td>十</td>
                      <td>元</td>
                      <td>角</td>
                      <td>分</td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr v-for="(item,index) in list" v-bind:key="item">
          <td>
            <div @click="addList" class="charge-table-icon">
              <i class="el-icon-plus" style="color: orangered;"></i>
            </div>
            <div @click="removeList(index)" class="charge-table-icon">
              <i class="el-icon-close" style="color: #4a90e2;"></i>
            </div>
          </td>
          <td>
            <div @click="showInput(index,'main')" v-if="!item.isShowMainInput" class="main-subject">
              <textarea readonly="readonly" v-model="item.main"></textarea>
            </div>
            <div class="main-subject" v-if="item.isShowMainInput">
              <textarea readonly="readonly" v-model="item.main" @blur="hideInput(index,'main')" v-focus
                        @keyup="keyupEvents(index,$event,1)"></textarea>
              <div class="dropdown-menu suggest-list special-elements" style="top: 60px;">
                <ul class="item-list">
                  <li v-for="main in mainList" v-bind:key="main" v-if="main.name.indexOf(item.main.trim())!=-1"
                      :class="main.name==item.main?'hover':''"
                      @click="voluationInput(index,'main',main.name)">{{ main.name }}
                  </li>
                </ul>
              </div>
            </div>
<!--            <i v-if="item.isShowMainInput" class="el-icon-more special-elements"-->
<!--               @click="selectionList(index,'main')"></i>-->
          </td>
          <td>
            <div @click="showInput(index,'subject')" v-if="!item.isShowSubjectInput" class="main-subject">
              <!--{{item.subject}}-->
              <textarea v-model="item.subject.name" readonly="readonly"></textarea>
            </div>
            <div class="main-subject" v-if="item.isShowSubjectInput">
              <!--<input type="text" v-model="item.subject" @blur="hideInput(index,'subject')" v-focus @keyup="keyupEvents(index,$event,2)">-->
              <textarea v-model="item.subject.name" @blur="hideInput(index,'subject')" v-focus
                        @keyup="keyupEvents(index,$event,2)" readonly="readonly"></textarea>
              <div class="dropdown-menu suggest-list special-elements" style="top: 60px;">
                <ul class="item-list">
                  <li v-for="subject in subjectList" v-bind:key="subject" v-if="subject.name.indexOf(item.subject.name.trim())!=-1"
                      :class="subject.name==item.subject.name?'hover':''"
                      @click="voluationInput(index,'subject',subject)">
                    {{ subject.name }}
                  </li>
                </ul>
              </div>
            </div>
<!--            <i v-if="item.isShowSubjectInput" class="el-icon-more special-elements"-->
<!--               @click="selectionList(index,'subject')"></i>-->
          </td>
          <td v-if="isAuxiliary" :disabled="!item.isAuxiliary" :class="item.isAuxiliary?'':'td-auxiliary-dis'">
            <div class="auxiliary-accounting" v-if="item.isAuxiliary" @click="selectionList(index,'auxiliary')">
              <div v-if="item.auxiliary && item.auxiliary!=''" class="auxiliary-single">
                <span>{{ item.auxiliary }}</span>
                <i class="el-icon-close" @click.stop="clearAuxiliary(index,$event)"></i>
              </div>
            </div>
          </td>
          <td>
            <table>
              <tr>
                <td width="50%" style="border-right: 1px solid #bab9b9;">
                  <table class="debtor-tbale debtor-lender-table">
                    <tr @click="showInput(index,'debtor')"
                        :class="item.debtor*1<0?'tr-negative':''">
                      <td v-for="debtor in item.debtorList" v-bind:key="debtor">{{ debtor }}</td>
                    </tr>

<!--                    <tr v-if="item.isShowDebtorInput">-->
<!--                      <input type="number" readonly @blur="hideInput(index,'debtor')" @keyup="debtorInputKeyUp(index,$event,3)"-->
<!--                             v-model="item.debtor" maxlength="12"-->
<!--                             v-focus>-->
<!--                    </tr>-->
                  </table>
                </td>
                <td width="50%">
                  <table class="lender-tbale debtor-lender-table">
                    <tr  @click="showInput(index,'lender')"
                        :class="item.lender*1<0?'tr-negative':''">
                      <td v-for="lender in item.lenderList" v-bind:key="lender">{{ lender }}</td>
                    </tr>

<!--                    <tr v-if="item.isShowLenderInput">-->
<!--                      <input type="number" readonly @blur="hideInput(index,'lender')" @keyup="lenderInputKeyUp(index,$event,4)"-->
<!--                             v-model="item.lender" maxlength="11"-->
<!--                             v-focus>-->
<!--                    </tr>-->
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
        <tr>
          <td :colspan="isAuxiliary?4:3" style="text-align: left;padding-left: 10px;">合计：</td>
          <td>
            <table>
              <tr>
                <td width="50%" style="border-right: 1px solid #bab9b9;">
                  <table class="debtor-tbale debtor-lender-table">
                    <tr>
                      <td v-for="debtor in debtorTotalList" v-bind:key="debtor">{{ debtor }}</td>
                    </tr>
                  </table>
                </td>
                <td width="50%">
                  <table class="lender-tbale debtor-lender-table">
                    <tr>
                      <td v-for="lender in lenderTotalList" v-bind:key="lender">{{ lender }}</td>
                    </tr>
                  </table>
                </td>
              </tr>
            </table>
          </td>
        </tr>
      </table>
      <el-row>
        <el-col :span="4">
          <div style="height: 50px;line-height: 50px;">制单人：{{this.uInfo.nickname}}</div>
        </el-col>
        <el-col :span="2">
          <div style="height: 50px;line-height: 50px;">审核人：</div>
        </el-col>
        <el-col :span="10">
          <div style="height: 50px;line-height: 50px;">
            <el-select disabled="false" v-model="auditUser" filterable  placeholder="请选择">
              <el-option
                v-for="item in auditUsersList"
                :key="item"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </div>
        </el-col>
        <el-col :span="8"></el-col>
      </el-row>
    </div>
    <el-dialog title="会计科目" width="30%" :visible.sync="dialogSubjectVisible">
      <subject-selector @closeSubjectDialog="closeSubjectDialog" ref="subjectSelector"></subject-selector>
      <span slot="footer" class="dialog-footer">
            <el-button @click="closeSubjectDialog">取消</el-button>
            <el-button class="filter-item" style="margin-left: 10px;" type="primary" icon="edit">确认</el-button>
          </span>
    </el-dialog>
    <el-dialog title="凭证附件管理"
               append-to-body
               :visible.sync="attachmentShow"
               width="60%">
<!--        <el-upload-->
<!--          ref="uploadRef"-->
<!--          action="/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan"-->
<!--          list-type="picture-card"-->
<!--          :file-list="attachmentList.attachmentFileList"-->
<!--          :before-upload="handleBeforeUpload"-->
<!--          :on-success="handleSuccess"-->
<!--          :on-preview="handlePictureCardPreview"-->
<!--          :on-remove="handleRemove">-->
<!--          <i class="el-icon-plus"></i>-->
<!--        </el-upload>-->
        <!-- <div class="block" v-for="att in attachmentList.attachmentFileList" :key="att">
          <el-image
            style="width: 100px; height: 100px"
            :src="att.url"
            fit="fill">
          </el-image>
        </div> -->

        <div class="block" v-for="(att, index) in attachmentList.attachmentFileList" :key="att">
          <img width="1300px" style="margin-right:20px" :src="att.url" @click="openPreview(index)" />
        </div>

        <el-dialog :visible.sync="attachmentList.dialogVisible">
          <img width="100%" :src="attachmentList.dialogImageUrl" alt="">
        </el-dialog>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="attachmentShow = false">取 消</el-button>
      </span>
    </el-dialog>
    <div v-loading="loading" class="dashboard-container"></div>
  </div>
</template>

<script>
import Vue from 'vue';
import {mapGetters, mapState} from "vuex";
import {
  addObj,
  getObj,
  putObj,
  getWordList,
  getMainList,
  getSubjectList,
  getAuditUserList,
  getVoucherNo, getVoucherBase, getFinancialPeriod,
} from '@/api/supplier/finance/voucher';
import {openAccount} from "@/api/supplier/finance/accountSets";
import {collatingData, formatDate} from "@/api/supplier/finance/financialUtils";

Vue.directive('focus', {
  // 当绑定元素插入到 DOM 中。
  inserted: function (el) {
    // 聚焦元素
    el.focus();
  }
});

export default {
  name: "voucherForm",
  props: ['id'],
  components: {
    'main-selector': () => import('@/components/finance/mainSelector'),
    'subject-selector': () => import('@/components/finance/subjectSelector'),
    'auxiliary-selector': () => import('@/components/finance/auxiliarySelector'),
  },
  watch: {//监听
    $route(to,from) { //路由变化方式，路由发生编号，方法就会执行
      this.init();
    }
  },
  data() {
    return {
      auditStatus:0,
      loadingCount:0,
      imgType: ['jpg','jpeg','png','bmp','gif'],
      attachmentList: {
        dialogImageUrl: '',
        dialogVisible: false,
        attachmentFileList: [],
      },
      attachmentShow: false,
      uInfo:{},
      query: {},
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      curUser: {name: ''},
      auditUsersList: [],
      auditUser: '',
      voucher: {
        word: '记', no: 1, date: new Date(), bill: 0, lastPeriod:''
      },
      expireTimeOption: {},
      list: [
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        },
        {
          main: '',
          isShowMainInput: false,
          subject: {
            number: '',
            name: '',
            code: '',
            detailJson: ''
          },
          isShowSubjectInput: false,
          debtor: '',
          debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowDebtorInput: false,
          lender: '',
          lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
          isShowLenderInput: false,
          isAuxiliary: false,
          auxiliary: ''
        }
      ],
      debtorTotal: 0,
      debtorTotalList: ['', '', '', '', '', '', '', '', 0, 0, 0],
      lenderTotal: 0,
      lenderTotalList: ['', '', '', '', '', '', '', '', 0, 0, 0],
      wordList: [],
      mainList: [],
      subjectList: [],
      dialogMainVisible: false,
      dialogSubjectVisible: false,
      dialogAuxiliaryVisible: false,
      dialogMainIndex: 0,
      dialogSubjectIndex: 0,
      dialogAuxiliaryIndex: 0,
      isAuxiliary: false,
      saveVisible: true,
      billNo: '',
      accountSetsId: '',
    }
  },
  mounted() {

  },
  created() {
    document.addEventListener('keydown', this.handleEvent);
    this.uInfo = this.userInfo;
    // this.changeVoucherNo();
    // getWordList().then(response => {
    //   this.wordList = response.data.data;
    // }).catch(err=>{
    //   console.log("getWordList: ",err);
    // }).finally((fin)=>{
    // });
    // getMainList().then(response => {
    //   this.mainList = response.data.data;
    // });
    this.accountSetsId = this.$route.query.accountSetsId;
    getAuditUserList(this.accountSetsId).then(response => {
      this.auditUsersList = response.data.data;
    }).catch(err=>{
      console.log("getAuditUserList: ",err);
    }).finally((fin)=>{
    });
    // getFinancialPeriod().then(response => {
    //   var constant1 = response.data.data;
    //   if(constant1!=null && constant1 !=undefined) {
    //     var enaleTime = constant1.enaleTime;//帐套启用年月
    //     var startTime = constant1.startTime;//当前记帐年月
    //     if(startTime!=null && startTime != undefined && startTime.length >0) {
    //       var startTimeDate = startTime + "-01";
    //       this.Constant.startTime = startTimeDate;
    //     }
    //   }
    // }).catch(err=>{
    //   console.log("getFinancialPeriod: ",err);
    // }).finally((fin)=>{
    // });
    this.init();
    // getSubjectList().then(response => {
    //   // this.subjectList = response.data.data;
    //   this.subjectData = response.data.data;
    //   console.log("subjectData==========over");
    // }).catch(err=>{
    //   console.log("getSubjectList: ",err);
    // }).finally((fin)=>{
    //   this.loadingCount = 99;
    // });
  },
  updated() {
    //给特定区域添加ID   绑定onmousedown 事件
    // var chargeContainer = document.getElementById('chargeContainer');
    // var outDiv = chargeContainer.getElementsByClassName('special-elements');
    // for (var i = 0; i < outDiv.length; i++) {
    //   outDiv[i].onmousedown = function (e) {
    //     //现代浏览器阻止默认事件
    //     if (e && e.preventDefault)
    //       e.preventDefault();
    //     //IE阻止默认事件
    //     else
    //       window.event.returnValue = false;
    //     return false;
    //   }
    // }
  },
  computed: {
    ...mapGetters(["userInfo"]),
    loading() {
      if(this.loadingCount == 99) {
        return false;
      }
      return true;
    },
    voucherDate() {
      if(this.voucher.date!=undefined) {

        var strs = formatDate("yyyy-MM-dd", this.voucher.date).split("-");
        return strs[0] + "年第" + strs[1] + "期";
      }else{
        return "";
      }

    },
    voucherAuditStatus() {
      if(this.auditStatus==1){
        return "已审核";
      }else {
        return "未审核";
      }
    },
  },
  methods: {
    openPreview(index = 0) {
      this.$ImagePreview(this.attachmentList.attachmentFileList, index,{
        closeOnClickModal: true,
        click:(data,index)=>{
//          this.$message.success('点击事件'+index)
        },
        beforeClose:()=>{
//          this.$message.success('关闭回调')
        }
      });
    },
    saveAttachmentHandle() {

    },
    handleBeforeUpload(file,fileList) {

    },
    handleSuccess(res,file,fileList) {

    },
    handleRemove(file, fileList) {

    },
    handlePictureCardPreview(file) {
      this.attachmentList.dialogImageUrl = file.url;
      this.attachmentList.dialogVisible = true;
    },
    showAttachmentDialog() {
      this.attachmentShow = true;
    },
    changeVoucherNo() {

    },
    judgeIsAuxiliary() {
      var flag = false;
      this.isAuxiliary = flag;
    },
    openMainDialog() {
      this.dialogMainVisible = true
    },
    closeMainDialog(main) {
      if (!main.isTrusted) {
        this.list[this.dialogMainIndex].main = main;
      }
      this.dialogMainVisible = false
    },
    openSubjectDialog() {
      this.dialogSubjectVisible = true
    },
    closeSubjectDialog(sub) {
      if (!sub.isTrusted) {
        this.list[this.dialogSubjectIndex].subject = sub;
      }
      this.dialogSubjectVisible = false;
      this.judgeIsAuxiliary();
    },
    openAuxiliaryDialog() {
      this.dialogAuxiliaryVisible = true
    },
    closeAuxiliaryDialog(aux) {
      if (!aux.isTrusted) {
        this.list[this.dialogAuxiliaryIndex].auxiliary = aux;
      }
      this.dialogAuxiliaryVisible = false;
    },
    clearAuxiliary(index, e) {
      this.list[index].auxiliary = '';
    },

    handleEvent(event) {
      //console.log(event);
      if (window.location.hash == '#/general_ledger/voucher_entry') {
        if (event.keyCode === 83 && event.ctrlKey) {
          //console.log('拦截到83+ctrl');
          this.preservation(2);
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 115) {
          //console.log('拦截到115');//F4
          this.addList();
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 121) {
          //console.log('拦截到121');//F10
          event.preventDefault();
          event.returnValue = false;
          return false;
        } else if (event.keyCode === 122) {
          //console.log('拦截到122');//F11
          event.preventDefault();
          event.returnValue = false;
          return false;
        }
      } else {
        //需要销毁事件 防止全局生效
        //document.removeEventListener('keydown', this.handleEvent);
      }

    },
    showInput(index, type) {
      for (var i in this.list) {
        this.list[i].isShowDebtorInput = false;
        this.list[i].isShowLenderInput = false;
        this.list[i].isShowMainInput = false;
        this.list[i].isShowSubjectInput = false;

        if (i == index && type == 'debtor') {
          this.list[index].isShowDebtorInput = true;
        } else if (i == index && type == 'lender') {
          this.list[index].isShowLenderInput = true;
        } else if (i == index && type == 'main') {
          this.list[index].isShowMainInput = false;
        } else if (i == index && type == 'subject') {
          this.list[index].isShowSubjectInput = true;
        }
      }
    },
    hideInput(index, type) {
      var inx = 0;
      if (type == 'debtor') {
        //判断是否有小数点
        inx = this.list[index].debtor.indexOf('.');
        if (inx != -1 && this.list[index].debtor.length - 1 != inx) {
          this.list[index].debtor = (this.list[index].debtor * 1).toFixed(2);
        }
        this.list[index].isShowDebtorInput = false;
      } else if (type == 'lender') {
        //判断是否有小数点
        inx = this.list[index].lender.indexOf('.');
        if (inx != -1 && this.list[index].lender.length - 1 != inx) {
          this.list[index].lender = (this.list[index].lender * 1).toFixed(2);
        }
        this.list[index].isShowLenderInput = false;
      } else if (type == 'main') {
        this.list[index].isShowMainInput = false;
      } else if (type == 'subject') {
        this.list[index].isShowSubjectInput = false;
      }
    },
    voluationInput(index, type, val) {
      if (type == 'main') {
        this.list[index].main = val;
        this.list[index].isShowMainInput = false;
      } else if (type == 'subject') {
        this.list[index].subject.number = val.number;
        this.list[index].subject.name = val.name;
        this.list[index].subject.code = val.code;
        this.list[index].subject.detailJson = val.detailJson;
        this.list[index].isShowSubjectInput = false;
      }
      this.judgeIsAuxiliary();
    },
    selectionList(index, type) {
      //console.log('弹出选择列表');
      // if (type == 'main') {
      //   this.dialogMainIndex = index;
      //   this.openMainDialog();
      // } else if (type == 'subject') {
      //   this.dialogSubjectIndex = index;
      //   this.openSubjectDialog();
      // } else if (type == 'auxiliary') {
      //   this.dialogAuxiliaryIndex = index;
      //   this.openAuxiliaryDialog();
      // }
    },

    keyupEvents(index, e, remaind) {
      if (e.keyCode == 37) {
        //console.log('拦截到37');//左
        this.keyboardEvents('left', index * 4 + remaind);
        return;
      } else if (e.keyCode == 38) {
        //console.log('拦截到38');//上
        this.keyboardEvents('up', index * 4 + remaind);
        return;
      } else if (e.keyCode == 39) {
        //console.log('拦截到39');//右
        this.keyboardEvents('right', index * 4 + remaind);
        return;
      } else if (e.keyCode == 40) {
        //console.log('拦截到40');//下
        this.keyboardEvents('down', index * 4 + remaind);
        return;
      } else if (e.keyCode == 13) {
        //console.log('拦截到13');//enter键
        this.keyboardEvents('enter', index * 4 + remaind);
        return;
      } else if (e.keyCode == 118) {
        //console.log('拦截到118');//F7
        if (remaind == 1) {
          this.selectionList(index, 'main');
        } else if (remaind == 2) {
          this.selectionList(index, 'subject');
        }
        return;
      }

      var main = this.list[index].main;
      var subject = this.list[index].subject.name;
      if (index - 1 >= 0) {
        if (main.indexOf('//') != -1 || subject.indexOf('//') != -1) {
          this.list[index].main = this.list[index - 1].main;
          this.list[index].subject = this.list[index - 1].subject;
          this.list[index].debtor = this.list[index - 1].debtor;
          this.list[index].debtorList = this.list[index - 1].debtorList;
          this.list[index].lender = this.list[index - 1].lender;
          this.list[index].lenderList = this.list[index - 1].lenderList;
          this.list[index].auxiliary = this.list[index - 1].auxiliary;
          this.calcDebtorTotal();
          this.calcLenderTotal();
        }
        if (main.indexOf('..') != -1) {
          this.list[index].main = this.list[index - 1].main;
        }
      }
      //判断是否显示辅助核算
      this.judgeIsAuxiliary();
    },
    keyboardEvents(type, number) {
      var total = this.list.length * 4;
      if (type == 'enter') {
        number++;
      } else if (type == 'left' && number - 1 > 0) {
        number--;
      } else if (type == 'right' && number + 1 <= total) {
        number++;
      } else if (type == 'up' && number - 4 > 0) {
        number = number - 4;
      } else if (type == 'down' && number + 4 <= total) {
        number = number + 4;
      }
      if (type == 'enter' && number > total) {
        this.addList();
      }
      var index = parseInt(number / 4);
      var remaind = number % 4;
      if (remaind == 1) {
        this.showInput(index, 'main');
      } else if (remaind == 2) {
        this.showInput(index, 'subject');
      } else if (remaind == 3) {
        this.showInput(index, 'debtor');
      } else if (remaind == 0) {
        this.showInput(index - 1, 'lender');
      }
    },
    debtorInputKeyUp(index, e, remaind) {
      //console.log(e);
      //this.list[index].debtor=this.list[index].debtor.replace(/\D/g,'');
      if (e.keyCode === 187) {
        this.calcDebtorTotal(index);
        this.calcLenderTotal(index);
        var cha = this.lenderTotal - this.debtorTotal;

        if (cha == 0) {
          cha = '';
        }
        this.list[index].debtor = cha + '';
      } else if (e.keyCode === 32) {
        this.list[index].isShowLenderInput = true;
        this.list[index].isShowDebtorInput = false;
        this.list[index].lender = this.list[index].debtor.trim();
        this.list[index].debtor = '';
        this.list[index].debtorList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        var lenderList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        this.list[index].lenderList = collatingData(this.list[index].lender, lenderList);
        this.calcDebtorTotal();
        this.calcLenderTotal();
        return;
      } else if ((e.keyCode >= 37 && e.keyCode <= 40) || e.keyCode == 13) {
        this.keyupEvents(index, e, remaind);
        return;
      }
      this.list[index].lender = '';
      this.list[index].lenderList = ['', '', '', '', '', '', '', '', 0, 0, 0];
      var debtor = this.list[index].debtor;
      var debtorList = ['', '', '', '', '', '', '', '', 0, 0, 0];
      this.list[index].debtorList = collatingData(debtor, debtorList);
      this.calcDebtorTotal();
      this.calcLenderTotal();
    },
    calcDebtorTotal(index) {
      var debtorTotal = 0;
      for (var i in this.list) {
        if (this.list[i].debtor != null && this.list[i].debtor != '') {
          if (!(index && index == i)) {
            debtorTotal += (this.list[i].debtor) * 1;
          }
        }
      }
      this.debtorTotal = debtorTotal;
      debtorTotal = debtorTotal + '';
      var debtorTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
      this.debtorTotalList = collatingData(debtorTotal, debtorTotalList);
    },
    lenderInputKeyUp(index, e, remaind) {
      //this.list[index].lender=this.list[index].lender.replace(/\D/g,'');
      if (e.keyCode === 187) {
        this.calcDebtorTotal(index);
        this.calcLenderTotal(index);
        var cha = this.debtorTotal - this.lenderTotal;
        if (cha == 0) {
          cha = '';
        }
        this.list[index].lender = cha + '';
      } else if (e.keyCode === 32) {
        this.list[index].isShowDebtorInput = true;
        this.list[index].isShowLenderInput = false;
        this.list[index].debtor = this.list[index].lender.trim();
        this.list[index].lender = '';
        this.list[index].lenderList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        var debtorList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        this.list[index].debtorList = collatingData(this.list[index].debtor, debtorList);
        this.calcLenderTotal();
        this.calcDebtorTotal();
        return;
      } else if ((e.keyCode >= 37 && e.keyCode <= 40) || e.keyCode == 13) {
        this.keyupEvents(index, e, remaind);
        return;
      }
      this.list[index].debtor = '';
      this.list[index].debtorList = ['', '', '', '', '', '', '', '', 0, 0, 0];
      var lender = this.list[index].lender;
      var lenderList = ['', '', '', '', '', '', '', '', 0, 0, 0];
      this.list[index].lenderList = collatingData(lender, lenderList);
      this.calcLenderTotal();
      this.calcDebtorTotal();
    },
    calcLenderTotal(index) {
      var lenderTotal = 0;
      for (var i in this.list) {
        if (this.list[i].lender != null && this.list[i].lender != '') {
          if (!(index && index == i)) {
            lenderTotal += (this.list[i].lender) * 1;
          }
        }
      }
      this.lenderTotal = lenderTotal;
      lenderTotal = lenderTotal + '';
      var lenderTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
      this.lenderTotalList = collatingData(lenderTotal, lenderTotalList);
    },
    addList() {
      var obj = {
        main: '',
        isShowMainInput: false,
        subject: {
          number: '',
          name: '',
          code: '',
          detailJson: ''
        },
        isShowSubjectInput: false,
        debtor: '',
        debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
        isShowDebtorInput: false,
        lender: '',
        lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
        isShowLenderInput: false,
        isAuxiliary: false,
        auxiliary: ''
      };
      this.list.push(obj);
    },
    removeList(index) {
      if (this.list.length <= 2) {
        this.$message.error('至少保存两行');
        return;
      }
      this.list.splice(index, 1);
      this.calcDebtorTotal();
      this.calcLenderTotal();
    },

    checkListData() {
      if ((this.debtorTotal != 0 || this.lenderTotal != 0) && this.lenderTotal * 1 != this.debtorTotal * 1) {
        this.$message.error('凭证借贷不平衡，请检查');
        return;
      }
      var mainFlag = true;
      for (var i in this.list) {
        if (this.list[i].main != null && this.list[i].main != '') {
          mainFlag = false;
        }
        if (this.list[i].subject.name == null || this.list[i].subject.name == '') {
          this.$message.error('第' + (i * 1 + 1) + '行中的会计科目为必填项，请填写了再提交');
          return;
        }
        if ((this.list[i].debtor == null || this.list[i].debtor == '') && (this.list[i].lender == null || this.list[i].lender == '')) {
          this.$message.error('第' + (i * 1 + 1) + '行中借方金额、贷方金额必须填一个，请填写了再提交');
          return;
        }
      }
      if (mainFlag) {
        this.$message.error('必须填写一个摘要，请填写了再提交');
        return;
      }

      if (this.auditUser == null || this.auditUser.trim().length==0) {
        this.$message.error('请选择审核人');
        return;
      }

      var list = [];
      for (var a = 0; a < this.list.length; a++) {
        var obj = {
          explanation: this.list[a].main,
          accountNumber: this.list[a].subject.number,
          accountName: this.list[a].subject.name,
          accountCode: this.list[a].subject.code,
          auxiliaryType: this.list[a].subject.detailJson,
          itemObjectKeys: this.list[a].auxiliary,
          basicDebitAmount: this.list[a].debtor,
          basicCreditAmount: this.list[a].lender
        };
        list.push(obj);
      }
      var attList = [];
      if(this.attachmentList.attachmentFileList!=null && this.attachmentList.attachmentFileList.length>0) {
        for (var j=0;j<this.attachmentList.attachmentFileList.length;j++) {
          var item = this.attachmentList.attachmentFileList[j];
          let extension = item.name.substring(item.name.lastIndexOf('.')+1);
          let fsize = item.size;
          attList.push({name:item.name,url:item.url,type:extension,size:fsize});
        }
      }
      var reqObj = {
        id: this.id,
        billNo: this.billNo,
        billStatus: 2,
        auditUser: this.auditUser,
        voucherType: this.voucher.word,
        voucherNumber: this.voucher.no,
        year: this.formatDate("yyyy-MM-dd", this.voucher.date),
        creditTotal: this.lenderTotal,
        debitTotal: this.debtorTotal,
        attachments: this.voucher.bill,
        list: list,
        attachmentList:attList,
      };


      return reqObj;
    },
    formatDate(fmt, date) {
      if (typeof date === 'string') {
        date = new Date(date);
      }

      var o = {
        "M+": date.getMonth() + 1,                 //月份
        "d+": date.getDate(),                    //日
        "h+": date.getHours(),                   //小时
        "m+": date.getMinutes(),                 //分
        "s+": date.getSeconds(),                 //秒
        "q+": Math.floor((date.getMonth() + 3) / 3), //季度
        "S": date.getMilliseconds()             //毫秒
      };
      if (/(y+)/.test(fmt))
        fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
      for (var k in o)
        if (new RegExp("(" + k + ")").test(fmt))
          fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
      return fmt;
    },
    onSubmit() {
      var reqObj = this.checkListData();
      //console.log(reqObj);

      if (reqObj) {
        putObj(this.id, reqObj).then(response => {
          if (response.data.code == '200') {

            this.$notify({
              title: '成功',
              message: '修改成功',
              type: 'success',
              duration: 2000
            });

            this.$emit('submitSuccess', response);

          } else {
            this.$notify({
              title: '失败',
              message: response.data.message,
              type: 'warning',
              duration: 2000
            })
          }
        });
      }
    },
    init() {
      this.id = this.$route.query.id;
      const self = this;
      if (this.id !== '' && this.id !== undefined) {
        getObj(this.id)
          .then(response => {
            const data = response.data.data;
            this.voucher = {word: data.voucherType, no: data.voucherNumber, date: data.year, bill: data.attachments};
            this.list = [];
            var isAuxiliary = false;
            for (var i = 0; i < data.list.length; i++) {
              var debtor = data.list[i].basicDebitAmount == 0 ? '' : data.list[i].basicDebitAmount + '';
              var debtorList = debtor == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(debtor, ['', '', '', '', '', '', '', '', 0, 0, 0]);
              var lender = data.list[i].basicCreditAmount == 0 ? '' : data.list[i].basicCreditAmount + '';
              var lenderList = lender == '' ? ['', '', '', '', '', '', '', '', 0, 0, 0] : collatingData(lender, ['', '', '', '', '', '', '', '', 0, 0, 0]);
              if (data.list[i].itemObjectKeys != null && data.list[i].itemObjectKeys != '') {
                isAuxiliary = true;
              }
              var obj = {
                main: data.list[i].explanation,
                isShowMainInput: false,
                subject: {
                  number: data.list[i].accountNumber,
                  name: data.list[i].accountName,
                  code: data.list[i].accountCode,
                  detailJson: data.list[i].auxiliaryType
                },
                isShowSubjectInput: false,
                debtor: debtor,
                debtorList: debtorList,
                isShowDebtorInput: false,
                lender: lender,
                lenderList: lenderList,
                isShowLenderInput: false,
                isAuxiliary: data.list[i].itemObjectKeys == '' ? false : true,
                auxiliary: data.list[i].itemObjectKeys
              };
              this.list.push(obj);
            }
            this.debtorTotal = data.debitTotal + '';
            this.debtorTotalList = collatingData(this.debtorTotal, ['', '', '', '', '', '', '', '', 0, 0, 0]);
            this.lenderTotal = data.creditTotal + '';
            this.lenderTotalList = collatingData(this.lenderTotal, ['', '', '', '', '', '', '', '', 0, 0, 0]);
            this.dialogMainVisible = false;
            this.dialogSubjectVisible = false;
            this.dialogAuxiliaryVisible = false;
            this.saveVisible = false;
            this.dialogMainIndex = 0;
            this.dialogSubjectIndex = 0;
            this.dialogAuxiliaryIndex = 0;
            this.isAuxiliary = isAuxiliary;
            this.billNo = data.billNo;
            this.auditUser = data.auditUser;
            this.auditStatus = data.auditStatus;
            this.attachmentList.attachmentFileList = data.attachmentList //查看凭证
          }).catch(err=>{}).finally((fin)=>{this.loadingCount = 99;});
      } else {
        this.voucher = {word: '记', no: 1, date: new Date(), bill: 0};
        this.list = [
          {
            main: '',
            isShowMainInput: false,
            subject: {
              number: '',
              name: '',
              code: '',
              detailJson: ''
            },
            isShowSubjectInput: false,
            debtor: '',
            debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowDebtorInput: false,
            lender: '',
            lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowLenderInput: false,
            isAuxiliary: false,
            auxiliary: ''
          },
          {
            main: '',
            isShowMainInput: false,
            subject: {
              number: '',
              name: '',
              code: '',
              detailJson: ''
            },
            isShowSubjectInput: false,
            debtor: '',
            debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowDebtorInput: false,
            lender: '',
            lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowLenderInput: false,
            isAuxiliary: false,
            auxiliary: ''
          },
          {
            main: '',
            isShowMainInput: false,
            subject: {
              number: '',
              name: '',
              code: '',
              detailJson: ''
            },
            isShowSubjectInput: false,
            debtor: '',
            debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowDebtorInput: false,
            lender: '',
            lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowLenderInput: false,
            isAuxiliary: false,
            auxiliary: ''
          },
          {
            main: '',
            isShowMainInput: false,
            subject: {
              number: '',
              name: '',
              code: '',
              detailJson: ''
            },
            isShowSubjectInput: false,
            debtor: '',
            debtorList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowDebtorInput: false,
            lender: '',
            lenderList: ['', '', '', '', '', '', '', '', 0, 0, 0],
            isShowLenderInput: false,
            isAuxiliary: false,
            auxiliary: ''
          }
        ];
        this.debtorTotal = 0;
        this.debtorTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        this.lenderTotal = 0;
        this.lenderTotalList = ['', '', '', '', '', '', '', '', 0, 0, 0];
        this.dialogMainVisible = false;
        this.dialogSubjectVisible = false;
        this.dialogAuxiliaryVisible = false;
        this.dialogMainIndex = 0;
        this.dialogSubjectIndex = 0;
        this.dialogAuxiliaryIndex = 0;
        this.isAuxiliary = false;
      }

      this.expireTimeOption = {
        disabledDate(date) {
          //disabledDate 文档上：设置禁用状态，参数为当前日期，要求返回 Boolean
          const period_date = new Date(self.voucher.lastPeriod);//this.voucher.lastPeriod
          return (
            date.getTime() < period_date- 8.64e7
          );
        }
      };

      // try {
      //   this.attachmentList.attachmentFileList = [];
      //   this.attachmentList.attachmentFileList.push({name: "5.png",size:100000,url:'blob:http://localhost:7798/981fb765-a352-4bb5-ab34-b48a0935c188'});
      //   if(this.bill!=null && this.bill != undefined) {
      //     this.bill.no = this.attachmentList.attachmentFileList.length;
      //   }
      //   if(this.$refs.uploadRef!=null && this.$refs.uploadRef != undefined) {
      //     this.$refs.uploadRef.fileList = this.attachmentList.attachmentFileList;
      //   }
      // }catch (e) {
      //   console.log(e);
      // }
    }
  }
}
</script>

<style scoped>
table {
  width: 100%;
  border-spacing: 0;
  border-collapse: collapse;
  padding: 0;
  margin: 0;
}

table td {
  text-align: center;
  table-layout: fixed;
  padding: 0px;
  position: relative;
}

.main-subject {
  position: relative;
  height: 60px;
  line-height: 30px;
  text-align: left;
}

.charge-container {
  padding: 10px;
  font-size: 14px;
  color: #444;
  font-weight: 400;
  background-color: white;
}

.charge {
  width: 1124px;
  margin: auto;
}

.charge-header {
  margin-bottom: 10px;
}

.charge-header > div {
  display: inline-block;
  margin-right: 15px;
}

.tip-box {
  width: 330px;
  padding: 10px;
  position: absolute;
  top: 27px;
  right: -15px;
  z-index: 1005;
  background-color: #fff;
  box-shadow: 0 0 6px rgba(170, 170, 170, .73);
  display: none;
}

.tip-box-table tr {
  height: 25px;
}

.el-icon-info {
  font-size: 18px;
  margin-left: 30px;
  cursor: pointer;
}

.el-icon-info:hover + .tip-box {
  display: inline-block;
}

.el-icon-more {
  position: absolute;
  top: 22px;
  right: 10px;
  z-index: 2;
  color: #666;
  cursor: pointer;
  font-size: 16px;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: 0px;
  float: left;
  padding: 5px 0;
  margin: 2px 0 0;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
}

.suggest-list {
  width: 100%;
  height: auto;
  z-index: 1015;
  min-width: inherit;
  display: block;
  overflow: hidden;
  border: none;
  box-shadow: 0 1px 6px rgba(0, 0, 0, .2);
}

.suggest-list .item-list {
  max-height: 375px;
  overflow: auto;
  list-style: none;
  margin: 0px;
  padding: 0px;
}

.item-list li {
  display: flex;
  padding: 0 10px;
  height: 28px;
  line-height: 28px;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.item-list li:hover {
  background: #ecebeb;
}

.item-list li.hover {
  background: #ecebeb;
}

.charge-table, .tip-box-table {
  border: 1px solid #bab9b9;
}

.charge-table, .debtor-tbale, .lender-tbale > tr {
  height: 60px;
}

.charge-table > tr:first-child {
  height: 50px;
}

.td-auxiliary-dis {
  background-color: #f7f7f7;
}

.auxiliary-accounting {
  height: 60px;
  overflow: auto;
  padding: 15px 0 0 30px;
}

.auxiliary-accounting:before {
  content: "+";
  font-size: 30px;
  color: #4a90e2;
  cursor: pointer;
  padding: 0 11px;
  position: absolute;
  top: 0;
  left: 0;
  line-height: 60px;
}

.auxiliary-single {
  display: flex;
  float: left;
  height: 28px;
  line-height: 28px;
  margin-right: 5px;
  cursor: pointer;
  background: #eee;
  padding: 0 8px;
  border-radius: 2px;
}

.auxiliary-single span {
  max-width: 90px;
  overflow: hidden;
  height: 28px;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.auxiliary-single i {
  color: red;
  margin: 8px 0 8px 7px;
  font-size: 10px;
  visibility: hidden;
}

.auxiliary-single:hover i {
  visibility: inherit;
}

.charge-table-icon {
  cursor: pointer;
  display: inline-block;
}

.debtor-lender-table tr td {
  width: 9%;
  border-right: 1px solid #bab9b9;
}

.debtor-lender-table tr td:nth-child(3) {
  border-right-color: rgba(74, 144, 226, .5);
}

.debtor-lender-table tr td:nth-child(6) {
  border-right-color: rgba(74, 144, 226, .5);
}

.debtor-lender-table tr td:nth-child(9) {
  border-right-color: rgba(226, 106, 74, .5);
}

.debtor-lender-table tr td:last-child {
  border-right: none;
}

.tr-negative {
  color: red;
}

.charge-table input, select {
  width: 100%;
  height: 60px;
}

.charge-table textarea {
  width: 100%;
  height: 60px;
  padding: 9px 14px 9px 10px;
  overflow: auto;
  resize: none;
  border: none;
  border-radius: 0px;
  margin: 0;
  color: #444;
  box-sizing: border-box;
}
</style>
