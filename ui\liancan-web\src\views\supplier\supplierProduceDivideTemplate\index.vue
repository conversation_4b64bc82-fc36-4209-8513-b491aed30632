<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="addHandler" >新增</el-button>
<!--                <el-button type="primary"  icon="el-icon-download" size="small" @click="printOrderHandler" >导出</el-button>-->
<!--                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>-->
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-edit"
                    @click="editRow(row,index)">编辑
                </el-button>
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>
        <!-- 打开新增页面 开始 -->
        <el-dialog :title="subTitle"
                   :visible.sync="isShow"
                   v-if="isShow"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm" width="60%"  style="height: 90%;">
                <el-form ref="formAdd" label-width="80px">
                    <el-form-item label="模板类型" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-radio-group v-model="templateType" @change="menuClick">
                                <el-radio-button label="0">组装</el-radio-button>
                                <el-radio-button label="1">拆分</el-radio-button>
                            </el-radio-group>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="模板名称" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-input
                                ref="templateName"
                                v-model="templateName"
                                type="text"
                                clearable="true">
                            </el-input>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="组件" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="3">
                            <el-button type="primary" size="small" @click="selectGoodMergeHandler">从商品库中选择</el-button>
                        </el-col>
                    </el-form-item>
                    <el-form-item>
                        <div style="height: 200px;overflow-y: scroll;">
                            <avue-crud :option="mergeOption"
                                       :table-loading="mergeLoading"
                                       :data="mergeData"
                                       :page="mergePage"
                                       :before-open="beforeOpenMerge"
                                       v-model="mergeForm"
                                       ref="crudMerge"
                                       @row-update="rowUpdateMerge"
                                       @row-save="rowSaveMerge"
                                       @row-del="rowDelMerge"
                                       @search-change="searchChangeMerge"
                                       @search-reset="searchResetMerge"
                                       @current-change="currentChangeMerge"
                                       @size-change="sizeChangeMerge"
                                       @cell-click="mergeHandleRowClick"
                                       @on-load="detailOnLoad">
                                <template slot="menu" slot-scope="{row,index}">
                                    <el-button
                                        type="text"
                                        size="small"
                                        icon="el-icon-edit"
                                        v-if="!row.$cellEdit"
                                        @click="rowCellMerge(row,index)"
                                    >修改</el-button>
                                    <el-button
                                        type="text"
                                        size="small"
                                        icon="el-icon-check"
                                        v-if="row.$cellEdit"
                                        @click="rowSaveMerge(row,index)"
                                    >保存</el-button>
                                    <el-button
                                        type="text"
                                        size="mini"
                                        icon="el-icon-delete"
                                        @click="deleteRowMerge(row)">删除
                                    </el-button>
                                </template>
                            </avue-crud>
                        </div>
                    </el-form-item>
                    <el-form-item label="分件" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="3">
                            <el-button type="primary" size="small" @click="selectGoodDivideHandler">从商品库中选择</el-button>
                        </el-col>
                    </el-form-item>
                    <el-form-item>
                        <div style="height: 200px;overflow-y: scroll;">
                            <avue-crud :option="divideOption"
                                       :table-loading="divideLoading"
                                       :data="divideData"
                                       :page="dividePage"
                                       :before-open="beforeOpenDivide"
                                       v-model="divideForm"
                                       ref="crudDivide"
                                       @row-update="rowUpdateDivide"
                                       @row-save="rowSaveDivide"
                                       @row-del="rowDelDivide"
                                       @search-change="searchChangeDivide"
                                       @search-reset="searchResetDivide"
                                       @current-change="currentChangeDivide"
                                       @size-change="sizeChangeDivide"
                                       @cell-click="divideHandleRowClick"
                                       @on-load="detailOnLoad">
                                <template slot="menu" slot-scope="{row,index}">
                                    <el-button
                                        type="text"
                                        size="small"
                                        icon="el-icon-edit"
                                        v-if="!row.$cellEdit"
                                        @click="rowCellDivide(row,index)"
                                    >修改</el-button>
                                    <el-button
                                        type="text"
                                        size="small"
                                        icon="el-icon-check"
                                        v-if="row.$cellEdit"
                                        @click="rowSaveDivide(row,index)"
                                    >保存</el-button>
                                    <el-button
                                        type="text"
                                        size="mini"
                                        icon="el-icon-delete"
                                        @click="deleteRowDivide(row)">删除
                                    </el-button>
                                </template>
                            </avue-crud>
                        </div>
                    </el-form-item>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddForm">取消</el-button>
                    <el-button v-if="this.editType!=='view'" type="primary" @click="saveSelectHandle">保存</el-button>
                </span>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 组件 公共商品选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowMergeSelectGoods"
                   v-if="isShowMergeSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeMergeSelectForm" width="60%">
            <avue-crud :option="mergeGoodsListOption"
                       :table-loading="mergeGoodsListLoading"
                       :data="mergeGoodsListData"
                       :page="mergeGoodsListPage"
                       v-model="mergeGoodsListForm"
                       ref="crudMergeSelectGoods"
                       @search-change="searchChangeMerge"
                       @search-reset="searchResetMerge"
                       @current-change="currentChangeMerge"
                       @size-change="sizeChangeMerge"
                       @on-load="mergeGoodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="mergeAddGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 组件 商品选择 结束 -->
        <!-- 分件 公共商品选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowDivideSelectGoods"
                   v-if="isShowDivideSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeDivideSelectForm" width="60%">
            <avue-crud :option="divideGoodsListOption"
                       :table-loading="divideGoodsListLoading"
                       :data="divideGoodsListData"
                       :page="divideGoodsListPage"
                       v-model="divideGoodsListForm"
                       ref="crudDivideSelectGoods"
                       @search-change="searchChangeDivide"
                       @search-reset="searchResetDivide"
                       @current-change="currentChangeDivide"
                       @size-change="sizeChangeDivide"
                       @on-load="divideGoodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="divideAddGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 分件 商品选择 结束 -->
        <!-- 打开详情页面 开始 -->
        <el-dialog title="详情"
                   :visible.sync="isShowDetail"
                   v-if="isShowDetail"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeShowForm" width="60%"  style="height: 90%;">
            <el-row type="flex" class="row-bg" justify="right">
                <el-col :span="24" style="height: 100%;" align="right">
                    <el-button type="primary" icon="el-icon-printer" size="small" v-print="print" >打印</el-button>
                </el-col>
            </el-row>
            <div id="printArea">
                <el-row type="flex" class="row-bg" justify="left" style="height: 60px;">
                    <el-col :span="24">
                        <div style="width: 100%;height:100%;text-align:center;"><h1>采购入库单</h1></div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left" style="margin-top: 45px;">
                    <el-col :span="2">
                        <div class="head-label">单据日期</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.businessDate}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">供应商</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.supplierCustomerName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">业务员</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.salesmanName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">单据号</div>
                    </el-col>
                    <el-col :span="4">
                        <div>{{this.form.code}}</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">登记人</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.createUserName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">登记时间</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.createTime}}</div>
                    </el-col>

                    <el-col :span="4">
                        <div class="head-label">关联采购订单</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.sourceCode}}</div>
                    </el-col>
                </el-row>
                <div style="height: 600px;overflow-y: scroll;margin-top: 20px;">
                    <el-table
                        :data="detailData"
                        stripe
                        border
                        fit
                        show-summary
                        :header-cell-style="tableHeaderStyle"
                        :row-style="tableDetailStyle"
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column property="rowId" label="序号" align="center"></el-table-column>
                        <el-table-column property="name" label="商品名称" align="center"></el-table-column>
                        <el-table-column property="unit" label="计量单位" align="center"></el-table-column>
                        <el-table-column property="warehouseName" label="入库仓库" align="center"></el-table-column>
                        <el-table-column property="qty" label="入库数量" align="center"></el-table-column>
                        <el-table-column property="price" label="入库单价" align="center"></el-table-column>
                        <el-table-column property="amt" label="入库金额" align="center"></el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
        <!-- 打开详情页面 结束 -->
    </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove} from "@/api/supplier/supplierProduceDivideTemplate";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";
export const DIC = {
    templateType: [{
        label: '组装',
        value: "0"
    }, {
        label: '拆分',
        value: "1"
    }
    ],
    componentType: [{
        label: '组件',
        value: "0"
    }, {
        label: '分件',
        value: "1"
    }
    ],

}
export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            subTitle: '新增',
            editType: 'add',
            templateType: '',
            templateName: '',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: true,
                editBtn: false,
                viewBtn: false,
                selection: true,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: '模板类型',
                        prop: 'templateType',
                        type: 'select',
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        rules: [
                            {
                                required: false,
                                message: '请选择模板类型',
                                trigger: 'blur'
                            }
                        ],
                        dicData: DIC.templateType,
                    },
                    {
                        label: "模板编号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                    },
                    {
                        label: "模板名称",
                        prop: "templateName",
                        type: "input",
                        width: 180,
                        search: true,
                    },
                    {
                        label: "组件",
                        prop: "componentName",
                        type: "input",
                        width: 180,
                    },
                    {
                        label: "分件",
                        prop: "divideComponent",
                        type: "input",
                        width: 180,
                    },
                    {
                        label: '组件类型',
                        prop: 'componentType',
                        type: 'select',
                        hide: true,
                        search: true,
                        rules: [
                            {
                                required: false,
                                message: '请选择模板类型',
                                trigger: 'blur'
                            }
                        ],
                        dicData: DIC.componentType,
                    },
                    {
                        label: "组件名称",
                        prop: "goodGoodId",
                        type: "select",
                        hide: true,
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "修改人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                        display: false
                    },
                    {
                        label: "最后修改日期",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        display: false
                    },
                ]
            },
            data: [],

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },

            supplierList: [],
            salesmanList: [],
            supplierCustomerId: '',
            businessDate: '',
            salesman: '',
            supplierValue: {},
            isShow: false,
            isShowDetail: false,

            //新增组件单弹窗参数 start

            isShowMergeSelectGoods: false,
            isShowMergeDetail: false,

            saveData: [],

            queryMerge: {},
            rowMerge: {},
            mergeLoading: true,
            mergeData: [],
            mergePage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            mergeForm: {},
            mergeOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "入库仓库",
                        prop: "warehouseId",
                        type: "select",
                        cell: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "入库数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "入库单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "入库金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        rules: [{
                            required: false,
                            message: "请输入入库金额",
                            trigger: "blur"
                        }],
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            //新增组件单弹窗参数 end

            //新增分件单弹窗参数 start
            isShowDivide: false,
            isShowDivideSelectGoods: false,
            isShowDivideDetail: false,
            queryDivide: {},
            rowDivide: {},
            divideLoading: true,
            divideData: [],
            dividePage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            divideForm: {},
            divideOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "入库仓库",
                        prop: "warehouseId",
                        type: "select",
                        cell: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "入库数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "入库单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "入库金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        rules: [{
                            required: false,
                            message: "请输入入库金额",
                            trigger: "blur"
                        }],
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            //新增分件单弹窗参数 end

            //选择-组件商品弹窗参数 start
            mergeQueryGoodsList: {},
            mergeGoodsListLoading: true,
            mergeGoodsListData: [],
            mergeGoodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            mergeGoodsListForm: {},
            mergeGoodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品类型",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品类型",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "商品大类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品大类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品小类",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品小类",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
            //选择-组件商品弹框参数 end

            //选择-分件商品弹窗参数 start
            divideQueryGoodsList: {},
            divideGoodsListLoading: true,
            divideGoodsListData: [],
            divideGoodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            divideGoodsListForm: {},
            divideGoodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品类型",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品类型",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "商品大类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品大类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品小类",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品小类",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
            //选择-分件商品弹框参数 end

        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "mergeData"(newVal,oldVal) {
            this.summaryMergeAmt();
        },
        "divideData"(newVal,oldVal) {
            this.summaryDivideAmt();
        },
    },
    mounted() {
    },
    methods: {
        rowSave(row, loading, done) {
            var strs = row.customerType;
            var str = "";
            for (var i of strs) {
                str += i + ",";
            }
            if (str.endsWith(",")) {
                str = str.substring(0, str.length - 1);
            }
            row.customerType = str;
            add(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {
            var strs = row.customerType;
            var str = "";
            for (var i of strs) {
                str += i + ",";
            }
            if (str.endsWith(",")) {
                str = str.substring(0, str.length - 1);
            }
            row.customerType = str;
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },

        menuClick(idx) {
            // console.log(">>>>>>>>>>>>>", idx)
            if (this.activeIdx == idx) return
            this.activeIdx = idx
        },
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        addHandler () {
            this.form = {};
            this.businessDate = '';//new Date();
            this.supplierCustomerId = '';
            this.salesman = '';
            this.editType = 'add';
            this.subTitle = '新增';
            this.isShow = true;
        },
        closeAddForm() {
            if (this.saveData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShow = false;
                    });
            }else{
                this.isShow = false;
            }
        },
        closeShowForm(){
            this.isShowDetail = false;
        },

        viewRow(row,index) {
            // console.log("======>"+JSON.stringify(row));
            this.editType = 'view';
            this.subTitle = '详情';
            this.form = row;
            this.mergeForm = {};
            this.mergeData = [];

            this.divideForm = {};
            this.divideData = [];

            this.templateType = this.form.templateType;
            this.templateName = this.form.templateName;
            this.isShow = true;
        },

        editRow(row,index) {
            // console.log("======>"+JSON.stringify(row));
            this.editType = 'edit';
            this.subTitle = '修改';
            this.form = row;
            this.mergeForm = {};
            this.mergeData = [];

            this.divideForm = {};
            this.divideData = [];

            this.templateType = this.form.templateType;
            this.templateName = this.form.templateName;
            this.isShow = true;

        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        detailOnLoad(page, params = {}) {
            this.mergeLoading = true;
            this.divideLoading = true;
            if (this.form.id!==undefined && this.form.id!=='') {
                getDetail(this.form.id).then(res => {
                    const data = res.data.data;
                    this.businessDate = data.businessDate;
                    this.supplierCustomerId = data.supplierCustomerId;
                    this.salesman = data.salesman;

                    this.mergeData = data.mergeDetailList;
                    this.mergePage.total = this.mergeData.length;
                    this.mergeData.forEach((item) => {
                        item.$cellEdit = false;
                        item.name = item.goodName;
                    });
                    this.mergeLoading = false;

                    this.divideData = data.divideDetailList;
                    this.dividePage.total = this.divideData.length;
                    this.divideData.forEach((item) => {
                        item.$cellEdit = false;
                        item.name = item.goodName;
                    });
                    this.divideLoading = false;
                });
            }else{
                this.mergePage.total = 0;
                this.mergeData = [];
                this.mergeData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.mergeLoading = false;


                this.dividePage.total = 0;
                this.divideData = [];
                this.divideData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.divideLoading = false;
            }
        },
        //主界面基本方法 end
        //选择商品基本方法 end

        //新增采购入库单基本方法 start
        saveSelectHandle() {
            //保存前先检查数据
            //检测单据日期
            //检测供应商
            //业务员
            if (this.templateType === undefined || this.templateType === '') {
                this.$message.warning("请选择模板类型");
                return;
            }
            if (this.templateName === undefined || this.templateName === '') {
                this.$message.warning("请填写模板名称");
                return;
            }
            //如果当前保存为空，则不提示
            if (this.mergeData.length==0) {
                this.$message.warning("请选择组件的商品")
                return;
            }
            if (this.mergeData.length>1) {
                this.$message.warning("组件只能选择一个商品")
                return;
            }
            //如果当前保存为空，则不提示
            if (this.divideData.length==0) {
                this.$message.warning("请选择分件的商品")
                return;
            }

            var isSave = true;
            this.mergeData.forEach((item,index)=>{
                var warehouseId = item.warehouseId;
                var qty = item.qty;
                var price = item.price;
                if (warehouseId==='') {
                    isSave = false;
                    this.$message.warning("组件,第"+index+"行,未选择入库仓库");
                    return;
                }else if (qty===0) {
                    isSave = false;
                    this.$message.warning("组件,第"+index+"行,入库数量不正确");
                    return;
                }
                item.amt = qty*price;
            });

            isSave = true;
            this.divideData.forEach((item,index)=>{
                var warehouseId = item.warehouseId;
                var qty = item.qty;
                var price = item.price;
                if (warehouseId==='') {
                    isSave = false;
                    this.$message.warning("分件,第"+index+"行,未选择入库仓库");
                    return;
                }else if (qty===0) {
                    isSave = false;
                    this.$message.warning("分件,第"+index+"行,入库数量不正确");
                    return;
                }
                item.amt = qty*price;
            });

            if (!isSave) {
                return;
            }

            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(() => {
                this.form.templateType = this.templateType;
                this.form.templateName = this.templateName;
                this.form.salesman = this.salesman;

                this.saveData = [];
                this.mergeData.forEach((item,index)=>{
                    item.componentType = 0;
                    this.saveData.push(item);
                });
                this.divideData.forEach((item,index)=>{
                    item.componentType = 1;
                    this.saveData.push(item);
                })

                if (this.editType === 'edit') {
                    this.loading = true;
                    update(this.form,this.saveData).then((res) => {
                        this.loading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShow = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                }else {
                    this.loading = true;
                    add(this.form,this.saveData).then((res) => {
                        this.loading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShow = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                }
            })
            .then(() => {
                this.isShow = false;
            });
        },
/////////////////////////////////////////////////////////////////////////////////////////////////
        closeMergeSelectForm() {
            this.isShowMergeSelectGoods = false;
        },
        mergeGoodsListOnLoad(page, params = {}) {
            this.mergeGoodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.mergeQueryGoodsList)).then(res => {
                const data = res.data.data;
                this.mergeGoodsListPage.total = data.length;
                this.mergeGoodsListData = data;
                this.mergeGoodsListLoading = false;
            });
        },
        mergeAddGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.mergeRefreshData(row);
            this.isShowMergeSelectGoods = false
        },
        selectGoodMergeHandler() {
            this.isShowMergeSelectGoods = true;
        },
        beforeOpenMerge(done, type) {
            done();
        },
        summaryMergeAmt() {
            var iTotalAmt = 0;
            this.mergeData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalMergeAmt = iTotalAmt;
        },
        searchChangeMerge(params, done) {
            this.queryMerge = params;
            this.mergePage.currentPage = 1
            this.mergeOnLoad(this.mergePage, params);
            done();
        },

        searchResetMerge() {
            this.queryMerge = {};
            this.mergeOnLoad(this.mergePage);
        },
        currentChangeMerge(currentPage) {
            this.mergePage.currentPage = currentPage;
        },
        sizeChangeMerge(pageSize) {
            this.mergePage.pageSize = pageSize;
        },

        mergeRefreshData(row) {
            row.goodName = row.name;
            row.warehouseId = "";
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            this.mergeData.push(row);
        },
        deleteRowMerge(row) {
            // console.log(row)
        },
        //新增采购入库单基本方法 end

        //新增采购入库行编辑方法 start

        rowCellMerge(row, index) {
            this.rowMerge= row;
            this.$refs.crudMerge.rowCell(row, index)
        },
        rowSaveMerge(row, index) {
            //修改mergeData对应的row
            //计算对应行的采购金额
            var qty = row.qty;
            var price = row.price;
            var amt = qty*price;
            row.amt = amt;
            this.mergeData[index] = row;
            row.$cellEdit = false;
            this.$refs.crudMerge.rowCellUpdate();
            //合计所有的行
            this.summaryMergeAmt();
        },
        rowCancelEditMerge(row, index) {
            this.onLoad(this.page,this.query);
        },
        rowUpdateMerge(row, index, loading, done) {
            done()
        },
        rowDelMerge(row) {
        },
        mergeHandleRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["warehouseId", "qty", "price", "amt"].includes(column.property)) {
                    this.$refs.crudMerge.rowCell(row, row.$index);
                }
            }
        },
        /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
        closeDivideSelectForm() {
            this.isShowDivideSelectGoods = false;
        },
        divideGoodsListOnLoad(page, params = {}) {
            this.divideGoodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.divideQueryGoodsList)).then(res => {
                const data = res.data.data;
                this.divideGoodsListPage.total = data.length;
                this.divideGoodsListData = data;
                this.divideGoodsListLoading = false;
            });
        },
        divideAddGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.divideRefreshData(row);
            this.isShowDivideSelectGoods = false
        },
        selectGoodDivideHandler() {
            this.isShowDivideSelectGoods = true;
        },
        summaryDivideAmt() {
            var iTotalAmt = 0;
            this.divideData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalDivideAmt = iTotalAmt;
        },
        beforeOpenDivide(done, type) {
            done();
        },
        searchChangeDivide(params, done) {
            this.queryDivide = params;
            this.dividePage.currentPage = 1
            this.divideOnLoad(this.dividePage, params);
            done();
        },
        searchResetDivide() {
            this.queryDivide = {};
            this.divideOnLoad(this.dividePage);
        },
        currentChangeDivide(currentPage) {
            this.dividePage.currentPage = currentPage;
        },
        sizeChangeDivide(pageSize) {
            this.dividePage.pageSize = pageSize;
        },

        divideRefreshData(row) {
            row.goodName = row.name;
            row.warehouseId = "";
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            this.divideData.push(row);
        },
        deleteRowDivide(row) {
            // console.log(row)
        },
        //新增采购入库单基本方法 end

        //新增采购入库行编辑方法 start

        rowCellDivide(row, index) {
            this.rowDivide= row;
            this.$refs.crudDivide.rowCell(row, index)
        },
        rowSaveDivide(row, index) {
            //修改DivideData对应的row
            //计算对应行的采购金额
            var qty = row.qty;
            var price = row.price;
            var amt = qty*price;
            row.amt = amt;
            this.divideData[index] = row;
            row.$cellEdit = false;
            this.$refs.crudDivide.rowCellUpdate();
            //合计所有的行
            this.summaryDivideAmt();
        },
        rowCancelEditDivide(row, index) {
            this.onLoad(this.page,this.query);
        },
        rowUpdateDivide(row, index, loading, done) {
            done()
        },
        rowDelDivide(row) {
        },
        divideHandleRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["warehouseId", "qty", "price", "amt"].includes(column.property)) {
                    this.$refs.crudDivide.rowCell(row, row.$index);
                }
            }
        }
        //新增采购入库行编辑方法 end
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
