<template>
  <basic-container>
    <avue-crud :option="warningOption"
               :table-loading="warningLoading"
               :data="warningData"
               :page="warningPage"
               v-model="warningForm"
               ref="warningForm"
               @search-change="searchChangeWarning"
               @search-reset="searchResetWarning"
               @selection-change="selectionChangeWarning"
               @current-change="currentChangeWarning"
               @size-change="sizeChangeWarning"
               @on-load="onLoadWarning">
      <template slot="menu" slot-scope="{row}">
        <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="openWarning(row,'1')">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="openWarning(row,'2')">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="checkReply(row)">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="checkNanagement(row)">查看
        </el-button>
        <el-button v-if="row.inquiryStatus == '5'" size="mini" type="text" @click="checkFile(row)">查看
        </el-button>
      </template>
    </avue-crud>

    <el-dialog title="查看" :visible.sync="warningDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div>
        <div  class="row-table">
          <div class="row-table-first">预警日期</div><div class="row-table-second">{{this.warningDate}}</div>
          <div class="row-table-first">当前状态</div><div class="row-table-second">{{this.inquiryStatus}}</div>
          <div class="row-table-first">办理单位</div><div class="row-table-second">{{this.handlingUnit}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first">违规单位</div><div class="row-table-second">{{this.schoolName}}</div>
          <div class="row-table-first">单位类型</div><div class="row-table-second">{{this.unitType}}</div>
          <div class="row-table-first">违规分类</div><div class="row-table-second">{{this.category}}</div>
        </div>
        <div  class="row-table">
          <div class="row-table-first" style="width: 10%;">违规内容</div><div class="row-table-second" style="width: 90%;">{{this.content}}</div>
        </div>
        <div  class="row-table" style="line-height: 80px;">
          <div class="row-table-first" style="width: 10%;">违规取证内容</div><div class="row-table-second" style="width: 90%;height: 80px;">{{this.evidence}}</div>
        </div>
      </el-row>
      <el-row v-if="this.warningType == '2'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">已发函询</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询日期：</span>{{this.inquiryDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询单位：</span>{{this.inquiryDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询人姓名：</span>{{this.pushInquerUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询内容：</span><a href="javascript:void(0);" style="color: #1e9fff" @click="openMakeInquiry">点此查看</a></div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType3 == '3'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">食堂回复</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复日期：</span>{{this.replyTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复单位：</span>{{this.replyDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复人姓名：</span>{{this.replyName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复意见：</span>{{this.replyContent}}</div>
        </el-col>
        <el-col :span="24">
          <div>图片:</div>
          <span v-for="(item,index) in this.fileList">
        <img :src="item.url" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item.url)" class="avatar">
        </span>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType4 == '4'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">处置信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置日期：</span>{{this.handleDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置单位：</span>{{this.handleDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置人姓名：</span>{{this.handleUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置意见：</span>{{this.handleContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType5 == '5'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">归档信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档日期：</span>{{this.fileDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档单位：</span>{{this.fileDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档人姓名：</span>{{this.fileUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档意见：</span>{{this.fileContent}}</div>
        </el-col>
      </el-row>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getViolationWarningList} from "@/api/liancan/illegalWarnLog";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      query: {},
      warningForm:{},
      warningPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      warningData:[],
      warningLoading:true,
      warningOption: {
        /* height:'auto',*/
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: '预警日期',
            prop: 'illegalDate',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            width: 90,
          },
          {
            label: "单位名称",
            prop: "deptName",
            type: "input",
          },
          /* {
             label: '预警时间',
             prop: 'startDate',
             type: 'date',
             format: 'yyyy-MM-dd',
             valueFormat: 'yyyy-MM-dd',
             width:100,
             search:true,
             hide: true,
           },
           {
             label: '',
             prop: 'endDate',
             type: 'date',
             format: 'yyyy-MM-dd',
             valueFormat: 'yyyy-MM-dd',
             width:100,
             search:true,
             hide: true,
           },*/
          {
            label: "违规分类",
            prop: "category",
            type: "select",
            search:true,
            width: 80,
            dicData: [
              {
                label: "收入违规",
                value: "0"
              },
              {
                label: "支出违规",
                value: "1"
              },
              {
                label: "采购违规",
                value: "2"
              },
              {
                label: "财务违规",
                value: "3"
              },
              {
                label: "招标违规",
                value: "4"
              },
              {
                label: "仓管违规",
                value: "5"
              }
            ],

          },
          {
            label: "违规内容",
            prop: "content",
            type: "input",
            width:600,
          },
          {
            label: "当前状态",
            prop: "inquiryStatus",
            type: "select",
            width:90,
            search:true,
            dicData: [
              {
                label: "待函询",
                value: "1"
              },
              {
                label: "待食堂回复",
                value: "2"
              },
              {
                label: "待处置",
                value: "3"
              },
              {
                label: "待归档",
                value: "4"
              },
              {
                label: "已归档",
                value: "5"
              }
            ],
          },
          {
            label: "办理单位",
            prop: "handlingUnit",
            type: "input",
            width:150,
          },
        ]
      },
      warningRecordOption1:{
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        emptyBtn: false,
        submitBtn: false,
        labelWidth: 150,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            display:false,
          },
          {
            label: "违规预警记录编号",
            prop: "inquiryNo",
            type: "input",
            disabled:true,
          },
          {
            label: "违规单位",
            prop: "deptName",
            type: "input",
            disabled:true,
          },
          {
            label: "违规类型",
            prop: "type",
            type: "select",
            disabled:true,
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=illegal_type",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请输入类型",
              trigger: "blur"
            }],
          },
          {
            label: "违规内容",
            prop: "content",
            type: "input",
            disabled:true,
            span:24,
          },
          {
            label: '违规时间',
            prop: 'illegalDate',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            disabled:true,
          },
          {
            label: '违规发现时间',
            prop: 'findDate',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            disabled:true,
          },
          {
            label: "单位管理员姓名",
            prop: "userName",
            type: "input",
            disabled:true,
          },
          {
            label: "管理员手机号",
            prop: "userPhone",
            type: "input",
            disabled:true,
          },
          {
            label: "违规取证",
            prop: "evidence",
            type: "textarea",
            span: 24,
            disabled:true,
          },
        ]
      },
      managementOption:{
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        labelWidth: 150,
        column: [
          {
            label: "请输入处置意见",
            prop: "content",
            type: "textarea",
            minRows:10,
            maxlength:1000,
            span:24,
            showWordLimit:true,
            placeholder:"请具体说明情况，限1000字。",
            rules: [{
              required: true,
              message: "请输入处置意见",
              trigger: "click"
            }]

          },
        ]
      },
      fileOption:{
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        labelWidth: 150,
        column: [
          {
            label: "请输入归档意见",
            prop: "content",
            type: "textarea",
            minRows:10,
            maxlength:1000,
            span:24,
            showWordLimit:true,
            placeholder:"请具体说明情况，限1000字。",
            rules: [{
              required: true,
              message: "请输入归档意见",
              trigger: "click"
            }]

          },
        ]
      },
      warningDetailsVisible:false,
      warningType:undefined,
      warningType3:undefined,
      warningType4:undefined,
      warningType5:undefined,
      warningDate:undefined,
      inquiryStatus:undefined,
      handlingUnit:undefined,
      schoolName:undefined,
      unitType:undefined,
      category:undefined,
      content:undefined,
      evidence:undefined,
      inquiryDate:undefined,
      inquiryDeptName:undefined,
      pushInquerUserName:undefined,
      fileList:[],
      handleDate:undefined,
      handleDeptName:undefined,
      handleUserName:undefined,
      handleContent:undefined,
      fileDate:undefined,
      fileDeptName:undefined,
      fileUserName:undefined,
      fileContent:undefined,
      replyTime:undefined,
      replyDeptName:undefined,
      replyName:undefined,
      replyContent:undefined,
    }
  },
  created(){
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
  },
  methods: {
    searchChangeWarning(params, done) {
      this.query = params;
      this.warningPage.currentPage = 1
      this.onLoadWarning(this.warningPage, params);
      done();
    },
    searchResetWarning() {
      this.query = {};
      this.onLoadWarning(this.warningPage);
    },
    selectionChangeWarning(list) {
      this.selectionList = list;
    },
    currentChangeWarning(currentPage){
      this.warningPage.currentPage = currentPage;
    },
    sizeChangeWarning(pageSize){
      this.warningPage.pageSize = pageSize;
    },
    selectionClearWarning() {
      this.selectionList = [];
      this.$refs.warningForm.toggleSelection();
    },
    onLoadWarning(page, params = {}) {
      this.warningLoading = true;
      getViolationWarningList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.schoolId).then(res => {
        const data = res.data.data;
        this.warningPage.total = data.total;
        this.warningData = data.records;
        this.warningLoading = false;
        this.selectionClearWarning();
      });
    },
    openWarning(row,status){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = status;
      this.warningType = status;
      this.warningType3 = status;
      this.warningType4 = status;
      this.warningType5 = status;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.unitType = row.unitType;

      this.warningDetailsVisible = true;
    },
    checkReply(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    checkNanagement(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.warningType4 = 4;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.handleUserName = row.handleUserName;
      this.handleContent = row.handleContent;
      this.handleDeptName = row.handleDeptName;
      this.handleDate = row.handleDate;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    checkFile(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.warningType4 = 4;
      this.warningType5 = 5;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.handleUserName = row.handleUserName;
      this.handleContent = row.handleContent;
      this.handleDeptName = row.handleDeptName;
      this.handleDate = row.handleDate;
      this.fileUserName = row.fileUserName;
      this.fileContent = row.fileContent;
      this.fileDate = row.fileDate;
      this.fileDeptName = row.fileDeptName;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
  }
}
</script>

<style scoped>
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}
</style>
