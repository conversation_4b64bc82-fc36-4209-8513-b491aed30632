<template>
  <basic-container>
    <avue-form :option="searchOption" ref="searchForm" v-model="searchForm" @change="changOutlets">

    </avue-form>
    <div>
      <div class="all-mess">
        <el-radio-group v-model="tabPosition" style="margin-bottom: 30px;" @change="menuClick">
          <el-radio-button label="0">按人员列表显示(默认)</el-radio-button>
          <el-radio-button label="1">按窗口设备显示</el-radio-button>
        </el-radio-group>
        <!--     <div class="mess-header" style="margin-bottom: 30px;">
          <div
            :class="{acitve:activeIdx==index}"
            v-for="(item,index) in messList"
            :key="index"
            @click="menuClick(index)"
            style="width: 161px;">
            {{item}}
          </div>

        </div>-->
        <div class="mess-content" v-if="this.activeIdx == 0">
          <div class="tableUser">
            <div class="btnUser">
              <avue-form
                ref="batchEditForm"
                v-model="batchEditForm"
                :option="batchEditOption"
              >
              </avue-form>
              <div class="button">
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-search"
                  style="margin-left: 20px;"
                  @click="selectSearch"
                >查询</el-button>
                <el-button
                  size="small"
                  type="primary"
                  icon="el-icon-reset"
                  style="margin-left: 20px;"
                  @click="cloceSearch"
                >清空</el-button>
                <!-- </template> -->
              </div>
            </div>
            <div class="tableUserBtn">
              <el-button v-if="permission.system_personnel_photoDown_download && this.modeType != '1'" size="small" type="primary" icon="el-icon-upload" @click="checkDownload">勾选下传</el-button>
              <el-button v-if="permission.system_personnel_photoDown_all_download && this.modeType != '1'" size="small" type="primary" icon="el-icon-upload" @click="allCheckDownload">全部下传</el-button>
              <el-button v-if="permission.system_personnel_photoDown_delete && this.modeType != '1'" size="small" type="primary" icon="el-icon-delete" @click="checkDelete">勾选删除</el-button>
              <el-button v-if="permission.system_personnel_photoDown_all_delete && this.modeType != '1'" size="small" type="primary" icon="el-icon-delete" @click="allCheckDelete">全部删除</el-button>
            </div>
            <div style="margin-top: 15px;">
              <el-table
                ref="multipleTable"
                :data="tableData"
                tooltip-effect="dark"
                style="width: 100%"
                height="760"
                border
                stripe="true"
                @selection-change="handleSelectionChange"
                :header-cell-style="{background:'#f5f7fa'}"
              >
                <el-table-column
                  type="selection"
                  width="55"
                >
                </el-table-column>
                <el-table-column
                  type="index"
                  :index="indexMethod"
                  fixed
                >
                </el-table-column>
                <el-table-column
                  prop="userName"
                  label="姓名"
                  width ="100"
                  fixed
                >
                </el-table-column>
                <el-table-column
                  prop="sex"
                  :formatter="sexFormate"
                  label="性别"
                  width ="50"
                >
                </el-table-column>
                <el-table-column
                  prop="studentJobNo"
                  label="编号"
                  width ="120"
                >
                </el-table-column>
                <el-table-column
                  prop="personnelType"
                  :formatter="personnelTypeFormate"
                  label="人员类别"
                  width ="120"
                >
                </el-table-column>
                <el-table-column
                  prop="mealsTypeName"
                  label="用餐类别"
                  width ="130"
                >
                </el-table-column>
                <el-table-column
                  prop="deptName"
                  label="部门"
                  width ="130"
                  overHidden="true"
                >
                </el-table-column>
                <el-table-column
                  prop="attributeName"
                  label="人员属性"
                  width ="130"
                >
                </el-table-column>
                <el-table-column
                  label="照片"
                >
                  <template slot-scope="scope">
                    　　　　<img :src="scope.row.avatar" width="40" height="40" class="head_pic" @click="handleClickPreview(scope.row.avatar)"/>
                    　　</template>
                </el-table-column>
                <el-table-column
                  prop="cardId"
                  label="一卡通卡号"
                  width ="130"
                >
                </el-table-column>
                <el-table-column
                  prop="effectiveTime"
                  label="有效期"
                  width ="130"
                >
                </el-table-column>
                <el-table-column
                  prop="downCount"
                  label="已下传窗口设备数量"
                >
                </el-table-column>
                <el-table-column
                  prop="downCount"
                  label="未下传窗口设备数量"
                >
                </el-table-column>
                <el-table-column
                  label="操作"
                  width="300"
                >
                  <template
                    slot-scope="scope"
                    style="text-align: left;"
                  >
                    <el-button size="small" type="primary" icon="el-icon-upload" @click="down(scope.row)">下传</el-button>
                    <el-button size="small" type="primary" icon="el-icon-delete" @click="deteleDevicePersonnel(scope.row)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>

              <div
                class="block"
                style="text-align: right;margin-top:5px;"
              >
                <el-pagination
                  background
                  @size-change="sizeChange"
                  @current-change="currentChange"
                  :page-sizes="[10,20,30,40,50,100, 200, 300,400,500]"
                  :page-size="10"
                  :total="this.page.total"
                  @on-load="onLoad"
                  layout="total, sizes, prev, pager, next, jumper"
                >
                </el-pagination>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="mess-content" v-if="this.activeIdx == 1">
        <avue-crud :option="windowDeviceoption" :table-loading="windowDeviceLoading" :data="windowDeviceData" :page="windowDevicePage"
          :permission="permissionList" :before-open="beforeOpen" v-model="windowDeviceForm" ref="windowDeviceForm"
          @search-change="windowDeviceSearchChange" @search-reset="windowDeviceSearchReset" @selection-change="windowDeviceSelectionChange"
          @current-change="windowDeviceCurrentChange" @size-change="windowDeviceSizeChange" @on-load="onLoadWindowDevice">
          <template slot="menu" slot-scope="{row}">
            <el-button class="filter-item" size="small" type="primary" @click="personnelManage(row)">人员管理</el-button>
          </template>
        </avue-crud>
      </div>
    </div>
    <el-dialog :title="`${this.windowDeviceName}`" :visible.sync="deviceVisible" :append-to-body="true" @close="successfulBiddingFormClose"
      width="60%">
      <avue-crud :option="deviceOption" :table-loading="deviceTableLoading" :data="deviceData" :page="devicePage"
        :before-open="beforeOpen" v-model="deviceForm" ref="deviceForm" @search-change="deviceSearchChange"
        @search-reset="deviceSearchReset" @selection-change="deviceSelectionChange" @current-change="deviceCurrentChange"
        @size-change="deviceSizeChange" @on-load="onLoadDevice">
        <template slot="menuLeft">
          <el-button v-if="permission.system_personnel_photoDown_down && this.delDownStatus == 1" class="filter-item" size="small" type="primary" icon="el-icon-upload"
            @click="downPersonnel()">下传</el-button>
        </template>
        <template slot="menuLeft">
          <el-button v-if="permission.system_personnel_photoDown_del && this.delDownStatus == 2" class="filter-item" size="small" type="primary" icon="el-icon-delete"
            @click="deleteDown">删除</el-button>
        </template>
      </avue-crud>
    </el-dialog>
    <el-dialog :title="`人员管理`" :visible.sync="personnelVisible" :append-to-body="true" @close="closePersonnelDown"
      width="80%">
      <div class="tableUser">
        <div class="btnUser">
          <avue-form
            ref="batchEditForm"
            v-model="batchEditForm1"
            :option="batchEditOption"
          >
          </avue-form>
          <div class="button" style="position: absolute;top: 6.5vh;left: 21vw;">
            <el-button
              size="small"
              type="primary"
              icon="el-icon-search"
              style="margin-left: 20px;"
              @click="selectSearch1"
            >查询</el-button>
            <el-button
              size="small"
              type="primary"
              icon="el-icon-reset"
              style="margin-left: 20px;"
              @click="cloceSearch1"
            >清空</el-button>
            <!-- </template> -->
          </div>
        </div>
        <div class="tableUserBtn">
          <el-button v-if="permission.system_personnel_photoDown_download" class="filter-item" size="small" type="primary" icon="el-icon-upload" @click="checkPersonnelDownload">勾选下传</el-button>
          <el-button v-if="permission.system_personnel_photoDown_all_download" class="filter-item" size="small" type="primary" icon="el-icon-upload" @click="allCheckDownload2">全部下传</el-button>
          <el-button v-if="permission.system_personnel_photoDown_delete" class="filter-item" size="small" type="primary" icon="el-icon-delete" @click="checkPersonnelDelete">勾选删除</el-button>
          <el-button v-if="permission.system_personnel_photoDown_all_delete" class="filter-item" size="small" type="primary" icon="el-icon-delete" @click="allCheckDelete2">全部删除</el-button>
        </div>
        <div style="margin-top: 15px;">
          <el-table
            ref="multipleTable"
            :data="personnelData"
            tooltip-effect="dark"
            style="width: 100%"
            height="760"
            border
            stripe="true"
            @selection-change="handleSelectionChange"
            :header-cell-style="{background:'#f5f7fa'}"
          >
            <el-table-column
              type="selection"
              width="55"
            >
            </el-table-column>
            <el-table-column
              type="index"
              :index="indexMethod"
              fixed
            >
            </el-table-column>
            <el-table-column
              prop="userName"
              label="姓名"
              width ="100"
              fixed
            >
            </el-table-column>
            <el-table-column
              prop="sex"
              :formatter="sexFormate"
              label="性别"
              width ="50"
            >
            </el-table-column>
            <el-table-column
              prop="studentJobNo"
              label="编号"
              width ="120"
            >
            </el-table-column>
            <el-table-column
              prop="personnelType"
              :formatter="personnelTypeFormate"
              label="人员类别"
              width ="120"
            >
            </el-table-column>
            <el-table-column
              prop="mealsTypeName"
              label="用餐类别"
              width ="130"
            >
            </el-table-column>
            <el-table-column
              prop="deptName"
              label="部门"
              width ="130"
              overHidden="true"
            >
            </el-table-column>
            <el-table-column
              prop="attributeName"
              label="人员属性"
              width ="130"
            >
            </el-table-column>
            <el-table-column
              label="照片"
            >
              <template slot-scope="scope">
                　　　　<img :src="scope.row.avatar" width="40" height="40" class="head_pic" @click="handleClickPreview(scope.row.avatar)"/>
                　　</template>
            </el-table-column>
            <el-table-column
              prop="cardId"
              label="一卡通卡号"
              width ="130"
            >
            </el-table-column>
            <el-table-column
              prop="effectiveTime"
              label="有效期"
              width ="130"
            >
            </el-table-column>
            <el-table-column
              prop="downPerStatus"
              :formatter="downPerStatus"
              label="下传状态"
            >
            </el-table-column>
            <el-table-column
              label="操作"
              width="300"
            >
              <template
                slot-scope="scope"
                style="text-align: left;"
              >
                <el-button v-if="permission.system_personnel_photoDown_down" size="small" type="primary" icon="el-icon-upload" @click="downPersonnel2(scope.row)">下传</el-button>
                <el-button v-if="permission.system_personnel_photoDown_del" size="small" type="primary" icon="el-icon-delete" @click="deletePersonnel2(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>

          <div
            class="block"
            style="text-align: right;margin-top:5px;"
          >
            <el-pagination
              background
              @size-change="personnelSizeChange"
              @current-change="personnelCurrentChange"
              :page-sizes="[10,20,30,40,50,100, 200, 300,400,500]"
              :page-size="10"
              :total="this.personnelPage.total"
              @on-load="onLoadPersonnel"
              layout="total, sizes, prev, pager, next, jumper"
            >
            </el-pagination>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 照片弹出窗-->
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
  import {
    getList,
    getDetail,
    add,
    update,
    remove,
    exportPersonnelData,
    batchUpdatePersonnel,
    getPersonnelList,
    downPersonnelDevice,
    deletePersonnelDevice,
    selectDownPersonnelDevice,
    allDownPersonnelDevice,
    allDeletePersonnelDevice,
    getPersonnelDevieceList
  } from "@/api/personnel/systemPersonnel";
  import {
    getDeviceList,
    getWindowDeviceList
  } from "@/api/business/device/index";
  import {
    getDeptTree
  } from "@/api/setting/dept/systemDeptSetting";
  import {
    getDeptAgencyType,
  } from "@/api/system/dept";
  import {
    mapGetters
  } from "vuex";
  import website from '@/config/website';
  const DIC = {
    sex: [{
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
    VAILD: [{
        label: '学生',
        value: '1'
      }, {
        label: '教职工',
        value: '2'
      },

    ],
    PERSONNEL: [{
      label: '停用',
      value: '0'
    }, {
      label: '启用',
      value: '1'
    }, {
      label: '注销',
      value: '2'
    }, {
      label: '锁定',
      value: '3'
    }],
    OPEN: [{
      label: '开通伙食费钱包',
      value: '1'
    }],
    ONLIN: [{
      label: '下线',
      value: '0'
    }, {
      label: '在线',
      value: '1'
    }],
    DOWN: [{
      label: '未下传',
      value: '0'
    }, {
      label: '已下传',
      value: '1'
    }]
  }
  export default {
    data() {
      return {
        tabPosition: '0',
        activeIdx: 0,
        messList: ['按人员列表显示(默认)', '按窗口设备显示'],
        form: {},
        query: {},
        importForm: {},
        editForm: {},
        deviceForm: {},
        personnelForm: {},
        personnelDevice: {},
        batchEditForm: {},
        batchEditForm1: {},
        loading: true,
        deviceTableLoading: true,
        windowDeviceLoading: true,
        personnelLoading: true,
        importDialogVisible: false,
        batchEditVisible: false,
        deviceVisible: false,
        personnelVisible: false,
        dialogVisible: false,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        devicePage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        windowDevicePage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        personnelPage: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        multipleSelection: [],
        multipleSelection1: [],
        selectionDeviceList: [],
        personnelSelectionList: [],
        deviceData: [],
        windowDeviceData: [],
        personnelData: [],
        tableData: [],
        outletsId: undefined,
        personnelId: undefined,
        delDownStatus: undefined,
        windowDeviceName: undefined,
        dialogImageUrl: undefined,
        personnelStatus: undefined,
        windowId: undefined,
        deviceCode: undefined,
        downStatus:undefined,
        delDownStatus2:undefined,
        modeType:undefined,
        searchForm: {
          outletsId: '',
        },
        option: {
          /*  height:'auto',
            calcHeight: 30,*/
          calcHeight: 127,
          height: '800px',
          searchShow: true,
          searchMenuSpan: 6,
          /*    header: false,*/
          tip: false,
          border: true,
          index: true,
          indexLabel:"序号",
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          selection: true,
          column: [{
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              search: true,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
              rules: [{
                required: true,
                message: '请选择性别',
                trigger: 'blur'
              }]
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
              search: true,
              rules: [{
                required: true,
                message: "请输入编号",
                trigger: "blur"
              }],
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
              search: true,
              rules: [{
                required: true,
                message: "请输入用餐类别",
                trigger: "blur"
              }],
            },
            /*            {
                          label: "用餐类别",
                          prop: "mealsType",
                          type: "select",
                          /!*           rules: [{
                                       required: true,
                                       message: "请输入用餐类别",
                                       trigger: "blur"
                                     }],*!/
                        },*/
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
              minWidth: 120,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                label: "title",
                value: "id"
              },
              editDisplay: false,
              viewDisplay: false,
              multiple:true,
              /*slot:true,*/
              search:true,
              hide:true,
              rules: [{
                required: true,
                message: "请输入部门",
                trigger: "click"
              }]
            },
            {
              label: '人员类别',
              prop: 'personnelType',
              type: "radio",
              //slot: true,
              dicData: DIC.VAILD,
              search: true,
              rules: [{
                required: true,
                message: '请选择人员类别',
                trigger: 'blur'
              }]
            },
            /*            {
                          label: "人员属性",
                          prop: "attribute",
                          span: 20,
                          hide: true,
                          type: "checkbox",
                          dicData: DIC.VAILD,
                          rules: [{
                            required: true,
                            message: "请选择人员属性",
                            trigger: "blur"
                          }],
                          mock: {
                            type: 'dic',
                          },

                        },*/
            {
              label: "人员属性",
              prop: "attribute",
              type: "select",
              dicUrl: '/api/service/rabbit-liancan/userRole/dict',
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: "人员照片",
              prop: "avatar",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span: 24,
              slot: true,
              rules: [{
                required: true,
                message: "请上传人员照片",
                trigger: "blur"
              }],
            },
            {
              label: "人员状态",
              prop: "status",
              type: "radio",
              dicData: DIC.PERSONNEL,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              rules: [{
                required: true,
                message: "请输入人员状态",
                trigger: "blur"
              }],
            },
            /*            {
                          label: "人员状态",
                          prop: "status",
                          type: "radio",
                          hide: true,
                          dicData: DIC.PERSONNEL,
                          rules: [{
                            required: true,
                            message: "请输入人员状态",
                            trigger: "blur"
                          }],
                        },*/
            {
              label: "一卡通卡号",
              prop: "cardId",
              type: "input",
              rules: [{
                required: true,
                message: "请输入一卡通卡号",
                trigger: "blur"
              }],
            },
            {
              label: "有效期",
              prop: "effectiveTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              rules: [{
                required: true,
                message: "请选择有效期",
                trigger: "blur"
              }],
              pickerOptions: {
                disabledDate(time) {
                  return time.getTime() < Date.now();
                },
              }
            },
            {
              label: "已下传窗口设备数量",
              prop: "downCount",
              type: "input",
            },
            {
              label: "未下传窗口设备数量",
              prop: "notDownCount",
              type: "input",
            },
          ]
        },
        windowDeviceoption: {
      /*    height: 'auto',
          calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          selection: true,
          labelWidth: 150,
          dialogWidth: "70%",
          column: [{
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "窗口组",
              prop: "groupName",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: "窗口号",
              prop: "number",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 60,
            },
            {
              label: "窗口名称",
              prop: "windowName",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: "人脸识别IP",
              prop: "faceRecognitionIp",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 120,
            },
            {
              label: "消费机主板IP",
              prop: "consumerComputerIp",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 120,
            },
            {
              label: "设备码",
              prop: "equipmentCode",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 200,
            },
            {
              label: "设备默认种类",
              prop: "equipmentType",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
              width: 120,
            },
            {
              label: "SD卡使用情况",
              prop: "sdCard",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: '是否在线',
              prop: 'onlineStatus',
              type: "radio",
              //slot: true,
              dicData: DIC.ONLIN,
              width: 90,
            },
            {
              label: "已下传人员照片数量",
              prop: "downCount",
              type: "input",
              span: 24,
            },
            {
              label: "未下传人员照片数量",
              prop: "notDownCount",
              type: "input",
              span: 24,
            },
          ]
        },
        searchOption: {
          emptyBtn: false,
          submitBtn: false,
          column: [{
            label: "营业网点",
            prop: "outletsId",
            type: "select",
            //dicFlag: false,
            multiple: false,
            dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
            props: {
              label: "name",
              value: "id"
            },
            disabled: false,
          }, ]
        },
        deviceOption: {
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          selection: true,
          labelWidth: 150,
          dialogWidth: "70%",
          menu: false,
          column: [{
            label: "窗口设备",
            prop: "windowDevice",
            type: "input",
            span: 24,
          }, {
            label: "状态",
            prop: "deviceStatus",
            type: "input",
            span: 24,
          }]
        },
        personnelOption: {
          /*        height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          selection: true,
          labelWidth: 150,
          dialogWidth: "70%",
          column: [{
              label: "主键",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              span: 24,
              search: true,
              rules: [{
                required: true,
                message: "请输入姓名",
                trigger: "blur"
              }],
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
              rules: [{
                required: true,
                message: '请选择性别',
                trigger: 'blur'
              }]
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
              search: true,
              rules: [{
                required: true,
                message: "请输入编号",
                trigger: "blur"
              }],
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
              search: true,
              rules: [{
                required: true,
                message: "请输入用餐类别",
                trigger: "blur"
              }],
            },
            {
              label: "部门",
              prop: "deptName",
              type: "input",
              overHidden: true,
              minWidth: 120,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                label: "title",
                value: "id"
              },
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              slot: true,
              search: true,
              hide: true,
              rules: [{
                required: true,
                message: "请输入部门",
                trigger: "click"
              }]
            },
            {
              label: '人员类别',
              prop: 'personnelType',
              type: "select",
              //slot: true,
              dicData: DIC.VAILD,
              search: true,
              rules: [{
                required: true,
                message: '请选择人员类别',
                trigger: 'blur'
              }]
            },
            /*            {
                          label: "人员属性",
                          prop: "attribute",
                          span: 20,
                          hide: true,
                          type: "checkbox",
                          dicData: DIC.VAILD,
                          rules: [{
                            required: true,
                            message: "请选择人员属性",
                            trigger: "blur"
                          }],
                          mock: {
                            type: 'dic',
                          },

                        },*/
            {
              label: "人员属性",
              prop: "attribute",
              type: "select",
              search: true,
              dicUrl: '/api/service/rabbit-liancan/userRole/dict',
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: "人员照片",
              prop: "avatar",
              type: 'upload',
              listType: 'picture-img',
              action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
              propsHttp: {
                res: 'data',
                url: 'link',
              },
              span: 24,
              slot: true,
              rules: [{
                required: true,
                message: "请上传人员照片",
                trigger: "blur"
              }],
            },
            {
              label: "人员状态",
              prop: "status",
              type: "radio",
              dicData: DIC.PERSONNEL,
              rules: [{
                required: true,
                message: "请输入人员状态",
                trigger: "blur"
              }],
            },
            {
              label: "一卡通卡号",
              prop: "cardId",
              type: "input",
              rules: [{
                required: true,
                message: "请输入一卡通卡号",
                trigger: "blur"
              }],
            },
            {
              label: "有效期",
              prop: "effectiveTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              rules: [{
                required: true,
                message: "请选择有效期",
                trigger: "blur"
              }],
              pickerOptions: {
                disabledDate(time) {
                  return time.getTime() < Date.now();
                },
              }
            },
            {
              label: "下传状态",
              prop: "downPerStatus",
              span: 20,
              type: "select",
              dicData: DIC.DOWN,
              search: true,
              mock: {
                type: 'dic',
              },

            },
          ]
        },
        batchEditOption: {
          emptyBtn: false,
          submitBtn: false,
          column: [
            {
              label: "姓名",
              prop: "userName",
              type: "input",
              span: 6
            },
            {
              label: "编号",
              prop: "studentJobNo",
              type: "input",
              span: 6
            },
            {
              label: "部门",
              prop: "deptId",
              type: "tree",
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                label: "title",
                value: "id"
              },
              span: 6
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              span: 6,
              props: {
                res: "data",
                label: "name",
                value: "id"
              }
            },
            {
              label: '人员类别',
              prop: 'personnelType',
              type: "select",
              //slot: true,
              dicData: DIC.VAILD,
              span: 6,
            },
          ]

        },
      }
    },
    computed: {
      ...mapGetters(["permission", 'userInfo']),
      permissionList() {
        return {
          /*          addBtn: this.vaildData(this.permission.system_personnel_add, false),
                    viewBtn: this.vaildData(this.permission.system_personnel_view, false),
                    delBtn: this.vaildData(this.permission.system_personnel_delete, false),
                    editBtn: this.vaildData(this.permission.system_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.multipleSelection.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      },
      deviceIds() {
        let deviceIds = [];
        this.selectionDeviceList.forEach(ele => {
          deviceIds.push(ele.windowId);
        });
        return deviceIds.join(",");
      },
    },

    created() {
      getDeptAgencyType().then(res =>{
        if (res.data.data.applicationMode === '3'){
          this.modeType = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
          this.windowDeviceoption.menu = false;
        }else if (res.data.data.applicationMode === '0'){
          this.modeType = '1';
          this.option.addBtn = false;
          this.option.delBtn = false;
          this.option.viewBtn = false;
          this.option.editBtn = false;
          this.option.menu = false;
          this.windowDeviceoption.menu = false;
        }else {
          this.modeType = '2';
        }
      })
      const {
        messName
      } = this.$route.query
      if (messName) {
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log(this.activeIdx)
      }
      this.page.currentPage = 1;
      this.onLoad(this.page);
    },
    methods: {
      menuClick(idx) {
        console.log(">>>>>>>>>>>>>", idx)
        if (this.activeIdx == idx) return
        this.activeIdx = idx
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      personnelSearchReset() {
        this.query = {};
        this.onLoadPersonnel(this.personnelPage);
      },
      deviceSearchReset() {
        this.query = {};
        this.onLoadDevice(this.devicePage);
      },
      windowDeviceSearchReset() {
        this.query = {};
        this.onLoadWindowDevice(this.windowDevicePage);
      },
      searchChange(params, done) {
        this.searchForm = params;
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      personnelSearchChange(params, done) {
        this.query = params;
        this.personnelPage.currentPage = 1
        this.onLoadPersonnel(this.personnelPage, params);
        done();
      },
      windowDeviceSearchChange(params, done) {
        this.query = params;
        this.windowDevicePage.currentPage = 1
        this.onLoadWindowDevice(this.windowDevicePage, params);
        done();
      },
      deviceSearchChange(params, done) {
        this.query = params;
        this.devicePage.currentPage = 1
        this.onLoadDevice(this.devicePage, params);
        done();
      },
      selectionChange(list) {
        this.multipleSelection = list;
      },
      personnelSelectionChange(list) {
        this.multipleSelection = list;
      },
      windowDeviceSelectionChange(list) {
        this.selectionList = list;
      },
      deviceSelectionChange(list) {
        this.selectionDeviceList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.refreshTable();
        this.$refs.crud.toggleSelection();
      },
      personnelSelectionClear() {
        this.selectionList = [];
        this.$refs.personnelForm.toggleSelection();
      },
      windowDeviceSelectionClear() {
        this.selectionList = [];
        this.$refs.windowDeviceForm.toggleSelection();
      },
      deviceSelectionClear() {
        this.selectionList = [];
        this.$refs.deviceForm.toggleSelection();
      },
      currentChange(currentPage) {
        this.page.currentPage = currentPage;
        this.onLoad(this.page);
      },
      personnelCurrentChange(currentPage) {
        this.personnelPage.currentPage = currentPage;
        this.onLoadPersonnel(this.personnelPage);
      },
      windowDeviceCurrentChange(currentPage) {
        this.windowDevicePage.currentPage = currentPage;
      },
      deviceCurrentChange(currentPage) {
        this.devicePage.currentPage = currentPage;
      },
      sizeChange(pageSize) {
        this.page.pageSize = pageSize;
        this.onLoad(this.page);
      },
      personnelSizeChange(pageSize) {
        this.personnelPage.pageSize = pageSize;
        this.onLoadPersonnel(this.personnelPage);
      },
      windowDeviceSizeChange(pageSize) {
        this.windowDevicePage.pageSize = pageSize;
      },
      deviceSizeChange(pageSize) {
        this.devicePage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        params.userName = this.batchEditForm.userName;
        params.studentJobNo = this.batchEditForm.studentJobNo;
        params.deptId = this.batchEditForm.deptId;
        params.mealsType = this.batchEditForm.mealsType;
        params.personnelType = this.batchEditForm.personnelType;
        getPersonnelList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.tableData = data.records;
        });
      },
      onLoadDevice(page, params = {}) {
        this.deviceTableLoading = true;
        getDeviceList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.outletsId).then(res => {
          const data = res.data.data;
          this.devicePage.total = data.total;
          this.deviceData = data.records;
          this.deviceTableLoading = false;
          this.deviceSelectionClear();
        });
      },
      onLoadWindowDevice(page, params = {}) {
        this.windowDeviceLoading = true;
        getWindowDeviceList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.outletsId).then(
          res => {
            const data = res.data.data;
            this.windowDevicePage.total = data.total;
            this.windowDeviceData = data.records;
            this.windowDeviceLoading = false;
            this.windowDeviceSelectionClear();
          });
      },
      // onLoadPersonnel(page, params = {}) {
      //   getPersonnelDevieceList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.windowId).then(
      //     res => {
      //       const data = res.data.data;
      //       this.personnelPage.total = data.total;
      //       this.personnelData = data.records;
      //     });
      // },
      onLoadPersonnel(page, params = {}) {
        console.log(this.batchEditForm1)
        if(this.batchEditForm1.userName != ''){
          params.userName = this.batchEditForm1.userName;
        }
        if(this.batchEditForm1.studentJobNo != ''){
          params.studentJobNo = this.batchEditForm1.studentJobNo;
        }
        if(this.batchEditForm1.deptId != ''){
          params.deptId = this.batchEditForm1.deptId;
        }
        if(this.batchEditForm1.mealsType != ''){
          params.mealsType = this.batchEditForm1.mealsType;
        }
        if(this.batchEditForm1.personnelType != ''){
          params.personnelType = this.batchEditForm1.personnelType;
        }
        getPersonnelDevieceList(page.currentPage, page.pageSize, Object.assign(params, this.query), this.windowId).then(
          res => {
            const data = res.data.data;
            this.personnelPage.total = data.total;
            this.personnelData = data.records;
          });
      },
      changOutlets(value) {
        this.outletsId = this.searchForm.outletsId;
        this.onLoadWindowDevice(this.windowDevicePage)
      },
      down(row){
        this.downStatus = "find";
        this.delDownStatus = 1;
        this.personnelId = row.id;
        this.multipleSelection = [];
        this.multipleSelection.push(row.id);
        this.onLoadDevice(this.devicePage);
        this.windowDeviceName = "下传";
        this.deviceVisible = true;
      },
      deteleDevicePersonnel(row) {
        this.delDownStatus = 2;
        this.personnelId = row.id;
        this.multipleSelection = [];
        this.multipleSelection.push(row.id);
        this.onLoadDevice(this.devicePage);
        this.windowDeviceName = "删除";
        this.delDownStatus2 = "find";
        this.deviceVisible = true;
      },
      checkDownload(row) {
        if (this.multipleSelection.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.downStatus = "more";
        this.delDownStatus = 1;
        this.personnelId = row.id;
        this.onLoadDevice(this.devicePage);
        this.windowDeviceName = "下传";
        this.personnelStatus = 1;
        this.deviceVisible = true;
      },
      allCheckDownload(row) {
        this.delDownStatus = 1;
        this.personnelId = row.id;
        this.onLoadDevice(this.devicePage);
        this.windowDeviceName = "下传";
        this.personnelStatus = 3;
        this.deviceVisible = true;
      },
      checkDelete(row) {
        if (this.multipleSelection.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.delDownStatus2 = "more";
        this.delDownStatus = 2;
        this.personnelId = row.id;
        this.onLoadDevice(this.devicePage);
        this.windowDeviceName = "删除";
        this.deviceVisible = true;
      },
      allCheckDelete(row) {
        this.delDownStatus = 2;
        this.personnelId = row.id;
        this.onLoadDevice(this.devicePage);
        this.windowDeviceName = "删除";
        this.personnelStatus = 4;
        this.deviceVisible = true;
      },
      saveDown() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
      },
      deleteDown() {
        if (this.selectionDeviceList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        if (this.delDownStatus2 === "more"){
          this.personnelDevice.personnelId = this.ids;
        }else {
          this.personnelDevice.personnelId = this.personnelId
        }
        this.personnelDevice.ids = this.deviceIds;
        if (this.personnelStatus == 4) {
          this.personnelDevice.type = "delete_all";
          allDeletePersonnelDevice(this.personnelDevice).then(() => {
            this.onLoadDevice(this.devicePage)
            this.multipleSelection = [];
            this.deviceVisible = false;
            this.$message({
              type: "success",
              message: "操作成功，等待人脸机反馈!"
            });
          }, error => {
            this.deviceVisible = false;
            window.console.log(error);
          });
        } else {
          deletePersonnelDevice(this.personnelDevice).then(() => {
            this.page.currentPage = 1;
            this.onLoad(this.page);
            this.$refs.multipleTable.clearSelection();
            this.multipleSelection = [];
            this.deviceVisible = false;
            this.$message({
              type: "success",
              message: "操作成功，等待人脸机反馈"
            });
          }, error => {
            this.deviceVisible = false;
            window.console.log(error);
          });
        }
      },
      personnelManage(row) {
        this.windowId = row.windowId
        this.deviceCode = row.equipmentCode
        this.personnelPage.currentPage = 1
        this.onLoadPersonnel(this.personnelPage)
        this.personnelVisible = true;
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      downPersonnel(row) {
        if (this.selectionDeviceList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        if (this.downStatus === "more"){
          this.personnelDevice.personnelId = this.ids;
        }else {
          this.personnelDevice.personnelId = this.personnelId
        }
        this.personnelDevice.ids = this.deviceIds;
        if (this.personnelStatus == 3) {
          this.personnelDevice.type = "update";
          allDownPersonnelDevice(this.personnelDevice).then(() => {
            this.deviceVisible = false;
            this.page.currentPage = 1;
            this.onLoad(this.page);
            this.$refs.multipleTable.clearSelection();
            this.multipleSelection = [];
            this.$message({
              type: "success",
              message: "操作成功，等待人脸机反馈"
            });
          }, error => {
            this.deviceVisible = false;
            window.console.log(error);
          });
        } else {
          downPersonnelDevice(this.personnelDevice).then(() => {
            this.deviceVisible = false;
            this.page.currentPage = 1;
            this.onLoad(this.page);
            this.$refs.multipleTable.clearSelection();
            this.multipleSelection = [];
            this.$message({
              type: "success",
              message: "操作成功，等待人脸机反馈"
            });
          }, error => {
            this.deviceVisible = false;
            window.console.log(error);
          });
        }
      },
      downPersonnel2(row) {
        this.personnelDevice.ids = this.windowId;
        this.personnelDevice.personnelId = row.id
        this.personnelDevice.type = "update";
        downPersonnelDevice(this.personnelDevice).then(() => {
          this.personnelPage.currentPage = 1;
          this.onLoadPersonnel(this.personnelPage)
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈!"
          });
        }, error => {
          window.console.log(error);
        });
      },
      deletePersonnel2(row) {
        this.personnelDevice.ids = this.windowId;
        this.personnelDevice.personnelId = row.id
        this.personnelDevice.type = "delete";
        deletePersonnelDevice(this.personnelDevice).then(() => {
          this.personnelPage.currentPage = 1;
          this.onLoadPersonnel(this.personnelPage)
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈!"
          });
        }, error => {
          window.console.log(error);
        });
      },
      checkPersonnelDownload() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.personnelDevice.personnelId = this.ids;
        this.personnelDevice.ids = this.windowId;
        downPersonnelDevice(this.personnelDevice).then(() => {
          this.personnelPage.currentPage = 1;
          this.onLoadPersonnel(this.personnelPage)
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          window.console.log(error);
        });
      },
      checkPersonnelDelete() {
        if (this.multipleSelection.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.personnelDevice.personnelId = this.ids;
        this.personnelDevice.ids = this.windowId;
        deletePersonnelDevice(this.personnelDevice).then(() => {
          this.personnelPage.currentPage = 1;
          this.onLoadPersonnel(this.personnelPage)
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          window.console.log(error);
        });
      },
      allCheckDownload2() {
        this.personnelDevice.ids = this.windowId;
        this.personnelDevice.type = "update";
        allDownPersonnelDevice(this.personnelDevice).then(() => {
          this.personnelPage.currentPage = 1;
          this.onLoadPersonnel(this.personnelPage)
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          window.console.log(error);
        });
      },
      allCheckDelete2() {
        this.personnelDevice.ids = this.windowId;
        this.personnelDevice.type = "delete_all";
        allDeletePersonnelDevice(this.personnelDevice).then(() => {
          this.personnelPage.currentPage = 1;
          this.onLoadPersonnel(this.personnelPage)
          this.$message({
            type: "success",
            message: "操作成功，等待人脸机反馈"
          });
        }, error => {
          window.console.log(error);
        });
      },
      closePersonnelDown() {
        this.deviceCode = "";
        this.query = {};
        this.personnelVisible = false;
      },
      sexFormate(row, index) {
        if (row.sex == 1) {
          return "男";
        } else if (row.sex == 2) {
          return "女";
        }
      },
      personnelTypeFormate(row, index) {
        if (row.personnelType == 1) {
          return "人员(1)";//2023-02-03需求更改人员类别，原来1为学生,2为教职工
        } else if (row.personnelType == 2) {
          return "人员(2)";//2023-02-03需求更改人员类别，原来1为学生,2为教职工
        }
      },
      downPerStatus(row,index){
        if (row.downPerStatus == 0) {
          return "未下传";
        } else if (row.downPerStatus == 1) {
          return "已下传";
        }
      },
      indexMethod(index) {
        return (this.page.currentPage - 1) * this.page.pageSize + index + 1
      },
      handleSelectionChange(val) {
        this.multipleSelection = val;
      },
      handleSelectionChange1(val) {
        this.multipleSelection = val;
      },
      selectSearch() {
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      cloceSearch() {
        this.batchEditForm.userName = "";
        this.batchEditForm.studentJobNo = "";
        this.batchEditForm.deptId = "";
        this.batchEditForm.mealsType = "";
        this.batchEditForm.personnelType = "";
        this.$refs.multipleTable.clearSelection();
        this.page.currentPage = 1;
        this.onLoad(this.page);
      },
      selectSearch1() {
        this.personnelPage.currentPage = 1;
        this.onLoadPersonnel(this.personnelPage);
      },
      cloceSearch1() {
        this.batchEditForm1.userName = "";
        this.batchEditForm1.studentJobNo = "";
        this.batchEditForm1.deptId = "";
        this.batchEditForm1.mealsType = "";
        this.batchEditForm1.personnelType = "";
        this.personnelPage.currentPage = 1;
        this.onLoadPersonnel(this.personnelPage);
      }
    }
  }
</script>

<style lang="scss" scoped>
  .all-mess {
    background: #fff;
    overflow: hidden;

    .mess-header {
      display: flex;
      height: 50px;
      background: #F1F6FF;

      ::v-deep div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;

        &:hover {
          color: #3775da;
        }

        &.acitve {
          color: #3775da;
          background: #fff;
        }
      }
    }

    .mess-content {
      height: 1100px;
      padding: 0 20px;
      box-sizing: border-box;

      .mess-item {
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;

        .mess-name {
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20rpx;
        }

        .mess-detail {
          color: #3775da;
          text-decoration: underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }

    .mess-footer {
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;

      .mess-but {
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }

    .btnUser {
      position: relative;
      .avue-group__item {
        margin-bottom: 0px !important;
      }
      // border:1px solid #000;
      .button {
        position: absolute;
        top: 60px;
        left: 442px;
      }
      .el-col-24 {
        width: 25% !important;
      }
    }
  }
</style>
