<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.order_merchant_config_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api//merchant/orderMerchantConfig";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
          height:'auto',
          calcHeight: 30,
          labelWidth: 120,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          column: [
            {
              label: "主键",
              prop: "id",
              type: "input",
              hide:true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "学校/单位",
              prop: "companyId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select2",
              props: {
                label: "deptName",
                value:"id"
              },
              span: 24,
              hide: false,
              search:true,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "支付通道",
              prop: "channel",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=pay_channel",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              span: 24,
              rules: [{
                required: true,
                message: "请选择支付通道",
                trigger: "blur"
              }],
            },
            {
              label: "收费类型",
              prop: "type",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=charge_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              span: 24,
              rules: [{
                required: true,
                message: "请选择收费类型",
                trigger: "blur"
              }],
            },
            {
              label: "小程序ID",
              prop: "appId",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入小程序ID",
                trigger: "blur"
              }],
            },
            {
              label: "商户编号",
              prop: "merchant",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入商户号",
                trigger: "blur"
              }],
            },
            {
              label: "商户应用编号",
              prop: "merchantAppId",
              type: "input",
              span: 24,
            },
            {
              label: "商户秘钥",
              prop: "merchantKey",
              type: "input",
              span: 24,
              rules: [{
                required: true,
                message: "请输入商户Key值",
                trigger: "blur"
              }],
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.order_merchant_config_add, false),
          viewBtn: this.vaildData(this.permission.order_merchant_config_view, false),
          delBtn: this.vaildData(this.permission.order_merchant_config_delete, false),
          editBtn: this.vaildData(this.permission.order_merchant_config_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
