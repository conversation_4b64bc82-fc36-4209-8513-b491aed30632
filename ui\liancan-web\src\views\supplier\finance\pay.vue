<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="primary" size="small" plain @click="orderPay">订单支付(食材采购)
        </el-button>
        <el-button type="primary" size="small" plain @click="otherCourse">其他科目支付
        </el-button>
        <el-button type="primary" size="small" plain @click="">工资单管理
        </el-button>
      </template>
      <template slot="menu" slot-scope="scope">
        <el-button size="mini" type="text" @click="approval(scope.row)">{{scope.row.approvalStatus == 1 ? "查看":"去审批"}}
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="选择订单" :visible.sync="orderVisible" :append-to-body="true" @close="orderFormClose" width="60%">
      <el-button @click="selectOrder">所属订单</el-button>
    </el-dialog>
    <el-dialog title="选择订单" :visible.sync="ordersVisible" :append-to-body="true" @close="ordersFormClose" width="60%">
      <avue-crud ref="ordersForm" v-model="ordersForm" :option="option2" :data="orderData" @on-load="getFoodOrder" :page="page"
                 :table-loading="menuTableLoading" @selection-change="selectionChange" @search-change="searchChanges">
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectPayer">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="支付订单" :visible.sync="payOrderVisible" :append-to-body="true" @close="payOrderFormClose" width="60%">
      <avue-form ref="payOrderForm" :option="option3" v-model="payOrderForm" @submit="addOrder">
        <template slot="approvedBy" slot-scope="scope">
          <chooseUser ref="listSysUserRef" v-model="payOrderForm.approvedBy" placeholder="选择审批人" :text="payOrderForm.userName"></chooseUser>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="支付订单详情" :visible.sync="payOrderDetailVisible" :append-to-body="true" @close="payOrderDetailFormClose" width="60%">
      <avue-form ref="payOrderDetailForm" :option="option4" v-model="payOrderDetailForm">
        <template slot="approvedBy" slot-scope="scope">
          <chooseUser ref="listSysUserRef" v-model="payOrderDetailForm.approvedBy" placeholder="选择审批人" :text="payOrderDetailForm.userName"></chooseUser>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="show" @click="pass(1)">通过</el-button>
        <el-button v-if="show" @click="pass(2)">不通过</el-button>
        <el-button v-if="pay" @click="pay">去支付</el-button>
      </span>
    </el-dialog>
    <el-dialog title="其他支付订单" :visible.sync="otherOrderVisible" :append-to-body="true" @close="otherOrderFormClose" width="60%">
      <avue-form ref="otherOrderForm" :option="option5" v-model="otherOrderForm" @submit="addOrder">
        <template slot="approvedBy" slot-scope="scope">
          <chooseUser ref="listSysUserRef" v-model="otherOrderForm.approvedBy" placeholder="选择审批人" :text="otherOrderForm.userName"></chooseUser>
        </template>
      </avue-form>
    </el-dialog>
    <el-dialog title="供应商" :visible.sync="supplierVisible" :append-to-body="true" @close="supplierFormClose" width="60%">
      <avue-crud ref="crud" v-model="supplierForm" :option="option6" :data="supplierData" @on-load="loadMenus" :page="page"
                 :table-loading="menuTableLoading" @selection-change="selectionChanges">
      </avue-crud>
      <span slot="footer" class="dialog-footer">
        <el-button @click="selectPayee">确定</el-button>
      </span>
    </el-dialog>
    <el-dialog title="其他订单详情" :visible.sync="otherOrderDetailVisible" :append-to-body="true" @close="otherOrderDetailFormClose" width="60%">
      <avue-form ref="otherOrderDetailForm" :option="option7" v-model="otherOrderDetailForm">
        <template slot="approvedBy" slot-scope="scope">
          <chooseUser ref="listSysUserRef" v-model="payOrderDetailForm.approvedBy" placeholder="选择审批人" :text="payOrderDetailForm.userName"></chooseUser>
        </template>
      </avue-form>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="show" @click="pass(1)">通过</el-button>
        <el-button v-if="show" @click="pass(2)">不通过</el-button>
        <el-button v-if="pay" @click="pay">去支付</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove, getPayee, getFoodOrder, getGoodList, getSupplier} from "@/api/supplier/finance/pay";
  import {mapGetters} from "vuex";
  var DIC = {
    orderStatus: [{
      label: '未配送',
      value: "0"
    },{
      label: '已按期配送',
      value: "1"
    },{
      label: '取消/拒单',
      value: "2"
    }],
    payType: [{
      label: '供应商',
      value: "0"
      },
      /*{
        label: '员工',
        value: "1"
      },*/
    ],
    payCourse: [{
      label: '燃料采购支出',
      value: "1"
    },{
      label: '工资福利',
      value: "2"
    },{
      label: '水电支出',
      value: "3"
    },{
      label: '设备维修支出',
      value: "4"
    },{
      label: '停餐退款',
      value: "5"
    }]
  }
  export default {
    data() {
      return {
        form: {},
        orderForm: {},
        ordersForm: {},
        payOrderForm: {},
        payOrderDetailForm: {},
        otherOrderForm: {},
        supplierForm: {},
        otherOrderDetailForm: {},
        query: {},
        params: {},
        loading: true,
        menuTableLoading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        selectionLists: [],
        orderVisible: false,
        ordersVisible: false,
        payOrderVisible: false,
        payOrderDetailVisible: false,
        otherOrderVisible: false,
        supplierVisible: false,
        otherOrderDetailVisible: false,
        show: false,
        pay: false,
        option: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          selection: true,
          column: [
            {
              label: "支付单号",
              prop: "payNumber",
              type: "input",
              addDisplay: false,
              search: true,
            },
            {
              label: "申请时间",
              prop: "applyTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              search: true,
            },
            {
              label: "交易内容",
              prop: "transactionContent",
              type: "input",
            },
            {
              label: "支出科目",
              prop: "course",
              type: "select",
              span: 24,
              search: true,
              rules: [{
                required: true,
                message: "请输入科目类型",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=pay_course",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              viewDisplay: false,
            },
            {
              label: "支付方式",
              prop: "payType",
              type: "select",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=pay_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              viewDisplay: false,
            },
            {
              label: "收款方",
              prop: "payee",
              type: "input",
              span: 24,
            },
            {
              label: "支付方",
              prop: "payer",
              type: "input",
              span: 24,
            },
            {
              label: "交易明细",
              prop: 'goodsList',
              type: 'dynamic',
              hide: true,
              span: 24,
              children: {
                align: 'center',
                headerAlign: 'center',
                width: '100%',
                addBtn: false,
                delBtn: false,
                column: [{
                  label: 'id',
                  prop: 'id',
                  type: 'input',
                  hide: true,
                  display: false,
                  showColumn: false,
                },
                  {
                    label: "商品",
                    prop: "goodsId",
                    type: "select",
                    dicFlag: false,
                    dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",//todo
                    props: {
                      label: "name",
                      value: "id"
                    },
                  },
                  /*{
                      label: "供应商",
                      prop: "supplierId",
                      type: "input",
                      editDisabled: true,
                  },*/
                  {
                    label: "单价",
                    prop: "price",
                    type: "input",
                  },
                  {
                    label: "采购数量",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "计量单位",
                    prop: "unit",
                    type: "select",
                    dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                    props: {
                      label: "dictValue",
                      value: "dictKey"
                    },
                  },
                  {
                    label: "订单号",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "实际收货数量",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "实际需付",
                    prop: "subtotal",
                    type: "number",
                  },
                ]
              },
              rules: [{
                required: true,
                message: '请选择商品',
                trigger: 'blur'
              }]
            },
            {
              label: "申请人",
              prop: "applicant",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
            },
            {
              label: "审批人",
              prop: "approvedBy",
              type: "input",
              span: 24,
              hide: true,
            },
            {
              label: "审批时间",
              prop: "approvalTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
            },
            {
              label: "审批状态",
              prop: "approvalStatus",
              type: "select",
              addDisplay: false,
              editDisplay: false,
              search: true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=approval_statu",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "审批理由",
              prop: "approvalReason",
              type: "input",
              span: 24,
              hide: true,
            },
            {
              label: "其他材料",
              prop: "otherMaterials",
              type: "input",
              span: 24,
              hide: true,
              viewDisplay: false,
            },
            {
              label: "支付账号",
              prop: "paymentAccount",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              viewDisplay: false,
            },
            {
              label: "收银账号",
              prop: "cashierAccount",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              viewDisplay: false,
            },
            {
              label: "价税合计",
              prop: "priceAndTax",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              rules: [{
                required: true,
                message: "请输入价税合计",
                trigger: "blur"
              }],
            },
          ]
        },
        option1: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn: false,
          selection: true,
          column: [
            {
              label: "所属订单",
              prop: "order",
              type: "button",
            },
          ]
        },
        option2: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn: false,
          selection: true,
          menu: false,
          column: [
            {
              label: "订单号",
              prop: "id",
              type: "input",
            },
            {
              label: "下单时间",
              prop: "createTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd HH:mm:ss",
              display: false,
            },
            {
              label: "采购人",
              prop: "createUser",
              //type: "input",
              type: "select",
              dicUrl: '/api/service/rabbit-user/user-list',
              props: {
                label: "realName",
                value: "id"
              },
            },
            {
              label: "订单确认人",
              prop: "verifyUserId",
              type: "select",
              dicUrl: '/api/service/rabbit-user/user-list',
              props: {
                label: "realName",
                value: "id"
              },
            },
            {
              label: "供应商",
              prop: "supplierId",
              type: "select",
              dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
              props: {
                label: "deptName",
                value: "id"
              },
              search: true
            },
            {
              label: "采购商品",
              prop: 'goodsList',
              type: 'input',
              hide: true,
              span: 24,
              rules: [{
                required: true,
                message: '请选择商品',
                trigger: 'blur'
              }]
            },
            {
              label: "收货人A",
              prop: "consignee",
              //type: "input",
              type: "select",
              dicUrl: '/api/service/rabbit-user/user-list',
              props: {
                label: "realName",
                value: "id"
              },
            },
            {
              label: "收货人B",
              prop: "verifyConsignee",
              type: "select",
              dicUrl: '/api/service/rabbit-user/user-list',
              props: {
                label: "realName",
                value: "id"
              },
            },
            {
              label: "订单状态",
              prop: "orderStatus",
              type: "select",
              dicData: DIC.orderStatus,
            },
            {
              label: "实付金额",
              prop: "totalPrices",
              type: "input",
              display: false,
            },
          ]
        },
        option3: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn: false,
          selection: true,
          //menuBtn: false,
          emptyBtn: false,
          submitText: '申请支付',
          column: [
            {
              label: "支付单号",
              prop: "payNumber",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              search: true,
              disabled: true
            },
            {
              label: "所属订单",
              prop: "transactionContent",
              type: "input",
              span: 24,
              disabled: true
            },
            {
              label: "采购方",
              prop: "payer",
              type: "input",
              span: 24,
              disabled: true
            },
            {
              label: "支付给(供应商)",
              prop: "payee",
              type: "select",
              span: 24,
              dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
              props: {
                label: "deptName",
                value: "id"
              },
              disabled: true
            },
            {
              label: "发票",
              prop: "invoices",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "采购清单",
              prop: 'goodsList',
              type: 'dynamic',
              hide: true,
              span: 24,
              disabled: true,
              children: {
                align: 'center',
                headerAlign: 'center',
                width: '100%',
                addBtn: false,
                delBtn: false,
                column: [{
                  label: 'id',
                  prop: 'id',
                  type: 'input',
                  hide: true,
                  display: false,
                  showColumn: false,
                },
                  {
                    label: "商品",
                    prop: "goodsId",
                    type: "select",
                    dicFlag: false,
                    dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",//todo
                    props: {
                      label: "name",
                      value: "id"
                    },
                  },
                  /*{
                      label: "供应商",
                      prop: "supplierId",
                      type: "input",
                      editDisabled: true,
                  },*/
                  {
                    label: "单价",
                    prop: "price",
                    type: "input",
                  },
                  {
                    label: "采购数量",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "计量单位",
                    prop: "unit",
                    type: "select",
                    dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                    props: {
                      label: "dictValue",
                      value: "dictKey"
                    },
                  },
                  {
                    label: "订单号",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "实际收货数量",
                    prop: "quantity",
                    type: "number",
                  },
                  {
                    label: "实际需付",
                    prop: "realMoney",
                    type: "number",
                  },
                ]
              },
            },
            {
              label: "审批人",
              prop: "approvedBy",
              span: 24,
              hide: true,
              formslot: true, //自定义表单
              rules: [{
                required: false,
                message: '请选择审批人',
                trigger: 'blur'
              }],
            },
            {
              label: "本次支付金额",
              prop: "priceAndTax",
              type: "number",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
              rules: [{
                required: true,
                message: "请输入价税合计",
                trigger: "blur"
              }],
            },
          ]
        },
        option4: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menuBtn: false,
          selection: true,
          column: [
            {
              label: "支付单号",
              prop: "payNumber",
              type: "input",
              addDisplay: false,
              search: true,
              disabled: true
            },
            {
              label: "申请时间",
              prop: "applyTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              search: true,
              disabled: true
            },
            {
              label: "交易内容",
              prop: "transactionContent",
              type: "input",
              disabled: true
            },
            /*{
              label: "支出科目",
              prop: "course",
              type: "select",
              span: 24,
              search: true,
              rules: [{
                required: true,
                message: "请输入科目类型",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=pay_course",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              viewDisplay: false,
              disabled: true
            },
            {
              label: "支付方式",
              prop: "payType",
              type: "select",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=pay_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              viewDisplay: false,
              disabled: true
            },*/
            {
              label: "收款方",
              prop: "payee",
              type: "input",
              span: 24,
              disabled: true
            },
            {
              label: "支付方",
              prop: "payer",
              type: "input",
              span: 24,
              disabled: true
            },
            {
              label: "交易明细",
              prop: 'goodsList',
              type: 'dynamic',
              hide: true,
              span: 24,
              disabled: true,
              children: {
                align: 'center',
                headerAlign: 'center',
                width: '100%',
                addBtn: false,
                delBtn: false,
                column: [{
                  label: 'id',
                  prop: 'id',
                  type: 'input',
                  hide: true,
                  display: false,
                  showColumn: false,
                },
                  {
                    label: "商品",
                    prop: "goodsId",
                    type: "select",
                    dicFlag: false,
                    dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",//todo
                    props: {
                      label: "name",
                      value: "id"
                    },
                    disabled: true
                  },
                  /*{
                      label: "供应商",
                      prop: "supplierId",
                      type: "input",
                      editDisabled: true,
                  },*/
                  {
                    label: "单价",
                    prop: "price",
                    type: "input",
                    disabled: true
                  },
                  {
                    label: "采购数量",
                    prop: "quantity",
                    type: "number",
                    disabled: true
                  },
                  {
                    label: "计量单位",
                    prop: "unit",
                    type: "select",
                    dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                    props: {
                      label: "dictValue",
                      value: "dictKey"
                    },
                    disabled: true
                  },
                  {
                    label: "订单号",
                    prop: "quantity",
                    type: "number",
                    disabled: true
                  },
                  {
                    label: "实际收货数量",
                    prop: "quantity",
                    type: "number",
                    disabled: true
                  },
                  {
                    label: "实际需付",
                    prop: "realMoney",
                    type: "number",
                    disabled: true
                  },
                ]
              },
              rules: [{
                required: true,
                message: '请选择商品',
                trigger: 'blur'
              }]
            },
            {
              label: "申请人",
              prop: "applicant",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              disabled: true
            },
            {
              label: "审批人",
              prop: "approvedBy",
              span: 24,
              hide: true,
              formslot: true, //自定义表单
              rules: [{
                required: false,
                message: '请选择审批人',
                trigger: 'blur'
              }],
            },
            {
              label: "审批时间",
              prop: "approvalTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              disabled: true
            },
            {
              label: "审批状态",
              prop: "approvalStatus",
              type: "select",
              addDisplay: false,
              editDisplay: false,
              search: true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=approval_statu",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              disabled: true
            },
            {
              label: "审批理由",
              prop: "approvalReason",
              type: "input",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入审批理由",
                trigger: "blur"
              }],
            },
            /*{
              label: "其他材料",
              prop: "otherMaterials",
              type: "input",
              span: 24,
              hide: true,
              viewDisplay: false,
            },
            {
              label: "支付账号",
              prop: "paymentAccount",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "收银账号",
              prop: "cashierAccount",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },*/
            {
              label: "本次支付金额",
              prop: "priceAndTax",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              rules: [{
                required: true,
                message: "请输入价税合计",
                trigger: "blur"
              }],
              disabled: true
            },
          ]
        },
        option5: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          editBtn: false,
          selection: true,
          column: [
            {
              label: "支付单号",
              prop: "payNumber",
              type: "input",
              addDisplay: false,
              disabled: true
            },
            {
              label: "收款方类型",
              prop: "payeeType",
              type: "select",
              span: 24,
              rules: [{
                required: true,
                message: "请输入收款方",
                trigger: "blur"
              }],
              dicData: DIC.payType,
              hide: true,
              viewDisplay: false
            },
            {
              label: "支付给",
              prop: "payee",
              type: "input",
              span: 24,
              hide: true,
              rules: [{
                //required: true,
                message: "请输入收款方",
                trigger: "blur"
              }],
              disabled: true
            },
            {
              label: "支付科目",
              prop: "course",
              type: "select",
              dicData: DIC.payCourse,
              span: 24,
              search: true,
              rules: [{
                required: true,
                message: "请输入科目类型",
                trigger: "blur"
              }],
            },
            {
              label: "支付金额",
              prop: "priceAndTax",
              type: "input",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入收入金额",
                trigger: "blur"
              }],
              slot: true
            },
            {
              label: "发票",
              prop: "invoices",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "交易内容",
              prop: "transactionContent",
              type: "textarea",
              span: 24,
            },
            {
              label: "其他材料",
              prop: "otherMaterials",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "审批人",
              prop: "approvedBy",
              span: 24,
              hide: true,
              formslot: true, //自定义表单
              rules: [{
                required: false,
                message: '请选择审批人',
                trigger: 'blur'
              }],
            },
          ]
        },
        option6: {
          height:'auto',
          calcHeight: 30,
          searchShow: false,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: true,
          menu: false,
          column: [{
            label: "企业名称",
            prop: "name",
            type: "input",
          },
            {
              label: "企业性质",
              prop: "companyNatures",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
              props: {
                label: "dictValue",
                value: "dictKey"
              },

            },
            {
              label: "联系人",
              prop: "contactsName",
            },
            {
              label: "联系人手机",
              prop: "mobile",
            },
            {
              label: "地址",
              prop: "address",
            },
            {
              label: "中标供应商商品或服务",
              prop: "commodityType",
            }
          ]
        },
        option7: {
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          addBtn: false,
          editBtn: false,
          delBtn: false,
          menuBtn: false,
          selection: true,
          column: [
            {
              label: "支付单号",
              prop: "payNumber",
              type: "input",
              addDisplay: false,
              search: true,
              disabled: true
            },
            {
              label: "收款方",
              prop: "payee",
              type: "input",
              span: 24,
              disabled: true
            },
            {
              label: "发票",
              prop: "invoices",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "交易内容",
              prop: "transactionContent",
              type: "input",
              disabled: true
            },
            {
              label: "其他材料",
              prop: "otherMaterials",
              type: 'upload',
              dataType: 'array',
              listType: 'picture-card',
              propsHttp: {
                res: 'data',
                url: 'link'
              },
              action: '/api/service/rabbit-resource/oss/endpoint/put-file',
              span: 24,
              hide: true
            },
            {
              label: "本次支付金额",
              prop: "priceAndTax",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              rules: [{
                required: true,
                message: "请输入价税合计",
                trigger: "blur"
              }],
              disabled: true
            },
            {
              label: "申请人",
              prop: "applicant",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              disabled: true
            },
            {
              label: "申请时间",
              prop: "applyTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              search: true,
              disabled: true
            },
            {
              label: "审批人",
              prop: "approvedBy",
              span: 24,
              hide: true,
              formslot: true, //自定义表单
              rules: [{
                required: false,
                message: '请选择审批人',
                trigger: 'blur'
              }],
            },
            {
              label: "审批时间",
              prop: "approvalTime",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              hide: true,
              disabled: true
            },
            {
              label: "审批状态",
              prop: "approvalStatus",
              type: "select",
              addDisplay: false,
              editDisplay: false,
              search: true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=approval_statu",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              disabled: true
            },
            {
              label: "审批理由",
              prop: "approvalReason",
              type: "input",
              span: 24,
              hide: true,
              rules: [{
                required: true,
                message: "请输入审批理由",
                trigger: "blur"
              }],
            },
          ]
        },
        data: [],
        orderData: [],
        supplierData: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.pay_add, false),
          viewBtn: this.vaildData(this.permission.pay_view, false),
          delBtn: this.vaildData(this.permission.pay_delete, false),
          editBtn: this.vaildData(this.permission.pay_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    watch: {
      'otherOrderForm.payeeType'() {
        if (this.otherOrderForm.payeeType !== '' && this.otherOrderForm.payeeType !== undefined) {
          getSupplier().then(res =>{
            this.supplierData = res.data.data;
          })
          this.menuTableLoading = false;
          this.supplierVisible = true;
        }
      },
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChanges(list) {
        this.selectionLists = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionList = [];
          this.$refs.crud.toggleSelection();
        });
      },
      orderPay(){
        this.orderVisible = true
      },
      selectOrder(){
        this.orderVisible = false;
        this.ordersVisible = true;
        //this.getFoodOrder(this.page);
      },
      searchChanges(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.getFoodOrder(this.page, params);
        done();
      },
      getFoodOrder(page, params = {}){
        getFoodOrder(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res =>{
          const data = res.data.data;
          this.orderData = data.records;
          this.page.total = data.total;
          this.menuTableLoading = false;
          this.selectionList = [];
          //this.$refs.payOrderForm.toggleSelection();
        })
      },
      selectPayer(){
        let thisDay = new Date()
        let yyyy = thisDay.getFullYear()
        let MM = thisDay.getMonth() + 1
        if (MM < 10) MM = '0' + MM
        let DD = thisDay.getDate()
        if (DD < 10) DD = '0' + DD
        let hh = thisDay.getHours()
        if (hh < 10) hh = '0' + hh
        let mm = thisDay.getMinutes()
        if (mm < 10) mm = '0' + mm
        let ss = thisDay.getSeconds()
        if (ss < 10) ss = '0' + ss
        this.payOrderForm.payNumber = yyyy  + MM  + DD + hh  + mm  + ss
        console.log("---------"+JSON.stringify(this.payOrderForm.payNumber));
        var transactionContent = ""
        var i = 1
        this.selectionList.forEach(ele => {
          transactionContent = transactionContent + ele.id
          if (i++ < this.selectionList.length){
            transactionContent = transactionContent + ","
          }
        });
        this.payOrderForm.transactionContent = transactionContent;
        console.log("---------"+JSON.stringify(this.payOrderForm.transactionContent));
        //采购方
        getPayee().then(res => {
          this.payOrderForm.payer = res.data.data;
        });
        //供应商
        this.payOrderForm.payee = this.selectionList[0].supplierId
        //支付金额
        var realMoney = 0;
        //采购清单
        getGoodList(this.payOrderForm.transactionContent).then(res =>{
          this.payOrderForm.goodsList = res.data.data;
          //console.log((10+12.12).toFixed(2))
          this.payOrderForm.goodsList.forEach(good =>{
            //console.log(good.realMoney)
            realMoney = (parseInt(realMoney) + good.realMoney).toFixed(2);
          })
          this.payOrderForm.priceAndTax = realMoney;
          console.log(realMoney)
        })
        this.payOrderForm.course = "0"
        this.ordersVisible = false;
        this.payOrderVisible = true
      },
      approval(row){
        console.log(row)
        if (row.course === "0"){
          this.payOrderDetailVisible = true
          getDetail(row.id).then(res => {
            this.payOrderDetailForm = res.data.data;
            getGoodList(res.data.data.transactionContent).then(res =>{
              this.payOrderDetailForm.goodsList = res.data.data;
            })
            if (res.data.data.approvalStatus === "0"){
              this.show = true;
            }else{
              this.pay =  true;
            }
          });
        }else {
          this.otherOrderDetailVisible = true
          getDetail(row.id).then(res => {
            this.otherOrderDetailForm = res.data.data;
            if (res.data.data.approvalStatus === "0"){
              this.show = true;
            }else{
              this.pay =  true;
            }
          });
        }
      },
      addOrder(row, loading, done){
        console.log(row)
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "支付申请发送成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
        this.otherOrderVisible = false
      },
      pass(approvalStatus){
        let pay = {
          "id":this.payOrderDetailForm.id,
          "approvalStatus":approvalStatus,
          "approvalReason":this.payOrderDetailForm.approvalReason,
        };
        console.log(JSON.stringify(pay))
        update(pay).then(() => {
          this.$message({
            type: "success",
            message: "支付申请发送成功!"
          }), error => {
            done();
            window.console.log(error);
          };
        });
      },
      otherCourse(){
        this.otherOrderVisible = true
        let thisDay = new Date()
        let yyyy = thisDay.getFullYear()
        let MM = thisDay.getMonth() + 1
        if (MM < 10) MM = '0' + MM
        let DD = thisDay.getDate()
        if (DD < 10) DD = '0' + DD
        let hh = thisDay.getHours()
        if (hh < 10) hh = '0' + hh
        let mm = thisDay.getMinutes()
        if (mm < 10) mm = '0' + mm
        let ss = thisDay.getSeconds()
        if (ss < 10) ss = '0' + ss
        this.otherOrderForm.payNumber = yyyy  + MM  + DD + hh  + mm  + ss
      },
      selectPayee(){
        this.otherOrderForm.payee = this.selectionLists[0].name;
        getPayee().then(res => {
          this.otherOrderForm.payer = res.data.data;
        });
        this.supplierVisible = false;
      }
    }
  };
</script>

<style>
</style>
