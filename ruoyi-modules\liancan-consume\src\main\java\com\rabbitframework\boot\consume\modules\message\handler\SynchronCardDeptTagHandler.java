package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.liancan.api.RemoteSystemDeptSettingService;
import com.rabbitframework.boot.modules.setting.vo.CardDeptVO;
import com.rabbitframework.boot.modules.setting.entity.SystemDeptSettingEntity;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * SYNCHRON_CARD_DEPT_TAG消息处理器
 * 处理同步接收一卡通部门的数据
 */
@Slf4j
@Component
public class SynchronCardDeptTagHandler implements MessageHandler {

    @DubboReference
    private RemoteSystemDeptSettingService systemDeptSettingService;

    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_DEPT_TAG消息: {}", obj.toJSONString());

            // 按照原来的逻辑解析list格式的数据
            List<CardDeptVO> deptList = new Gson().fromJson(obj.getString("list"),
                    new TypeToken<List<CardDeptVO>>() {
                    }.getType());

            if (deptList == null || deptList.isEmpty()) {
                log.warn("部门列表为空，跳过处理");
                return;
            }

            log.info("监听同步接收一卡通部门的数据，共{}条记录", deptList.size());

            // 按照原来的逻辑处理部门数据
            List<SystemDeptSettingEntity> dList = deptList.stream().map(dept -> {
                SystemDeptSettingEntity systemDeptSettingEntity = new SystemDeptSettingEntity();
                if (dept.getAction().equals("insert") || dept.getAction().equals("update")) {
                    systemDeptSettingEntity.setId(dept.getId());
                    systemDeptSettingEntity.setDeptId(dept.getSchoolId().toString());
                    systemDeptSettingEntity.setDeptName(dept.getDeptName());
                    systemDeptSettingEntity.setSort(0);
                    systemDeptSettingEntity.setParentId(dept.getPid());
                    systemDeptSettingEntity.setCreateTime(new Date());
                    systemDeptSettingEntity.setIsDel(0);
                    systemDeptSettingEntity.setDeptCategory(1);
                } else {
                    // 删除操作
                    systemDeptSettingEntity.setId(dept.getId());
                    systemDeptSettingEntity.setDeptId(dept.getSchoolId().toString());
                    systemDeptSettingEntity.setDeptName(dept.getDeptName());
                    systemDeptSettingEntity.setSort(0);
                    systemDeptSettingEntity.setParentId(dept.getPid());
                    systemDeptSettingEntity.setCreateTime(new Date());
                    systemDeptSettingEntity.setDeptCategory(1);
                    systemDeptSettingEntity.setIsDel(1);
                    systemDeptSettingEntity.setUpdateTime(new Date());
                }
                return systemDeptSettingEntity;
            }).collect(Collectors.toList());

            // 批量保存或更新
            systemDeptSettingService.saveOrUpdateBatch(dList);

            log.info("成功处理一卡通部门数据同步，共处理{}条记录", dList.size());

            // 无论成功失败都不抛异常，确保消息被确认

        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_DEPT_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }

    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_DEPT_TAG";
    }
}