package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.liancan.api.RemoteSystemDeptSettingService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * SYNCHRON_CARD_DEPT_TAG消息处理器
 * 处理同步接收一卡通部门的数据
 */
@Slf4j
@Component
public class SynchronCardDeptTagHandler implements MessageHandler {
    
    @DubboReference
    private RemoteSystemDeptSettingService systemDeptSettingService;
    
    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_DEPT_TAG消息: {}", obj.toJSONString());
            
            // 解析消息参数
            String schoolId = obj.getString("school_id");
            String deptName = obj.getString("dept_name");
            String deptCode = obj.getString("dept_code");
            String cardDeptId = obj.getString("card_dept_id");
            String cardDeptPid = obj.getString("card_dept_pid");
            
            log.info("监听同步接收一卡通部门的数据: schoolId={}, deptName={}, cardDeptId={}", schoolId, deptName, cardDeptId);
            
            // 验证必要参数
            if (!StringUtils.hasText(schoolId)) {
                log.warn("学校ID为空，跳过处理");
                return;
            }
            
            if (!StringUtils.hasText(cardDeptId)) {
                log.warn("一卡通部门ID为空，跳过处理");
                return;
            }
            
            // 同步一卡通部门数据
            boolean syncResult = systemDeptSettingService.synchronizeCardDept(
                schoolId, deptName, deptCode, cardDeptId, cardDeptPid);
            
            if (syncResult) {
                log.info("成功同步一卡通部门数据: schoolId={}, cardDeptId={}, deptName={}", schoolId, cardDeptId, deptName);
            } else {
                log.warn("同步一卡通部门数据失败: schoolId={}, cardDeptId={}, deptName={}", schoolId, cardDeptId, deptName);
            }
            
            // 无论成功失败都不抛异常，确保消息被确认
            
        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_DEPT_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }
    
    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_DEPT_TAG";
    }
}