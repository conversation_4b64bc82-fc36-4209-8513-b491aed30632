<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @row-update="rowUpdate"
                   @row-save="rowSave"
                   @row-del="rowDel"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">

            <template slot="menuLeft">
                <el-button class="filter-item"
                           style="margin-left: 10px;"
                           @click="handleCreate"
                           type="primary"
                           icon="el-icon-plus">添加
                </el-button>
            </template>
            <template slot="baiduMapForm" slot-scope="{row,index}">
                <baidu-map class="map" :center="{lng: 110.92905, lat: 21.69089}" @click="getClickInfo" :zoom="15" :scroll-wheel-zoom="true" >

                </baidu-map>
            </template>
            <template slot-scope="{row}" slot="menu">
                <el-button type="text"
                           icon="el-icon-edit"
                           size="small"
                           plain
                           style="border: 0;background-color: transparent !important;"
                           @click="$refs.crud.rowEdit(row,index)">编辑
                </el-button>
                <el-button type="text"
                           icon="el-icon-delete"
                           size="small"
                           plain
                           style="border: 0;background-color: transparent !important;"
                           @click="$refs.crud.rowDel(row,index)">删除
                </el-button>
            </template>
            <template slot="frontImg" slot-scope="{row}">
                <el-image style="width: 30px; height: 50px" :src="row.frontImg" fit="cover"
                          @click="handleClickPreview(row.frontImg)"></el-image>
            </template>
            <template slot="backImg" slot-scope="{row}">
                <el-image style="width: 30px; height: 50px" :src="row.backImg" fit="cover"
                          @click="handleClickPreview(row.backImg)"></el-image>
            </template>
            <template slot="status" slot-scope="{row}">
                <el-tag v-if="row.status == '1'" type="success">正常</el-tag>
                <el-tag v-if="row.status == '0'" type="danger">停用</el-tag>
            </template>
        </avue-crud>
        <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%"
                   :append-to-body="true">
            <img width="100%" height="100%" :src="dialogImageUrl" alt="">
        </el-dialog>

        //自定义新增弹出框
        <el-dialog title="新增往来单位"
                   append-to-body
                   :visible.sync="addRowShow"
                   v-if="addRowShow"
                   width="50%">
            <div class="el-dialog-div" style="height: 600px;">
                <span
                    style="color: red">提示：请先在系统中查找并选择所要添加的往来单位，接着点击【下一步】继续操作。如果找不到企业，可以手动添加。</span>
                <div style="margin-top: 20px;"></div>
                <avue-crud :option="optionSub"
                           :table-loading="loadingSub"
                           :data="dataSub"
                           :page="pageSub"
                           :before-open="beforeOpenSub"
                           v-model="formSub"
                           ref="crudSub"
                           @search-change="searchChangeSub"
                           @search-reset="searchResetSub"
                           @selection-change="selectionChangeSub"
                           @current-change="currentChangeSub"
                           @size-change="sizeChangeSub"
                           @on-load="onLoadSub">

                    <template slot="frontImg" slot-scope="{row}">
                        <el-image style="width: 30px; height: 50px" :src="row.frontImg" fit="cover"
                                  @click="handleClickPreview(row.frontImg)"></el-image>
                    </template>
                    <template slot="backImg" slot-scope="{row}">
                        <el-image style="width: 30px; height: 50px" :src="row.backImg" fit="cover"
                                  @click="handleClickPreview(row.backImg)"></el-image>
                    </template>

                    <template slot="menuRight">
                        <span style="margin-left: 20px;">找不到企业，请&nbsp;&nbsp;</span>
                        <el-button type="primary"
                                   size="small"
                                   @click="addRowSelfHandler">手动添加往来单位
                        </el-button>
                    </template>
                </avue-crud>
            </div>
            <span slot="footer" class="dialog-footer">
            <el-button @click="closeSelectLandle">返&nbsp;&nbsp;回</el-button>
            <el-button type="primary" @click="nextStepHandle">下一步</el-button>
          </span>
        </el-dialog>

        //新增往来单位下一步
        <el-dialog title="新增"
                   append-to-body
                   :visible.sync="addRowNextStepShow"
                   :close-on-click-modal="false"
                   width="35%">
            <div class="el-dialog-div" style="height: 350px;">
                <el-form>
                    <el-col>
                        <el-form-item label="已选单位:">
                            <el-input style="width: 80%;overflow: hidden" v-model="selectCustomers.name"
                                      readonly></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col>
                        <el-form-item label="食堂:">
                            <el-select v-model="canteenId" placeholder="请选择食堂" value="">
                                <el-option
                                    v-for="item in canteenList"
                                    :key="item.id"
                                    :label="item.deptName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col>
                        <el-form-item label="类型(上述单位是本公司的):" required>
                            <el-checkbox-group v-model="customerTypes">
                                <el-checkbox label="0">供应商</el-checkbox>
                                <el-checkbox label="1">客户</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                    </el-col>
                    <el-col>
                        <el-form-item label="送货线路:">
                            <el-select v-model="deliveryRoute" placeholder="请选择送货线路">
                                <el-option
                                    v-for="item in deliveryRouteList"
                                    :key="item.id"
                                    :label="item.lineName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                </el-form>

            </div>
            <span slot="footer" class="dialog-footer">
            <el-button @click="closeSaveHandle">取&nbsp;&nbsp;消</el-button>
            <el-button type="primary" @click="nextStepSaveHandle">保&nbsp;&nbsp;存</el-button>
          </span>
        </el-dialog>

    </basic-container>
</template>

<script>
import {add, addCustomer, getDetail, getList, getParentList, remove, update, getCanteenList, getDeliveryRouteList} from "@/api/supplier/supplierBaseCustomer";
import {mapGetters} from "vuex";

var DIC = {
    customerType: [{
        label: '供应商',
        value: '0'
    }, {
        label: '客户',
        value: '1'
    }],
    sourceType: [{
        label: '自增',
        value: 0
    }, {
        label: '系统',
        value: 1
    }],
    useType: [{
        label: '停用',
        value: '0'
    }, {
        label: '正常',
        value: '1'
    }],


}
export default {
    data() {
        return {
            form: {},
            query: {},
            loading: true,
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            dialogImageUrl: '',
            dialogVisible: false,
            addRowShow: false,
            detail: {},
            center:{},
            canteenId: '',
            companyNature: '',
            canteenList: [],
            adr:'',
            option: {
                height: 'auto',
                searchShow: true,
                searchMenuSpan: 6,
                labelWidth: 180,
                searchLabelWidth: 150,
                tip: false,
                border: true,
                index: true,
                viewBtn: true,
                menu: true,
                selection: true,
                align: 'center',
                addBtn: false,
                editBtn: false,
                delBtn: false,
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "单位名称",
                        prop: "customerName",
                        type: "input",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请填写单位名称",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "单位简称",
                        prop: "simpleName",
                        type: "input",
                        search: true,
                    },
                    {
                        label: "食堂",
                        prop: "canteenId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                        props: {
                            label: "deptName",
                            value:"id"
                        },
                        search: true,
                    },
                    {
                        label: "食堂类型",
                        prop: "canteenType",
                        type: "select",
                        search:true,
                        dicData: [
                            {
                                label: "其他食堂",//2023-02-09 需求更改 学生食堂 为 其他食堂
                                value: "1"
                            },
                            {
                                label: "员工食堂",//2023-02-09 需求更改 教师食堂(单位食堂) 为 员工食堂
                                value: "2"
                            },
                            {
                                label: "零售商店",
                                value: "3"
                            }
                        ],
                        change:({value,column})=>{
                            if (value === '3') {
                                this.option.column[13].rules = [];
                                this.option.column[13].disabled = true;
                            }else {
                                this.option.column[13].rules = [{ required: true, message: "请选择限定服务范围", trigger: "click" }]
                                this.option.column[13].disabled = false;
                            }
                        },
                    },
                    {
                        label: "社会信用代码",
                        prop: "regNum",
                        type: "input",
                        width: 180,
                        // span: 24,
                        rules: [{
                            required: false,
                            message: "请填写社会信用代码",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "联系人",
                        prop: "contactsName",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请填写联系人姓名",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: '联系电话',
                        prop: 'mobile',
                        type: 'input',
                        rules: [
                            {
                                required: false,
                                message: '请输入联系人手机',
                                trigger: 'blur'
                            }
                        ]
                    },
                    {
                        label: "送货线路",
                        prop: "deliveryRoute",
                        type: "select",
                        search: true,
                        dicData: [],
                        props: {
                            label: "lineName",
                            value: "id"
                        },
                    },
                    {
                        label: "类型",
                        prop: "customerType",
                        type: "checkbox",
                        search: true,
                        rules: [
                            {
                                required: true,
                                message: '请选择企业类型',
                                trigger: 'blur'
                            }
                        ],
                        slot: true,
                        multiple: true,
                        dicData: DIC.customerType,
                    },
                    {
                        label: '来源',
                        prop: 'sourceType',
                        type: 'select',
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        rules: [
                            {
                                required: false,
                                message: '请选择来源',
                                trigger: 'blur'
                            }
                        ],
                        dicData: DIC.sourceType,
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "radio",
                        slot:true,
                        dicData: [
                            {
                                label: '正常',
                                value: "1"
                            },
                            {
                                label: '停用',
                                value: "0"
                            },
                        ],
                        value: "1",
                        rules: [{
                            required: true,
                            message: "请选择状态",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "备注",
                        prop: "remarks",
                        type: "input",
                        rules: [
                            {
                                required: false,
                                message: '请输入备注',
                                trigger: 'blur'
                            }
                        ]
                    },
                    {
                        label: "操作人",
                        prop: "updateUser",
                        type: "select",
                        dicUrl: '/api/service/rabbit-supplier/user-list',
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false
                    },
                    {
                        label: "操作时间",
                        prop: "updateTime",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false
                    },
                    //以下内容不显示列表/////////////////////////////////////////////////////////////////////////////////////////////////////////////////////
                    {
                        label: "企业性质",
                        prop: "customerNature",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
                        search: false,
                        hide: true,
                        addDisplay: true,
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择企业性质",
                            trigger: "blur"
                        }],

                    },
                    {
                        label: "法人代表",
                        prop: "legalRepresentative",
                        type: "input",
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        rules: [{
                            required: false,
                            message: "请填写法人代表",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: '省份',
                        prop: 'province',
                        type: 'select',
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                        props: {
                            label: "regionName",
                            value: "id",
                        },
                        dicUrl: `/api/rabbit-system/region/getProvince`,
                        cascaderItem: ['city', 'area'],
                        cascaderChange: true,
                        rules: [
                            {
                                required: false,
                                message: '请选择省份',
                                trigger: 'blur'
                            }
                        ],
                    },
                    {
                        label: '城市',
                        prop: 'city',
                        type: 'select',
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                        props: {
                            label: "regionName",
                            value: "id",
                        },
                        dicFlag: false,
                        dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
                        cascaderItem: ['area'],
                        cascaderChange: true,
                        rules: [
                            {
                                required: false,
                                message: '请选择城市',
                                trigger: 'blur'
                            }
                        ],
                    },
                    {
                        label: '地区',
                        prop: 'area',
                        type: 'select',
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                        props: {
                            label: "regionName",
                            value: "id",
                        },
                        dicFlag: false,
                        dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
                        rules: [
                            {
                                required: false,
                                message: '请选择地区',
                                trigger: 'blur'
                            }
                        ],
                    },
                    {
                        label: "街道",
                        prop: "street",
                        type: "input",
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                        rules: [{
                            required: false,
                            message: "请输入街道",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "企业地址",
                        prop: "customerContactAddress",
                        type: "input",
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                        disabled: true,
                        rules: [{
                            required: false,
                            message: "请填写企业地址",
                            trigger: "blur"
                        }],
                    },

                    {
                        label: "身份证号码",
                        prop: "corporateIdentity",
                        type: "input",
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                        rules: [{
                            required: false,
                            message: "请输入身份证号码",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "营业执照",
                        prop: "businessUrl",
                        type: 'upload',
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        listType: 'picture-img',
                        action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                        propsHttp: {
                            res: 'data',
                            url: 'link',
                        },
                        span: 24,
                        rules: [{
                            required: false,
                            message: "请上传营业执照",
                            trigger: "blur"
                        }],
                        hide: true,
                    },
                    {
                        label: "身份证",
                        prop: "identityUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        action: '/api/service/rabbit-resource/oss/endpoint/put-file?code=liancan',
                        propsHttp: {
                            res: 'data',
                            url: 'link',
                        },
                        span: 24,
                        rules: [{
                            required: false,
                            message: "请上传身份证照",
                            trigger: "blur"
                        }],
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                    },
                    {
                        label: "经度",
                        prop: "longitude",
                        type: "input",
                        hide: true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                    },
                    {
                        label: "纬度",
                        prop: "latitude",
                        type: "input",
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "坐标选取",
                        prop: "baiduMap",
                        type: "text",
                        span: 24,
                        formslot:true,
                        addDisplay: true,
                        editDisplay: true,
                        viewDisplay: false,
                        hide: true,
                    },
                ]
            },
            data: [],

            //弹框数据
            formSub: {},
            querySub: {},
            loadingSub: true,
            pageSub: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionListSub: [],
            addRowNextStepShow: false,
            selectCustomers: {id: '', name: ''},
            customerTypes: [],
            optionSub: {
                height: '400px',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                searchLabelWidth: 150,
                tip: false,
                border: true,
                index: true,
                viewBtn: true,
                menu: false,
                selection: true,
                align: 'center',
                addBtn: false,
                editBtn: false,
                delBtn: false,
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "企业性质",
                        prop: "companyNature",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nature_enterprise",
                        search: true,
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择企业性质",
                            trigger: "blur"
                        }],

                    },
                    {
                        label: "企业名称",
                        prop: "name",
                        type: "input",
                        search: true,
                        rules: [{
                            required: true,
                            message: "请填写企业名称",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "社会信用代码",
                        prop: "regNum",
                        type: "input",
                        width: 180,
                        // span: 24,
                        rules: [{
                            required: false,
                            message: "请填写社会信用代码",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "法人代表",
                        prop: "legalRepresentative",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请填写法人代表",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "企业地址",
                        prop: "address",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: true,
                        disabled: true,
                        rules: [{
                            required: false,
                            message: "请填写企业地址",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "联系人姓名",
                        prop: "contactsName",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请填写联系人姓名",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "身份证号码",
                        prop: "corporateIdentity",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入身份证号码",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: '联系人手机',
                        prop: 'mobile',
                        type: 'input',
                        rules: [
                            {
                                required: false,
                                message: '请输入联系人手机',
                                trigger: 'blur'
                            }
                        ]
                    },
                ]
            },
            dataSub: [],

            //送货线路
            deliveryRoute: '',
            deliveryRouteList: [],
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        },

    },
    created() {
        //加载单位食堂
        // getCanteenList()
        //     .then(res => {
        //         this.canteenList = res.data.data;
        //     }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    async mounted() {
        await getDeliveryRouteList().then(res => {
            console.log("getDeliveryRouteList",res);
            this.deliveryRouteList = res.data.data.records;
            const index = this.$refs.crud.findColumnIndex("deliveryRoute");
            this.option.column[index].dicData = this.deliveryRouteList;
        });
    },
    methods: {
        getClickInfo(e) {
            console.log("=====>",e);
            this.form.longitude = e.point.lng;
            this.form.latitude = e.point.lat;
        },
        handleCreate(row, loading, done) {
            this.addRowShow = true;
        },
        addRowSelfHandler() {
            this.$refs.crud.rowAdd();
        },
        closeSelectLandle() {
            this.addRowShow = false;
            this.selectCustomers = {};
        },
        async nextStepHandle() {
            //判断是否选择了对应的行,如果不选择不往下执行
            if (this.selectionListSub.length === 0) {
                this.$message.warning("请选择至少一条数据");
                return;
            }else if (this.selectionListSub.length > 1) {
                this.$message.warning("每次只能添加一个单位");
                return;
            }
            var ids = '';
            var names = '';
            for (var item of this.selectionListSub) {
                ids += item.id + ",";
                names += item.name + ",";
            }
            ids = ids.substring(0, ids.length - 1);
            names = names.substring(0, names.length - 1);

            this.selectCustomers = {id: ids, name: names};

            this.addRowShow = false;
            this.addRowNextStepShow = true;
            this.companyNature = this.selectionListSub[0].companyNature;
            //刷新食堂数据
            //加载单位食堂
            await getCanteenList(ids)
                .then(res => {
                    this.canteenList = res.data.data;
                }).catch(err=>{}).finally(fin=>{this.loading = false;});
        },
        closeSaveHandle() {
            this.addRowNextStepShow = false;
            this.selectCustomers = {};
        },
        nextStepSaveHandle() {
            //检查数据
            if (this.selectCustomers.id == '') {
                this.$message.warning("请选择至少一条数据");
                return;
            }
            if (this.customerTypes.length == 0) {
                this.$message.warning("请选择客户类型");
                return;
            }
            this.addRowShow = false;
            this.addRowNextStepShow = false;
            var types = '';
            for (var type of this.customerTypes) {
                types += type + ",";
            }
            types = types.substring(0, types.length - 1);
            addCustomer(this.selectCustomers.id, types, this.canteenId,this.companyNature,this.deliveryRoute).then(res => {
                if (res.data.code != 200) {
                    this.$message.error("保存往来单位错误:" + res.data.message);
                } else {
                    this.onLoad(this.page)
                }
            });
        },
        rowSave(row, loading, done) {
            var strs = row.customerType;
            var str = "";
            for (var i of strs) {
                str += i + ",";
            }
            if (str.endsWith(",")) {
                str = str.substring(0, str.length - 1);
            }
            row.customerType = str;
            add(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowUpdate(row, index, loading, done) {
            var strs = row.customerType;
            var str = "";
            for (var i of strs) {
                str += i + ",";
            }
            if (str.endsWith(",")) {
                str = str.substring(0, str.length - 1);
            }
            row.customerType = str;
            update(row).then(() => {
                loading();
                this.onLoad(this.page);
                this.$message({
                    type: "success",
                    message: "操作成功!"
                });
            }, error => {
                done();
                window.console.log(error);
            });
        },
        rowDel(row) {
            this.$confirm("确定将选择数据删除?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
                .then(() => {
                    return remove(row.id);
                })
                .then(() => {
                    this.onLoad(this.page);
                    this.$message({
                        type: "success",
                        message: "操作成功!"
                    });
                });
        },
        handleDelete() {
        },
        beforeOpen(done, type) {
            if (["edit"].includes(type)) {
                getDetail(this.form.id).then(res => {
                    this.form = res.data.data;
                    if (res.data.data.sourceType == '1') {
                        const arr = ['customerType', 'corporateIdentity', 'remarks', 'contactsName', 'customerName', 'customerNature', 'simpleName', 'canteenId', 'canteenType', 'status','deliveryRoute'];
                        this.option.column.forEach((item, index) => {
                            if (arr.includes(item.prop)) {
                                this.$set(this.option.column[index], 'disabled', false);
                            } else {
                                this.$set(this.option.column[index], 'disabled', true);
                            }
                        })
                    } else {
                        this.option.column.forEach((item, index) => {
                            this.$set(this.option.column[index], 'disabled', false);
                        })
                    }
                });
            } else if (["add"].includes(type)) {
                this.form.isUse = "1";
            }
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                for (var item of this.data) {
                    var str = item.customerType;
                    var strs = str.split(",");
                    item.customerType = ['0', '1'];//strs;
                }
                this.loading = false;
                this.selectionClear();
            });
        },
        handleClickPreview: function (url) {
            this.dialogImageUrl = url;
            this.dialogVisible = true;
        },

        //弹出框
        beforeOpenSub(done, type) {
            if (["edit"].includes(type)) {
                getDetail(this.formSub.id).then(res => {
                    this.formSub = res.data.data;
                });
            } else if (["add"].includes(type)) {
                this.form.isUse = "1";
            }
            done();
        },
        sizeChangeSub(pageSize) {
            this.pageSub.pageSize = pageSize;
        },
        searchResetSub() {
            this.querySub = {};
            this.onLoadSub(this.pageSub);
        },
        searchChangeSub(params, done) {
            this.querySub = params;
            this.pageSub.currentPage = 1
            this.onLoadSub(this.pageSub, params);
            done();
        },
        selectionChangeSub(list) {
            this.selectionListSub = list;
        },
        currentChangeSub(currentPage) {
            this.pageSub.currentPage = currentPage;
        },
        onLoadSub(page, params = {}) {
            this.loadingSub = true;
            getParentList(page.currentPage, page.pageSize, Object.assign(params, this.querySub)).then(res => {
                const data = res.data.data;
                this.pageSub.total = data.total;
                this.dataSub = data.records;
                this.loadingSub = false;
                this.selectionClearSub();
            });
        },
        selectionClearSub() {
            this.selectionListSub = [];
            this.$refs.crudSub.toggleSelection();
        },
    }
};
</script>
<style>
.map {
    width: 100%;
    height: 70vh;
}

::v-deep .anchorBL {
    display: none !important;
}
</style>
