<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            //自定义新增按钮
            <template slot="menuLeft" slot-scope="scope">
                <el-button type="primary"  icon="el-icon-plus" size="small" @click="addStockInHandler" >新增</el-button>
                <el-button type="primary"  icon="el-icon-download" size="small" @click="printOrderHandler" >导出</el-button>
                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>
            </template>
            <template slot="menu" slot-scope="{row,index}">
                <el-button
                    type="text"
                    size="mini"
                    icon="el-icon-view"
                    @click="viewRow(row,index)">查看
                </el-button>
            </template>
        </avue-crud>
        <!-- 打开新增页面 开始 -->
        <el-dialog title="新增"
                   :visible.sync="isShowStockIn"
                   v-if="isShowStockIn"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeAddForm" width="60%"  style="height: 90%;">
                <el-form ref="formAdd" label-width="80px">
                    <el-form-item label="单据日期" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-date-picker type="date"
                                            placeholder="选择日期"
                                            :picker-options="pickerOptions"
                                            v-model="businessDate"
                                            format="yyyy-MM-dd"
                                            value-format="yyyy-MM-dd"
                                            style="width: 100%;"></el-date-picker>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="客户" prop="region" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="6">
                            <el-select v-model="supplierCustomerId" placeholder="请选择" style="width: 100%;">
                                <el-option
                                    v-for="item in supplierList"
                                    :key="item.id"
                                    :label="item.customerName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-col>
                        <el-col :span="8"></el-col>
                        <el-col :span="2">业务员</el-col>
                        <el-col :span="6">
                            <el-select v-model="salesman" placeholder="请选择" style="width: 100%;">
                                <el-option
                                    v-for="item in salesmanList"
                                    :key="item.id"
                                    :label="item.contactName"
                                    :value="item.id">
                                </el-option>
                            </el-select>
                        </el-col>
                    </el-form-item>
                    <el-form-item label="产品出库" :rules="[{ required: true, message: '必选', trigger: 'change' }]">
                        <el-col :span="3">
                            <el-button type="primary" size="small" @click="selectGoodHandler">从商品库中选择</el-button>
                        </el-col>
<!--                        <el-col :span="6">-->
<!--                            <el-button type="success" size="small" @click="selectOrderHandler">从采购订单中选择</el-button>-->
<!--                        </el-col>-->
                    </el-form-item>
                </el-form>
                <div style="height: 350px;overflow-y: scroll;">
                    <avue-crud :option="stockInOption"
                               :table-loading="stockInLoading"
                               :data="stockInData"
                               :page="stockInPage"
                               :before-open="beforeOpenStockIn"
                               v-model="stockInForm"
                               ref="crudStockIn"
                               @row-update="rowUpdateStockIn"
                               @row-save="rowSaveStockIn"
                               @row-del="rowDelStockIn"
                               @search-change="searchChangeStockIn"
                               @search-reset="searchResetStockIn"
                               @selection-change="selectionChangeStockIn"
                               @current-change="currentChangeStockIn"
                               @size-change="sizeChangeStockIn"
                               @cell-click="handleRowClick"
                               @on-load="stockInOnLoad">
                        <template slot="menu" slot-scope="{row,index}">
                            <el-button
                                type="text"
                                size="small"
                                icon="el-icon-edit"
                                v-if="!row.$cellEdit"
                                @click="rowCellStockIn(row,index)"
                            >修改</el-button>
                            <el-button
                                type="text"
                                size="small"
                                icon="el-icon-check"
                                v-if="row.$cellEdit"
                                @click="rowSaveStockIn(row,index)"
                            >保存</el-button>
                            <el-button
                                type="text"
                                size="mini"
                                icon="el-icon-delete"
                                @click="deleteRowStockIn(row,index)">删除
                            </el-button>
                        </template>
                    </avue-crud>
                </div>
                <div>
                    出库总额:{{totalAmt}}元
                </div>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="closeAddForm">取消</el-button>
                    <el-button type="primary" @click="saveSelectHandle">保存</el-button>
                </span>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
        <!-- 公共商品选择 开始 -->
        <el-dialog title="选择商品"
                   :visible.sync="isShowSelectGoods"
                   v-if="isShowSelectGoods"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   @close="closeSelectForm" width="60%">
            <avue-crud :option="goodsListOption"
                       :table-loading="goodsListLoading"
                       :data="goodsListData"
                       :page="goodsListPage"
                       v-model="goodsListForm"
                       ref="crudSelectGoods"
                       @search-change="searchChange2"
                       @search-reset="searchReset2"
                       @selection-change="selectionChange2"
                       @current-change="currentChange2"
                       @size-change="sizeChange2"
                       @on-load="goodsListOnLoad">
                <template slot="menu" slot-scope="scope">
                    <el-button type="text"
                               size="mini"
                               @click="addGoods(scope.row)">选择
                    </el-button>
                </template>
            </avue-crud>
        </el-dialog>
        <!-- 商品选择 结束 -->
        <!-- 打开详情页面 开始 -->
        <el-dialog title="详情"
                   :visible.sync="isShowStockInDetail"
                   v-if="isShowStockInDetail"
                   :append-to-body="true"
                   :close-on-click-modal="false"
                   :close-on-press-escape="false"
                   :before-close="closeShowForm" width="60%"  style="height: 90%;">
            <el-row type="flex" class="row-bg" justify="right">
                <el-col :span="24" style="height: 100%;" align="right">
                    <el-button type="primary" icon="el-icon-printer" size="small" v-print="print" >打印</el-button>
                </el-col>
            </el-row>
            <div id="printArea">
                <el-row type="flex" class="row-bg" justify="left" style="height: 60px;">
                    <el-col :span="24">
                        <div style="width: 100%;height:100%;text-align:center;"><h1>其他出库单</h1></div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left" style="margin-top: 45px;">
                    <el-col :span="2">
                        <div class="head-label">单据日期</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.form.businessDate}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">业务员</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.salesmanName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">单据号</div>
                    </el-col>
                    <el-col :span="8">
                        <div>{{this.form.code}}</div>
                    </el-col>
                </el-row>
                <el-row type="flex" class="row-bg" justify="left">
                    <el-col :span="2">
                        <div class="head-label">登记人</div>
                    </el-col>
                    <el-col :span="3">
                        <div>{{this.createUserName}}</div>
                    </el-col>

                    <el-col :span="2">
                        <div class="head-label">登记时间</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.createTime}}</div>
                    </el-col>

                    <el-col :span="4">
                        <div class="head-label">关联其他入库单</div>
                    </el-col>
                    <el-col :span="6">
                        <div>{{this.form.sourceCode}}</div>
                    </el-col>
                </el-row>
                <div style="height: 600px;overflow-y: scroll;margin-top: 20px;">
                    <el-table
                        :data="stockInData"
                        stripe
                        border
                        fit
                        show-summary
                        :header-cell-style="tableHeaderStyle"
                        :row-style="tableDetailStyle"
                        :summary-method="getSummaries"
                        style="width: 100%">
                        <el-table-column property="rowId" label="序号" align="center"></el-table-column>
                        <el-table-column property="name" label="商品名称" align="center"></el-table-column>
                        <el-table-column property="unitName" label="计量单位" align="center"></el-table-column>
                        <el-table-column property="warehouseName" label="出库仓库" align="center"></el-table-column>
                        <el-table-column property="qty" label="出库数量" align="center"></el-table-column>
                        <el-table-column property="price" label="出库单价" align="center"></el-table-column>
                        <el-table-column property="amt" label="出库金额" align="center"></el-table-column>
                    </el-table>
                </div>
            </div>
        </el-dialog>
        <!-- 打开新增页面 结束 -->
    </basic-container>
</template>

<script>
import {getList, getDetail, add, update, exportList} from "@/api/supplier/supplierOtherStockOut";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";

export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            createUserName:'',
            salesmanName:'',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "单据日期",
                        prop: "businessDate",
                        type: "date",
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            disabledDate(time) {
                                return time.getTime() < Date.now();
                            }
                        }
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据号",
                        prop: "code",
                        type: "input",
                        width: 180,
                        search: true,
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                    },
                    {
                        label: "出库商品",
                        prop: "goodsName",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: false,
                        props: {
                            label: 'name',
                            value: 'goodGoodId'
                        },
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "出库成本总额",
                        prop: "totalAmt",
                        type: "number",
                        labelWidth: 140,
                        mock: {
                            type: 'number',
                            max: 1,
                            min: 2,
                            precision: 2
                        },
                        rules: [ {
                            required: true,
                            message: "请输入采购总额",
                            trigger: "blur"
                        } ],
                        precision: 2,
                        display: false
                    },
                    {
                        label: '业务员',
                        prop: 'salesman',
                        type:'select',
                        search: true,
                        dicUrl: `/api/rabbit-supplier/user-list`,
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        multiple:true
                    },
                    {
                        label: "出库仓库",
                        prop: "warehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "登记人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: "/api/service/rabbit-supplier/user-list",
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                        display: false
                    },
                    {
                        label: "登记时间",
                        prop: "createTime",
                        type: "date",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: 'yyyy-MM-dd HH:mm:ss',
                        display: false
                    },
                ]
            },
            data: [],
            isShowStockIn: false,
            isShowSelectGoods: false,
            isShowSelectOrder: false,

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },

            //新增出库单弹窗参数 start
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            isShowStockInDetail: false,
            supplierList: [],
            salesmanList: [],
            supplierCustomerId: '',
            businessDate: '',
            salesman: '',
            supplierValue: {},
            queryStockIn: {},
            rowStockIn: {},
            stockInLoading: true,
            stockInData: [],
            stockInPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            stockInForm: {},
            stockInOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: false,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                emptyBtn: false,
                submitBtn: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "status",
                        prop: "status",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display: false,
                        hide: true,
                    },
                    {
                        label: "商品编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "商品名称",
                        prop: "name",
                        type: "input",
                        append:'选择',
                        disabled: true,
                        appendClick:()=>{
                            var that=this;
                            {
                                //判断当前状态,只有新增才允许选择商品
                                if (that.currentOpenType == 'add') {
                                    that.isShow2=true;
                                }else{
                                    this.$message.warning('当前模式不允许选择商品');
                                }
                            }
                        },
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        span: 24,
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        disabled: true,
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "出库仓库",
                        prop: "warehouseId",
                        type: "select",
                        cell: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateStockIn(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        // rules: [{
                        //     required: true,
                        //     message: "请选择出库仓库",
                        //     trigger: "blur"
                        // }],
                        search: true,
                    },
                    {
                        label: "出库数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        // rules: [{
                        //     required: true,
                        //     message: "请输入出库数量",
                        //     trigger: "blur"
                        // }],
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateStockIn(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        formatter:(val)=>{
                            if(val.qty===0) {
                                return '';
                            }
                            return amtFilters(val.qty);
                        },
                    },
                    {
                        label: "出库单价",
                        prop: "price",
                        type: "input",
                        dataType:'number',
                        cell: true,
                        // rules: [{
                        //     required: true,
                        //     message: "请输入出库单价",
                        //     trigger: "blur"
                        // }],
                        // change: ({row, value}) => {
                        //     if (!value) return;
                        //     this.rowUpdateStockIn(row);      //值发生变化时添加change事件，然后执行rowUpdate事件
                        // },
                        formatter:(val)=>{
                            if(val.price===0) {
                                return '';
                            }
                            return amtFilters(val.price);
                        },
                    },
                    {
                        label: "出库金额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        rules: [{
                            required: false,
                            message: "请输入出库金额",
                            trigger: "blur"
                        }],
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            //新增出库单弹窗参数 end

            //选择商品弹窗参数 start
            queryGoodsList: {},
            goodsListLoading: true,
            goodsListData: [],
            goodsListPage: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            goodsListForm: {},
            goodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                addBtn: false,
                delBtn: false,
                viewBtn: false,
                editBtn: false,
                selection: false,
                align: 'center',
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "一类",
                        prop: "type",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择一类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['bidding'],
                        search: true,
                    },
                    {
                        label: "二类",
                        prop: "bidding",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择二类",
                            trigger: "blur"
                        }],
                        dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        filterable: true,
                        searchFilterable: true,
                        cascaderItem: ['biddingTypeId'],
                        search: true,
                        dicFlag: false,
                    },
                    {
                        label: "商品名称",
                        prop: "biddingTypeId",
                        type: "select",
                        rules: [{
                            required: true,
                            message: "请选择商品",
                            trigger: "blur"
                        }],
                        dicFlag: false,
                        dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "商品子项名称",
                        prop: "name",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请输入商品名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "商品子项编码",
                        prop: "goodCode",
                        type: "input",
                    },
                    {
                        label: "计量单位",
                        prop: "unit",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                        props: {
                            label: "dictValue",
                            value: "dictKey"
                        },
                        rules: [{
                            required: true,
                            message: "请选择计量单位",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "商品图片",
                        prop: "imgUrl",
                        type: 'upload',
                        listType: 'picture-img',
                        propsHttp: {
                            res: 'data',
                            url: 'link'
                        },
                        span: 24,
                    },
                    {
                        label: "每一计量单位折合净重为(斤)",
                        labelWidth: 180,
                        prop: "netWeight",
                        type: "input",
                        rules: [{
                            required: false,
                            message: "请输入净含量",
                            trigger: "blur"
                        }],
                        addDisplay: true,
                    },
                    {
                        label: "品牌",
                        prop: "brand",
                        type: "input",
                    },
                    {
                        label: "规格",
                        prop: "foodSpec",
                        type: "input",
                        hide: true,
                    },
                    {
                        label: "保质期",
                        prop: "shelfLife",
                        type: "input",
                    },
                    {
                        label: "生产厂家",
                        prop: "manufacturer",
                        type: "input",
                    },
                    {
                        label: "生产许可",
                        prop: "production",
                        type: "input",
                    },
                    {
                        label: "产地",
                        prop: "origin",
                        type: "input",
                    },
                    {
                        label: "质量等级",
                        prop: "qualityLevel",
                        type: "input",
                    },
                    {
                        label: "质量标准",
                        prop: "qualityStandard",
                        type: "input",
                    },

                    {
                        label: "储存方法",
                        prop: "storage",
                        type: "input",
                    },
                    {
                        label: "备注",
                        prop: "remark",
                        type: "input",
                    },
                ]
            },
            //选择商品弹框参数 end

        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "stockInData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.stockInData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },
        addStockInHandler () {
            this.form = {};
            this.businessDate = '';//new Date();
            this.supplierCustomerId = '';
            this.salesman = '';
            this.editType = 'add';
            this.isShowStockIn = true;
        },
        closeAddForm() {
            if (this.stockInData.length > 0) {
                this.$confirm("数据未保存,是否确定关闭?", {
                    confirmButtonText: "确定",
                    cancelButtonText: "取消",
                    type: "warning"
                })
                    .then(() => {
                    })
                    .then(() => {
                        this.isShowStockIn = false;
                    });
            }else{
                this.isShowStockIn = false;
            }
        },
        closeSelectForm() {
            this.isShowSelectGoods = false;
        },
        closeShowForm(){
            this.isShowStockInDetail = false;
        },

        viewRow(row,index) {
            this.editType = 'view';
            this.form = row;
            this.stockInOnLoad(this.stockInPage)
            this.isShowStockInDetail = true;
        },

        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //主界面基本方法 end

        //选择商品基本方法 start
        selectionClearGoodsList() {
            this.selectionList = [];
            this.$refs.crudSelectGoods.toggleSelection();
        },
        searchChange2(params, done) {
            this.queryGoodsList = params;
            this.goodsListPage.currentPage = 1
            this.goodsListOnLoad(this.goodsListPage, params);
            done();
        },
        searchReset2() {
            this.queryGoodsList = {};
            this.goodsListOnLoad(this.goodsListPage);
        },
        selectionChange2(list) {
            this.selectionList = list;
        },
        currentChange2(currentPage) {
            this.goodsListPage.currentPage = currentPage;
        },
        sizeChange2(pageSize) {
            this.goodsListPage.pageSize = pageSize;
        },
        goodsListOnLoad(page, params = {}) {
            this.goodsListLoading = true;
            getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.queryGoodsList)).then(res => {
                const data = res.data.data;
                this.goodsListPage.total = data.length;
                this.goodsListData = data;
                this.goodsListLoading = false;
                this.selectionClearGoodsList();
            });
        },
        addGoods(row) {
            row.addOrEdit = 'add'//新增
            row.$cellEdit = false;
            this.stockInRefreshData(row);
            this.isShowSelectGoods = false
        },
        //选择商品基本方法 end

        //新增采购出库单基本方法 start
        selectionClearStockIn() {
            this.selectionList = [];
            try{
                this.$refs.crudStockIn.toggleSelection();
            }catch(err){console.error(err);}
        },
        saveSelectHandle() {
            //保存前先检查数据
            //检测单据日期
            //检测供应商
            //业务员
            if (this.businessDate === undefined || this.businessDate === '') {
                this.$message.warning("请选择单据日期");
                return;
            }
            if (this.supplierCustomerId === undefined || this.supplierCustomerId === '') {
                this.$message.warning("请选择供应商");
                return;
            }
            // if (this.salesman) {
            //
            // }
            //如果当前保存为空，则不提示
            if (this.stockInData.length==0) {
                this.$message.warning("请选择商品")
                return;
            }

            var isSave = true;
            this.stockInData.forEach((item,index)=>{
                var warehouseId = item.warehouseId;
                var qty = item.qty;
                var price = item.price;
                if (warehouseId==='') {
                    isSave = false;
                    this.$message.warning("第"+index+"行,未选择出库仓库");
                    return;
                }else if (qty===0) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,出库数量不正确");
                    return;
                }else if (price===0) {
                    isSave = false;
                    this.$message.warning("第"+index+"行,出库单价不正确");
                    return;
                }
                item.amt = qty*price;
            });

            if (!isSave) {
                return;
            }

            this.$confirm("是否确定保存?", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            })
            .then(() => {
                this.form.supplierCustomerId = this.supplierCustomerId;
                this.form.businessDate = this.businessDate;
                this.form.salesman = this.salesman;
                if (this.editType === 'edit') {
                    this.stockInLoading = true;
                    update(this.form,this.stockInData).then((res) => {
                        this.stockInLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShowStockIn = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                }else {
                    this.stockInLoading = true;
                    add(this.form,this.stockInData).then((res) => {
                        this.stockInLoading = false;
                        if (res.data.code == 200) {
                            this.onLoad(this.page);
                            this.$message({
                                type: "success",
                                message: "操作成功!"
                            });
                            this.isShowStockIn = false;
                        }else{
                            this.$message({
                                type: "error",
                                message: "保存失败:"+res.data.message,
                            });
                        }
                    }, error => {
                        window.console.log(error);
                    });
                }
            })
            .then(() => {
                this.isShowStockIn = false;
            });
        },
        selectGoodHandler() {
            this.isShowSelectGoods = true;
        },
        selectOrderHandler() {
            this.isShowSelectOrder = true;
        },
        beforeOpenStockIn(done, type) {
            done();
        },
        searchChangeStockIn(params, done) {
            this.queryStockIn = params;
            this.stockInPage.currentPage = 1
            this.stockInOnLoad(this.stockInPage, params);
            done();
        },
        searchResetStockIn() {
            this.queryStockIn = {};
            this.stockInOnLoad(this.stockInPage);
        },
        selectionChangeStockIn(list) {
            this.selectionList = list;
        },
        currentChangeStockIn(currentPage) {
            this.stockInPage.currentPage = currentPage;
        },
        sizeChangeStockIn(pageSize) {
            this.stockInPage.pageSize = pageSize;
        },
        stockInOnLoad(page, params = {}) {
            this.stockInLoading = true;
            if (this.form.id!==undefined && this.form.id!=='') {
                getDetail(this.form.id).then(res => {
                    const data = res.data.data;
                    this.businessDate = data.businessDate;
                    this.supplierCustomerId = data.supplierCustomerId;
                    this.salesman = data.salesman;
                    this.stockInData = data.detailList;
                    this.createUserName = data.createUserName;
                    this.salesmanName = data.salesmanName;
                    this.stockInPage.total = this.stockInData.length;
                    var row = 0;
                    this.stockInData.forEach((item) => {
                        row++;
                        item.rowId = row;
                        item.$cellEdit = false;
                    });
                    this.stockInLoading = false;
                    this.selectionClearStockIn();
                });
            }else{
                this.stockInPage.total = 0;
                this.stockInData = [];
                this.stockInData.forEach((item) => {
                    item.$cellEdit = false;
                });
                this.stockInLoading = false;
                this.selectionClearStockIn();
            }
        },
        stockInRefreshData(row) {
            row.warehouseId = "";
            row.qty = 0;
            row.price = 0.00;
            row.amt = 0.00;
            this.stockInData.push(row);
        },
        deleteRowStockIn(row, index) {
            // console.log(row)
            this.stockInData.splice(index,1);
        },
        //新增采购出库单基本方法 end

        //新增采购出库行编辑方法 start

        rowCellStockIn(row, index) {
            this.rowStockIn= row;
            this.$refs.crudStockIn.rowCell(row, index)
            // this.$refs.crudStockIn.rowCancel(row, row.$index);
        },
        rowSaveStockIn(row, index) {
            //修改stockInData对应的row
            //计算对应行的采购金额
            var qty = row.qty;
            var price = row.price;
            var amt = qty*price;
            row.amt = amt;
            this.stockInData[index] = row;
            // this.$refs.crudStockIn.rowCell(row, index)
            row.$cellEdit = false;
            this.$refs.crudStockIn.rowCellUpdate();
            //合计所有的行
            this.summaryAmt();
        },
        rowCancelEditStockIn(row, index) {
            // this.$refs.crudEx.rowCancel(row, index);
            this.onLoad(this.page,this.query);
        },
        rowUpdateStockIn(row, index, loading, done) {
            done()
        },
        rowDelStockIn(row) {
        },
        handleRowClick(row, column) {    //列的双击事件
            if ((!row.$cellEdit) && column.label != '操作') {
                if (["warehouseId", "qty", "price", "amt"].includes(column.property)) {
                    this.$refs.crudStockIn.rowCell(row, row.$index);
                }
            }
        },
        //新增采购出库行编辑方法 end
        printOrderHandler(params = {}) {
            this.$confirm("是否导出其他出库数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                params.isSupplyFlag = 1
                exportList(Object.assign(params, this.query)).then(res => {
                    const blob = new Blob([res.data]);
                    const fileName = '其他出库记录.xlsx';
                    const linkNode = document.createElement('a');

                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();

                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
            });
        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
