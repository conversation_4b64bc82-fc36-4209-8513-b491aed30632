<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="openDeptSummaryDateils(row)">查看
        </el-button>
      </template>
      <template slot="menuLeft">
        <el-button v-if="permission.order_dishess_export" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportOrderDishesData">导出</el-button>
      </template>
    </avue-crud>
    <el-dialog :title="`预订菜品详情`" :visible.sync="managementVisible" :append-to-body="true" @close="managementVisible = false" width="70%">
      <el-row>
        <div>
          <div class="flex">
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              营业网点：{{this.businessOutletsName}}
            </div>
            <div style="width:20%;margin-bottom: 20px;margin-top:20px;float: left;">
              用餐日期：{{this.mealDate}}
            </div>
          </div>
        </div>
      </el-row>
      <el-row>
        <div style="margin-bottom: 15px;">用餐人次</div>
        <el-table
          :data="dishesData"
          border
          style="width: 100%;">
          <el-table-column
            type="index"
            width="50">
          </el-table-column>
          <el-table-column
            prop="morSubNumber"
            label="早餐预订份数"
            width="180" style="background: #fafafa;">
          </el-table-column>
          <el-table-column
            prop="aftSubNumber"
            label="中餐预订份数"
            width="180">
          </el-table-column>
          <el-table-column
            prop="dinSubNumber"
            label="晚餐预订份数">
          </el-table-column>
          <el-table-column
            prop="nigSubNumber"
            label="夜餐预订份数">
          </el-table-column>
          <el-table-column
            prop="subTotal"
            label="合计份数">
          </el-table-column>
        </el-table>
      </el-row>
      <div style="margin-bottom: 15px;margin-top: 20px;">预订菜品及份数：</div>
      <avue-crud :option="deptSummaryOption"
                 :table-loading="deptSummaryLoading"
                 :data="deptSummaryData"
                 :page="deptSummaryPage"
                 v-model="deptSummaryForm"
                 ref="deptSummaryForm"
                 @search-change="deptSummarySearchChange"
                 @search-reset="deptSummarySearchReset"
                 @selection-change="deptSummarySelectionChange"
                 @current-change="deptSummaryCurrentChange"
                 @size-change="deptSummarySizeChange"
                 @on-load="onLoadDeptSummary">
      </avue-crud>
    </el-dialog>
  </basic-container>
</template>

<script>
  import {getDishesList,getDiningList,getGoodsList,exportDishesData} from "@/api/businessManage/studentUniteOrder";
  import {mapGetters} from "vuex";

  const DIC = {
    sex: [
      {
        label: '男',
        value: "1"
      },
      {
        label: '女',
        value: "2"
      }
    ],
  }
  export default {
    data() {
      return {
        form: {},
        query: {},
        deptDetailsForm:{},
        loading: true,
        deptSummaryLoading:true,
        deptDetailsLoading:true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptSummaryPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        deptDetailsPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        deptSummaryData:[],
        deptDetailsData:[],
        dishesData:[],
        searchFrom:{},
        deptSummaryForm:{},
        searchForm:{},
        searchDeptUserForm:{},
        dialogVisible:false,
        managementVisible:false,
        deptDetailsVisible:false,
        dialogImageUrl:undefined,
        title:undefined,
        businessOutletsName:undefined,
        businessOutletsId:undefined,
        mealDate:undefined,
        payStartTime:undefined,
        payEndTime:undefined,
        serveFoodStartDate:undefined,
        serveFoodEndDate:undefined,
        isPublish:undefined,
        planId:undefined,
        deptId:undefined,
        option: {
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          column: [
            {
              label: "所属食堂",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "营业网点",
              prop: "businessOutletsId",
              type: "select",
              span: 24,
              rules: [{
                required: true,
                message: "请输入营业网点",
                trigger: "blur"
              }],
              hide:true,
              search:true,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              props: {
                label: "name",
                value: "id"
              },
            },
            {
              label: '用餐时间',
              prop: 'diningTime',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
            {
              label: "营业网点名称",
              prop: "businessOutletsName",
              type: "input",
            },
            {
              label: "用餐日期",
              prop: "dinnerDate",
              type: "date",
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
            },
            {
              label: '星期',
              prop: 'week',
              type: 'input',
              span: 20,
              dicData: DIC.week
            },
            {
              label: '用餐人次',
              prop: 'mealNumber',
              type: 'input',
            },
            {
              label: '预订菜品种类',
              prop: 'orderDishesTypeNumber',
              type: 'input',
            },
            {
              label: '预订菜品份数',
              prop: 'orderDishesCopies',
              type: 'input',
            },
          ]
        },
        deptSummaryOption:{
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          header: false,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          searchSpan:100,
          column: [
            {
              label: '菜品名称',
              prop: 'goodsName',
              type: 'input',
            },
            {
              label: '早餐预订份数',
              prop: 'morSubNumber',
              type: 'input',
            },
            {
              label: '中餐预订份数',
              prop: 'aftSubNumber',
              type: 'input',
            },
            {
              label: '晚餐预订份数',
              prop: 'dinSubNumber',
              type: 'input',
            },
            {
              label: '夜餐预订份数',
              prop: 'nigSubNumber',
              type: 'input',
            },
            {
              label: '合计份数',
              prop: 'subTotal',
              type: 'input',
            },
          ]
        },
        deptDetailsOption:{
          /*          height:'auto',
                    calcHeight: 30,
                    searchShow: true,
                    searchMenuSpan: 6,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: false,
          editBtn:false,
          delBtn:false,
          selection: false,
          labelWidth: 150,
          dialogWidth: 900,
          addBtn:false,
          align: 'center',
          menu:false,
          column: [
            {
              label: "部门",
              prop: "deptName",
              type: "input",
            },
            {
              label: '姓名',
              prop: 'userName',
              type: 'input',
              search:true,
            },
            {
              label: '性别',
              prop: 'sex',
              type: "radio",
              //slot: true,
              dicData: DIC.sex,
            },
            {
              label: '学号/工号',
              prop: 'studentJobNo',
              type: 'input',
            },
            {
              label: "用餐类别",
              prop: "mealsType",
              type: "select",
              search:true,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: '伙食费',
              prop: 'balance',
              type: 'input',
            },
            {
              label: '已订餐',
              prop: 'diningStatus',
              type: 'input',
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission"]),
      permissionList() {
        return {
          /*        addBtn: this.vaildData(this.permission.work_personnel_add, false),
                  viewBtn: this.vaildData(this.permission.work_personnel_view, false),
                  delBtn: this.vaildData(this.permission.work_personnel_delete, false),
                  editBtn: this.vaildData(this.permission.work_personnel_edit, false)*/
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    methods: {
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.searchFrom = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.searchFrom = params;
        if (params.diningTime != '' && params.diningTime != null && params.diningTime != undefined) {
          params.startDate = params.diningTime[0];
          params.endDate = params.diningTime[1];
        }
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getDishesList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      deptSummarySearchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoadDeptSummary(this.deptSummaryPage);
      },
      deptSummarySearchChange(params, done) {
        this.query = params;
        this.searchForm = params;
        this.deptSummaryPage.currentPage = 1
        this.onLoadDeptSummary(this.deptSummaryPage, params);
        done();
      },
      deptSummarySelectionChange(list) {
        this.selectionList = list;
      },
      deptSummarySelectionClear() {
        this.selectionList = [];
        this.$refs.deptSummaryForm.toggleSelection();
      },
      deptSummaryCurrentChange(currentPage){
        this.deptSummaryPage.currentPage = currentPage;
      },
      deptSummarySizeChange(pageSize){
        this.deptSummaryPage.pageSize = pageSize;
      },
      onLoadDeptSummary(page, params = {}) {
        this.deptSummaryLoading = true;
        getGoodsList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.mealDate,this.businessOutletsId).then(res => {
          const data = res.data.data;
          this.deptSummaryPage.total = data.total;
          this.deptSummaryData = data.records;
          this.deptSummaryLoading = false;
          this.deptSummarySelectionClear();
        });
      },


      deptDetailsSearchReset() {
        this.query = {};
        this.searchDeptUserForm = {};
        this.onLoadDeptDetails(this.deptDetailsPage);
      },
      deptDetailsSearchChange(params, done) {
        this.query = params;
        this.searchDeptUserForm = params;
        this.deptDetailsPage.currentPage = 1
        this.onLoadDeptDetails(this.deptDetailsPage, params);
        done();
      },
      deptDetailsSelectionChange(list) {
        this.selectionList = list;
      },
      deptDetailsSelectionClear() {
        this.selectionList = [];
        this.$refs.deptDetailsForm.toggleSelection();
      },
      deptDetailsCurrentChange(currentPage){
        this.deptDetailsPage.currentPage = currentPage;
      },
      deptDetailsSizeChange(pageSize){
        this.deptDetailsPage.pageSize = pageSize;
      },
      onLoadDeptDetails(page, params = {}) {
        this.deptDetailsLoading = true;
        params.planId = this.planId;
        params.deptId = this.deptId;
        getDeptUserDetailsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.deptDetailsPage.total = data.total;
          this.deptDetailsData = data.records;
          this.deptDetailsLoading = false;
          this.deptDetailsSelectionClear();
        });
      },
      handleClickPreview: function(url) {
        this.dialogImageUrl = url;
        this.dialogVisible = true;
      },
      /*      exportBiddingData(){
              const loading = this.$loading({
                lock: true,
                text: '正在导出数据，请稍后',
                spinner: 'el-icon-loading',
                background: 'rgba(0, 0, 0, 0.7)'
              });
              exportBiddingData2(this.searchFrom).then(res => {
                loading.close();
                const blob = new Blob([res.data]);
                const fileName = '中标情况统计报表.xlsx';
                const linkNode = document.createElement('a');

                linkNode.download = fileName; //a标签的download属性规定下载文件的名称
                linkNode.style.display = 'none';
                linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
                document.body.appendChild(linkNode);
                linkNode.click(); //模拟在按钮上的一次鼠标单击

                URL.revokeObjectURL(linkNode.href); // 释放URL 对象
                document.body.removeChild(linkNode);
              })
            },*/
      openDeptSummaryDateils(row){
        this.businessOutletsName = row.businessOutletsName;
        this.mealDate = row.mealDate;
        this.businessOutletsId = row.businessOutletsId;
/*        this.deptSummaryPage.currentPage = 1;
        this.onLoadDeptSummary(this.deptSummaryPage)*/
        getDiningList(row.mealDate,row.businessOutletsId).then(res => {
          console.log(">>>>>>>>>>>>>>",JSON.stringify(res.data.data))
          this.dishesData = res.data.data;
        });
        this.deptSummaryPage.currentPage = 1;
        this.onLoadDeptSummary(this.deptSummaryPage);
        this.managementVisible = true;
      },
      openDeptUserDetails(row){
        this.deptId = row.deptId;
        this.planId = row.planId;
        this.searchDeptUserForm.deptId = this.deptId;
        this.searchDeptUserForm.planId = this.planId;
        this.deptDetailsVisible = true;
      },
      exportOrderDishesData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出下方明细数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDishesData(this.searchFrom).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '预订菜品份数报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
    }
  };
</script>

<style>
</style>
