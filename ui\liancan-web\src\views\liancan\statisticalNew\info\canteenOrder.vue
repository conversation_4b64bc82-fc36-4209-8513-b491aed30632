<template>
  <basicContainer>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="scope" slot="menu">
        <el-button size="mini" class="el-icon-view" type="text" @click="showDetail(scope.row)">详情</el-button>
      </template>
      <template slot="totalQuantity" slot-scope="scope">
        <span size="medium" type="blue">共{{scope.row.totalQuantity}}种商品</span>
      </template>
      <template slot="sendTime" slot-scope="scope">
        <span>{{scope.row.sendTime}}之前</span>
      </template>
    </avue-crud>

    <el-dialog title="详情" :visible.sync="detailVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <detailVue :query="pageParams"></detailVue>
    </el-dialog>
  </basicContainer>
</template>

<script>
import {getList} from "@/api/liancan/order";
import {getColumn} from "@/api/liancan2/common";
import detailVue from "@/views/liancan/order/orderDetail";
import {mapGetters} from "vuex";
var DIC = {
  isPutaway: [{
    label: '纸质发票',
    value: "0"
  },{
    label: '电子发票',
    value: "1"
  }],
  orderStatus: [{
    label: '未接单',
    value: "0"
  },{
    label: '食堂已收',
    value: "1"
  },{
    label: '取消/拒单',
    value: "2"
  },{
    label: '已送达',
    value: "3"
  },{
    label: '配送中',
    value: "4"
  }],
  isBuy: [{
    label: '未确认',
    value: "0"
  },{
    label: '确认通过',
    value: "1"
  },{
    label: '确认拒绝',
    value: "2"
  }],
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      pageParams: {},
      detailVisible: false,
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: false,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        menuWidth: 120,
        align: "center",
        searchSpan:130,
        column: [
          {
            label: "采购食堂",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
            props: {
              label: "deptName",
              value:"id"
            },
            hide: true,
            addDisplay: false,
            viewDisplay: false,
            editDisplay: false,
            search: false,
          },
          {
            label: "采购食堂",
            prop: "deptName",
            type: "input",
          },
          {//0
            label: "订单号",
            prop: "id",
            type: "input",
            width: 160,
            search: true,
          },
          {//1
            label: "供应商",
            prop: "supplierName",
            type: "input",
            viewDisplay: true,
            addDisplay: false,
            editDisplay: false,
            width: 170,
            overHidden: true,
          },
          {//2
            label: "供应商",
            prop: "supplierId",
            type: "select",
            dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
            props: {
              label: "name",
              value: "id"
            },
            search: true,
            hide: true,
            viewDisplay: false,
            addDisplay: true,
            editDisplay: true,
            showClose: true,
          },
          {//4
            label: "采购人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-liancan2/user/getUserList?roleAlias=placeorder',
            props: {
              label: "realName",
              value: "id"
            },
            search: true,
            viewDisplay: false,
            addDisplay: true,
            editDisplay: true,
            width: 120,
            hide: true
          },
          {//5
            label: "下单时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            display: false,
            width: 150,
          },
          {//6
            label: "发票",
            prop: "invoiceId",
            hide: true,
            type: "select",
            dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
            props: {
              label: "num",
              value: "id"
            },
            //dicFlag: true,
          },
          {//7
            label: "采购确认人",
            prop: "verifyUserName",
            type: "input",
            /*dicUrl: '/api/service/rabbit-user/user-list',
            props: {
                label: "realName",
                value: "id"
            },*/
            width: 120,
          },
          {//8
            label: "确认状态",
            prop: "isBuy",
            type: "select",
            dicData: DIC.isBuy,
            width: 100,
          },
          {//9
            label: "商品种类数",
            prop: "totalQuantity",
            type: "input",
            //type: 'radio',
            slot: true,
            //align: 'center',
            display: false,
            width: 110,
          },
          {//10
            label: "采购总价",
            prop: "totalPrices",
            type: "number",
            precision:2,
            mock:{
              type:'number',
              max:1,
              min:2,
              precision:2
            },
            display: false,
            width: 90,
          },
          {//11
            label: "送货时间",
            prop: "sendTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            slot: true,
          },
          {//12
            label: "订单状态",
            prop: "orderStatus",
            type: "select",
            dicData: DIC.orderStatus,
            width: 100,
          },
          {//13
            label: "送货地址",
            prop: "site",
            type: "input",
            hide: true,
          },
          {//14
            label: "电话",
            prop: "phone",
            type: "input",
            hide: true,
          },
          {//15
            label: "联系人",
            prop: "userName",
            type: "input",
            hide: true,
          },
          {//16
            label: "拒单理由",
            prop: "rejectReason",
            maxlength: 300,
            showWordLimit:true,
            type: "textarea",
            display: false,
            hide: true,
            span: 24,
          },
          {//17
            label: "审单备注",
            prop: "remark",
            maxlength: 300,
            showWordLimit:true,
            type: "textarea",
            display: false,
            hide: true,
            span: 24,
          },
          {//17
            label: "收货状态",
            prop: "receiveStatus",
            type: "input",
            display: false,
            hide: true,
          },
          {//19
            label: "收货时间",
            prop: "receiveTime",
            type: "input",
            display: false,
            hide: true,
          },
          {//20
            label: "订单入库状态",
            prop: "putStatus",
            type: "input",
            display: false,
            hide: true,
          },
          {
            label: "采购开始时间",
            prop: "startDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
            display: false,
          },
          {
            label: "采购结束时间",
            prop: "endDate",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
            display: false,
          },
          /*             {
                           label: '采购时间',
                           prop: 'timeRange',
                           type: 'date',
                           format: 'yyyy-MM-dd',
                           valueFormat: 'yyyy-MM-dd HH:mm:ss',
                           search: true,
                           searchRange:true,
                           showColumn: false,
                           hide: true,
                       },*/
          {//
            label: "采购商品",
            prop: 'goodsList',
            type: 'dynamic',
            hide: true,
            span: 24,
            children: {
              align: 'center',
              headerAlign: 'center',
              width: '100%',
              addBtn: false,
              delBtn: false,
              column: [{
                label: 'id',
                prop: 'id',
                type: 'input',
                hide: true,
                display: false,
                showColumn: false,
              },
                {
                  label: "商品",
                  prop: "goodsId",
                  type: "select",
                  //dicFlag: false,
                  dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
                  props: {
                    label: "name",
                    value: "id"
                  },
                  disabled: false,
                },
                {
                  label: "采购单价",
                  prop: "price",
                  type: "number",
                  disabled: false,
                  precision:2,
                  mock:{
                    type:'number',
                    max:1,
                    min:2,
                    precision:2
                  },
                  //minRows: 0,
                },
                {
                  label: "计量单位",
                  prop: "unit",
                  type: "select",
                  dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                  props: {
                    label: "dictValue",
                    value: "dictKey"
                  },
                  disabled: false,
                },
                {
                  label: "采购数量",
                  prop: "quantity",
                  type: "number",
                },
                {
                  label: "小计",
                  prop: "subtotal",
                  type: "number",
                  precision:2,
                  mock:{
                    type:'number',
                    max:1,
                    min:2,
                    precision:2
                  },
                  //minRows: 0,
                },
              ]
            },
            rules: [{
              required: true,
              message: '请选择商品',
              trigger: 'blur'
            }]
          },
        ],
        disabledFlag:true,
      },
      data: [],



    }
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
  },
  mounted(){
    this.getPrevious()
  },
  components: {
    'detailVue': detailVue  //将别名demo 变成 组件 Demo
  },
  methods: {
    beforeOpen(done, type) {
      if (["view"].includes(type)) {
        if(this.form.isBuy == "2"){
          getColumn(this.option.column, 'remark').display = true;
          getColumn(this.option.column, 'rejectReason').display = false;
        }else{
          if(this.form.orderStatus == "2"){
            getColumn(this.option.column, 'rejectReason').display = true;
          }else{
            getColumn(this.option.column, 'rejectReason').display = false;
          }
          getColumn(this.option.column, 'remark').display = false;
        }
      }
      done();
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('采购结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('采购开始时间不能为空');
        }else {
          let startDateTime = new Date(params.startDate);
          let endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('采购开始时间不能大于结束时间');
          }
        }
      }
      this.query = params;
      this.page.currentPage = 1;
      /*          if (params.timeRange != '' && params.timeRange != null && params.timeRange != undefined) {
                    params.startDate = params.timeRange[0];
                    params.endDate = params.timeRange[1];
                }*/
      params.timeRange = null;
      this.onLoad(this.page, params);
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if (!!this.query.id && (isNaN(this.query.id) || this.query.id.length > 19)) {
        this.data = [];
        this.loading = false;
        return;
      }
      this.query.deptId = this.schoolId
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        console.log(data);
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    showDetail (row) {
      this.detailVisible = true;
      this.pageParams = row;
    },
  }
}
</script>

<style scoped>

</style>
