<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button type="danger"
                   size="small"
                   icon="el-icon-delete"
                   plain
                   v-if="permission.windowLimitSetting_delete"
                   @click="handleDelete">删 除
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
  import {getList, getDetail, add, update, remove} from "@/api/windowLimitSetting/windowLimitSetting";
  import {getDeptTree} from "@/api/setting/dept/systemDeptSetting";
  import {mapGetters} from "vuex";

  export default {
    data() {
      return {
        form: {},
        query: {},
        loading: true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        selectionList: [],
        option: {
/*          height:'auto',
          calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          selection: true,
          searchSpan: 100,
          menuWidth: 180,
          labelWidth:120,
          column: [
            {
              label: "所属食堂",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/canteen/select",
              props: {
                label: "deptName",
                value:"id"
              },
              search: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "营业网点",
              prop: "outletsId",
              type: "select",
              search: true,
              rules: [{
                required: true,
                message: "请输入营业网点",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dict",
              props: {
                label: "name",
                value: "id"
              },
              cascaderItem: ['windowId'],
              cascaderChange: true
            },
            {
              label: "限窗窗口",
              prop: "windowList",
              type: "select",
              multiple: true,
              span: 24,
              dicFlag: false,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dictForWindow/{{key}}",
              props: {
                label: "name",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择限制的窗口",
                trigger: "blur"
              }],
            },
            {
              label: "限窗窗口",
              prop: "windowIds",
              type: "select",
              span: 24,
              search: true,
              dicFlag: false,
              hide:true,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
              dicUrl: "/api/service/rabbit-businessOutlets/businessOutlets/dictForWindow/{{key}}",
              props: {
                label: "name",
                value: "id"
              },
              rules: [{
                required: true,
                message: "请选择限制的窗口",
                trigger: "blur"
              }],
            },
            {
              label: "限窗适用模式",
              prop: "foodScopeList",
              type: "checkbox",
              rules: [{
                required: true,
                message: "请输入限窗适用模式",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=food_scope",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "限窗标题",
              prop: "title",
              type: "input",
              rules: [{
                required: true,
                message: "请输入限窗标题",
                trigger: "blur"
              }],
            },
            {
              label: "排序",
              prop: "sort",
              type: "number",
              rules: [{
                required: true,
                message: "请输入排序",
                trigger: "blur"
              }],
              minRows: 0,
              width: 70,
            },
            {
              label: "限窗状态",
              prop: "startStopStatus",
              type: "radio",
              search: true,
              labelWidth:120,
              rules: [{
                required: true,
                message: "请输入限窗状态",
                trigger: "blur"
              }],
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=start_stop_status",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "限定部门",
              prop: "deptSettingList",
              type: "tree",
              //dicData: [],
              multiple: true,
              dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
              props: {
                //res: "data",
                label: "title",
                value:"id"
              },
              // checkStrictly: true,
              overHidden: true,
              //slot: true,
              //hide:true,
              rules: [{
                required: false,
                message: "请选择所属部门",
                trigger: "click"
              }]
            },
            {
              label: "限定用餐类别",
              prop: "diningTypeList",
              type: "select",
              span: 24,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                label: "name",
                value: "id"
              },
              overHidden: true,
              multiple: true,
            },
            {
              label: "限定用餐类别",
              prop: "diningTypeIds",
              type: "select",
              span: 24,
              search: true,
              hide:true,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
              dicUrl: "/api/service/rabbit-liancan/diningType/dict",
              props: {
                label: "name",
                value: "id"
              },
            },
            {
              label: "限定性别",
              prop: "sex",
              type: "radio",
              span: 24,
              search: true,
              dicData: [
                {
                  label: "不限制",
                  value: "0"
                },
                {
                  label: "男",
                  value: "1"
                },
                {
                  label: "女",
                  value: "2"
                }
              ],
              labelWidth:120,
          /*    dicUrl: "/api/service/rabbit-system/dict/dictionary?code=sex",
              props: {
                label: "dictValue",
                value: "dictKey"
              },*/
            },
            {
              label: "限定属性",
              prop: "attributeIdList",
              type: "checkbox",
              span: 24,
              dicUrl: '/api/service/rabbit-liancan/userRole/dict',
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: "限定属性",
              prop: "attributeIds",
              type: "select",
              span: 24,
              search: true,
              hide:true,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
              dicUrl: '/api/service/rabbit-liancan/userRole/dict',
              props: {
                res: "data",
                label: "name",
                value: "id"
              },
            },
            {
              label: "限定民族",
              prop: "nationList",
              type: "select",
              span: 24,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              multiple: true,
            },
            {
              label: "限定民族",
              prop: "nations",
              type: "select",
              span: 24,
              search: true,
              hide:true,
              addDisplay:false,
              editDisplay:false,
              viewDisplay:false,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=nation",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
            },
            {
              label: "学校",
              prop: "deptId",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: "限定时间",
              prop: "limitTimeDataset",
              type: "input",
              span: 24,
              rules: [{
                validator: (rule, value, callback) => this.validateTimeRanges(rule, value, callback),
                trigger: 'blur'
              }],
              placeholder: "请输入时间段，格式如：00:00~06:00 或 00:00~06:00,06:00~08:00"
            },
          ]
        },
        data: []
      };
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.windowLimitSetting_add, false),
          viewBtn: this.vaildData(this.permission.windowLimitSetting_view, false),
          delBtn: this.vaildData(this.permission.windowLimitSetting_delete, false),
          editBtn: this.vaildData(this.permission.windowLimitSetting_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      if (this.userInfo.userType === 'canteen') {
        this.option.column[0].search = false;
        this.option.column[0].hide = true;
      }
    },
    methods: {
      validateTimeRanges(rule, value, callback) {
        if (!value) {
          callback();
          return;
        }

        // 验证整体格式
        const fullTimeRangePattern = /^(([01]\d|2[0-3]):([0-5]\d)~([01]\d|2[0-3]):([0-5]\d)(,(?!$)|$))+$/;
        if (!fullTimeRangePattern.test(value)) {
          callback(new Error('时间格式不正确，请使用00:00~23:59格式，多个时间段请使用,分隔'));
          return;
        }

        // 检查每个时间段
        const timeRanges = value.split(',');
        const ranges = [];

        for (let range of timeRanges) {
          range = range.trim();

          // 解析时间
          const [start, end] = range.split('~');
          const startMinutes = this.timeToMinutes(start);
          const endMinutes = this.timeToMinutes(end);

          // 确保结束时间大于开始时间
          if (endMinutes <= startMinutes) {
            callback(new Error('结束时间必须大于开始时间'));
            return;
          }

          // 检查时间段重叠
          for (const existingRange of ranges) {
            if (this.isOverlapping(
              existingRange.start,
              existingRange.end,
              startMinutes,
              endMinutes
            )) {
              callback(new Error('时间段存在重叠'));
              return;
            }
          }

          ranges.push({ start: startMinutes, end: endMinutes });
        }

        callback();
      },

      timeToMinutes(timeStr) {
        const [hours, minutes] = timeStr.split(':').map(Number);
        return hours * 60 + minutes;
      },

      isOverlapping(start1, end1, start2, end2) {
        return Math.max(start1, start2) < Math.min(end1, end2);
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.onLoad(this.page);
      },
      searchChange(params, done) {
        this.query = params;
        this.page.currentPage = 1
        this.onLoad(this.page, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      }
    }
  };
</script>

<style>
</style>
