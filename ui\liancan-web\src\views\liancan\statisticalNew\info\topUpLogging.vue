<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="permission.top_up_logging_export && !this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exports">导出</el-button>
      </template>
      <template slot="payStatus" slot-scope="{row}">
        <el-tag v-if="row.payStatus == 'PAYFAIL'" type="danger">支付失败</el-tag>
        <el-tag v-if="row.payStatus == 'PAID'" type="success">支付成功</el-tag>
        <el-tag v-if="row.payStatus == 'WAITPAY'" type="blue">等待回调</el-tag>
        <el-tag v-if="row.payStatus == 'CANCELPAY'" type="warning">支付取消</el-tag>
      </template>
      <template slot="menuLeft">
        <a style="color: red" type="danger"
           size="small"
           icon="el-icon-delete"
           plain
        >统计时间：{{this.startDate}} - {{this.endDate}}
        </a>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-view"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   @click.stop="rechargeDetails(row)">详情
        </el-button>
      </template>
    </avue-crud>
    <el-dialog title="充值详情" :visible.sync="rechargeDetailsVisible" width="60%" left :append-to-body="true" @close="rechargeDetailsVisible = false">
      <el-row>
        <div>
          <div  class="row-table">
            <div class="row-table-first">姓名</div><div class="row-table-second">{{rechargeForm.userName}}</div>
            <div class="row-table-first">性别</div><div class="row-table-second">{{rechargeForm.sex == '1'?'男':rechargeForm.sex == '2'?'女':''}}</div>
            <div class="row-table-first">编号</div><div class="row-table-second">{{rechargeForm.studentJobNo}}</div>
          </div>
          <div  class="row-table">
            <div class="row-table-first">充值账户</div><div class="row-table-second">{{rechargeForm.mealsType == '0'?'统缴餐钱包':rechargeForm.mealsType == '1'?'自选餐钱包':''}}</div>
            <div class="row-table-first">充值方式</div><div class="row-table-second">{{rechargeForm.topUpType == 'WXPAY'?'微信支付':rechargeForm.topUpType == 'CASH'?'现金支付':''}}</div>
            <div class="row-table-first">充值金额</div><div class="row-table-second">{{rechargeForm.balance}}</div>
          </div>
          <div  class="row-table">
            <div class="row-table-first">手续费费率</div><div class="row-table-second">{{rechargeForm.serviceChargeRate}}</div>
            <div class="row-table-first">手续费</div><div class="row-table-second">{{rechargeForm.serviceCharge}}</div>
            <div class="row-table-first">实收金额</div><div class="row-table-second">{{rechargeForm.actualAmount}}</div>
          </div>
          <div  class="row-table">
            <div class="row-table-first">充值操作人</div><div class="row-table-second">{{rechargeForm.topUpUser}}</div>
            <div class="row-table-first">操作人身份</div><div class="row-table-second">{{rechargeForm.identity}}</div>
            <div class="row-table-first">充值时间</div><div class="row-table-second">{{rechargeForm.createTime}}</div>
          </div>
          <div  class="row-table">
            <div class="row-table-first">支付状态</div><div class="row-table-second">{{rechargeForm.payStatus == 'WAITPAY'?'等待回调':
            rechargeForm.payStatus == 'PAID'?'支付成功':
            rechargeForm.payStatus == 'PAYFAIL'?'支付失败':
            rechargeForm.payStatus == 'CANCELPAY'?'支付取消':''}}</div>
            <div class="row-table-first">备注</div><div class="row-table-second" style="width: 56.2%;">{{rechargeForm.remark}}</div>
          </div>
        </div>
      </el-row>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, exportTopUpData,getRechargeDetails} from "@/api/topUp/topUpLogging";
import {
  getDeptTree
} from "@/api/setting/dept/systemDeptSetting";
import {mapGetters} from "vuex";
const DIC = {
  TOPUPTYPE:[{
    label: '微信支付',
    value: 'WXPAY'
  },{
    label: '现金支付',
    value: 'CASH'
  },{
    label: '通联支付',
    value: 'ALLIN'
  }],
  OPERATION:[{
    label: '老师',
    value: 'TEACHER'
  },{
    label: '家长',
    value: 'PARENT'
  },{
    label: '学生',
    value: 'STUDENT'
  },{
    label: '职工',
    value: 'UNIT'
  },{
    label: '其他',
    value: 'OTHER'
  }],
  PAYSTATUS:[{
    label: '支付失败',
    value: 'PAYFAIL'
  },{
    label: '支付成功',
    value: 'PAID'
  },{
    label: '等待回调',
    value: 'WAITPAY'
  }],
  sex: [
    {
      label: '男',
      value: "1"
    },
    {
      label: '女',
      value: "2"
    }
  ],
}
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      searchForm:{},
      rechargeForm:{},
      selectionList: [],
      startDate:undefined,
      endDate:undefined,
      rechargeDetailsVisible:false,
      option: {
        /*   height:'auto',*/
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        editBtn: false,
        delBtn: false,
        addBtn: false,
        viewBtn: false,
        selection: false,
        searchSpan:100,
        showSummary: true,
        sumColumnList: [
          {
            name: "balance",
            type: "sum"
          },
        ],
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            hide: true
          },
          {
            label: "单位",
            prop: "unitId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search:true,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }]
          },
          {
            label: "学校",
            prop: "schoolId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/select",
            props: {
              label: "deptName",
              value:"id"
            },
            hide: false,
            search:true,
            rules: [{
              required: true,
              message: "请选择部门",
              trigger: "click"
            }]
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search: true,
            sortable:true,
          },
          {
            label: "性别",
            prop: "sex",
            type: "select",
            dicData: DIC.sex,
            hide:true,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            search: true,
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptName",
            type: "input",
            width: 150,
            overHidden: true,
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              //res: "data",
              label: "title",
              value:"id"
            },
          },
          {
            label: "充值账户",
            prop: "mealsType",
            type: "select",
            search:true,
            sortable:true,
            dicData:[
              {
                label: "统缴餐钱包",
                value: "0"
              },
              {
                label: "自选餐钱包",
                value: "1"
              }
            ]
          },
          {
            label: "充值方式",
            prop: "topUpType",
            type: "select",
            dicData: DIC.TOPUPTYPE,
            search: true,
            sortable:true,
          },
          {
            label: "支付状态",
            prop: "payStatus",
            type: "select",
            dicData: DIC.PAYSTATUS,
            search: true,
            slot:true,
            sortable:true,
            /*  hide:true,*/
          },
          {
            label: "开始金额",
            prop: "startAmount",
            type: "input",
            hide:true,
            sortable:true,
          },
          {
            label: "结束金额",
            prop: "endAmount",
            type: "input",
            hide:true,
            sortable:true,
          },
          {
            label: "开始时间",
            prop: "startDateTime",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },

          },
          {
            label: "结束时间",
            prop: "endDateTime",
            type: "datetime",
            hide: true,
            search: true,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            mock: {
              type: 'datetime',
              format: 'yyyy-MM-dd',
              now: true,
            },
          },
          /*            {
                        label: '充值时间',
                        prop: 'date',
                        type:'datetime',
                        searchSpan:6,
                        searchRange:true,
                        search:true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        hide:true,
                      },*/
          {
            label: "操作人身份",
            prop: "rechargeOperationType",
            type: "select",
            dicData: DIC.OPERATION,
            search: true,
            hide:true,
          },
          {
            label: "充值金额",
            prop: "balance",
            sortable:true,
          },
          {
            label: "手续费费率",
            prop: "serviceChargeRate",
            type: "input",
            sortable:true,
          },
          {
            label: "手续费",
            prop: "serviceCharge",
            type: "input",
            sortable:true,
          },
          {
            label: "实收金额",
            prop: "actualAmount",
            type: "number",
            sortable:true,
          },
          {
            label: "充值操作人",
            prop: "topUpUser",
            type: "input",
            search:true,
            sortable:true,
          },
          {
            label: "操作人身份",
            prop: "identity",
            type: "input",
            hide:true,
          },
          {
            label: "充值时间",
            prop: "createTime",
            type: "input",
            width:160,
            sortable:true,
            /*hide:true,*/
          },
          /*      {
                  label: "充值单号",
                  prop: "orderId",
                  type: "input",
                  width:160,
                },*/
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.topUpLogging_add, false),
        viewBtn: this.vaildData(this.permission.topUpLogging_view, false),
        delBtn: this.vaildData(this.permission.topUpLogging_delete, false),
        editBtn: this.vaildData(this.permission.topUpLogging_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    /*      getDeptTree().then(res => {
            const index = this.$refs.crud.findColumnIndex("deptId");
            this.option.column[index].dicData = res.data.data;
          });*/
    this.getMonthStartEnd();
    // 单位列表是否显示
    this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    if (this.userInfo.userType === 'school'){
      this.option.column[1].hide = true
      this.option.column[2].hide = true
      this.option.column[1].search = false
      this.option.column[2].search = false
      /*  this.option.printBtn = true;*/
    }else if (this.userInfo.userType === 'education'){
      this.option.column[2].search = false;
      this.option.column[2].search = false;
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
      this.option.column[7].search = false;
    }else if (this.userInfo.userType === 'jiWei'){
      this.option.column[2].search = false;
      this.option.column[2].hide = true;
      this.option.column[1].hide = true;
      this.option.column[1].search = false;
      this.option.column[7].search = false;
    }else if (this.userInfo.userType === 'canteen'){
      this.option.column[1].hide = true
      this.option.column[2].hide = true
      this.option.column[1].search = false
      this.option.column[2].search = false
    }
  },
  methods: {
    getMonthStartEnd(){
      var startDate = new Date().getFullYear()+' 01-01'
      var endDate = new Date().getFullYear()+' 12-31'
      this.startDate = startDate;
      this.endDate = endDate;
    },
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.searchForm ={};
      this.getMonthStartEnd();
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (params.startDateTime != '' && params.startDateTime != null && params.startDateTime != undefined){
        if (params.endDateTime == '' || params.endDateTime == null || params.endDateTime == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDateTime != '' && params.endDateTime != null && params.endDateTime != undefined){
        if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDateTime);
          var endDateTime = new Date(params.endDateTime);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      if (params.startDateTime == '' || params.startDateTime == null || params.startDateTime == undefined){
        this.getMonthStartEnd();
      }else {
        this.startDate = params.startDateTime;
        this.endDate = params.endDateTime;
      }
      this.query = params;
      this.searchForm = params;
      this.page.currentPage = 1
      /*      if (params.date != '' && params.date != null && params.date != undefined) {
              params.startDateTime = params.date[0];
              params.endDateTime = params.date[1];
              this.startDate = params.date[0];
              this.endDate = params.date[1];
            }*/
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.canteenId = this.schoolId;
      }
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    exports(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出充值明细数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportTopUpData(this.searchForm, this.ids).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '充值明细报表.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    rechargeDetails(row){
      getRechargeDetails(row.id).then(res => {
        this.rechargeForm = res.data.data;
      });
      this.rechargeDetailsVisible = true;
    }
  }
};
</script>

<style scoped>
.row-table{
  display: flex;
  line-height: 40px;
}
.row-table-first{
  background-color: #fafafa;
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 10%;
}
.row-table-second{
  border-style: solid;
  border-color: #EBEEF5;
  border-width: 0.1px;
  justify-content: center;
  align-items: center;
  display: flex;
  width: 23%;
}
</style>
