<template>
  <basic-container>
    <div class="search_container">
      <el-form :inline="true" :model="dataForm">
        <el-form-item label="被检单位">
          <el-select v-model="dataForm.deptId" placeholder="被检单位" clearable filterable @change="getDataList()">
            <el-option v-for="item in canteenList" :key="item.id" :label="item.deptName" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label>
          <el-select v-model="dataForm.checkItem" placeholder="检测项目" clearable filterable @change="getDataList()">
            <el-option v-for="item in dictList" :key="item.dictValue" :label="item.dictValue" :value="item.dictValue"></el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item label="时间" style="margin-left: 30px;">
          <el-date-picker v-model="dataForm.createTimeStart" type="date" value-format="yyyy-MM-dd"  placeholder="时间起" clearable style="width:140px" />
          至
          <el-date-picker v-model="dataForm.createTimeEnd" type="date" value-format="yyyy-MM-dd"  placeholder="时间止" clearable style="width:140px" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getDataList()" icon="el-icon-search">查询</el-button>
          <el-button @click="getDataList('empty')" icon="el-icon-delete">清空</el-button>
          <!-- <el-button   type="primary" icon="el-icon-plus">新增</el-button>
          <el-button  type="danger" icon="el-icon-delete">批量删除</el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="avue-empty" v-show="!hasData" style="margin-top:150px;margin-bottom: 800px;"><div class="avue-empty__image" style="height: 50px;"><img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNDEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiAgPGcgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMCAxKSIgZmlsbD0ibm9uZSIgZmlsbC1ydWxlPSJldmVub2RkIj4KICAgIDxlbGxpcHNlIGZpbGw9IiNGNUY1RjUiIGN4PSIzMiIgY3k9IjMzIiByeD0iMzIiIHJ5PSI3Ii8+CiAgICA8ZyBmaWxsLXJ1bGU9Im5vbnplcm8iIHN0cm9rZT0iI0Q5RDlEOSI+CiAgICAgIDxwYXRoIGQ9Ik01NSAxMi43Nkw0NC44NTQgMS4yNThDNDQuMzY3LjQ3NCA0My42NTYgMCA0Mi45MDcgMEgyMS4wOTNjLS43NDkgMC0xLjQ2LjQ3NC0xLjk0NyAxLjI1N0w5IDEyLjc2MVYyMmg0NnYtOS4yNHoiLz4KICAgICAgPHBhdGggZD0iTTQxLjYxMyAxNS45MzFjMC0xLjYwNS45OTQtMi45MyAyLjIyNy0yLjkzMUg1NXYxOC4xMzdDNTUgMzMuMjYgNTMuNjggMzUgNTIuMDUgMzVoLTQwLjFDMTAuMzIgMzUgOSAzMy4yNTkgOSAzMS4xMzdWMTNoMTEuMTZjMS4yMzMgMCAyLjIyNyAxLjMyMyAyLjIyNyAyLjkyOHYuMDIyYzAgMS42MDUgMS4wMDUgMi45MDEgMi4yMzcgMi45MDFoMTQuNzUyYzEuMjMyIDAgMi4yMzctMS4zMDggMi4yMzctMi45MTN2LS4wMDd6IiBmaWxsPSIjRkFGQUZBIi8+CiAgICA8L2c+CiAgPC9nPgo8L3N2Zz4K" alt=""></div><p class="avue-empty__desc">暂无数据</p></div>
    <div id="chartColumn" style="width: 100%; height: 400px;margin-top:30px;"></div>
    <div style="text-align: center;margin-top:0px;">检测项目：{{checkCount}}项<span style="padding: 0px 50px;">&nbsp;</span>检测次数：{{checkTimes}}次</div>
    <div style="border: 0px solid #f00;margin: 5%;margin-top: 30px;position: relative;">
      <div style="display: flex; justify-content: space-between;padding: 0px 10px 10px 10px;">
        <div>检测项目数量列表:<a style="margin-left: 10px;color:#00f;cursor: pointer;" @click="exportData">导出</a></div>
        <div>单位：次</div>
      </div>
      <div class="datatable">
        <avue-crud :data="dataList" :option="option"></avue-crud>
      </div>
    </div>
    <div style="height: 500px;"></div>
  </basic-container>
</template>

<script>

import request from "@/router/axios";
import echarts from "echarts";
import { dateFormat } from "@/util/date";
export default {
  data() {
    return {
      chartColumn: null,
      canteenList: [],
      hasData:false,
      checkCount:0,
      checkTimes:0,
      dictList:[],
      dataList:[],
      dataForm: {
        deptId: "",
        createTimeStart: dateFormat(
          new Date(new Date().getTime() - 1000 * 3600 * 24 * 30),
          "yyyy-MM-dd"
        ),
        createTimeEnd: dateFormat(new Date(), "yyyy-MM-dd")
      },
      option:{
          border:true,
          page:false,
          align:'center',
          menuAlign:'center',
          refreshBtn:false,
          columnBtn:false,
          addBtn:false,
          menu:false,
          column:[
             {label:'检测项目',prop:'checkItem'}
             , {label:'检测次数',prop:'checkCount'}
             , {label:'百分比',prop:'percentage'}
          ]
        }
    };
  },
  mounted() {
    //this.drawLine();
    this.getCanteenList();
    this.getDataList();
   // this.getDictList();
  },
  methods: {
    exportData(){
      var aoa = [
            ['检测项目', '检测次数', '百分比']
        ];
        this.dataList.forEach(e=>{
          aoa.push([e.checkItem,e.checkCount,e.percentage]);
        })
        //创建book
        var wb = XLSX.utils.book_new();
        var sheet = XLSX.utils.aoa_to_sheet(aoa);
        sheet["B2"].s = {									//为某个单元格设置单独样式
          font: {
            name: '宋体',
            sz: 24,
            bold: true,
            color: { rgb: "FFFFAA00" }
          },
          alignment: { horizontal: "center", vertical: "center", wrap_text: true },
          fill: { bgcolor: { rgb: 'ffff00' } }
        }
        var timestamp = (new Date()).getTime();
        //sheet写入book
        XLSX.utils.book_append_sheet(wb, sheet, "file");
        //输出
        XLSX.writeFile(wb, "检测项目数量列表.xls");
    },
    getData(url,params,callbackFun) {
      request({
        url: url,
        params: {...params},
        method: "get"
      }).then(
        res => {
          callbackFun(res);
        },
        error => { }
      );
    },
    getCanteenList() {
      request({
        url: "/api/service/rabbit-system/dept/getDeptAndSubDept?deptCategory=4",
        method: "get"
      }).then(
        res => {
          console.log(res, " res  getCanteenList ... ");
          this.canteenList = res.data.data;
        },
        error => { }
      );
    },getDictList() {
      request({
        url: "/api/service/rabbit-system/dict/dictionary?code=fast_check_item",
        method: "get"
      }).then(
        res => {
          console.log(res, " res  getDictList ... ");
          this.dictList = res.data.data;
          console.log(this.dictList,'this.dictList...')
        },
        error => { }
      );
    },
    getDataList(action) {
      if (action === "empty") {
        for (const key in this.dataForm) {
          if (this.dataForm.hasOwnProperty(key)) {
            this.dataForm[key] = "";
          }
        }
      }
      let params = this.dataForm;
      this.getData('/api/service/rabbit-supplier/foodcheck/statCheckItem',params,(res)=>{
          this.checkCount=0;
          this.checkTimes=0;
          let czzDataList=[]
          this.dataList=[]
          let data = res.data.data;
          if(data&&data.length>0){
            this.hasData=true;
            document.getElementById('chartColumn').style.display='block';
          }else{
            this.hasData=false;
            document.getElementById('chartColumn').style.display='none';
          }
          let itemObj={}
          if(data){
            data.forEach(e => {
              czzDataList.push({name:e.checkItem,value:e.checkCount});
              this.checkTimes+=e.checkCount;
              this.dataList.push({checkItem:e.checkItem,checkCount:e.checkCount,percentage:0})
              itemObj[e.checkItem]=1;
            });
          }
          this.dataList.forEach(e=>{
            e.percentage=(e.checkCount/this.checkTimes*100).toFixed(2)+'%'
          })
          this.checkCount=Object.keys(itemObj).length;
          //this.drawLine(xAxisData, passData, unpassData);
          this.drawBarChart('chartColumn',czzDataList);
      })
    },
    drawBarChart(renderid,czzDataList) {
      // 定义使用的颜色数组
      //const colors = ['#c23531','#2f4554', '#61a0a8', '#d48265', '#91c7ae','#749f83'];
      //czzDataList=[{name: "卫生许可",value:70},{name: "健康证件",value:45},{name: "营业执照",value:90}]
      let colors={吊白块:"#39C5FF",农药:"#69FFB3","重金属":"#5DFDFF","取水证件":"#8694FF","管理制度":"#5DFDFF","应急预案":"#5DFFB1","承诺书":"#C0FF5D"}
        czzDataList.forEach(e=>{
            e['color']=colors[e.name];
        })
        // var dataArr = [
        //     { name: "卫生许可", value: zjData.wsxk || 0, barColor: '#39C5FF' },
        //     { name: "健康证件", value: zjData.jkzj || 0, barColor: '#69FFB3' },
        //     { name: "营业执照", value: zjData.yyjz || 0, barColor: '#5E93FF' }
        // ];
        var dataTxt = [], dataVal = [];
        czzDataList.forEach(e => {
            dataTxt.push(e.name);
            dataVal.push(e.value);
        })

      // echart 配置项
      let option = {
          title: {
              show: false,
              text: '检测项目数量统计'
          },
          toolbox: {
          　　show: true,
          　　feature: {
          　　　　saveAsImage: {
          　　　　show:true,
          　　　　excludeComponents :['toolbox'],
          　　　　pixelRatio: 2
          　　　　}
          　　}
          },
          color: colors,
          xAxis: {
              name: '',
              data:dataTxt
          },
          grid: {
                left: '5%',
                //top: '20px',
                right: '5%',
                bottom: '30px',
                // containLabel: false
            },
          yAxis: {
              name: '检测项目数量统计'
          },
          series: [
              {
                  type: 'bar',
              name: '人数3',
                  data: czzDataList,
                  itemStyle: {
                      normal:{
                        color: function (param) {
                                var d = czzDataList[param.dataIndex];
                                return d.color||'#50CCFF';
                            },
                      }
                  },
                  barWidth: '40',
                  label: {
                      show: true,
                      position: 'top',
                      formatter: function (p) {
                          return p.value+'次';
                      }
                  },
              }
          ]
      }
      var chart = echarts.init(document.getElementById(renderid));
      chart.setOption(option);
      chart.resize();
    }
  }
};
</script>

<style>
.el-row {
  margin-bottom: 20px;
  font-size: medium;
}
.datatable .avue-crud__menu{display: none;}
.datatable .avue-crud__pagination{display: none}
</style>
