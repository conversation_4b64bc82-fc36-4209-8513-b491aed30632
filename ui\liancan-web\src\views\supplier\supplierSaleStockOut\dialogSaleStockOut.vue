<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="menu" slot-scope="scope">
                <el-button type="text"
                           size="mini"
                           @click="addOrder(scope.row)">选择
                </el-button>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {getOrderList, getDetail, orderDetail} from "@/api/liancan/order";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";

const DIC = {
    isPutaway: [{
        label: '纸质发票',
        value: "0"
    },{
        label: '电子发票',
        value: "1"
    }],
    orderStatus: [{
        label: '未接单',
        value: "0"
    },{
        label: '食堂已收',
        value: "1"
    },{
        label: '取消/拒单',
        value: "2"
    },{
        label: '已送达',
        value: "3"
    },{
        label: '配送中',
        value: "4"
    }],
    isBuy: [{
        label: '未确认',
        value: "0"
    },{
        label: '确认通过',
        value: "1"
    },{
        label: '确认拒绝',
        value: "2"
    }],
}
export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            option: {
                height: 'auto',
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchShow: true,  //首次加载是否显示搜索
                searchMenuSpan: 4, //搜索按钮长度
                // searchSpan:24,      //搜索框长度  最大长度24
                // searchLabelWidth: 120, //搜索框标题宽度 默认80
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "客户单位",
                        prop: "deptParentName",
                        type: "input",
                    },
                    {
                        label: "客户单位",
                        prop: "deptParentId",
                        type: "select",
                        dicUrl: "/api/service/rabbit-system/dept/canteen/select",
                        props: {
                            label: "deptName",
                            value:"id"
                        },
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        display:false,
                        // search: true,
                        hide: true,
                    },
                    {
                        label: "食堂",
                        prop: "deptId",
                        type: "tree",
                        dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
                        props: {
                            label: "deptName",
                            value:"id"
                        },
                        hide: true,
                        addDisplay: false,
                        viewDisplay: false,
                        editDisplay: false,
                        search:true,
                    },
                    {
                        label: "采购食堂",
                        prop: "deptName",
                        type: "input",
                    },
                    {//0
                        label: "订单号",
                        prop: "id",
                        type: "input",
                        width: 160,
                        search: true,
                    },
                    {//1
                        label: "供应商",
                        prop: "supplierName",
                        type: "input",
                        viewDisplay: true,
                        addDisplay: false,
                        editDisplay: false,
                        width: 170,
                        overHidden: true,
                        hide: true,
                    },
                    {//2
                        label: "供应商",
                        prop: "supplierId",
                        type: "select",
                        dicUrl: '/api/service/rabbit-signUp/supplierSignUp/schoolSupplier',
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                        hide: true,
                        viewDisplay: false,
                        addDisplay: true,
                        editDisplay: true,
                        showClose: true,
                    },
                    {//4
                        label: "采购人",
                        prop: "createUser",
                        type: "select",
                        dicUrl: '/api/service/rabbit-liancan2/user/getUserList?roleAlias=placeorder',
                        props: {
                            label: "realName",
                            value: "id"
                        },
                        search: true,
                        viewDisplay: false,
                        addDisplay: true,
                        editDisplay: true,
                        width: 120,
                        hide: true
                    },
                    {//5
                        label: "下单时间",
                        prop: "createTime",
                        type: "datetime",
                        format: "yyyy-MM-dd HH:mm:ss",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        display: false,
                        width: 150,
                    },
                    {//6
                        label: "发票",
                        prop: "invoiceId",
                        hide: true,
                        type: "select",
                        dicUrl: '/api/service/rabbit-liancan/invoiceTitle/dict',
                        props: {
                            label: "num",
                            value: "id"
                        },
                        //dicFlag: true,
                    },
                    {//7
                        label: "采购确认人",
                        prop: "verifyUserName",
                        type: "input",
                        /*dicUrl: '/api/service/rabbit-user/user-list',
                        props: {
                            label: "realName",
                            value: "id"
                        },*/
                        width: 120,
                    },
                    {//8
                        label: "确认状态",
                        prop: "isBuy",
                        type: "select",
                        dicData: DIC.isBuy,
                        width: 100,
                    },
                    {//9
                        label: "商品种类数",
                        prop: "totalQuantity",
                        type: "input",
                        //type: 'radio',
                        slot: true,
                        //align: 'center',
                        display: false,
                        width: 110,
                    },
                    {//10
                        label: "采购总价",
                        prop: "totalPrices",
                        type: "number",
                        precision:2,
                        mock:{
                            type:'number',
                            max:1,
                            min:2,
                            precision:2
                        },
                        display: false,
                        width: 90,
                    },
                    {//11
                        label: "送货时间",
                        prop: "sendTime",
                        type: "datetime",
                        format: "yyyy-MM-dd HH:mm",
                        valueFormat: "yyyy-MM-dd HH:mm:ss",
                        slot: true,
                    },
                    {//12
                        label: "订单状态",
                        prop: "orderStatus",
                        type: "select",
                        dicData: DIC.orderStatus,
                        width: 100,
                    },
                    {//13
                        label: "送货地址",
                        prop: "site",
                        type: "input",
                        hide: true,
                    },
                    {//14
                        label: "电话",
                        prop: "phone",
                        type: "input",
                        hide: true,
                    },
                    {//15
                        label: "联系人",
                        prop: "userName",
                        type: "input",
                        hide: true,
                    },
                    {//16
                        label: "拒单理由",
                        prop: "rejectReason",
                        maxlength: 300,
                        showWordLimit:true,
                        type: "textarea",
                        display: false,
                        hide: true,
                        span: 24,
                    },
                    {//17
                        label: "审单备注",
                        prop: "remark",
                        maxlength: 300,
                        showWordLimit:true,
                        type: "textarea",
                        display: false,
                        hide: true,
                        span: 24,
                    },
                    {//17
                        label: "收货状态",
                        prop: "receiveStatus",
                        type: "input",
                        display: false,
                        hide: true,
                    },
                    {//19
                        label: "收货时间",
                        prop: "receiveTime",
                        type: "input",
                        display: false,
                        hide: true,
                    },
                    {//20
                        label: "订单入库状态",
                        prop: "putStatus",
                        type: "input",
                        display: false,
                        hide: true,
                    },
                    {
                        label: "采购开始时间",
                        prop: "startDate",
                        type: "datetime",
                        hide: true,
                        search: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        mock: {
                            type: 'datetime',
                            format: 'yyyy-MM-dd',
                            now: true,
                        },
                        display: false,
                    },
                    {
                        label: "采购结束时间",
                        prop: "endDate",
                        type: "datetime",
                        hide: true,
                        search: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        mock: {
                            type: 'datetime',
                            format: 'yyyy-MM-dd',
                            now: true,
                        },
                        display: false,
                    },
                    {//
                        label: "采购商品",
                        prop: 'goodsList',
                        type: 'dynamic',
                        hide: true,
                        span: 24,
                        children: {
                            align: 'center',
                            headerAlign: 'center',
                            width: '100%',
                            addBtn: false,
                            delBtn: false,
                            column: [{
                                label: 'id',
                                prop: 'id',
                                type: 'input',
                                hide: true,
                                display: false,
                                showColumn: false,
                            },
                                {
                                    label: "商品",
                                    prop: "goodsId",
                                    type: "select",
                                    //dicFlag: false,
                                    dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
                                    props: {
                                        label: "name",
                                        value: "id"
                                    },
                                    disabled: false,
                                },
                                {
                                    label: "采购单价",
                                    prop: "price",
                                    type: "number",
                                    disabled: false,
                                    precision:2,
                                    mock:{
                                        type:'number',
                                        max:1,
                                        min:2,
                                        precision:2
                                    },
                                    //minRows: 0,
                                },
                                {
                                    label: "计量单位",
                                    prop: "unit",
                                    type: "select",
                                    dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
                                    props: {
                                        label: "dictValue",
                                        value: "dictKey"
                                    },
                                    disabled: false,
                                },
                                {
                                    label: "采购数量",
                                    prop: "quantity",
                                    type: "number",
                                },
                                {
                                    label: "小计",
                                    prop: "subtotal",
                                    type: "number",
                                    precision:2,
                                    mock:{
                                        type:'number',
                                        max:1,
                                        min:2,
                                        precision:2
                                    },
                                    //minRows: 0,
                                },
                            ]
                        },
                        rules: [{
                            required: true,
                            message: '请选择商品',
                            trigger: 'blur'
                        }]
                    },
                ],
            },
            data: [],
            supplierList: [],
            salesmanList: [],
            supplierCustomerId: '',
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
        //计算合计金额
        "orderData"(newVal,oldVal) {
            this.summaryAmt();
        },
    },
    mounted() {
    },
    methods: {
        tableHeaderStyle({ row, column, rowIndex, columnIndex }) {
            return 'background-color: #F0F8FF;color:black;font-size:15px;font-weight: bold;text-align:center'
        },
        tableDetailStyle({ row, column, rowIndex, columnIndex }) {
            return 'font-size:13px;text-align:center'
        },
        getSummaries(param) {
            const { columns, data } = param;
            const sums = [];
            columns.forEach((column, index) => {
                if (index === 0) {
                    sums[index] = '合计';
                    return;
                }
                const values = data.map(item => Number(item[column.property]));
                const columnArr = ['qty','amt'];
                if (columnArr.includes(column.property)) {
                    if (!values.every(value => isNaN(value))) {
                        sums[index] = values.reduce((prev, curr) => {
                            const value = Number(curr);
                            if (!isNaN(value)) {
                                return prev + curr;
                            } else {
                                return prev;
                            }
                        }, 0);
                    } else {
                        sums[index] = '';
                    }
                } else {
                    sums[index] = '';
                }
            });

            return sums;
        },

        summaryAmt() {
            var iTotalAmt = 0;
            this.orderData.forEach((item,index)=>{
                var qty = item.qty;
                var price = item.price;
                iTotalAmt += (qty*price);
            });
            this.totalAmt = iTotalAmt;
        },
        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {

        },
        selectionClear() {

        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            if (!!this.query.id && (isNaN(this.query.id) || this.query.id.length > 19)) {
                this.data = [];
                this.loading = false;
                return;
            }

            params.type = '';
            this.query.type = '';
            params.orderStatus = '6';
            getOrderList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //主界面基本方法 end
        addOrder(row) {
            if (row.id!==undefined && row.id!=='') {
                orderDetail(row.id).then(res => {
                    const data = res.data.data;
                    var orderData = data;
                    var salesOrderId = row.id;
                    var salesOrderCode = row.id;
                    var supplierCustomerId = row.deptParentId;
                    var supplierCanteenId = row.deptId;
                    orderData.forEach((item) => {
                        item.$cellEdit = false;
                    });
                    this.$emit('child-event',salesOrderId,salesOrderCode,supplierCustomerId,supplierCanteenId,orderData);
                });
            }
        },
    },
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
