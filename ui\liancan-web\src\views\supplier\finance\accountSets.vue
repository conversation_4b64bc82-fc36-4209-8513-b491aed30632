<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-edit"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.isUse == '0'"
                   @click="$refs.crud.rowEdit(row,index)">修改
        </el-button>
        <el-button type="text"
                   icon="el-icon-delete"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.isUse == '0'"
                   @click="$refs.crud.rowDel(row,index)">删除
        </el-button>
        <el-button type="text"
                   icon="el-icon-turn-off"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.isUse == '0'"
                   @click.stop="openAccount(row, 0)">启用
        </el-button>
      </template>
      <template slot="frontImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.frontImg" fit="cover" @click="handleClickPreview(row.frontImg)"></el-image>
      </template>
      <template slot="backImg" slot-scope="{row}">
        <el-image  style="width: 30px; height: 50px" :src="row.backImg" fit="cover" @click="handleClickPreview(row.backImg)"></el-image>
      </template>
      <template slot="status"  slot-scope="{row}">
        <el-tag v-if="row.isUse == '0'" size="medium" type="blue">未启用</el-tag>
        <el-tag v-if="row.isUse == '1'" size="medium" type="success">已启用</el-tag>
      </template>
    </avue-crud>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </basic-container>
</template>

<script>
import {getList, getDetail, add, update, remove, passOrNo, openAccount} from "@/api/supplier/finance/accountSets";
import {mapGetters} from "vuex";
import {trialBalance} from "@/api/supplier/finance/beginningBalance";
import {amtFilters, amtFiltersEx} from "@/api/supplier/finance/financialUtils";
var DIC = {
  isUse: [{
    label: '未启用',
    value: 0
  },{
    label: '已启用',
    value: 1
  }],
}
export default {
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: false,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        align: 'center',
        editBtn:false,
        delBtn:false,
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          // {
          //   label: "所属食堂",
          //   prop: "canteenId",
          //   type: "select",
          //   addDisplay: true,
          //   editDisplay: true,
          //   viewDisplay: true,
          //   labelWidth: 150,
          //   dicUrl: "/api/service/rabbit-system/dept/canteen/select",
          //   props: {
          //     label: "deptName",
          //     value:"id"
          //   },
          // },
          {
            label: "帐套名称",
            prop: "accountName",
            type: "input",
            addDisplay: true,
            editDisplay: true,
            viewDisplay: true,
            search: false,
            maxlength:30,
            showWordLimit:true,
            rules: [{
              required: true,
              message: "帐套名称",
              trigger: "blur"
            }],
            labelWidth: 150,
          },
          {
            label: "帐套启用年月",
            prop: "enaleTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            labelWidth: 150,
            maxlength:30,
            showWordLimit:true,
            formatter:(row,value,label)=>{
              if(value == ''){
                return ''
              } else{
                return value.substring(0, 4)+'年'+value.substring(5, 7)+'月'
              }
            }
          },
          {
            label: "当前记帐年月",
            prop: "startTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            labelWidth: 150,
            maxlength:30,
            showWordLimit:true,
            formatter:(row,value,label)=>{
              if(value == ''){
                return ''
              } else{
                return value.substring(0, 4)+'年'+value.substring(5, 7)+'月'
              }
            }
          },
          {
            label: "帐套状态",
            prop: "isUse",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            labelWidth: 150,
            maxlength:30,
            dicData: DIC.isUse,
          },
          {
            label: "会计制度",
            prop: "financialRuleId",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
            dicUrl: "/api/service/rabbit-supplier/finance/financialRule/list",
            props: {
              label: "financialRuleName",
              value:"financialRuleId"
            },
          },
          {
            label: "创建人",
            prop: "createUserName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "创建时间",
            prop: "createTime",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
        ]
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.account_sets_add, false),
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
  },
  methods: {
    rowSave(row, loading, done) {
      if(row.isUse===1) {
        this.$message.warning("帐套已启用,不能编辑");
        done();
        return;
      }
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      if(row.isUse===1) {
        this.$message.warning("帐套已启用,不能编辑");
        done();
        return;
      }
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      //已经启用的帐套不能删除
      if(row.isUse===1) {
        this.$message.warning("帐套已启用,不能删除");
        this.loading = false;
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      // if (this.selectionList.length === 0) {
      //   this.$message.warning("请选择至少一条数据");
      //   return;
      // }
      // this.$confirm("确定将选择数据删除?", {
      //   confirmButtonText: "确定",
      //   cancelButtonText: "取消",
      //   type: "warning"
      // })
      //   .then(() => {
      //     return remove(this.ids);
      //   })
      //   .then(() => {
      //     this.onLoad(this.page);
      //     this.$message({
      //       type: "success",
      //       message: "操作成功!"
      //     });
      //     this.$refs.crud.toggleSelection();
      //   });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        if(this.form.isUse===1) {
          this.$message.warning("帐套已启用,不能编辑");
          done();
          return;
        }
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }else if(["add"].includes(type)) {
        this.form.financialRuleId = "8888888888888888888";
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    handleClickPreview: function(url) {
      this.dialogImageUrl = url;
      this.dialogVisible = true;
    },
    openAccount(row){
      this.checkVisible = true;
      trialBalance(row.id).then(res => {
        if(res.data.code=='200') {
          let data = JSON.parse(res.data.data);
          if(data.isBeginAmountBalance=='false'||
            data.isYearAmountBalance=='false'||
            data.isYearBeginBalance=='false') {
            this.$message.warning("财务初始余额试算不平衡，请核对录入的财务初始余额是否正确！");
            return;
          }else {
            this.$confirm("您确定要启用【"+row.accountName+"】么？", {
              confirmButtonText: "我确认启用帐套",
              cancelButtonText: "我再检查一下",
              type: "warning"
            }).then(() => {//启用帐套
              return openAccount(row);
            }).then(() => {
              this.onLoad(this.page);
              this.$message({
                type: "success",
                message: "启用成功!"
              });
            });
          }
        }else{
          this.$notify({
            title: '失败',
            message: '检查失败,无法连接服务器',
            type: 'warning',
            duration: 2000
          });
        }
      });
    },
  }
};
</script>
