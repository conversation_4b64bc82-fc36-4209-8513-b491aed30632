package com.rabbitframework.boot.consume.modules.message.handler;

import com.alibaba.fastjson.JSONObject;
import org.dromara.liancan.api.RemoteConsumDetailsService;
import org.dromara.liancan.api.RemoteMealsOrderBalanceRecordService;
import org.dromara.liancan.api.RemoteSystemPersonnelService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.dromara.liancan.api.RemoteFoodRechargeDetailService;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * SYNCHRON_CARD_STATUS_TAG消息处理器
 * 处理同步接收一卡通充值金额消费状态
 */
@Slf4j
@Component
public class SynchronCardStatusTagHandler implements MessageHandler {
    
    @DubboReference
    private RemoteFoodRechargeDetailService foodRechargeDetailService;
    
    @DubboReference
    private RemoteConsumDetailsService consumDetailsService;
    
    @DubboReference
    private RemoteSystemPersonnelService systemPersonnelService;
    
    @DubboReference
    private RemoteMealsOrderBalanceRecordService mealsOrderBalanceRecordService;
    
    @Override
    public void handle(JSONObject obj) {
        try {
            log.info("处理SYNCHRON_CARD_STATUS_TAG消息: {}", obj.toJSONString());
            
            // 解析消息参数
            String schoolId = obj.getString("school_id");
            String cardUserId = obj.getString("card_user_id");
            String transType = obj.getString("trans_type"); // 交易类型：1-充值，2-消费
            String transAmount = obj.getString("trans_amount"); // 交易金额
            String transTime = obj.getString("trans_time"); // 交易时间
            String transStatus = obj.getString("trans_status"); // 交易状态：1-成功，0-失败
            String balance = obj.getString("balance"); // 余额
            String serialNo = obj.getString("serial_no"); // 流水号
            
            log.info("监听同步接收一卡通充值金额消费状态: schoolId={}, cardUserId={}, transType={}, amount={}, status={}", 
                    schoolId, cardUserId, transType, transAmount, transStatus);
            
            // 验证必要参数
            if (!StringUtils.hasText(schoolId)) {
                log.warn("学校ID为空，跳过处理");
                return;
            }
            
            if (!StringUtils.hasText(cardUserId)) {
                log.warn("一卡通用户ID为空，跳过处理");
                return;
            }
            
            if (!StringUtils.hasText(transType)) {
                log.warn("交易类型为空，跳过处理");
                return;
            }
            
            // 根据交易类型处理不同的业务逻辑
            if ("1".equals(transType)) {
                // 处理充值状态同步
                handleRechargeStatus(schoolId, cardUserId, transAmount, transTime, transStatus, balance, serialNo);
            } else if ("2".equals(transType)) {
                // 处理消费状态同步
                handleConsumeStatus(schoolId, cardUserId, transAmount, transTime, transStatus, balance, serialNo);
            } else {
                log.warn("未知的交易类型: {}", transType);
            }
            
            // 无论成功失败都不抛异常，确保消息被确认
            
        } catch (Exception e) {
            log.error("处理SYNCHRON_CARD_STATUS_TAG消息失败: {}", e.getMessage(), e);
            // 这里不抛异常，确保消息被确认，因为原始代码无论成功失败都返回CommitMessage
        }
    }
    
    /**
     * 处理充值状态同步
     */
    private void handleRechargeStatus(String schoolId, String cardUserId, String transAmount, 
                                    String transTime, String transStatus, String balance, String serialNo) {
        try {
            log.info("处理充值状态同步: cardUserId={}, amount={}, status={}", cardUserId, transAmount, transStatus);
            
            // 这里可以根据实际业务需求实现充值状态同步逻辑
            // 例如：更新充值记录状态、同步用户余额等
            
            // 示例：查询用户信息并更新余额
            if ("1".equals(transStatus) && StringUtils.hasText(balance)) {
                // 充值成功，更新用户余额
                // String personnelListJson = systemPersonnelService.getPersonnelList(Long.parseLong(cardUserId), "1", Long.parseLong(schoolId));
                // 实际的业务逻辑需要根据具体需求实现
            }
            
            log.info("充值状态同步处理完成: cardUserId={}", cardUserId);
            
        } catch (Exception e) {
            log.error("处理充值状态同步失败: cardUserId={}, error={}", cardUserId, e.getMessage(), e);
        }
    }
    
    /**
     * 处理消费状态同步
     */
    private void handleConsumeStatus(String schoolId, String cardUserId, String transAmount, 
                                   String transTime, String transStatus, String balance, String serialNo) {
        try {
            log.info("处理消费状态同步: cardUserId={}, amount={}, status={}", cardUserId, transAmount, transStatus);
            
            // 这里可以根据实际业务需求实现消费状态同步逻辑
            // 例如：更新消费记录状态、同步用户余额、记录交易流水等
            
            // 示例：记录消费明细
            if ("1".equals(transStatus)) {
                // 消费成功，记录消费明细
                // 实际的业务逻辑需要根据具体需求实现
            }
            
            log.info("消费状态同步处理完成: cardUserId={}", cardUserId);
            
        } catch (Exception e) {
            log.error("处理消费状态同步失败: cardUserId={}, error={}", cardUserId, e.getMessage(), e);
        }
    }
    
    @Override
    public String getSupportedTag() {
        return "SYNCHRON_CARD_STATUS_TAG";
    }
}