<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               :permission="permissionList"
               :before-open="beforeOpen"
               v-model="form"
               ref="crud"
               @row-update="rowUpdate"
               @row-save="rowSave"
               @row-del="rowDel"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
      <template slot="menuLeft">
        <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportArchiveRefund">导出</el-button>
      </template>
      <template slot-scope="{row}" slot="menu">
        <el-button type="text"
                   icon="el-icon-setting"
                   size="small"
                   plain
                   style="border: 0;background-color: transparent !important;"
                   v-if="row.ifVoucher == 0"
                   @click.stop="createVoucher(row)">生成凭证
        </el-button>
      </template>
    </avue-crud>
  </basic-container>
</template>

<script>
import {getDetail, add, update, remove,exportArchiveRefund} from "@/api/liancan/archive/refundDetails/archiveRefundDetails";
import {createArchiveVoucher} from "@/api/supplier/finance/financialVoucher";
import {mapGetters} from "vuex";
import {getArchiveRefundDetailsList} from "@/api/supplier/finance/financialBill";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      searchForm:{},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        addBtn: false,
        selection: false,
        menu:true,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "姓名",
            prop: "userName",
            type: "input",
            search:true,
            sortable:true,
          },
          {
            label: "学号/工号",
            prop: "studentJobNo",
            type: "input",
            search:true,
            sortable:true,
          },
          {
            label: "编码",
            prop: "financialCode",
            type: "input",
            sortable:true,
          },
          {
            label: "原部门",
            prop: "deptName",
            type: "input",
            sortable:true,
          },
          {
            label: "部门",
            prop: "deptId",
            type: "tree",
            dicUrl: "/api/service/rabbit-setting/dept/systemDeptSetting/tree/scope",
            props: {
              label: "title",
              value: "id"
            },
            editDisplay: false,
            viewDisplay: false,
            multiple:true,
            /*slot:true,*/
            search:true,
            hide:true,
            rules: [{
              required: true,
              message: "请输入部门",
              trigger: "click"
            }]
          },
          {
            label: "食堂",
            prop: "canteenId",
            type: "tree",
            search: true,
            hide:true,
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
          },
          {
            label: "所属食堂",
            prop: "canteenName",
            type: "input",
            overHidden: true,
            sortable:true,
          },
          {
            label: "钱包",
            prop: "balanceType",
            type: 'select',
            search:true,
            sortable:true,
            dicData: [{
              value: "1",
              label: "统缴餐钱包"
            },
              {
                value: "2",
                label: "自选餐钱包"
              }
            ],
          },
          {
            label: "存档退款金额",
            prop: "money",
            type: "input",
            sortable:true,
          },
          {
            label: "操作人",
            prop: "createUser",
            type: "select",
            dicUrl: '/api/service/rabbit-user/user-list',
            props: {
              label: "realName",
              value: "id"
            },
            sortable:true,
          },
          {
            label: "操作时间",
            prop: "createTime",
            type: "datetime",
            format: "yyyy-MM-dd HH:mm:ss",
            valueFormat: "yyyy-MM-dd HH:mm:ss",
            sortable:true,
          },
          {
            label: "备注",
            prop: "remark",
            type: "input",
            overHidden: true,
          },
          {
            label: "开始时间",
            prop: "startDate",
            type: "date",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            hide: true,
            search: true,
            display: false,
            showColumn: false,
          },
          {
            label: "结束时间",
            prop: "endDate",
            format: "yyyy-MM-dd",
            valueFormat: "yyyy-MM-dd",
            type: "date",
            hide: true,
            search: true,
            display: false,
            showColumn: false,
          },
        ]
      },
      data: []
    };
  },
  computed: {
    ...mapGetters(["permission","userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.archive_refund_details_add, false),
        viewBtn: this.vaildData(this.permission.archive_refund_details_view, false),
        delBtn: this.vaildData(this.permission.archive_refund_details_delete, false),
        editBtn: this.vaildData(this.permission.archive_refund_details_edit, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created() {
    this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
  },
  methods: {
    rowSave(row, loading, done) {
      add(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowUpdate(row, index, loading, done) {
      update(row).then(() => {
        loading();
        this.onLoad(this.page);
        this.$message({
          type: "success",
          message: "操作成功!"
        });
      }, error => {
        done();
        window.console.log(error);
      });
    },
    rowDel(row) {
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(row.id);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        });
    },
    handleDelete() {
      if (this.selectionList.length === 0) {
        this.$message.warning("请选择至少一条数据");
        return;
      }
      this.$confirm("确定将选择数据删除?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          return remove(this.ids);
        })
        .then(() => {
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
          this.$refs.crud.toggleSelection();
        });
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      if (params.startDate != '' && params.startDate != null && params.startDate != undefined){
        if (params.endDate == '' || params.endDate == null || params.endDate == undefined){
          done();
          return this.$message.error('结束时间不能为空');
        }
      }
      if (params.endDate != '' && params.endDate != null && params.endDate != undefined){
        if (params.startDate == '' || params.startDate == null || params.startDate == undefined){
          done();
          return this.$message.error('开始时间不能为空');
        }else {
          var startDateTime = new Date(params.startDate);
          var endDateTime = new Date(params.endDate);
          if (startDateTime.getTime() > endDateTime.getTime()){
            done();
            return this.$message.error('开始时间不能大于结束时间');
          }
        }
      }
      this.searchForm = params;
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      if(this.schoolId != null && this.schoolId != ''){
        params.unitId = this.schoolId;
      }
      getArchiveRefundDetailsList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getArchiveRefundDetailsList ======= ', res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      });
    },
    exportArchiveRefund(){
      const loading = this.$loading({
        lock: true,
        text: '正在导出以下数据，请稍后',
        spinner: 'el-icon-loading',
        background: 'rgba(0, 0, 0, 0.7)'
      });
      exportArchiveRefund(this.searchForm).then(res => {
        loading.close();
        const blob = new Blob([res.data]);
        const fileName = '存档退款明细报表.xls';
        const linkNode = document.createElement('a');

        linkNode.download = fileName; //a标签的download属性规定下载文件的名称
        linkNode.style.display = 'none';
        linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
        document.body.appendChild(linkNode);
        linkNode.click(); //模拟在按钮上的一次鼠标单击

        URL.revokeObjectURL(linkNode.href); // 释放URL 对象
        document.body.removeChild(linkNode);
      })
    },
    createVoucher(row) {
      this.$confirm("确定生成凭证?", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        this.loading = true;
        let param = {
          billCode: row.financialCode,
          billNo: row.id,
          canteenId: row.canteenId
        }
        return createArchiveVoucher(param);
      }).then(
        () => {
          this.loading = false;
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        },
        error => {
          this.loading = false;
          this.$message.error(error);
        }
      );
    }
  }
};
</script>

<style>
</style>
