<template>
  <basic-container>
    <avue-crud :option="option"
               :table-loading="loading"
               :data="data"
               :page="page"
               v-model="form"
               ref="crud"
               @search-change="searchChange"
               @search-reset="searchReset"
               @selection-change="selectionChange"
               @current-change="currentChange"
               @size-change="sizeChange"
               @on-load="onLoad">
    </avue-crud>
  </basic-container>
</template>

<script>
import {getCanteenAgreementList} from "@/api/liancan/statistical";
import {mapGetters} from "vuex";
export default {
  props: {
    schoolId: String,
  },
  data() {
    return {
      form: {},
      query: {},
      loading: true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: true,
        addBtn: false,
        editBtn: false,
        delBtn: false,
        selection: false,
        align: "center",
        column: [
          {
            label: "商品名称",
            prop: "goodsName",
            type: "input",
            rules: [{
              required: true,
              message: "请输入商品名称",
              trigger: "blur"
            }],
            search: true,
          },
          {
            label: "商品类型",
            prop: "goodsType",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品类型",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-backendBidding/biddingPurchaseType/dict?filter=1",
            props: {
              label: "name",
              value: "id"
            },
            filterable: true,
            searchFilterable: true,
            cascaderItem: ['bidding'],
            search: true,
          },
          {
            label: "食材大类",
            prop: "bidding",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品大类",
              trigger: "blur"
            }],
            dicUrl: "/api/service/rabbit-liancan/biddingType/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            filterable: true,
            searchFilterable: true,
            cascaderItem: ['biddingTypeId'],
            search: true,
            dicFlag: false,
            overHidden: true,
          },
          {
            label: "食材小类",
            prop: "biddingTypeId",
            type: "select",
            rules: [{
              required: true,
              message: "请选择商品小类",
              trigger: "blur"
            }],
            dicFlag: false,
            dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dict/{{key}}",
            props: {
              label: "name",
              value: "id"
            },
            search: true,
          },
          {
            label: "计量单位",
            prop: "unit",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dict/dictionary?code=unit",
            props: {
              label: "dictValue",
              value: "dictKey"
            },
            rules: [{
              required: true,
              message: "请选择计量单位",
              trigger: "blur"
            }],
          },
          {
            label: "供应商名称",
            prop: "supplierName",
            type: "input",
            overHidden: true,
          },
          {
            label: '合同截止日期',
            prop: 'indate',
            type:'datetime',
            searchSpan: 6,
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
          },
          {
            label: "合同状态",
            prop: "agreementStatus",
            type: "select",
            width: 100,
            dicData: [
              {
                label: "正常",
                value: 0
              },
              {
                label: "过期",
                value: 1
              }
            ],
            search: true
          },
          {
            label: "合同文件",
            prop: "agreement",
            type: 'upload',
            listType: 'picture-img',
            propsHttp: {
              res: 'data',
              url: 'link'
            },
            tip: '只能上传jpg/png文件，大小不超过 500KB',
            // action: '/api/service/rabbit-resource/oss/endpoint/put-file',
            // rules: [{
            //   required: false,
            //   message: '请上传图片',
            //   trigger: 'click'
            // }],
            slot: true,
            span: 24,
            hide: true,
          },
        ]
      },
      data: []
    }
  },
  created(){
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
  },
  methods: {
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    onLoad(page, params = {}) {
      this.loading = true;
      this.query.deptId = this.schoolId;
      getCanteenAgreementList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        console.log('getCanteenAgreementList ====== ', res)
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      })
    }
  }
}
</script>

<style scoped>

</style>
