<template>
  <basic-container>
    <div class="table-form">
      <div style="margin-left: 15px;">
        <el-form ref="form" :model="form" label-width="80px" >
          <el-form-item label="当前帐套:">
            <div style="display: flex;flex-direction: row;">
                <div>
                  <el-select v-model="accountSetsValue" placeholder="请选择帐套" style="width:400px;">
                    <el-option
                      v-for="item in accountSetsList"
                      :key="item.id"
                      :label="item.accountName"
                      :value="item.id">
                    </el-option>
                  </el-select>
                </div>
                <div style="margin-left: 100px;">
                  <el-input v-model="this.peroidStr" placeholder="请选择日期" readonly="readonly" style="width: 250px;"></el-input>
                  <el-button type="primary" icon="el-icon-caret-bottom" @click="selectPeroidHandle">选择</el-button>
                  <el-button type="primary" icon="el-icon-refresh" @click="refreshData">刷新</el-button>
                </div>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <avue-crud :option="option"
                :table-loading="loading"
                :data="data"
                :page="page"
                :permission="permissionList"
                :before-open="beforeOpen"
                :cell-style="cellStyle"
                v-model="form"
                ref="crud"
                @search-change="searchChange"
                @search-reset="searchReset"
                @selection-change="selectionChange"
                @current-change="currentChange"
                @size-change="sizeChange"
                @on-load="onLoad"
                @cell-click="cellClick">
        <template slot="menuLeft" slot-scope="scope">
          <el-button class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportTable">打印</el-button>
          <el-button type="success" size="small" @click="handleExport">导出</el-button>
          <el-button v-if="open" type="primary"  size="small" @click="Toexpandall">全部展开</el-button>
          <el-button v-if="close" type="default" size="small" @click="ToClose">全部收起</el-button>
        </template>
      </avue-crud>
    </div>
    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog title="选择会计期间"
               append-to-body
               :visible.sync="selectPeroidShow"
               width="600px">
      <el-form ref="form" label-width="80px">
        <el-form-item label="查询区间">
          <el-col :span="11">
            <el-select v-model="startPeroid" ref="startPeroidName" placeholder="请选择开始期间">
              <el-option
                v-for="item in peroidList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col class="line" :span="2">-</el-col>
          <el-col :span="11">
            <el-select v-model="endPeroid" ref="endPeroidName" placeholder="请选择结束期间">
              <el-option
                v-for="item in peroidList"
                :key="item.value"
                :label="item.label"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
        </el-form-item>
      </el-form>
      <span slot="footer"
            class="dialog-footer">
        <el-button @click="selectPeroidShow = false">取 消</el-button>
        <el-button type="primary"
                   @click="afterSelectPeroidHandle">确 定</el-button>
      </span>
    </el-dialog>
  </basic-container>
</template>

<script>
import {getDetail, passOrNo, getSubjectBalanceSheet, processExportForSubjectBalanceSheet} from "@/api/supplier/finance/financialVoucherList";
import {mapGetters} from "vuex";
import {getAccountSetsList,getPeroidList} from "@/api/supplier/finance/voucher";

export default {
  data() {
    return {
      selectVoucherPeroid: '',
      peroidStr: '',
      startPeroid: '',
      endPeroid: '',
      peroidList: [],
      selectPeroidShow: false,
      accountSetsValue: '',
      accountSetsList:[],//当前食堂所有的帐套
      form: {},
      query: {},
      loading: false,
      page: {
        pageSize: 100000,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detail: {},
      spanArr:[],
      key:'businessDate',
      open: true,
      close:false,
      option: {
        menu: false,
        // height:'auto',
        // calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        // tip: false,
        border: true,
        // index: true,
        // viewBtn: false,
        // editBtn: false,
        addBtn:false,
        // refreshBtn: false,
        // searchBtn: false,
        // selection: false,
        // columnBtn: false,
        // addRowBtn: false,
        // printBtn:true,
        // excelBtn:true,
        align: 'center',
        // showSummary: true,
        rowKey:'id',
        rowParentKey:'parentId',
        //defaultExpandAll:true, //属性是否全部展开
        // sumColumnList: [
        //     {
        //       name: 'accountBalanceSheetDebitAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetCreditAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetMonthDebitAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetMonthCreditAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetYearDebitAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetYearCreditAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetEndDebitAmount',
        //       type: 'sum'
        //     },
        //     {
        //       name: 'accountBalanceSheetEndCreditAmount',
        //       type: 'sum'
        //     }
        //   ],
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
            labelWidth: 150,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
            labelWidth: 150,
          },
          {
              label: "凭证日期",
              prop: "businessDateRange",
              type: "daterange",
              format:'yyyy-MM-dd',
              valueFormat:'yyyy-MM-dd',
              startPlaceholder: '开始日期',
              endPlaceholder: '结束日期',
              search: true,
              searchRange: true,
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              searchSpan: 5,
              hide: true,
          },
          {
            label: "科目编码",
            prop: "subjectCode",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            maxlength:30,
            labelWidth: 150,
          },
          {
            label: "科目名称",
            prop: "subjectName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            search: false,
            maxlength:30,
            labelWidth: 150,
          },
          {
            label: "期初金额",
            children: [{
                label: '借方',
                prop: 'accountBalanceSheetDebitAmountStr',
              }, {
                label: '贷方',
                prop: 'accountBalanceSheetCreditAmountStr',
              }],
          },
          {
            label: "本期发生额",
            children: [{
                label: '借方',
                prop: 'accountBalanceSheetMonthDebitAmountStr'
              }, {
                label: '贷方',
                prop: 'accountBalanceSheetMonthCreditAmountStr',
              }]
          },
          {
            label: "本年累计发生额",
            children: [{
                label: '借方',
                prop: 'accountBalanceSheetYearDebitAmountStr'
              }, {
                label: '贷方',
                prop: 'accountBalanceSheetYearCreditAmountStr',
              }]
          },
          {
            label: "期末余额",
            children: [{
                label: '借方',
                prop: 'accountBalanceSheetEndDebitAmountStr'
              }, {
                label: '贷方',
                prop: 'accountBalanceSheetEndCreditAmountStr',
              }]
          }
        ]
      },
      data: [],
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        // addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
    }
  },
  methods: {
    refreshData() {
      // this.page.currentPage = 1
      // this.onLoad(this.page, this.query);
      if(this.peroidStr === '' || this.startPeroid === ''){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      this.getLedgerList()
    },
    selectPeroidHandle() {
      this.selectPeroidShow = true;
    },
    afterSelectPeroidHandle() {
      this.peroidStr = "";
      const s = this.$refs.startPeroidName.selectedLabel;
      const e = this.$refs.endPeroidName.selectedLabel;

      if(s != '' && e != '') {
        if(this.startPeroid > this.endPeroid) {
          this.$message.warning("开始期间不能大于结束期间");
          return;
        }
        this.peroidStr = s + '至' + e;
        this.selectPeroidShow = false;
      } else {
        if(s == '') {
          this.$message.warning("请选择查询的开始期间");
        }
        if(e == '') {
          this.$message.warning("请选择查询的结束期间");
        }
      }
    },
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    searchReset() {
      // this.query = {};
      this.startPeroid = '';
      this.endPeroid = '';
      this.peroidStr = '';
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      // this.query = params;
      // this.page.currentPage = 1
      // this.onLoad(this.page, params);
      if(this.peroidStr === '' || this.startPeroid === ''){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      this.getLedgerList()
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    selectionClear() {
      this.selectionList = [];
      this.$refs.crud.toggleSelection();
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    //
    onLoad(page, params = {}) {
      // this.loading = true;
      this.getgetAccountSets()
      getPeroidList()
        .then(res => {
          var list = res.data.data.records;
          if(list != null && list.length >0) {
            for (var i=0;i<list.length;i++) {
              const item = list[i];
              const p = item.voucherPeriod + '';
              const str = p.substring(0,4) + '年第' + p.substring(4,6) + '期';
              const it = {label:str,value:p};
              this.peroidList.push(it);
            }
          }
      });
    },
    async getgetAccountSets(){
      await getAccountSetsList()
        .then(res => {
          this.accountSetsList = res.data.data.records;
          if(this.accountSetsList!=null && this.accountSetsList.length == 1) {
            this.accountSetsValue = this.accountSetsList[0].id;
          }
      });
    },
    getLedgerList(){
      this.loading = true;
      let params = {}
      params.startPeroid = this.startPeroid;
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.accountSetsId = this.accountSetsValue;
      getSubjectBalanceSheet(this.page.currentPage, this.page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;
        this.selectionClear();
      }).catch(() => {
        this.loading = false;
      });
    },
    cellClick(row, column, cell, event) {
      if(column.label === "科目编码" && row.subjectCode!="合 计"){
        // console.log(row.id)
        this.$router.push({
          path: "/suplierSubsidiaryLedger",
          query: {
            subjectId:row.id,
            startPeroid: this.startPeroid,
            endPeroid: this.endPeroid,
            peroidStr: this.peroidStr,
            accountSetsId: this.accountSetsValue
          }
        });
      }
    },
    cellStyle({row,column,rowIndex,columnIndex}){
      if(columnIndex==0 && row.subjectCode!="合 计"){
        return {
          color:'blue',
          fontWeight:'bold',
          fontSize:'20',
          textDecoration: 'underline',
          cursor: 'pointer'
        }
      }
    },
    // 全部展开
    Toexpandall() {
      let els = document.getElementsByClassName('el-table__expand-icon')
      if(els.length != 0){
        this.open = false
        this.close = true
        for(let j1=0;j1<els.length;j1++){
          els[j1].classList.add("dafult")
        }
        if(this.$el.getElementsByClassName('el-table__expand-icon--expanded')){
          const open = this.$el.getElementsByClassName('el-table__expand-icon--expanded')
          for(let j=0;j<open.length;j++){
            open[j].classList.remove("dafult")
            // open[j].classList.remove("el-table__expand-icon--expanded")
            // open[j].click(function(event) {
            //   event.preventDefault();
            // })
          }
          const dafult = this.$el.getElementsByClassName('dafult')
          for(let a=0;a<dafult.length;a++){
            dafult[a].click()
          }
        }
      }
    },
    // 全部收起
    ToClose() {
        this.open = true
        this.close = false
        const elsopen = this.$el.getElementsByClassName('el-table__expand-icon--expanded')
        if(this.$el.getElementsByClassName('el-table__expand-icon--expanded')){
          for(let i=0;i<elsopen.length;i++){
            elsopen[i].click()
          }
        }
    },
    exportTable(){
      let that = this;
      //更改账套值时查询数据
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      this.loading = true;

      var xhr = new XMLHttpRequest(); // 用这种原生请求下载后端返回的二进制流打开就不会出现空白
      xhr.open(
        "get",
        "/api/service/rabbit-supplier/finance/financialVoucher/downloadForSubjectBalanceSheet?startPeroid="+this.startPeroid+"&endPeroid="+this.endPeroid+"&accountSetsId="+this.accountSetsValue,
        true
      );
      xhr.responseType = "blob";
      xhr.onload = function() {
        that.loading = false;
        const url = window.URL.createObjectURL(this.response);
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = url;
        link.setAttribute("download", "科目余额表.pdf");
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      };
      xhr.send();
    },
    handleExport( params = {}){
      if(this.startPeroid == '' || this.endPeroid == ''|| this.peroidStr == '' || this.startPeroid == undefined || this.endPeroid == undefined || this.peroidStr == undefined){
        this.$message.warning('请先选择日期');
        this.loading = false;
        return;
      }
      params.startPeroid = this.startPeroid;
      params.endPeroid = this.endPeroid;
      params.selectVoucherPeroid = this.selectVoucherPeroid;
      params.accountSetsId = this.accountSetsValue;
      processExportForSubjectBalanceSheet(Object.assign(params, this.query)).then(res => {
        if (!res.data) {
          return;
        }
        const blob = new Blob([res.data], {
          type: "application/vnd.ms-excel",
        }); // 构造一个blob对象来处理数据，并设置文件类型
        const href = URL.createObjectURL(blob); //创建新的URL表示指定的blob对象
        const a = document.createElement("a"); //创建a标签
        a.style.display = "none";
        a.href = href; // 指定下载链接
        let fileName = res.headers["content-disposition"];
        fileName = fileName.split("=")[1];
        a.download = decodeURIComponent(fileName); //指定下载文件名
        a.click(); //触发下载
        URL.revokeObjectURL(a.href); //释放URL对象
      });
    },
  }
};
</script>

<style  lang="less" scoped>
  /deep/ .table-form .el-form-item__label{
      display: none;
  }
  /deep/ .table-form .el-form-item__content{
    margin-left: 0 !important;
  }
</style>
