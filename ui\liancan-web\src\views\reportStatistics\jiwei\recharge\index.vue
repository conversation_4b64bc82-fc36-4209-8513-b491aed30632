<template>
  <div>
    <div class="all-mess">
      <div class="mess-header">
        <div
          :class="{acitve:activeIdx==index}"
          v-for="(item,index) in messList"
          :key="index"
          @click="menuClick(index)"
        >
          {{item}}
        </div>
      </div>
      <div class="mess-content" v-if="activeIdx == 0">
        <avue-crud :option="monthOption"
                   :data="monthData"
                   :page="monthPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="monthForm"
                   ref="monthForm"
                   @search-change="searchChangeMonth"
                   @search-reset="searchResetMonth"
                   @selection-change="selectionChangeMonth"
                   @current-change="currentChangeMonth"
                   @size-change="sizeChangeMonth"
                   @on-load="onLoadMonth">
          <template slot="menuLeft">
            <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportRechargeData">导出</el-button>
          </template>
          <el-date-picker
            v-model="month"
            type="month"
            placeholder="选择月">
          </el-date-picker>
        </avue-crud>
      </div>

      <div class="mess-content" v-if="activeIdx == 1">
        <avue-crud :option="yearOption"
                   :data="yearData"
                   :page="yearPage"
                   :permission="permissionList"
                   :before-open="beforeOpen"
                   v-model="yearForm"
                   ref="yearForm"
                   @search-change="searchChangeYear"
                   @search-reset="searchResetYear"
                   @selection-change="selectionChangeYear"
                   @current-change="currentChangeYear"
                   @size-change="sizeChangeYear"
                   @on-load="onLoadYear">
          <template slot="menuLeft">
            <el-button v-if="!this.unitSelect" class="filter-item" size="small" type="warning" icon="el-icon-upload" @click="exportYearRechargeData">导出</el-button>
          </template>
        </avue-crud>
      </div>
    </div>
  </div>
</template>

<script>
  import {getMonthRechargeList,getYearRechargeList,exportRechargeData,exportYearRechargeData} from "@/api//queryStatistics/foodWalletRecharge";
  import {mapGetters} from "vuex";
  export default {
    data() {
      return {
        activeIdx: 0,
        messList: [ '按月份统计', '按年份统计' ],
        form: {},
        query: {},
        monthForm:{},
        yearForm:{},
        loading: true,
        monthLoading:true,
        page: {
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        monthPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        yearPage:{
          pageSize: 10,
          currentPage: 1,
          total: 0
        },
        searchForm:{},
        searchForm1:{},
        searchForm2:{},
        selectionList: [],
        option: {
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          /*  searchShow: true,*/
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'stuRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'parRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'teaRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'facultyRechargeBalance1',
              type: 'sum'
            },
            {
              name: 'foodInitialBalance1',
              type: 'sum'
            },
            {
              name:'totalBalance1',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '统计日期',
              prop: 'queryDate',
              type:'datetime',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM-dd',
              valueFormat: 'yyyy-MM-dd',
              hide:true,
            },
            {
              label: "日期",
              prop: "createTime",
              type: "datetime",
              format: "yyyy-MM-dd",
              valueFormat: "yyyy-MM-dd",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
            {
              label: "学生在线充值",
              prop: "stuRechargeBalance1",
            },
            {
              label: "家长代学生充值",
              prop: "parRechargeBalance1",
            },
            {
              label: "老师代学生充值",
              prop: "teaRechargeBalance1",
            },
            {
              label: "现金充值",
              prop: "cashRechargeBalance1",
            },
            {
              label: "教职工充值",
              prop: "facultyRechargeBalance1",
            },
            {
              label: "伙食费钱包初始余额",
              prop: "foodInitialBalance1",
            },
            {
              label: "合计",
              prop: "totalBalance1",
            }
          ]
        },
        monthOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'onlineRechargeBalance',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name:'totalBalance1',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '月份',
              prop: 'monthDate',
              type:'month',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM',
              valueFormat: 'yyyy-MM',
              hide:true,
            },
            {
              label: "月份",
              prop: "createTime",
              type: "datetime",
              format: "yyyy-MM",
              valueFormat: "yyyy-MM",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
         /*   {
              label: "单位名称",
              prop: "deptName",
              type: "input",
              search:true,
            },*/
            {
              label: "单位",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              fixed:true,
              align: 'center',
              width: 120,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "单位类型",
              prop: "agencyType",
              type: "select",
              search:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入单位类型",
                trigger: "blur"
              }],
            },
            {
              label: "食堂名称",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value:"id"
              },
              width: 120,
              hide: false,
              search: true,
              fixed:true,
            },
            {
              label: "在线充值",
              prop: "onlineRechargeBalance",
            },
            {
              label: "现金充值",
              prop: "cashRechargeBalance1",
            },
            {
              label: "充值合计",
              prop: "totalBalance1",
            }
          ]
        },
        yearOption:{
          /*          height:'auto',*/
          /*    calcHeight: 30,*/
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: true,
          viewBtn: true,
          addBtn:false,
          menu:false,
          selection: false,
          showSummary: true,
          sumColumnList: [
            {
              name: 'onlineRechargeBalance',
              type: 'sum'
            },
            {
              name: 'cashRechargeBalance1',
              type: 'sum'
            },
            {
              name:'totalBalance1',
              type: 'sum'
            }
          ],
          column: [
            {
              label: "",
              prop: "id",
              type: "input",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
              hide: true,
            },
            {
              label: '年份',
              prop: 'yearDate',
              type:'year',
              searchSpan:6,
              searchRange:true,
              search:true,
              format: 'yyyy-MM',
              valueFormat: 'yyyy-MM',
              hide:true,
            },
            {
              label: "年份",
              prop: "createTime",
              type: "datetime",
              format: "yyyy",
              valueFormat: "yyyy",
              addDisplay: false,
              editDisplay: false,
              viewDisplay: false,
            },
  /*          {
              label: "单位名称",
              prop: "deptName",
              type: "input",
              search:true,
            },*/
            {
              label: "单位",
              prop: "deptId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/select",
              props: {
                label: "deptName",
                value:"id"
              },
              hide: false,
              search: true,
              fixed:true,
              align: 'center',
              width: 120,
              rules: [{
                required: true,
                message: "请选择部门",
                trigger: "click"
              }]
            },
            {
              label: "单位类型",
              prop: "agencyType",
              type: "select",
              search:true,
              dicUrl: "/api/service/rabbit-system/dict/dictionary?code=agency_type",
              props: {
                label: "dictValue",
                value: "dictKey"
              },
              rules: [{
                required: true,
                message: "请输入单位类型",
                trigger: "blur"
              }],
            },
            {
              label: "食堂名称",
              prop: "canteenId",
              type: "select",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value:"id"
              },
              width: 120,
              hide: false,
              search: true,
              fixed:true,
            },
            {
              label: "在线充值",
              prop: "onlineRechargeBalance",
            },
            {
              label: "现金充值",
              prop: "cashRechargeBalance1",
            },
            {
              label: "充值合计",
              prop: "totalBalance1",
            }
          ]
        },
        data: [],
        monthData:[],
        yearData:[],
      }
    },
    computed: {
      ...mapGetters(["permission","userInfo"]),
      permissionList() {
        return {
          addBtn: this.vaildData(this.permission.food_wallet_recharge_add, false),
          viewBtn: this.vaildData(this.permission.food_wallet_recharge_view, false),
          delBtn: this.vaildData(this.permission.food_wallet_recharge_delete, false),
          editBtn: this.vaildData(this.permission.food_wallet_recharge_edit, false)
        };
      },
      ids() {
        let ids = [];
        this.selectionList.forEach(ele => {
          ids.push(ele.id);
        });
        return ids.join(",");
      }
    },
    created() {
      const { messName } = this.$route.query
      if(messName){
        this.messList.forEach((item, idx) => {
          item == messName ? this.activeIdx = idx : ''
        })
        console.log( this.activeIdx )
      }
      // 单位列表是否显示
      this.unitSelect = (this.userInfo.userType === 'education' || this.userInfo.userType === 'jiWei')? true: false;
    },
    methods: {
      menuClick(idx) {
        if(this.activeIdx == idx) return
        this.activeIdx = idx
        if (idx == 0){
          this.monthPage.currentPage = 1;
          this.onLoadMonth(this.monthPage);
        }
        if (idx == 1){
          this.yearPage.currentPage = 1;
          this.onLoadYear(this.yearPage);
        }
      },
      rowSave(row, loading, done) {
        add(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowUpdate(row, index, loading, done) {
        update(row).then(() => {
          loading();
          this.onLoad(this.page);
          this.$message({
            type: "success",
            message: "操作成功!"
          });
        }, error => {
          done();
          window.console.log(error);
        });
      },
      rowDel(row) {
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(row.id);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
          });
      },
      handleDelete() {
        if (this.selectionList.length === 0) {
          this.$message.warning("请选择至少一条数据");
          return;
        }
        this.$confirm("确定将选择数据删除?", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        })
          .then(() => {
            return remove(this.ids);
          })
          .then(() => {
            this.onLoad(this.page);
            this.$message({
              type: "success",
              message: "操作成功!"
            });
            this.$refs.crud.toggleSelection();
          });
      },
      beforeOpen(done, type) {
        if (["edit", "view"].includes(type)) {
          getDetail(this.form.id).then(res => {
            this.form = res.data.data;
          });
        }
        done();
      },
      searchReset() {
        this.query = {};
        this.searchForm = {};
        this.onLoad(this.page);
      },
      searchResetMonth() {
        this.query = {};
        this.searchForm1 = {};
        this.onLoadMonth(this.monthPage);
      },
      searchResetYear() {
        this.query = {};
        this.searchForm2 = {};
        this.onLoadYear(this.yearPage);
      },
      searchChange(params, done) {
        this.searchForm = params;
        this.query = params;
        this.page.currentPage = 1
        if (params.queryDate != '' && params.queryDate != null && params.queryDate != undefined) {
          params.beginDate = params.queryDate[0];
          params.endDate = params.queryDate[1];
        }
        this.onLoad(this.page, params);
        done();
      },
      searchChangeMonth(params, done) {
        this.searchForm1 = params;
        this.query = params;
        this.monthPage.currentPage = 1
        this.onLoadMonth(this.monthPage, params);
        done();
      },
      searchChangeYear(params, done) {
        this.searchForm2 = params;
        this.query = params;
        this.yearPage.currentPage = 1
        this.onLoadYear(this.yearPage, params);
        done();
      },
      selectionChange(list) {
        this.selectionList = list;
      },
      selectionChangeMonth(list) {
        this.selectionList = list;
      },
      selectionChangeYear(list) {
        this.selectionList = list;
      },
      selectionClear() {
        this.selectionList = [];
        this.$refs.crud.toggleSelection();
      },
      selectionClearMonth() {
        this.selectionList = [];
        this.$refs.monthForm.toggleSelection();
      },
      selectionClearYear() {
        this.selectionList = [];
        this.$refs.yearForm.toggleSelection();
      },
      currentChange(currentPage){
        this.page.currentPage = currentPage;
      },
      currentChangeMonth(currentPage){
        this.monthPage.currentPage = currentPage;
      },
      currentChangeYear(currentPage){
        this.yearPage.currentPage = currentPage;
      },
      sizeChange(pageSize){
        this.page.pageSize = pageSize;
      },
      sizeChangeMonth(pageSize){
        this.monthPage.pageSize = pageSize;
      },
      sizeChangeYear(pageSize){
        this.yearPage.pageSize = pageSize;
      },
      onLoad(page, params = {}) {
        this.loading = true;
        getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.page.total = data.total;
          this.data = data.records;
          this.loading = false;
          this.selectionClear();
        });
      },
      onLoadMonth(page, params = {}) {
        this.monthLoading = true;
        getMonthRechargeList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.monthPage.total = data.total;
          this.monthData = data.records;
          this.monthLoading = false;
          this.selectionClearMonth();
        });
      },
      onLoadYear(page, params = {}) {
        this.yearLoading = true;
        getYearRechargeList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
          const data = res.data.data;
          this.yearPage.total = data.total;
          this.yearData = data.records;
          this.yearLoading = false;
          this.selectionClearYear();
        });
      },
      exportRechargeDetailData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        exportDateFoodData(this.searchForm).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '伙食费钱包充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportRechargeData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.searchForm1.status = "1";
        exportRechargeData(this.searchForm1).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      },
      exportYearRechargeData(){
        const loading = this.$loading({
          lock: true,
          text: '正在导出数据，请稍后',
          spinner: 'el-icon-loading',
          background: 'rgba(0, 0, 0, 0.7)'
        });
        this.searchForm2.status = "1";
        exportYearRechargeData(this.searchForm2).then(res => {
          loading.close();
          const blob = new Blob([res.data]);
          const fileName = '充值统计报表.xlsx';
          const linkNode = document.createElement('a');

          linkNode.download = fileName; //a标签的download属性规定下载文件的名称
          linkNode.style.display = 'none';
          linkNode.href = URL.createObjectURL(blob); //生成一个Blob URL
          document.body.appendChild(linkNode);
          linkNode.click(); //模拟在按钮上的一次鼠标单击

          URL.revokeObjectURL(linkNode.href); // 释放URL 对象
          document.body.removeChild(linkNode);
        })
      }
    }
  }
</script>

<style lang="scss" scoped>
  .all-mess{
    background: #fff;
    overflow: hidden;
    .mess-header{
      display: flex;
      height: 50px;
      background: #F1F6FF;
      ::v-deep div {
        width: 120px;
        height: 100%;
        font-size: 16px;
        color: #333;
        text-align: center;
        line-height: 50px;
        cursor: pointer;
        &:hover{
          color: #3775da;
        }
        &.acitve{
          color: #3775da;
          background: #fff;
        }
      }
    }
    .mess-content{
      padding: 0 20px;
      box-sizing: border-box;
      .mess-item{
        height: 48px;
        display: flex;
        align-items: center;
        font-size: 16px;
        color: #333;
        margin-top: 4px;
        .mess-state{
          margin-right: 10px;
        }
        .mess-name{
          width: 400px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          margin-right: 20rpx;
        }
        .mess-detail{
          color: #3775da;
          text-decoration:underline;
          margin-left: 50px;
          cursor: pointer;
        }
      }
    }
    .mess-footer{
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 40px 0 40px 0;
      position: relative;
      .mess-but{
        position: absolute;
        left: 40px;
        bottom: -2px;
        font-size: 16px;
        height: 38px;
        line-height: 10px;
      }
    }
  }
</style>
