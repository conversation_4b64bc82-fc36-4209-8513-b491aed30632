<template>
  <basic-container>
    <el-tabs v-model="activeName" class="tabs" @tab-click="handleClick">
      <el-tab-pane label="采购数量查询" name="first">
      <avue-crud :option="option"
                    :table-loading="loading"
                    :data="data"
                    :page="page"
                    :permission="permissionList"
                    :before-open="beforeOpen"
                    v-model="form"
                    ref="form"
                    @search-change="searchChange"
                    @search-reset="searchReset"
                    @selection-change="selectionChange"
                    @current-change="currentChange"
                    @size-change="sizeChange"
                    @on-load="onLoad">
          <template slot-scope="{row,type,size}" slot="menu">
            <el-button icon="el-icon-search" :size="size" :type="type" @click="openDetailDiaglog(row,'1')">查看</el-button>
          </template>
        </avue-crud>
      </el-tab-pane>
      <el-tab-pane label="数量预警提示" name="third">
        <avue-crud :option="warningOption"
                 :table-loading="warningLoading"
                 :data="warningData"
                 :page="warningPage"
                 :permission="permissionList"
                 v-model="warningForm"
                 ref="warningForm"
                 @search-change="searchChangeWarning"
                 @search-reset="searchResetWarning"
                 @selection-change="selectionChangeWarning"
                 @current-change="currentChangeWarning"
                 @size-change="sizeChangeWarning"
                 @on-load="onLoadWarning">
          <template slot="menu" slot-scope="{row}">
            <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="openWarning(row,'1')">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '1'" size="mini" type="text" @click="handOpenWarningPush(row)">去函询
            </el-button>
            <el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="openWarning(row,'2')">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="checkReply(row)">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="management(row)">处置
            </el-button>
            <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="checkNanagement(row)">查看
            </el-button>
            <el-button v-if="row.inquiryStatus == '4'" size="mini" type="text" @click="fileOpen(row)">归档
            </el-button>
            <el-button v-if="row.inquiryStatus == '5'" size="mini" type="text" @click="checkFile(row)">查看
            </el-button>
            <!--<el-button v-if="row.inquiryStatus == '2'" size="mini" type="text" @click="handOpenInquiry(row)">查看函询
            </el-button>
            <el-button v-if="row.inquiryStatus == '3'" size="mini" type="text" @click="handOpenInquiry(row)">查看函询
            </el-button>-->
          </template>
        </avue-crud>
      </el-tab-pane>
    </el-tabs>

    <el-dialog :visible.sync="dialogVisible" @close="dialogVisible=false" width="30%" height="40%" :append-to-body="true">
      <img width="100%" height="100%" :src="dialogImageUrl" alt="">
    </el-dialog>

    <el-dialog title="食材列表" :visible.sync="detailVisible" width="80%" left :append-to-body="true" @close="closeFoodVisible">
      <avue-crud :option="option1"
                  :table-loading="loading1"
                  :data="data1"
                  :page="page1"
                  :permission="permissionList"
                  :before-open="beforeOpen1"
                  v-model="form1"
                  ref="form1"
                  @search-change="searchChange1"
                  @search-reset="searchReset1"
                  @current-change="currentChange1"
                  @size-change="sizeChange1"
                  @on-load="onLoad1">
        <template slot-scope="{row,type,size}" slot="menu">
          <el-button icon="el-icon-search" :size="size" :type="type" @click="openOrderDiaglog(row,'1')">查看</el-button>
        </template>
      </avue-crud>
    </el-dialog>

    <el-dialog title="查看" :visible.sync="warningDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div>
        <el-col :span="24">
          <div sstyle="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">预警日期：</span>{{this.warningDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">当前状态：</span>{{this.inquiryStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">办理单位：</span>{{this.handlingUnit}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规单位：</span>{{this.schoolName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">单位类型：</span>{{this.unitType}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规分类：</span>{{this.category}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规内容：</span>{{this.content}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">违规取证内容：</span>{{this.evidence}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType == '2'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">已发函询</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询日期：</span>{{this.inquiryDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询单位：</span>{{this.inquiryDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询人姓名：</span>{{this.pushInquerUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">函询内容：</span><a href="javascript:void(0);" style="color: #1e9fff" @click="openMakeInquiry">点此查看</a></div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType3 == '3'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">食堂回复</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复日期：</span>{{this.replyTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复单位：</span>{{this.replyDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复人姓名：</span>{{this.replyName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">回复意见：</span>{{this.replyContent}}</div>
        </el-col>
        <el-col :span="24">
          <div>图片:</div>
          <span v-for="(item,index) in this.fileList">
        <img :src="item.url" style="width: 149px;height: 131px;margin-right: 20px;" @click="queryImg(item.url)" class="avatar">
        </span>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType4 == '4'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">处置信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置日期：</span>{{this.handleDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置单位：</span>{{this.handleDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置人姓名：</span>{{this.handleUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">处置意见：</span>{{this.handleContent}}</div>
        </el-col>
      </el-row>
      <el-row v-if="this.warningType5 == '5'">
        <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">归档信息</div>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档日期：</span>{{this.fileDate}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档单位：</span>{{this.fileDeptName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档人姓名：</span>{{this.fileUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">归档意见：</span>{{this.fileContent}}</div>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :title="函询" :visible.sync="warningRecordVisible1"
               :append-to-body="true" @close="successfulBiddingFormClose" width="60%">
      <div style="margin-top: -20px">
        <hr />
      </div>
      <inquiryDetailVue :query="pageParams"></inquiryDetailVue>
      <div style="margin-top: 65px;text-align: center;">
        <el-button @click="pushInquiry(pageParams)" type="primary">发送</el-button>
        <el-button @click=" warningRecordVisible1 = false">取消</el-button>
      </div>
    </el-dialog>
    <el-dialog :title="`处置`" :visible.sync="managementVisible" :append-to-body="true" @close="successfulBiddingFormClose" width="70%">
      <avue-form ref="managementForm" :option="managementOption" v-model="managementForm" @submit="saveManage">
      </avue-form>
    </el-dialog>
    <el-dialog title="食材价格趋势图" :visible.sync="foodPriceVisible" width="80%" left :append-to-body="true" @close="closeFoodPriceVisible">
      <avue-crud :option="option2"
               :table-loading="loading2"
               :data="data2"
               :page="page2"
               v-model="form2"
               ref="crud"
               @search-change="searchChange2"
               @search-reset="searchReset2"
               @selection-change="selectionChange2"
               @current-change="currentChange2"
               @size-change="sizeChange2"
               @on-load="onLoad2">
        <template slot="menu" slot-scope="scope">
          <el-button type="text" size="small" icon="el-icon-view" @click="opentView(scope.row)">查看
          </el-button>
        </template>
      </avue-crud>
    </el-dialog>

    <el-dialog title="查看" :visible.sync="viewDetailsVisible" width="60%" left :append-to-body="true" @close="tongjiaocan">
      <el-row>
        <!-- <div class="flex_title" style="margin:10px 0;font-size: 14px;font-weight: bold;">预警信息</div> -->
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">商品名称：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">价格：</span>{{this.totalPrices}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">计量单位：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">入库数量：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">入库小计：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">采购单号：</span>{{this.viewId}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">供应商：</span>{{this.supplierName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">采购人：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">采购时间：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">进仓登记人：</span>{{this.accepterUserName}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">验收进仓时间：</span>{{this.accepterTime}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">确认状态：</span>{{this.accepterStatus}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">进仓确认人：</span>{{this.accepterUserNameb}}</div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">确认时间：</span></div>
        </el-col>
        <el-col :span="24">
          <div style="margin-bottom: 2px;margin-top: -5px;"><span class="title_style">确认备注：</span></div>
        </el-col>
      </el-row>
    </el-dialog>

  </basic-container>
</template>

<script>
// import {getList, getDetail, add, update, remove, passOrNo} from "@/api/finance/subject";
import {getList} from "@/api/violations/quantityMonitor";
import inquiryDetailVue from "@/views/liancan2/home/<USER>/inquiryDetail";
import {getViolationWarningList,getWarningById,saveInquiry,getInquiryById,saveManage,saveFile} from "@/api/liancan/illegalWarnLog";
import {mapGetters} from "vuex";
import {getAccepterLogging} from "@/api/liancan/order";
var DIC = {
  // 余额方向
  balanceDirection: [{
    label: '借',
    value: "0"
  },{
    label: '贷',
    value: "1"
  }],
  // 类型
  systemDefault: [{
    label: '固定科目',
    value: "0"
  },{
    label: '自定义科目',
    value: "1"
  }],
  // 是否允许选择
  isSelect: [{
    label: '否',
    value: "0"
  },{
    label: '是',
    value: "1"
  }],
}
export default {
  data() {
    return {
      activeName: 'first',
      form: {},
      form1: {},
      form2: {},
      warningForm:{},
      query: {},
      query1: {},
      loading: true,
      loading1: true,
      loading2: true,
      warningLoading:true,
      page: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      page1: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      page2: {
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      warningPage:{
        pageSize: 10,
        currentPage: 1,
        total: 0
      },
      selectionList: [],
      dialogImageUrl:'',
      dialogVisible:false,
      checkVisible: false,
      detailVisible:false,
      foodPriceVisible:false,
      detail: {},
      option: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: false,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "月份",
            prop: "monthStr",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            search: true
          },
          // {
          //   label: "地区",
          //   formatter: (row,value,label,column) => {
          //     return label = ""
          //   }
          // },
          {
            label: '省份',
            prop: 'province',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            dicUrl: `/api/rabbit-system/region/getProvince`,
            cascaderItem: ['city', 'area'],
            cascaderChange: true,
            rules: [
              {
                required: true,
                message: '请选择省份',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // 临时格式化函数 - 将省份ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const provinceMap = {
                "110000": "北京市",
                "120000": "天津市",
                "310000": "上海市",
                "320000": "江苏省",
                "330000": "浙江省",
                "440000": "广东省",
                "500000": "重庆市",
                "510000": "四川省"
              };
              return provinceMap[value] || value;
            },
            // hide: true
          },
          {
            label: '城市',
            prop: 'city',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            dicFlag: false,
            dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
            cascaderItem: ['area'],
            cascaderChange: true,
            rules: [
              {
                required: true,
                message: '请选择城市',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // 临时格式化函数 - 将城市ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const cityMap = {
                "110100": "北京市",
                "120100": "天津市",
                "310100": "上海市",
                "320100": "南京市",
                "330100": "杭州市",
                "440100": "广州市",
                "500100": "重庆市",
                "510100": "成都市"
              };
              return cityMap[value] || value;
            },
            // hide: true
          },
          {
            label: '地区',
            prop: 'area',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            dicFlag: false,
            dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
            rules: [
              {
                required: true,
                message: '请选择地区',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // 临时格式化函数 - 将地区ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const areaMap = {
                // 北京市
                "110101": "东城区",
                "110102": "西城区",
                "110105": "朝阳区",
                "110106": "丰台区",
                "110107": "石景山区",
                "110108": "海淀区",
                "110109": "门头沟区",
                "110111": "房山区",
                "110112": "通州区",
                "110113": "顺义区",
                "110114": "昌平区",
                "110115": "大兴区",
                "110116": "怀柔区",
                "110117": "平谷区",
                "110118": "密云区",
                "110119": "延庆区",
                // 天津市
                "120101": "和平区",
                "120102": "河东区",
                "120103": "河西区",
                "120104": "南开区",
                "120105": "河北区",
                "120106": "红桥区",
                "120110": "东丽区",
                "120111": "西青区",
                "120112": "津南区",
                "120113": "北辰区",
                "120114": "武清区",
                "120115": "宝坻区",
                "120116": "滨海新区",
                "120117": "宁河区",
                "120118": "静海区",
                "120119": "蓟州区",
                // 上海市
                "310101": "黄浦区",
                "310104": "徐汇区",
                "310105": "长宁区",
                "310106": "静安区",
                "310107": "普陀区",
                "310109": "虹口区",
                "310110": "杨浦区",
                "310112": "闵行区",
                "310113": "宝山区",
                "310114": "嘉定区",
                "310115": "浦东新区",
                "310116": "金山区",
                "310117": "松江区",
                "310118": "青浦区",
                "310120": "奉贤区",
                "310151": "崇明区",
                // 江苏省南京市
                "320101": "市辖区",
                "320102": "玄武区",
                "320104": "秦淮区",
                "320105": "建邺区",
                "320106": "鼓楼区",
                "320111": "浦口区",
                "320113": "栖霞区",
                "320114": "雨花台区",
                "320115": "江宁区",
                "320116": "六合区",
                "320117": "溧水区",
                "320118": "高淳区",
                // 浙江省杭州市
                "330101": "市辖区",
                "330102": "上城区",
                "330103": "下城区",
                "330104": "江干区",
                "330105": "拱墅区",
                "330106": "西湖区",
                "330108": "滨江区",
                "330109": "萧山区",
                "330110": "余杭区",
                "330111": "富阳区",
                "330112": "临安区",
                "330113": "临平区",
                "330114": "钱塘区",
                // 广东省广州市
                "440101": "市辖区",
                "440103": "荔湾区",
                "440104": "越秀区",
                "440105": "海珠区",
                "440106": "天河区",
                "440111": "白云区",
                "440112": "黄埔区",
                "440113": "番禺区",
                "440114": "花都区",
                "440115": "南沙区",
                "440117": "从化区",
                "440118": "增城区",
                // 重庆市
                "500101": "万州区",
                "500102": "涪陵区",
                "500103": "渝中区",
                "500104": "大渡口区",
                "500105": "江北区",
                "500106": "沙坪坝区",
                "500107": "九龙坡区",
                "500108": "南岸区",
                "500109": "北碚区",
                "500110": "綦江区",
                "500111": "大足区",
                "500112": "渝北区",
                "500113": "巴南区",
                "500114": "黔江区",
                "500115": "长寿区",
                "500116": "江津区",
                "500117": "合川区",
                "500118": "永川区",
                "500119": "南川区",
                "500120": "璧山区",
                "500151": "铜梁区",
                "500152": "潼南区",
                "500153": "荣昌区",
                "500154": "开州区",
                "500155": "梁平区",
                "500156": "武隆区",
                "500229": "城口县",
                "500230": "丰都县",
                "500231": "垫江县",
                "500233": "忠县",
                "500235": "云阳县",
                "500236": "奉节县",
                "500237": "巫山县",
                "500238": "巫溪县",
                "500240": "石柱土家族自治县",
                "500241": "秀山土家族苗族自治县",
                "500242": "酉阳土家族苗族自治县",
                "500243": "彭水苗族土家族自治县",
                // 四川省成都市
                "510101": "市辖区",
                "510104": "锦江区",
                "510105": "青羊区",
                "510106": "金牛区",
                "510107": "武侯区",
                "510108": "成华区",
                "510112": "龙泉驿区",
                "510113": "青白江区",
                "510114": "新都区",
                "510115": "温江区",
                "510116": "双流区",
                "510117": "郫都区",
                "510118": "新津区",
                "510121": "金堂县",
                "510129": "大邑县",
                "510131": "蒲江县",
                "510181": "都江堰市",
                "510182": "彭州市",
                "510183": "邛崃市",
                "510184": "崇州市",
                "510185": "简阳市"
              };
              return areaMap[value] || value;
            },
            // hide: true
          },
          {
            label: "单位名称",
            prop: "deptId",
            type: "tree",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value: "id"
              },
              editDisplay: false,
              viewDisplay: false,
              search:false,
              // 临时格式化函数 - 将单位ID转换为中文名称，后续可删除
              formatter: (row, value) => {
                const deptMap = {
                  "1001": "北京市第一中学",
                  "1002": "北京市第二中学",
                  "1003": "天津市第一中学",
                  "1004": "天津市第二中学",
                  "1005": "上海市第一中学",
                  "1006": "上海市第二中学",
                  "1007": "南京市第一中学",
                  "1008": "南京市第二中学",
                  "1009": "杭州市第一中学",
                  "1010": "杭州市第二中学",
                  "1011": "广州市第一中学",
                  "1012": "广州市第二中学",
                  "1013": "重庆市第一中学",
                  "1014": "重庆市第二中学",
                  "1015": "成都市第一中学"
                };
                return deptMap[value] || value;
              },
          },
          {
            label: "食堂名称",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/canteen/select",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            // 临时格式化函数 - 将食堂ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const canteenMap = {
                "2001": "第一食堂",
                "2002": "第二食堂",
                "2003": "第三食堂",
                "2004": "第四食堂",
                "2005": "第五食堂",
                "2006": "第六食堂",
                "2007": "第七食堂",
                "2008": "第八食堂",
                "2009": "第九食堂",
                "2010": "第十食堂",
                "2011": "第十一食堂",
                "2012": "第十二食堂",
                "2013": "第十三食堂",
                "2014": "第十四食堂",
                "2015": "第十五食堂"
              };
              return canteenMap[value] || value;
            }
          },
          {
            label: "该月用餐人数",
            prop: "dinnerNum",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          // {
          //     label: "食材大类",
          //     prop: "bidding",
          //     type: "select",
          //     dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall",
          //     props: {
          //       label: "name",
          //       value: "id"
          //     },
          //     rules: [{
          //       required: true,
          //       message: "请选择食材大类",
          //       trigger: "blur"
          //     }],
          //     search:true,
          // },
          {
              label: "食材大类",
              prop: "bidding",
              type: "select",
              dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall",
                props: {
                    label: "name",
                    value: "id"
                },
              rules: [{
                required: true,
                message: "请选择食材大类",
                trigger: "blur"
              }],
              search:true,
              // 临时格式化函数 - 将食材大类ID转换为中文名称，后续可删除
              formatter: (row, value) => {
                const biddingMap = {
                  "1": "蔬菜类",
                  "2": "肉类",
                  "3": "水产类",
                  "4": "蛋奶类",
                  "5": "粮油类",
                  "6": "调味品类",
                  "7": "水果类",
                  "8": "豆制品类",
                  "9": "干货类",
                  "10": "饮料类"
                };
                return biddingMap[value] || value;
              },
          },
          {
            label: "该月实际采购总量(斤)",
            prop: "foodTotal",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "按营养膳食标准计算而来的该月理论采购总量(斤)",
            prop: "foodTheoryTotal",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "该月实际采购总量/该月理论采购总量百分比",
            prop: "foodTotalPercent",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
        ]
      },
      option1: {
        height:'auto',
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: false,
        viewBtn: false,
        addBtn:false,
        editBtn:false,
        delBtn:false,
        selection: true,
        align: 'center',
        column: [
          {
            label: "id",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "status",
            prop: "status",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            display: false,
            hide: true,
          },
          {
            label: "月份",
            prop: "monthStr",
            type: "select",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            labelWidth: 100,
            search: true
          },
          // {
          //   label: "地区",
          //   formatter: (row,value,label,column) => {
          //     return label = ""
          //   }
          // },
          {
            label: '省份',
            prop: 'province',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            dicUrl: `/api/rabbit-system/region/getProvince`,
            cascaderItem: ['city', 'area'],
            cascaderChange: true,
            rules: [
              {
                required: true,
                message: '请选择省份',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // 临时格式化函数 - 将省份ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const provinceMap = {
                "110000": "北京市",
                "120000": "天津市",
                "310000": "上海市",
                "320000": "江苏省",
                "330000": "浙江省",
                "440000": "广东省",
                "500000": "重庆市",
                "510000": "四川省"
              };
              return provinceMap[value] || value;
            },
            // hide: true
          },
          {
            label: '城市',
            prop: 'city',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            dicFlag: false,
            dicUrl: `/api/rabbit-system/region/getCity/{{key}}`,
            cascaderItem: ['area'],
            cascaderChange: true,
            rules: [
              {
                required: true,
                message: '请选择城市',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // 临时格式化函数 - 将城市ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const cityMap = {
                "110100": "北京市",
                "120100": "天津市",
                "310100": "上海市",
                "320100": "南京市",
                "330100": "杭州市",
                "440100": "广州市",
                "500100": "重庆市",
                "510100": "成都市"
              };
              return cityMap[value] || value;
            },
            // hide: true
          },
          {
            label: '地区',
            prop: 'area',
            type: 'select',
            props: {
              label: "regionName",
              value: "id",
            },
            dicFlag: false,
            dicUrl: `/api/rabbit-system/region/getArea/{{key}}`,
            rules: [
              {
                required: true,
                message: '请选择地区',
                trigger: 'blur'
              }
            ],
            disabled: true,
            search: true,
            // 临时格式化函数 - 将地区ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const areaMap = {
                // 北京市
                "110101": "东城区",
                "110102": "西城区",
                "110105": "朝阳区",
                "110106": "丰台区",
                "110107": "石景山区",
                "110108": "海淀区",
                "110109": "门头沟区",
                "110111": "房山区",
                "110112": "通州区",
                "110113": "顺义区",
                "110114": "昌平区",
                "110115": "大兴区",
                "110116": "怀柔区",
                "110117": "平谷区",
                "110118": "密云区",
                "110119": "延庆区",
                // 天津市
                "120101": "和平区",
                "120102": "河东区",
                "120103": "河西区",
                "120104": "南开区",
                "120105": "河北区",
                "120106": "红桥区",
                "120110": "东丽区",
                "120111": "西青区",
                "120112": "津南区",
                "120113": "北辰区",
                "120114": "武清区",
                "120115": "宝坻区",
                "120116": "滨海新区",
                "120117": "宁河区",
                "120118": "静海区",
                "120119": "蓟州区",
                // 上海市
                "310101": "黄浦区",
                "310104": "徐汇区",
                "310105": "长宁区",
                "310106": "静安区",
                "310107": "普陀区",
                "310109": "虹口区",
                "310110": "杨浦区",
                "310112": "闵行区",
                "310113": "宝山区",
                "310114": "嘉定区",
                "310115": "浦东新区",
                "310116": "金山区",
                "310117": "松江区",
                "310118": "青浦区",
                "310120": "奉贤区",
                "310151": "崇明区",
                // 江苏省南京市
                "320101": "市辖区",
                "320102": "玄武区",
                "320104": "秦淮区",
                "320105": "建邺区",
                "320106": "鼓楼区",
                "320111": "浦口区",
                "320113": "栖霞区",
                "320114": "雨花台区",
                "320115": "江宁区",
                "320116": "六合区",
                "320117": "溧水区",
                "320118": "高淳区",
                // 浙江省杭州市
                "330101": "市辖区",
                "330102": "上城区",
                "330103": "下城区",
                "330104": "江干区",
                "330105": "拱墅区",
                "330106": "西湖区",
                "330108": "滨江区",
                "330109": "萧山区",
                "330110": "余杭区",
                "330111": "富阳区",
                "330112": "临安区",
                "330113": "临平区",
                "330114": "钱塘区",
                // 广东省广州市
                "440101": "市辖区",
                "440103": "荔湾区",
                "440104": "越秀区",
                "440105": "海珠区",
                "440106": "天河区",
                "440111": "白云区",
                "440112": "黄埔区",
                "440113": "番禺区",
                "440114": "花都区",
                "440115": "南沙区",
                "440117": "从化区",
                "440118": "增城区",
                // 重庆市
                "500101": "万州区",
                "500102": "涪陵区",
                "500103": "渝中区",
                "500104": "大渡口区",
                "500105": "江北区",
                "500106": "沙坪坝区",
                "500107": "九龙坡区",
                "500108": "南岸区",
                "500109": "北碚区",
                "500110": "綦江区",
                "500111": "大足区",
                "500112": "渝北区",
                "500113": "巴南区",
                "500114": "黔江区",
                "500115": "长寿区",
                "500116": "江津区",
                "500117": "合川区",
                "500118": "永川区",
                "500119": "南川区",
                "500120": "璧山区",
                "500151": "铜梁区",
                "500152": "潼南区",
                "500153": "荣昌区",
                "500154": "开州区",
                "500155": "梁平区",
                "500156": "武隆区",
                "500229": "城口县",
                "500230": "丰都县",
                "500231": "垫江县",
                "500233": "忠县",
                "500235": "云阳县",
                "500236": "奉节县",
                "500237": "巫山县",
                "500238": "巫溪县",
                "500240": "石柱土家族自治县",
                "500241": "秀山土家族苗族自治县",
                "500242": "酉阳土家族苗族自治县",
                "500243": "彭水苗族土家族自治县",
                // 四川省成都市
                "510101": "市辖区",
                "510104": "锦江区",
                "510105": "青羊区",
                "510106": "金牛区",
                "510107": "武侯区",
                "510108": "成华区",
                "510112": "龙泉驿区",
                "510113": "青白江区",
                "510114": "新都区",
                "510115": "温江区",
                "510116": "双流区",
                "510117": "郫都区",
                "510118": "新津区",
                "510121": "金堂县",
                "510129": "大邑县",
                "510131": "蒲江县",
                "510181": "都江堰市",
                "510182": "彭州市",
                "510183": "邛崃市",
                "510184": "崇州市",
                "510185": "简阳市"
              };
              return areaMap[value] || value;
            },
            // hide: true
          },
          {
            label: "单位名称",
            prop: "deptId",
            type: "tree",
              dicUrl: "/api/service/rabbit-system/dept/dict",
              props: {
                label: "deptName",
                value: "id"
              },
              editDisplay: false,
              viewDisplay: false,
              search:false,
              // 临时格式化函数 - 将单位ID转换为中文名称，后续可删除
              formatter: (row, value) => {
                const deptMap = {
                  "1001": "北京市第一中学",
                  "1002": "北京市第二中学",
                  "1003": "天津市第一中学",
                  "1004": "天津市第二中学",
                  "1005": "上海市第一中学",
                  "1006": "上海市第二中学",
                  "1007": "南京市第一中学",
                  "1008": "南京市第二中学",
                  "1009": "杭州市第一中学",
                  "1010": "杭州市第二中学",
                  "1011": "广州市第一中学",
                  "1012": "广州市第二中学",
                  "1013": "重庆市第一中学",
                  "1014": "重庆市第二中学",
                  "1015": "成都市第一中学"
                };
                return deptMap[value] || value;
              },
          },
          {
            label: "食堂名称",
            prop: "canteenId",
            type: "select",
            dicUrl: "/api/service/rabbit-system/dept/dict",
            props: {
              label: "deptName",
              value:"id"
            },
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            // 临时格式化函数 - 将食堂ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const canteenMap = {
                "2001": "第一食堂",
                "2002": "第二食堂",
                "2003": "第三食堂",
                "2004": "第四食堂",
                "2005": "第五食堂",
                "2006": "第六食堂",
                "2007": "第七食堂",
                "2008": "第八食堂",
                "2009": "第九食堂",
                "2010": "第十食堂",
                "2011": "第十一食堂",
                "2012": "第十二食堂",
                "2013": "第十三食堂",
                "2014": "第十四食堂",
                "2015": "第十五食堂"
              };
              return canteenMap[value] || value;
            }
          },
          // {
          //     label: "食材大类",
          //     prop: "bidding",
          //     type: "select",
          //     dicUrl: "/api/service/rabbit-liancan/biddingType/dictForSmall",
          //     props: {
          //       label: "name",
          //       value: "id"
          //     },
          //     rules: [{
          //       required: true,
          //       message: "请选择食材大类",
          //       trigger: "blur"
          //     }],
          //     search:true,
          // },
          {
              label: "食材大类",
              prop: "bidding",
              type: "select",
              dicUrl: "/api/service/rabbit-backendBidding/biddingSmallClass/dictForSmall",
                props: {
                    label: "name",
                    value: "id"
                },
              rules: [{
                required: true,
                message: "请选择食材大类",
                trigger: "blur"
              }],
              search:true,
              // 临时格式化函数 - 将食材大类ID转换为中文名称，后续可删除
              formatter: (row, value) => {
                const biddingMap = {
                  "1": "蔬菜类",
                  "2": "肉类",
                  "3": "水产类",
                  "4": "蛋奶类",
                  "5": "粮油类",
                  "6": "调味品类",
                  "7": "水果类",
                  "8": "豆制品类",
                  "9": "干货类",
                  "10": "饮料类"
                };
                return biddingMap[value] || value;
              },
          },
          {
            label: "食材小类",
            prop: "biddingTypeId",
            type: "select",
            dicUrl: "/api/service/rabbit-liancan/schoolGoods/dict",
            props: {
              label: "name",
              value: "id"
            },
            // 临时格式化函数 - 将食材小类ID转换为中文名称，后续可删除
            formatter: (row, value) => {
              const biddingTypeMap = {
                "101": "叶菜类",
                "102": "根茎类",
                "103": "瓜果类",
                "104": "茄果类",
                "105": "豆类",
                "106": "菌菇类",
                "107": "葱蒜类",
                "108": "其他蔬菜"
              };
              return biddingTypeMap[value] || value;
            },
          },
          {
            label: "食材名称",
            prop: "foodName",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "食材生产厂家",
            prop: "foodManufacturer",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
          {
            label: "食材采购总量(斤)",
            prop: "foodTotal",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
            labelWidth: 150,
          },
          {
            label: "食材采购总量占所属食材大类采购总量百分比",
            prop: "foodTotalPercent",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: true,
          },
        ]
      },
      warningOption: {
        /* height:'auto',*/
        calcHeight: 30,
        searchShow: true,
        searchMenuSpan: 6,
        tip: false,
        border: true,
        index: true,
        viewBtn: false,
        selection: true,
        delBtn:false,
        editBtn:false,
        addBtn:false,
        column: [
          {
            label: "主键",
            prop: "id",
            type: "input",
            addDisplay: false,
            editDisplay: false,
            viewDisplay: false,
            hide: true,
          },
          {
            label: "食堂名称",
            prop: "deptName",
            type: "input",
          },
          {
            label: '预警时间',
            prop: 'illegalDate',
            type: 'date',
            format: 'yyyy-MM-dd',
            valueFormat: 'yyyy-MM-dd',
            width: 90,
          },
          {
            label: "违规分类",
            prop: "category",
            type: "select",
            search:true,
            width: 80,
            dicData: [
              {
                label: "收入违规",
                value: "0"
              },
              {
                label: "支出违规",
                value: "1"
              },
              {
                label: "采购违规",
                value: "2"
              },
              {
                label: "财务违规",
                value: "3"
              },
              {
                label: "招标违规",
                value: "4"
              },
              {
                label: "仓管违规",
                value: "5"
              }
            ],
          },
          {
            label: "违规内容",
            prop: "content",
            type: "input",
            width:600,
          },
          {
            label: "当前状态",
            prop: "inquiryStatus",
            type: "select",
            width:90,
            search:true,
            dicData: [
              {
                label: "待函询",
                value: "1"
              },
              {
                label: "待食堂回复",
                value: "2"
              },
              {
                label: "待处置",
                value: "3"
              },
              {
                label: "待归档",
                value: "4"
              },
              {
                label: "已归档",
                value: "5"
              }
            ],
          },
          {
            label: "办理单位",
            prop: "handlingUnit",
            type: "input",
            width:150,
          },
        ]
      },
      option2: {
          align: "center",
          height:'auto',
          calcHeight: 30,
          searchShow: true,
          searchMenuSpan: 6,
          tip: false,
          border: true,
          index: false,
          viewBtn: false,
          editBtn: false,
          addBtn: false,
          delBtn: false,
          selection: false,
          column: [
              {
                label: "所属食堂",
                prop: "deptId",
                type: "tree",
                dicUrl: "/api/service/rabbit-liancan2/user/getSubDeptList?deptId=" + (!!this.schoolId?this.schoolId:""),
                props: {
                  label: "deptName",
                  value:"id"
                },
                hide: true,
                addDisplay: false,
                search:true,
              },
              {
                label: "所属食堂",
                prop: "deptName",
                type: "input",
              },
              {
                  label: "采购单号",
                  prop: "id",
                  type: "input",
                  display: false,
              },
              {
                  label: "供应商",
                  prop: "supplierName",
                  type: "input",
                  overHidden: true,
                  width: 190,
              },
              {
                  label: "进仓金额",
                  prop: "totalPrices",
                  type: "number",
                  precision:2,
                  mock:{
                      type:'number',
                      max:1,
                      min:2,
                      precision:2
                  },
              },
              {
                  label: "进仓登记人",
                  prop: "accepterUserName",
                  type: "input",
              },
              {
                  label: "验收进仓时间",
                  prop: "accepterTime",
                  type: "datetime",
                  format: "yyyy-MM-dd HH:mm:ss",
                  valueFormat: "yyyy-MM-dd HH:mm:ss",
                  width: 160,
              },
              {
                  label: "进仓确认人",
                  prop: "accepterUserNameb",
                  type: "input",
              },
              {
                  label: "确认状态",
                  prop: "accepterStatus",
                  type: "select",
                  dicData: DIC.accepterStatus,
                  // 临时格式化函数 - 将确认状态转换为中文名称，后续可删除
                  formatter: (row, value) => {
                    const statusMap = {
                      "0": "未确认",
                      "1": "已确认",
                      "2": "已拒绝"
                    };
                    return statusMap[value] || value;
                  },
              }
          ]
      },
      data: [],
      data1: [],
      data2: [],
      warningData:[],
      detailRow: {},
      warningId:	undefined,
      warningDate:	undefined,
      warningType:	undefined,
      warningType3:	undefined,
      warningType4:	undefined,
      warningType5:	undefined,
      handlingUnit:	undefined,
      content:	undefined,
      evidence:	undefined,
      inquiryDate:	undefined,
      inquiryDeptName:	undefined,
      inquiryName:	undefined,
      replyContent:	undefined,
      replyName:	undefined,
      replyTime:	undefined,
      replyDeptName:	undefined,
      handleUserName:	undefined,
      handleContent:	undefined,
      handleDeptName:	undefined,
      handleDate:	undefined,
      fileUserName:	undefined,
      fileContent:	undefined,
      fileDate:	undefined,
      fileDeptName:	undefined,
      unitType:	undefined,
      fileList:	undefined,
      pushInquerUserName:	undefined,
      warningDetailsVisible:	false,
      warningRecordVisible1: false,
      managementVisible:	false,
      viewId: '',
      supplierName: '',
      totalPrices: '',
      accepterUserName: '',
      accepterTime: '',
      accepterUserNameb : '',
      accepterStatus: '',
      viewDetailsVisible: false,
    };
  },
  computed: {
    ...mapGetters(["permission", "userInfo"]),
    permissionList() {
      return {
        addBtn: this.vaildData(this.permission.account_sets_add, false),
        // viewBtn: this.vaildData(this.permission.bank_account_view, false),
        // delBtn: this.vaildData(this.permission.bank_account_delete, false),
        // editBtn: this.vaildData(this.permission.bank_account_edit, false),
        // checkBtn: this.vaildData(this.permission.bank_account_check, false)
      };
    },
    ids() {
      let ids = [];
      this.selectionList.forEach(ele => {
        ids.push(ele.id);
      });
      return ids.join(",");
    }
  },
  created(){
    if (this.userInfo.userType === 'canteen'){
      this.option.column[1].search = false;
      this.option.column[1].hide = true;
    }
  },
  components: {
    'inquiryDetailVue': inquiryDetailVue,
  },
  methods: {
    beforeOpen(done, type) {
      if (["edit", "view"].includes(type)) {
        getDetail(this.form.id).then(res => {
          this.form = res.data.data;
        });
      }
      done();
    },
    handleClick(tab) {
      console.log(tab)
    },
    searchReset() {
      this.query = {};
      this.onLoad(this.page);
    },
    searchChange(params, done) {
      this.query = params;
      this.page.currentPage = 1
      this.onLoad(this.page, params);
      done();
    },
    selectionChange(list) {
      this.selectionList = list;
    },
    currentChange(currentPage){
      this.page.currentPage = currentPage;
    },
    sizeChange(pageSize){
      this.page.pageSize = pageSize;
    },
    onLoad(page, params = {}) {
      this.loading = true;
      params.type = 1 //数量监测主页
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page.total = data.total;
        this.data = data.records;
        this.loading = false;

        // 临时模拟数据 - 当API返回空数据时使用，后续可删除此段代码
        // 开始：临时模拟数据块
        if (!this.data || this.data.length === 0) {
          this.data = [
            {
              id: 1,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              dinnerNum: 1200,
              bidding: "1",
              foodTotal: 850.5,
              foodTheoryTotal: 900.0,
              foodTotalPercent: "94.5%"
            },
            {
              id: 2,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110102",
              deptId: "1002",
              canteenId: "2002",
              dinnerNum: 980,
              bidding: "2",
              foodTotal: 720.3,
              foodTheoryTotal: 750.0,
              foodTotalPercent: "96.0%"
            },
            {
              id: 3,
              monthStr: "2024-01",
              province: "120000",
              city: "120100",
              area: "120101",
              deptId: "1003",
              canteenId: "2003",
              dinnerNum: 1500,
              bidding: "1",
              foodTotal: 1100.8,
              foodTheoryTotal: 1125.0,
              foodTotalPercent: "97.8%"
            },
            {
              id: 4,
              monthStr: "2024-01",
              province: "120000",
              city: "120100",
              area: "120102",
              deptId: "1004",
              canteenId: "2004",
              dinnerNum: 800,
              bidding: "3",
              foodTotal: 580.2,
              foodTheoryTotal: 600.0,
              foodTotalPercent: "96.7%"
            },
            {
              id: 5,
              monthStr: "2024-01",
              province: "310000",
              city: "310100",
              area: "310101",
              deptId: "1005",
              canteenId: "2005",
              dinnerNum: 2000,
              bidding: "2",
              foodTotal: 1450.6,
              foodTheoryTotal: 1500.0,
              foodTotalPercent: "96.7%"
            },
            {
              id: 6,
              monthStr: "2024-01",
              province: "310000",
              city: "310100",
              area: "310104",
              deptId: "1006",
              canteenId: "2006",
              dinnerNum: 1350,
              bidding: "1",
              foodTotal: 980.4,
              foodTheoryTotal: 1012.5,
              foodTotalPercent: "96.8%"
            },
            {
              id: 7,
              monthStr: "2024-01",
              province: "320000",
              city: "320100",
              area: "320101",
              deptId: "1007",
              canteenId: "2007",
              dinnerNum: 1800,
              bidding: "3",
              foodTotal: 1320.7,
              foodTheoryTotal: 1350.0,
              foodTotalPercent: "97.8%"
            },
            {
              id: 8,
              monthStr: "2024-01",
              province: "320000",
              city: "320100",
              area: "320102",
              deptId: "1008",
              canteenId: "2008",
              dinnerNum: 950,
              bidding: "2",
              foodTotal: 680.9,
              foodTheoryTotal: 712.5,
              foodTotalPercent: "95.6%"
            },
            {
              id: 9,
              monthStr: "2024-01",
              province: "330000",
              city: "330100",
              area: "330101",
              deptId: "1009",
              canteenId: "2009",
              dinnerNum: 1600,
              bidding: "1",
              foodTotal: 1150.3,
              foodTheoryTotal: 1200.0,
              foodTotalPercent: "95.9%"
            },
            {
              id: 10,
              monthStr: "2024-01",
              province: "330000",
              city: "330100",
              area: "330102",
              deptId: "1010",
              canteenId: "2010",
              dinnerNum: 1100,
              bidding: "3",
              foodTotal: 820.5,
              foodTheoryTotal: 825.0,
              foodTotalPercent: "99.5%"
            },
            {
              id: 11,
              monthStr: "2024-01",
              province: "440000",
              city: "440100",
              area: "440101",
              deptId: "1011",
              canteenId: "2011",
              dinnerNum: 2200,
              bidding: "2",
              foodTotal: 1580.8,
              foodTheoryTotal: 1650.0,
              foodTotalPercent: "95.8%"
            },
            {
              id: 12,
              monthStr: "2024-01",
              province: "440000",
              city: "440100",
              area: "440103",
              deptId: "1012",
              canteenId: "2012",
              dinnerNum: 1250,
              bidding: "1",
              foodTotal: 920.6,
              foodTheoryTotal: 937.5,
              foodTotalPercent: "98.2%"
            },
            {
              id: 13,
              monthStr: "2024-01",
              province: "500000",
              city: "500100",
              area: "500101",
              deptId: "1013",
              canteenId: "2013",
              dinnerNum: 1900,
              bidding: "3",
              foodTotal: 1380.4,
              foodTheoryTotal: 1425.0,
              foodTotalPercent: "96.9%"
            },
            {
              id: 14,
              monthStr: "2024-01",
              province: "500000",
              city: "500100",
              area: "500102",
              deptId: "1014",
              canteenId: "2014",
              dinnerNum: 1050,
              bidding: "2",
              foodTotal: 750.7,
              foodTheoryTotal: 787.5,
              foodTotalPercent: "95.3%"
            },
            {
              id: 15,
              monthStr: "2024-01",
              province: "510000",
              city: "510100",
              area: "510101",
              deptId: "1015",
              canteenId: "2015",
              dinnerNum: 1700,
              bidding: "1",
              foodTotal: 1250.9,
              foodTheoryTotal: 1275.0,
              foodTotalPercent: "98.1%"
            },
            {
              id: 16,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110105",
              deptId: "1001",
              canteenId: "2001",
              dinnerNum: 1400,
              bidding: "4",
              foodTotal: 420.8,
              foodTheoryTotal: 420.0,
              foodTotalPercent: "100.2%"
            },
            {
              id: 17,
              monthStr: "2024-01",
              province: "120000",
              city: "120100",
              area: "120103",
              deptId: "1003",
              canteenId: "2003",
              dinnerNum: 1200,
              bidding: "5",
              foodTotal: 180.5,
              foodTheoryTotal: 180.0,
              foodTotalPercent: "100.3%"
            },
            {
              id: 18,
              monthStr: "2024-01",
              province: "310000",
              city: "310100",
              area: "310105",
              deptId: "1005",
              canteenId: "2005",
              dinnerNum: 1800,
              bidding: "6",
              foodTotal: 95.2,
              foodTheoryTotal: 90.0,
              foodTotalPercent: "105.8%"
            },
            {
              id: 19,
              monthStr: "2024-01",
              province: "320000",
              city: "320100",
              area: "320104",
              deptId: "1007",
              canteenId: "2007",
              dinnerNum: 1600,
              bidding: "7",
              foodTotal: 320.6,
              foodTheoryTotal: 320.0,
              foodTotalPercent: "100.2%"
            },
            {
              id: 20,
              monthStr: "2024-01",
              province: "330000",
              city: "330100",
              area: "330103",
              deptId: "1009",
              canteenId: "2009",
              dinnerNum: 1400,
              bidding: "8",
              foodTotal: 280.4,
              foodTheoryTotal: 280.0,
              foodTotalPercent: "100.1%"
            }
          ];
          this.page.total = this.data.length;
        }
        // 结束：临时模拟数据块
        // 临时模拟数据结束
      });
    },
    openDetailDiaglog(row, status){
      console.log(row)
      this.detailRow.deptId = row.deptId
      this.detailRow.canteenId = row.canteenId
      this.detailRow.bidding = row.bidding
      this.detailVisible = true;
    },
    searchReset1() {
      this.query1 = {};
      this.onLoad1(this.page1);
    },
    searchChange1(params, done) {
      this.query1 = params;
      this.page1.currentPage = 1
      this.onLoad1(this.page1, params);
      done();
    },
    currentChange1(currentPage){
      this.page1.currentPage = currentPage;
    },
    sizeChange1(pageSize){
      this.page1.pageSize = pageSize;
    },
    onLoad1(page, params = {}) {
      this.loading1 = true;
      params.type = 2 //数量监测明细信息
      params.deptId = this.detailRow.deptId
      params.canteenId = this.detailRow.canteenId
      params.bidding = this.detailRow.bidding
      console.log(params)
      getList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
        const data = res.data.data;
        this.page1.total = data.total;
        this.data1 = data.records;
        this.loading1 = false;

        // 临时模拟数据 - 当API返回空数据时使用，后续可删除此段代码
        // 开始：临时模拟数据块 - option1
        if (!this.data1 || this.data1.length === 0) {
          this.data1 = [
            {
              id: 1,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "101",
              foodName: "大白菜",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 120.5,
              foodTotalPercent: "14.2%"
            },
            {
              id: 2,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "102",
              foodName: "胡萝卜",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 85.3,
              foodTotalPercent: "10.0%"
            },
            {
              id: 3,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "103",
              foodName: "土豆",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 95.8,
              foodTotalPercent: "11.3%"
            },
            {
              id: 4,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "104",
              foodName: "西红柿",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 78.2,
              foodTotalPercent: "9.2%"
            },
            {
              id: 5,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "105",
              foodName: "黄瓜",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 65.4,
              foodTotalPercent: "7.7%"
            },
            {
              id: 6,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "106",
              foodName: "青椒",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 45.6,
              foodTotalPercent: "5.4%"
            },
            {
              id: 7,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "107",
              foodName: "茄子",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 52.3,
              foodTotalPercent: "6.2%"
            },
            {
              id: 8,
              monthStr: "2024-01",
              province: "110000",
              city: "110100",
              area: "110101",
              deptId: "1001",
              canteenId: "2001",
              bidding: "1",
              biddingTypeId: "108",
              foodName: "菠菜",
              foodManufacturer: "北京蔬菜基地",
              foodTotal: 38.7,
              foodTotalPercent: "4.6%"
            }
          ];
          this.page1.total = this.data1.length;
        }
        // 结束：临时模拟数据块 - option1
        // 临时模拟数据结束
      });
    },
    closeFoodVisible(){
      this.detailRow = {}
      this.detailVisible = false;
    },
    searchResetWarning() {
      this.query = {};
      this.onLoadWarning(this.warningPage);
    },
    currentChangeWarning(currentPage){
      this.warningPage.currentPage = currentPage;
    },
    searchChangeWarning(params, done) {
      this.query = params;
      this.warningPage.currentPage = 1
      this.onLoadWarning(this.warningPage, params);
      done();
    },
    sizeChangeWarning(pageSize){
      this.warningPage.pageSize = pageSize;
    },
    onLoadWarning(page, params = {}) {
      this.warningLoading = true;
      getViolationWarningList(page.currentPage, page.pageSize, Object.assign(params, this.query),this.deptId).then(res => {
        const data = res.data.data;
        this.warningPage.total = data.total;
        this.warningData = data.records;
        this.warningLoading = false;
      });
    },
    openWarning(row,status){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = status;
      this.warningType3 = status;
      this.warningType4 = status;
      this.warningType5 = status;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.unitType = row.unitType;
      this.warningDetailsVisible = true;
    },
    handOpenWarningPush(row){
      this.pageParams = row;
      this.warningRecordVisible1 = true;
    },
    checkReply(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    management(row){
      this.warningId = row.id;
      this.managementVisible = true;
    },
    checkNanagement(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.warningType4 = 4;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.handleUserName = row.handleUserName;
      this.handleContent = row.handleContent;
      this.handleDeptName = row.handleDeptName;
      this.handleDate = row.handleDate;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    fileOpen(row){
      this.warningId = row.id;
      this.fileVisible = true;
    },
    checkFile(row){
      this.warningId = row.id;
      this.warningDate = row.warningDate;
      if (row.inquiryStatus == "1"){
        this.inquiryStatus = "待函询"
      }
      if (row.inquiryStatus == "2") {
        this.inquiryStatus = "待食堂回复"
      }
      if (row.inquiryStatus == "3") {
        this.inquiryStatus = "待处置"
      }
      if (row.inquiryStatus == "4") {
        this.inquiryStatus = "待归档"
      }
      if (row.inquiryStatus == "5") {
        this.inquiryStatus = "已归档"
      }
      if (row.category == "0") {
        this.category = "收入违规"
      }
      if (row.category == "1") {
        this.category = "支出违规"
      }
      if (row.category == "2") {
        this.category = "采购违规"
      }
      if (row.category == "3") {
        this.category = "财务违规"
      }
      if (row.category == "4") {
        this.category = "招标违规"
      }
      this.warningType = 2;
      this.warningType3 = 3;
      this.warningType4 = 4;
      this.warningType5 = 5;
      this.handlingUnit = row.handlingUnit;
      this.content = row.content;
      this.evidence = row.evidence;
      this.inquiryDate = row.inquiryDate;
      this.inquiryDeptName = row.pushInquiryDeptName;
      this.inquiryName = row.jiweiUserName;
      this.replyContent = row.replyContent;
      this.replyName = row.replyName;
      this.replyTime = row.replyTime;
      this.replyDeptName = row.replyDeptName;
      this.handleUserName = row.handleUserName;
      this.handleContent = row.handleContent;
      this.handleDeptName = row.handleDeptName;
      this.handleDate = row.handleDate;
      this.fileUserName = row.fileUserName;
      this.fileContent = row.fileContent;
      this.fileDate = row.fileDate;
      this.fileDeptName = row.fileDeptName;
      this.unitType = row.unitType;
      this.fileList = row.replyFileList;
      this.pushInquerUserName = row.pushInquerUserName;
      this.warningDetailsVisible = true;
    },
    currentChange2(currentPage){
        this.page2.currentPage = currentPage;
    },
    sizeChange2(pageSize){
        this.page2.pageSize = pageSize;
    },
    onLoad2(page, params = {}) {
        this.loading2 = true;
        params.queryType = 1//按商品查询
        params.biddingTypeId = this.detailRow.biddingTypeId
        console.log(params)
        getAccepterLogging(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
            const data = res.data.data;
            this.page2.total = data.total;
            this.data2 = data.records;
            this.loading2 = false;

            // 临时模拟数据 - 当API返回空数据时使用，后续可删除此段代码
            // 开始：临时模拟数据块 - option2
            if (!this.data2 || this.data2.length === 0) {
              this.data2 = [
                {
                  id: 1,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401001",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 1250.50,
                  accepterUserName: "张三",
                  accepterTime: "2024-01-15 09:30:00",
                  accepterUserNameb: "李四",
                  accepterStatus: "已确认"
                },
                {
                  id: 2,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401002",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 980.30,
                  accepterUserName: "王五",
                  accepterTime: "2024-01-16 14:20:00",
                  accepterUserNameb: "赵六",
                  accepterStatus: "已确认"
                },
                {
                  id: 3,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401003",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 1560.80,
                  accepterUserName: "张三",
                  accepterTime: "2024-01-17 10:15:00",
                  accepterUserNameb: "李四",
                  accepterStatus: "已确认"
                },
                {
                  id: 4,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401004",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 890.20,
                  accepterUserName: "王五",
                  accepterTime: "2024-01-18 16:45:00",
                  accepterUserNameb: "赵六",
                  accepterStatus: "已确认"
                },
                {
                  id: 5,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401005",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 1120.60,
                  accepterUserName: "张三",
                  accepterTime: "2024-01-19 11:30:00",
                  accepterUserNameb: "李四",
                  accepterStatus: "已确认"
                },
                {
                  id: 6,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401006",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 750.40,
                  accepterUserName: "王五",
                  accepterTime: "2024-01-20 13:20:00",
                  accepterUserNameb: "赵六",
                  accepterStatus: "已确认"
                },
                {
                  id: 7,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401007",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 1350.90,
                  accepterUserName: "张三",
                  accepterTime: "2024-01-21 08:45:00",
                  accepterUserNameb: "李四",
                  accepterStatus: "已确认"
                },
                {
                  id: 8,
                  deptId: "1001",
                  deptName: "北京市第一中学",
                  viewId: "PO202401008",
                  supplierName: "北京蔬菜供应有限公司",
                  totalPrices: 680.70,
                  accepterUserName: "王五",
                  accepterTime: "2024-01-22 15:10:00",
                  accepterUserNameb: "赵六",
                  accepterStatus: "已确认"
                }
              ];
              this.page2.total = this.data2.length;
            }
            // 结束：临时模拟数据块 - option2
            // 临时模拟数据结束
        });
    },
    openOrderDiaglog(row,status){
      console.log(row)
      this.foodPriceVisible = true;
    },
    closeFoodPriceVisible(){
      this.foodPriceVisible = false;
    },
    opentView(row, status){
      this.viewId = row.viewId
      this.supplierName = row.supplierName
      this.totalPrices = row.totalPrices
      this.accepterUserName = row.accepterUserName
      this.accepterTime = row.accepterTime
      this.accepterUserNameb = row.accepterUserNameb
      this.accepterStatus = row.accepterStatus
      this.viewDetailsVisible = true
    },
  }
};
</script>
