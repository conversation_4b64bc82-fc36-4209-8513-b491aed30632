<template>
    <basic-container>
        <avue-crud :option="option"
                   :table-loading="loading"
                   :data="data"
                   :page="page"
                   :before-open="beforeOpen"
                   v-model="form"
                   ref="crud"
                   @search-change="searchChange"
                   @search-reset="searchReset"
                   @selection-change="selectionChange"
                   @current-change="currentChange"
                   @size-change="sizeChange"
                   @on-load="onLoad">
            <template slot="menuLeft" slot-scope="scope">
<!--                <el-button type="primary"  icon="el-icon-download" size="small" @click="exportOrderHandler" >导出</el-button>-->
<!--                <el-button type="primary"  icon="el-icon-printer" size="small" @click="printOrderHandler" >打印</el-button>-->
            </template>
            <template slot="status" slot-scope="{row}">
                <el-tag v-if="row.status == '1'" type="success">正常</el-tag>
                <el-tag v-if="row.status == '0'" type="danger">停用</el-tag>
            </template>
        </avue-crud>
    </basic-container>
</template>

<script>
import {summaryByWarehouseList, getDetail, add, update, exportWarehouseList} from "@/api/supplier/supplierOtherStockIn";
import {mapGetters} from "vuex";
import {amtFilters} from "@/api/supplier/finance/financialUtils";
import {getSupplierCustomerList,getSalesmanList,getGoodsList} from "@/api/supplier/supplier";

var DIC = {
    customerType: [{
        label: '供应商',
        value: '0'
    }, {
        label: '客户',
        value: '1'
    }],
    sourceType: [{
        label: '自增',
        value: '0'
    }, {
        label: '系统',
        value: '1'
    }],
    useType: [{
        label: '停用',
        value: '0'
    }, {
        label: '正常',
        value: '1'
    }],
    isUse: [{
        label: '未启用',
        value: '0'
    },{
        label: '已启用',
        value: '1'
    }],
    billTypes: [{
        label: '全部',
        value: "0"
    },{
        label: '其他入库',
        value: "2"
    },{
        label: '其他出库',
        value: "1"
    },],
}

export default {
    data() {
        return {
            form: {},
            totalAmt: 0,
            warehouseForm: {},
            query: {},
            loading: true,
            editType: 'add',
            page: {
                pageSize: 10,
                currentPage: 1,
                total: 0
            },
            selectionList: [],
            option: {
                height: 'auto',
                searchShow: true,
                refreshBtn: false,
                columnBtn: false,
                searchBtn: false,
                searchMenuSpan: 6,
                labelWidth: 180,
                menuWidth: 80,
                tip: false,
                border: true,
                index: true,
                menu: false,
                addBtn: false,
                delBtn: false,
                editBtn: false,
                viewBtn: false,
                selection: false,
                align: "center",
                column: [
                    {
                        label: "id",
                        prop: "id",
                        type: "input",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                    },
                    {
                        label: "开始日期",
                        prop: "businessDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "businessDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "单据类型",
                        prop: "billType",
                        type: "select",
                        dicData: DIC.billTypes,
                        search: true,
                    },
                    {
                        label: "入库仓库",
                        prop: "warehouseId",
                        type: "select",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        hide: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: '业务员',
                        prop: 'salesman',
                        type:'select',
                        search: true,
                        hide: true,
                        dicUrl: `/api/rabbit-supplier/user-list`,
                        props: {
                            res:'data',
                            label: "contactName",
                            value: "id"
                        },
                        multiple:true
                    },
                    {
                        label: "商品名称",
                        prop: "goodGoodId",
                        type: "select",
                        hide: true,
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "仓库名称",
                        prop: "warehouseName",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请填写仓库名称",
                            trigger: "blur"
                        }],
                        search: true,
                    },
                    {
                        label: "仓库地址",
                        prop: "warehouseAddress",
                        type: "input",
                        rules: [{
                            required: true,
                            message: "请填写仓库地址",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "仓库管理员",
                        prop: "warehouseManager",
                        type: "select",
                        dicUrl: '/api/service/rabbit-supplier/user-list',
                        props: {
                            label: "contactName",
                            value: "id"
                        },
                        rules: [{
                            required: true,
                            message: "请填写仓管人员",
                            trigger: "blur"
                        }],
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "select",
                        slot:true,
                        dicData: DIC.isUse,
                        search: true,
                    },
                    {
                        label: "数量",
                        prop: "qty",
                        type: "input",
                        dataType:'number',
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                    {
                        label: "入库总额",
                        prop: "amt",
                        type: "input",
                        dataType:'number',
                        formatter:(val)=>{
                            if(val.amt===0) {
                                return '';
                            }
                            return amtFilters(val.amt);
                        },
                    },
                ]
            },
            data: [],

            print: {
                id: 'printArea',
                popTitle: '打印', // 打印配置页上方标题
                extraHead: '', //最上方的头部文字，附加在head标签上的额外标签,使用逗号分隔
                preview: '', // 是否启动预览模式，默认是false（开启预览模式，可以先预览后打印）
                previewTitle: '', // 打印预览的标题（开启预览模式后出现）,
                previewPrintBtnLabel: '', // 打印预览的标题的下方按钮文本，点击可进入打印（开启预览模式后出现）
                zIndex: '', // 预览的窗口的z-index，默认是 20002（此值要高一些，这涉及到预览模式是否显示在最上面）
                previewBeforeOpenCallback() {}, //预览窗口打开之前的callback（开启预览模式调用）
                previewOpenCallback() {}, // 预览窗口打开之后的callback（开启预览模式调用）
                beforeOpenCallback() {}, // 开启打印前的回调事件
                openCallback() {}, // 调用打印之后的回调事件
                closeCallback() {}, //关闭打印的回调事件（无法确定点击的是确认还是取消）
                url: '',
                standard: '',
                extraCss: '',
            },
        };
    },
    computed: {
        ...mapGetters(["permission", "userInfo"]),
        ids() {
            let ids = [];
            this.selectionList.forEach(ele => {
                ids.push(ele.id);
            });
            return ids.join(",");
        }
    },
    created() {
        //加载供应商
        getSupplierCustomerList()
            .then(res => {
                this.supplierList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
        //加载业务员
        getSalesmanList()
            .then(res => {
                this.salesmanList = res.data.data;
            }).catch(err=>{}).finally(fin=>{this.loading = false;});
    },
    watch: {
    },
    mounted() {
    },
    methods: {
        //主界面基本方法 start
        beforeOpen(done, type) {
            done();
        },
        searchReset() {
            this.query = {};
            this.onLoad(this.page);
        },
        searchChange(params, done) {
            this.query = params;
            this.page.currentPage = 1
            this.onLoad(this.page, params);
            done();
        },
        selectionChange(list) {
            this.selectionList = list;
        },
        selectionClear() {
            this.selectionList = [];
            this.$refs.crud.toggleSelection();
        },
        currentChange(currentPage) {
            this.page.currentPage = currentPage;
        },
        sizeChange(pageSize) {
            this.page.pageSize = pageSize;
        },
        onLoad(page, params = {}) {
            this.loading = true;
            params.isSupplyFlag = 1
            summaryByWarehouseList(page.currentPage, page.pageSize, Object.assign(params, this.query)).then(res => {
                const data = res.data.data;
                this.page.total = data.total;
                this.data = data.records;
                this.loading = false;
                this.selectionClear();
            });
        },
        //主界面基本方法 end
        exportOrderHandler(params = {}){
            this.$confirm("是否导出其他入库按仓库汇总数据?", "提示", {
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning"
            }).then(() => {
                params.isSupplyFlag = 1
                exportWarehouseList(Object.assign(params, this.query)).then(res => {
                    const blob = new Blob([res.data]);
                    const fileName = '其他入库按仓库汇总记录.xlsx';
                    const linkNode = document.createElement('a');

                    linkNode.download = fileName;
                    linkNode.style.display = 'none';
                    linkNode.href = URL.createObjectURL(blob);
                    document.body.appendChild(linkNode);
                    linkNode.click();

                    URL.revokeObjectURL(linkNode.href);
                    document.body.removeChild(linkNode);
                });
            });
        },
        printOrderHandler(){

        },
    }
};
</script>

<style>
.el-form-item{
    margin-bottom: 0px;
}
.head-label {
    color:black;
    font-weight: bold;
}
.table-head-label {
    font-weight: bold;
}
</style>
