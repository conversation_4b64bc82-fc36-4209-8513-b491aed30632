<template>
    <basic-container>
        <div style="overflow-y: scroll;margin-top: 20px;">
            <avue-crud :option="sumGoodsListOption"
                       :table-loading="sumGoodsListLoading"
                       :data="sumGoodsListData"
                       v-model="sumGoodsListForm"
                       ref="crudSelectSumGoods"
                       @search-change="sumGoodsSearchChange"
                       @search-reset="sumGoodsSearchReset"
                       @selection-change="sumGoodsSelectionChange"
            >
            </avue-crud>
        </div>
    </basic-container>
</template>
<script>
import {sumByGoods} from "@/api/supplier/supplierProduceReceiveOrder";
import {stockLoad} from "@/api/supplier/supplier";
export default {
    data() {
        return {
            querySumGoodsList: {},
            sumGoodsListLoading: false,
            sumGoodsListData: [],
            sumGoodsListForm: {},
            sumGoodsListOption: {
                height: 'auto',
                calcHeight: 30,
                searchShow: true,
                searchMenuSpan: 6,
                tip: false,
                border: true,
                index: false,
                menu:false,
                addBtn:false,
                align: 'center',
                column: [

                    {
                        label: "开始日期",
                        prop: "orderDateStart",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "结束日期",
                        prop: "orderDateEnd",
                        type: "date",
                        addDisplay: false,
                        editDisplay: false,
                        viewDisplay: false,
                        search: true,
                        hide: true,
                        format: 'yyyy-MM-dd',
                        valueFormat: 'yyyy-MM-dd',
                        span: 12,
                        rules: [ {
                            required: true,
                            message: '请选择单据日期',
                            trigger: 'click'
                        } ],
                        pickerOptions: {
                            // disabledDate(time) {
                            //     return time.getTime() > Date.now();
                            // }
                        }
                    },
                    {
                        label: "订单类型",
                        prop: "orderType",
                        type: "select",
                        hide: true,
                        dicData:[
                            {label:"领料单",value:0},
                            {label:"退料单",value:1}
                        ],
                        search: true,
                    },
                    {
                        label: "商品",
                        prop: "goodsId",
                        type: "select",
                        hide: true,
                        dicUrl: '/api/service/rabbit-supplier/product-list',
                        props: {
                            label: "name",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "仓库",
                        prop: "warehouseId",
                        type: "select",
                        hide: true,
                        dicUrl: "/api/service/rabbit-supplier/warehouse-list",
                        props: {
                            label: "warehouseName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "领退料人",
                        prop: "contactId",
                        type: "select",
                        hide: true,
                        dicUrl: '/api/service/rabbit-supplier/user-list',
                        props: {
                            label: "contactName",
                            value: "id"
                        },
                        search: true,
                    },
                    {
                        label: "仓库名称",
                        prop: "warehouseName",
                        type: "input",
                    },
                    {
                        label: "仓库地址",
                        prop: "warehouseAddress",
                        type: "input",
                    },
                    {
                        label: "仓管",
                        prop: "contactName",
                        type: "input",
                    },
                    {
                        label: "状态",
                        prop: "status",
                        type: "input",
                        formatter: (row, value) => {    //拼接字符串的用法 formatter
                            if(value === 0){
                                return "停用"
                            }
                            if(value === 1){
                                return "正常"
                            }
                        },
                    },

                    {
                        label: "数量",
                        prop: "sumAmount",
                        type: "input",
                    },
                    {
                        label: "出库成本金额",
                        prop: "sumAmt",
                        type: "input",
                    },
                ]
            },
        }

    },
    methods:{
        sumGoodsSearchChange(params, done) {
            this.querySumGoodsList = params;
            this.sumListOnLoad( params);
            done();
        },
        sumGoodsSearchReset() {
            this.querySumGoodsList = {};
        },
        sumGoodsSelectionChange(list) {
            this.selectionList = list;
        },
        selectionClearGoodsList() {
            this.selectionList = [];
            this.$refs.crudSelectSumGoods.toggleSelection();
        },
        sumListOnLoad(params = {}) {
            console.log(Object.assign(params, this.querySumGoodsList))
            sumByGoods('byStock',Object.assign(params, this.querySumGoodsList)).then(res => {
                const data = res.data.data;
                this.sumGoodsListData = data;
                this.sumGoodsListLoading = false;
                // this.selectionClearGoodsList();
            });
        },
    }
}
</script>
